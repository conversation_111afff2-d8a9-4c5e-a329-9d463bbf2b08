{"version": 3, "file": "playwright.config.js", "sourceRoot": "", "sources": ["playwright.config.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,YAAY,EAAE,OAAO,EAAE,MAAM,kBAAkB,CAAC;AAEzD;;GAEG;AACH,eAAe,YAAY,CAAC;IAC1B,OAAO,EAAE,OAAO;IAChB,wCAAwC;IACxC,OAAO,EAAE,EAAE,GAAG,IAAI;IAClB,MAAM,EAAE;QACN;;;WAGG;QACH,OAAO,EAAE,IAAI;KACd;IACD,oCAAoC;IACpC,aAAa,EAAE,IAAI;IACnB,iFAAiF;IACjF,UAAU,EAAE,CAAC,CAAC,OAAO,CAAC,GAAG,CAAC,EAAE;IAC5B,sBAAsB;IACtB,OAAO,EAAE,OAAO,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC/B,sCAAsC;IACtC,OAAO,EAAE,OAAO,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,SAAS;IACvC,qEAAqE;IACrE,QAAQ,EAAE,MAAM;IAChB,wGAAwG;IACxG,GAAG,EAAE;QACH,oFAAoF;QACpF,aAAa,EAAE,CAAC;QAChB,6DAA6D;QAC7D,OAAO,EAAE,uBAAuB;QAEhC,+FAA+F;QAC/F,KAAK,EAAE,gBAAgB;KACxB;IAED,2CAA2C;IAC3C,QAAQ,EAAE;QACR;YACE,IAAI,EAAE,UAAU;YAChB,GAAG,EAAE,EAAE,GAAG,OAAO,CAAC,gBAAgB,CAAC,EAAE;SACtC;QAED;YACE,IAAI,EAAE,SAAS;YACf,GAAG,EAAE,EAAE,GAAG,OAAO,CAAC,iBAAiB,CAAC,EAAE;SACvC;QAED;YACE,IAAI,EAAE,QAAQ;YACd,GAAG,EAAE,EAAE,GAAG,OAAO,CAAC,gBAAgB,CAAC,EAAE;SACtC;QAED,oCAAoC;QACpC;YACE,IAAI,EAAE,eAAe;YACrB,GAAG,EAAE,EAAE,GAAG,OAAO,CAAC,SAAS,CAAC,EAAE;SAC/B;QACD;YACE,IAAI,EAAE,eAAe;YACrB,GAAG,EAAE,EAAE,GAAG,OAAO,CAAC,WAAW,CAAC,EAAE;SACjC;KACF;IAED,yDAAyD;IACzD,SAAS,EAAE;QACT,OAAO,EAAE,YAAY;QACrB,IAAI,EAAE,IAAI;QACV,mBAAmB,EAAE,CAAC,OAAO,CAAC,GAAG,CAAC,EAAE;KACrC;CACF,CAAC,CAAC"}