#!/usr/bin/env node

/**
 * Documentation Cleanup Orchestrator for ADHD Trading Dashboard
 *
 * Comprehensive documentation cleanup that:
 * 1. Archives problematic files with broken references
 * 2. Rewrites core documentation with accurate information
 * 3. Validates the results
 * 4. Provides cleanup summary
 */

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';
import chalk from 'chalk';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);
const ROOT_DIR = path.join(__dirname, '../..');

/**
 * Main documentation cleanup orchestrator
 */
async function cleanupDocumentation() {
  console.log(chalk.blue('🧹 COMPREHENSIVE DOCUMENTATION CLEANUP'));
  console.log(chalk.blue('======================================\n'));

  const startTime = Date.now();
  let totalIssuesFixed = 0;

  try {
    // Phase 1: Archive problematic documentation
    console.log(chalk.yellow('📦 Phase 1: Archiving Problematic Documentation\n'));
    const { archiveProblematicDocumentation } = await import('./documentation-archiver.js');
    const archiveResults = await archiveProblematicDocumentation();
    totalIssuesFixed += archiveResults.archivedItems.length;

    console.log(
      chalk.green(`\n✅ Phase 1 Complete: ${archiveResults.archivedItems.length} files archived\n`)
    );

    // Phase 2: Rewrite core documentation
    console.log(chalk.yellow('📝 Phase 2: Rewriting Core Documentation\n'));
    const { rewriteCoreDocumentation } = await import('./documentation-rewriter.js');
    const rewriteResults = await rewriteCoreDocumentation();
    totalIssuesFixed += rewriteResults.filter((r) => r.success).length;

    console.log(
      chalk.green(
        `\n✅ Phase 2 Complete: ${rewriteResults.filter((r) => r.success).length} files rewritten\n`
      )
    );

    // Phase 3: Fix remaining issues
    console.log(chalk.yellow('🔧 Phase 3: Fixing Remaining Documentation Issues\n'));
    const { fixAllDocumentation } = await import('./documentation-fixer.js');
    const fixResults = await fixAllDocumentation();
    const totalFixes = fixResults.reduce((sum, r) => sum + (r.fixes?.length || 0), 0);
    totalIssuesFixed += totalFixes;

    console.log(chalk.green(`\n✅ Phase 3 Complete: ${totalFixes} issues fixed\n`));

    // Phase 4: Validation
    console.log(chalk.yellow('🔍 Phase 4: Validating Documentation Accuracy\n'));
    const { analyzeDocumentationAccuracy } = await import('./documentation-accuracy-checker.js');
    await analyzeDocumentationAccuracy();

    console.log(chalk.green(`\n✅ Phase 4 Complete: Documentation validated\n`));

    // Final Summary
    const endTime = Date.now();
    const duration = ((endTime - startTime) / 1000).toFixed(2);

    console.log(chalk.blue('🏁 CLEANUP COMPLETE'));
    console.log(chalk.blue('=================='));
    console.log(`⏱️  Duration: ${duration}s`);
    console.log(`🔧 Total Issues Fixed: ${totalIssuesFixed}`);
    console.log(`📦 Files Archived: ${archiveResults.archivedItems.length}`);
    console.log(`📝 Files Rewritten: ${rewriteResults.filter((r) => r.success).length}`);
    console.log(`🔗 References Fixed: ${totalFixes}`);

    // Next steps
    console.log(chalk.yellow('\n📋 NEXT STEPS:'));
    console.log('1. Review the validation results above');
    console.log('2. All problematic files have been permanently deleted');
    console.log('3. Update any remaining broken references manually');
    console.log('4. Run `yarn docs:check` periodically to maintain accuracy');

    return {
      success: true,
      totalIssuesFixed,
      archiveResults,
      rewriteResults,
      fixResults,
      duration,
    };
  } catch (error) {
    console.log(chalk.red(`\n❌ Cleanup failed: ${error.message}`));
    console.log(chalk.red(error.stack));

    return {
      success: false,
      error: error.message,
    };
  }
}

/**
 * Create cleanup summary report
 */
function createCleanupReport(results) {
  const reportPath = path.join(ROOT_DIR, 'docs/CLEANUP_REPORT.md');

  const content = `# Documentation Cleanup Report

> **🧹 Comprehensive cleanup completed on ${new Date().toISOString()}**

## 📊 Summary

- **Total Issues Fixed**: ${results.totalIssuesFixed}
- **Files Archived**: ${results.archiveResults?.archivedItems?.length || 0}
- **Files Rewritten**: ${results.rewriteResults?.filter((r) => r.success)?.length || 0}
- **Duration**: ${results.duration}s

## 📦 Archived Files

${
  results.archiveResults?.archivedItems
    ?.map((item) => `- \`${item.originalPath}\` → \`${item.archivePath}\``)
    .join('\n') || 'None'
}

## 📝 Rewritten Files

${
  results.rewriteResults
    ?.filter((r) => r.success)
    ?.map((item) => `- \`${item.path}\` - ${item.description}`)
    .join('\n') || 'None'
}

## 🔧 Maintenance

To maintain documentation accuracy:

1. **Regular Validation**: Run \`yarn docs:check\` monthly
2. **Update on Changes**: Update docs when modifying architecture
3. **Link Validation**: Verify links when adding new documentation
4. **Archive Old Content**: Move outdated content to archive

## 📚 Current Documentation

For current, accurate documentation see:

- **[Main Documentation Hub](./README.md)**
- **[Getting Started Guide](./GETTING_STARTED.md)**
- **[System Architecture](./ARCHITECTURE.md)**
- **[Development Workflow](./DEVELOPMENT.md)**

---

**Report Generated**: ${new Date().toISOString()}
**Cleanup Tool**: scripts/tools/documentation-cleanup.js
`;

  fs.writeFileSync(reportPath, content, 'utf8');
  return reportPath;
}

// Export for use in other tools
export { cleanupDocumentation };
export default cleanupDocumentation;

// Run if called directly
if (import.meta.url === `file://${process.argv[1]}`) {
  cleanupDocumentation()
    .then((results) => {
      if (results.success) {
        const reportPath = createCleanupReport(results);
        console.log(
          chalk.green(`\n📄 Cleanup report saved: ${path.relative(process.cwd(), reportPath)}`)
        );
      }
    })
    .catch(console.error);
}
