#!/usr/bin/env node

/**
 * Architecture Documentation Generator for ADHD Trading Dashboard
 *
 * Generates comprehensive documentation of the codebase's data flow patterns
 * and dependency relationships with visual diagrams and structured analysis.
 */

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';
import chalk from 'chalk';
import { analyzeDataFlow } from './data-flow-visualizer.js';
import { analyzeComponentRelationships } from './component-relationship-mapper.js';
import { analyzeStateManagement } from './state-management-analyzer.js';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

/**
 * Generate comprehensive architecture documentation
 */
async function generateArchitectureDocumentation() {
  console.log(chalk.blue('📚 GENERATING ARCHITECTURE DOCUMENTATION'));
  console.log(chalk.blue('==========================================\n'));

  try {
    // Run all analyses
    console.log(chalk.yellow('🔍 Running comprehensive analysis...\n'));
    
    const [dataFlowResults, componentResults, stateResults] = await Promise.all([
      analyzeDataFlow(),
      analyzeComponentRelationships(),
      analyzeStateManagement(),
    ]);

    // Generate documentation
    const documentation = generateDocumentationMarkdown(
      dataFlowResults,
      componentResults,
      stateResults
    );

    // Save documentation
    const outputPath = path.join(__dirname, '../../docs/ARCHITECTURE_ANALYSIS.md');
    fs.writeFileSync(outputPath, documentation);

    console.log(chalk.green(`✅ Architecture documentation generated: ${outputPath}\n`));

    // Generate individual diagram files
    await generateDiagramFiles(dataFlowResults, componentResults);

    return {
      dataFlowResults,
      componentResults,
      stateResults,
      documentationPath: outputPath,
    };
  } catch (error) {
    console.error(chalk.red('❌ Error generating documentation:'), error);
    throw error;
  }
}

/**
 * Generate markdown documentation
 */
function generateDocumentationMarkdown(dataFlowResults, componentResults, stateResults) {
  const timestamp = new Date().toISOString();
  
  return `# ADHD Trading Dashboard - Architecture Analysis

> **Generated on:** ${timestamp}
> **Tool:** Enhanced Development Tools Suite

## 📊 Executive Summary

This document provides a comprehensive analysis of the ADHD Trading Dashboard architecture, including data flow patterns, component relationships, and dependency structures.

### Key Metrics

- **Total Files Analyzed:** ${dataFlowResults.graph.nodes.size}
- **Components:** ${componentResults.graph.nodes.size}
- **Data Flow Connections:** ${dataFlowResults.graph.edges.length}
- **Component Relationships:** ${componentResults.graph.edges.length}
- **Performance Bottlenecks:** ${dataFlowResults.bottlenecks.length}
- **Architecture Violations:** ${componentResults.patterns.hierarchyViolations.length}

## 🌊 Data Flow Architecture

### Overview

The ADHD Trading Dashboard follows a simplified data flow pattern:

\`\`\`
Component → TradeStorageService → IndexedDB
\`\`\`

### Trade Data Lifecycle

\`\`\`mermaid
${dataFlowResults.diagrams.tradeLifecycle}
\`\`\`

### Data Flow Patterns

${generateDataFlowSection(dataFlowResults)}

## 🗺️ Component Architecture

### Package Dependencies

\`\`\`mermaid
${componentResults.diagrams.packageDependency}
\`\`\`

### Component Relationships

\`\`\`mermaid
${componentResults.diagrams.componentDependency}
\`\`\`

### Atomic Design Breakdown

${generateAtomicDesignSection(componentResults)}

## 🧠 State Management

${generateStateManagementSection(stateResults)}

## ⚠️ Architecture Issues

### Performance Bottlenecks

${generateBottlenecksSection(dataFlowResults.bottlenecks)}

### Hierarchy Violations

${generateViolationsSection(componentResults.patterns.hierarchyViolations)}

## 💡 Recommendations

${generateRecommendationsSection(dataFlowResults.recommendations, componentResults)}

## 📈 Metrics Dashboard

### File Type Distribution

${generateFileTypeDistribution(dataFlowResults)}

### Component Usage Analysis

${generateComponentUsageAnalysis(componentResults)}

---

*This documentation is automatically generated. For the latest analysis, run:*
\`\`\`bash
yarn analyze:all
\`\`\`
`;
}

/**
 * Generate data flow section
 */
function generateDataFlowSection(results) {
  const typeBreakdown = new Map();
  results.graph.nodes.forEach((node) => {
    typeBreakdown.set(node.type, (typeBreakdown.get(node.type) || 0) + 1);
  });

  let section = '#### File Type Breakdown\n\n';
  typeBreakdown.forEach((count, type) => {
    section += `- **${type}**: ${count} files\n`;
  });

  section += '\n#### Key Data Flow Patterns\n\n';
  section += '1. **Component → Hook → Service → Storage**: Primary data flow for trade operations\n';
  section += '2. **Context-based State Management**: React contexts for shared state\n';
  section += '3. **Service Layer Abstraction**: TradeStorageService abstracts IndexedDB operations\n';
  section += '4. **Hook-based Data Fetching**: Custom hooks manage data lifecycle\n\n';

  return section;
}

/**
 * Generate atomic design section
 */
function generateAtomicDesignSection(results) {
  let section = '';
  
  results.graph.hierarchy.forEach((components, level) => {
    section += `- **${level}**: ${components.length} components\n`;
  });

  section += '\n#### Most Used Components\n\n';
  results.patterns.mostUsed.slice(0, 5).forEach((item, index) => {
    section += `${index + 1}. **${item.name}** (used ${item.count} times) - ${item.component?.atomicLevel || 'unknown'} level\n`;
  });

  return section;
}

/**
 * Generate state management section
 */
function generateStateManagementSection(results) {
  if (!results || !results.patterns) {
    return 'State management analysis not available.\n';
  }

  let section = '### State Pattern Usage\n\n';
  Object.entries(results.patterns).forEach(([pattern, count]) => {
    section += `- **${pattern}**: ${count} occurrences\n`;
  });

  section += '\n### Context Usage\n\n';
  if (results.contexts && results.contexts.length > 0) {
    results.contexts.forEach(context => {
      section += `- **${context.name}**: ${context.providers} providers, ${context.consumers} consumers\n`;
    });
  }

  return section;
}

/**
 * Generate bottlenecks section
 */
function generateBottlenecksSection(bottlenecks) {
  if (bottlenecks.length === 0) {
    return 'No critical performance bottlenecks detected.\n';
  }

  let section = '';
  bottlenecks.slice(0, 5).forEach((bottleneck, index) => {
    section += `${index + 1}. **${path.basename(bottleneck.file)}**\n`;
    section += `   - Type: ${bottleneck.type}\n`;
    section += `   - Dependencies: ${bottleneck.degree}\n`;
    section += `   - Complexity: ${bottleneck.complexity}\n`;
    section += `   - Severity: ${bottleneck.severity}\n\n`;
  });

  return section;
}

/**
 * Generate violations section
 */
function generateViolationsSection(violations) {
  if (violations.length === 0) {
    return 'No atomic design hierarchy violations detected.\n';
  }

  let section = '';
  violations.slice(0, 10).forEach((violation, index) => {
    section += `${index + 1}. **${violation.violation}**\n`;
    section += `   - ${violation.from.name} → ${violation.to.name}\n`;
    section += `   - File: \`${violation.from.file}\`\n\n`;
  });

  return section;
}

/**
 * Generate recommendations section
 */
function generateRecommendationsSection(dataFlowRecs, componentResults) {
  let section = '### Data Flow Recommendations\n\n';
  
  if (dataFlowRecs.length === 0) {
    section += 'No critical data flow issues detected.\n\n';
  } else {
    dataFlowRecs.forEach((rec, index) => {
      section += `${index + 1}. **[${rec.type}] ${rec.target}**\n`;
      section += `   - Issue: ${rec.issue}\n`;
      section += `   - Solution: ${rec.solution}\n\n`;
    });
  }

  section += '### Component Architecture Recommendations\n\n';
  
  if (componentResults.patterns.leastUsed.length > 5) {
    section += '- 🧹 **Remove unused components** to reduce bundle size\n';
  }
  
  if (componentResults.patterns.hierarchyViolations.length > 0) {
    section += '- 🏗️ **Fix atomic design hierarchy violations**\n';
  }
  
  if (componentResults.patterns.crossFeature.length > 10) {
    section += '- 📦 **Move frequently cross-used components to shared package**\n';
  }

  const highComplexityComponents = Array.from(componentResults.graph.nodes.values())
    .filter(c => c.complexity.score > 50);
  
  if (highComplexityComponents.length > 0) {
    section += `- 🔧 **Refactor ${highComplexityComponents.length} high-complexity components**\n`;
  }

  return section;
}

/**
 * Generate file type distribution
 */
function generateFileTypeDistribution(results) {
  const typeBreakdown = new Map();
  results.graph.nodes.forEach((node) => {
    typeBreakdown.set(node.type, (typeBreakdown.get(node.type) || 0) + 1);
  });

  let section = '| File Type | Count | Percentage |\n';
  section += '|-----------|-------|------------|\n';
  
  const total = results.graph.nodes.size;
  typeBreakdown.forEach((count, type) => {
    const percentage = ((count / total) * 100).toFixed(1);
    section += `| ${type} | ${count} | ${percentage}% |\n`;
  });

  return section;
}

/**
 * Generate component usage analysis
 */
function generateComponentUsageAnalysis(results) {
  let section = '| Component | Usage Count | Level | Feature |\n';
  section += '|-----------|-------------|-------|----------|\n';
  
  results.patterns.mostUsed.slice(0, 10).forEach(item => {
    section += `| ${item.name} | ${item.count} | ${item.component?.atomicLevel || 'unknown'} | ${item.component?.feature || 'unknown'} |\n`;
  });

  return section;
}

/**
 * Generate individual diagram files
 */
async function generateDiagramFiles(dataFlowResults, componentResults) {
  const diagramsDir = path.join(__dirname, '../../docs/diagrams');
  
  // Ensure diagrams directory exists
  if (!fs.existsSync(diagramsDir)) {
    fs.mkdirSync(diagramsDir, { recursive: true });
  }

  // Save individual diagram files
  const diagrams = [
    { name: 'data-flow.mmd', content: dataFlowResults.diagrams.dataFlow },
    { name: 'trade-lifecycle.mmd', content: dataFlowResults.diagrams.tradeLifecycle },
    { name: 'component-dependencies.mmd', content: componentResults.diagrams.componentDependency },
    { name: 'package-dependencies.mmd', content: componentResults.diagrams.packageDependency },
  ];

  diagrams.forEach(diagram => {
    const filePath = path.join(diagramsDir, diagram.name);
    fs.writeFileSync(filePath, diagram.content);
    console.log(chalk.green(`✅ Diagram saved: ${filePath}`));
  });
}

// Export for use in other tools
export { generateArchitectureDocumentation };
export default generateArchitectureDocumentation;

// Run if called directly
if (import.meta.url === `file://${process.argv[1]}`) {
  generateArchitectureDocumentation().catch(console.error);
}
