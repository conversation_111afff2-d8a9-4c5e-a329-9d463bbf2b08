#!/usr/bin/env node

/**
 * Documentation Accuracy Checker for ADHD Trading Dashboard
 * 
 * Analyzes documentation files (.md) for accuracy against the actual codebase:
 * 1. Verifies file paths and references exist
 * 2. Checks package.json scripts match documented commands
 * 3. Validates code examples and snippets
 * 4. Identifies outdated information
 */

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';
import chalk from 'chalk';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);
const ROOT_DIR = path.join(__dirname, '../..');

/**
 * Get all markdown files
 */
function getMarkdownFiles(dir = ROOT_DIR) {
  const files = [];
  
  function traverse(currentDir) {
    if (!fs.existsSync(currentDir)) return;
    
    const items = fs.readdirSync(currentDir);
    
    for (const item of items) {
      const fullPath = path.join(currentDir, item);
      const stat = fs.statSync(fullPath);
      
      if (stat.isDirectory() && !item.startsWith('.') && item !== 'node_modules') {
        traverse(fullPath);
      } else if (item.endsWith('.md')) {
        files.push(fullPath);
      }
    }
  }
  
  traverse(dir);
  return files;
}

/**
 * Analyze a markdown file for accuracy issues
 */
function analyzeMarkdownFile(filePath) {
  try {
    const content = fs.readFileSync(filePath, 'utf8');
    const relativePath = path.relative(ROOT_DIR, filePath);
    
    const issues = [];
    const suggestions = [];
    
    // Check file references
    const fileReferences = extractFileReferences(content);
    fileReferences.forEach(ref => {
      const referencedPath = path.resolve(path.dirname(filePath), ref);
      if (!fs.existsSync(referencedPath)) {
        issues.push({
          type: 'BROKEN_FILE_REFERENCE',
          severity: 'HIGH',
          line: findLineNumber(content, ref),
          message: `Referenced file does not exist: ${ref}`,
          suggestion: 'Update file path or create missing file'
        });
      }
    });
    
    // Check package.json script references
    const scriptReferences = extractScriptReferences(content);
    const packageJson = getPackageJson();
    if (packageJson) {
      scriptReferences.forEach(script => {
        if (!packageJson.scripts || !packageJson.scripts[script]) {
          issues.push({
            type: 'INVALID_SCRIPT',
            severity: 'MEDIUM',
            line: findLineNumber(content, script),
            message: `Script "${script}" not found in package.json`,
            suggestion: 'Update script name or add script to package.json'
          });
        }
      });
    }
    
    // Check code blocks for syntax issues
    const codeBlocks = extractCodeBlocks(content);
    codeBlocks.forEach(block => {
      if (block.language === 'bash' || block.language === 'sh') {
        // Check for common bash command issues
        if (block.code.includes('npm ') && packageJson?.packageManager !== 'npm') {
          suggestions.push({
            type: 'PACKAGE_MANAGER_MISMATCH',
            severity: 'LOW',
            line: block.line,
            message: 'Using npm commands but project uses yarn',
            suggestion: 'Consider updating to yarn commands'
          });
        }
      }
    });
    
    // Check for outdated information
    const lastModified = fs.statSync(filePath).mtime;
    const daysSinceModified = (Date.now() - lastModified.getTime()) / (1000 * 60 * 60 * 24);
    
    if (daysSinceModified > 90) {
      suggestions.push({
        type: 'POTENTIALLY_OUTDATED',
        severity: 'LOW',
        message: `File not updated in ${Math.round(daysSinceModified)} days`,
        suggestion: 'Review for outdated information'
      });
    }
    
    // Check for TODO/FIXME comments
    const todos = content.match(/TODO|FIXME|XXX/gi) || [];
    if (todos.length > 0) {
      suggestions.push({
        type: 'INCOMPLETE_DOCUMENTATION',
        severity: 'MEDIUM',
        message: `Found ${todos.length} TODO/FIXME markers`,
        suggestion: 'Complete or remove TODO items'
      });
    }
    
    return {
      file: relativePath,
      lastModified,
      issues,
      suggestions,
      stats: {
        lines: content.split('\n').length,
        words: content.split(/\s+/).length,
        codeBlocks: codeBlocks.length,
        fileReferences: fileReferences.length,
        scriptReferences: scriptReferences.length
      }
    };
  } catch (error) {
    return {
      file: path.relative(ROOT_DIR, filePath),
      error: error.message,
      issues: [{
        type: 'READ_ERROR',
        severity: 'HIGH',
        message: `Cannot read file: ${error.message}`
      }],
      suggestions: []
    };
  }
}

/**
 * Extract file references from markdown content
 */
function extractFileReferences(content) {
  const references = [];
  
  // Match file paths in various formats
  const patterns = [
    /`([^`]+\.(js|ts|tsx|jsx|json|md))`/g,  // Inline code with file extensions
    /\[.*?\]\(([^)]+\.(js|ts|tsx|jsx|json|md))\)/g,  // Markdown links
    /packages\/[^\s\)]+/g,  // Package paths
    /src\/[^\s\)]+/g,  // Source paths
    /scripts\/[^\s\)]+/g  // Script paths
  ];
  
  patterns.forEach(pattern => {
    let match;
    while ((match = pattern.exec(content)) !== null) {
      const ref = match[1] || match[0];
      if (ref && !ref.startsWith('http') && !references.includes(ref)) {
        references.push(ref);
      }
    }
  });
  
  return references;
}

/**
 * Extract script references from markdown content
 */
function extractScriptReferences(content) {
  const scripts = [];
  
  // Match yarn/npm script commands
  const patterns = [
    /yarn\s+([a-zA-Z0-9:_-]+)/g,
    /npm\s+run\s+([a-zA-Z0-9:_-]+)/g
  ];
  
  patterns.forEach(pattern => {
    let match;
    while ((match = pattern.exec(content)) !== null) {
      const script = match[1];
      if (script && !scripts.includes(script)) {
        scripts.push(script);
      }
    }
  });
  
  return scripts;
}

/**
 * Extract code blocks from markdown
 */
function extractCodeBlocks(content) {
  const blocks = [];
  const lines = content.split('\n');
  let inCodeBlock = false;
  let currentBlock = null;
  
  lines.forEach((line, index) => {
    if (line.startsWith('```')) {
      if (inCodeBlock) {
        // End of code block
        if (currentBlock) {
          blocks.push(currentBlock);
        }
        inCodeBlock = false;
        currentBlock = null;
      } else {
        // Start of code block
        inCodeBlock = true;
        currentBlock = {
          language: line.slice(3).trim(),
          code: '',
          line: index + 1
        };
      }
    } else if (inCodeBlock && currentBlock) {
      currentBlock.code += line + '\n';
    }
  });
  
  return blocks;
}

/**
 * Find line number of text in content
 */
function findLineNumber(content, text) {
  const lines = content.split('\n');
  for (let i = 0; i < lines.length; i++) {
    if (lines[i].includes(text)) {
      return i + 1;
    }
  }
  return null;
}

/**
 * Get package.json content
 */
function getPackageJson() {
  try {
    const packagePath = path.join(ROOT_DIR, 'package.json');
    return JSON.parse(fs.readFileSync(packagePath, 'utf8'));
  } catch (error) {
    return null;
  }
}

/**
 * Display analysis results
 */
function displayResults(analyses) {
  console.log(chalk.blue('📚 DOCUMENTATION ACCURACY ANALYSIS'));
  console.log(chalk.blue('===================================\n'));
  
  const totalFiles = analyses.length;
  const filesWithIssues = analyses.filter(a => a.issues.length > 0).length;
  const totalIssues = analyses.reduce((sum, a) => sum + a.issues.length, 0);
  const totalSuggestions = analyses.reduce((sum, a) => sum + a.suggestions.length, 0);
  
  // Summary
  console.log(chalk.yellow('📊 SUMMARY:'));
  console.log(`   Total Documentation Files: ${totalFiles}`);
  console.log(`   Files with Issues: ${filesWithIssues}`);
  console.log(`   Total Issues: ${totalIssues}`);
  console.log(`   Total Suggestions: ${totalSuggestions}\n`);
  
  // High severity issues
  const highSeverityIssues = analyses.flatMap(a => 
    a.issues.filter(i => i.severity === 'HIGH').map(i => ({ ...i, file: a.file }))
  );
  
  if (highSeverityIssues.length > 0) {
    console.log(chalk.red('🚨 HIGH SEVERITY ISSUES:'));
    console.log('-------------------------');
    highSeverityIssues.forEach((issue, index) => {
      console.log(`${index + 1}. ${issue.file}${issue.line ? `:${issue.line}` : ''}`);
      console.log(`   Type: ${issue.type}`);
      console.log(`   Issue: ${issue.message}`);
      console.log(`   Fix: ${issue.suggestion}`);
      console.log('');
    });
  }
  
  // Medium severity issues
  const mediumSeverityIssues = analyses.flatMap(a => 
    a.issues.filter(i => i.severity === 'MEDIUM').map(i => ({ ...i, file: a.file }))
  );
  
  if (mediumSeverityIssues.length > 0) {
    console.log(chalk.yellow('⚠️  MEDIUM SEVERITY ISSUES:'));
    console.log('---------------------------');
    mediumSeverityIssues.slice(0, 10).forEach((issue, index) => {
      console.log(`${index + 1}. ${issue.file}${issue.line ? `:${issue.line}` : ''}`);
      console.log(`   Issue: ${issue.message}`);
      console.log(`   Fix: ${issue.suggestion}`);
      console.log('');
    });
    
    if (mediumSeverityIssues.length > 10) {
      console.log(`   ... and ${mediumSeverityIssues.length - 10} more\n`);
    }
  }
  
  // Suggestions
  const allSuggestions = analyses.flatMap(a => 
    a.suggestions.map(s => ({ ...s, file: a.file }))
  );
  
  if (allSuggestions.length > 0) {
    console.log(chalk.green('💡 IMPROVEMENT SUGGESTIONS:'));
    console.log('----------------------------');
    allSuggestions.slice(0, 5).forEach((suggestion, index) => {
      console.log(`${index + 1}. ${suggestion.file}`);
      console.log(`   Suggestion: ${suggestion.message}`);
      console.log(`   Action: ${suggestion.suggestion}`);
      console.log('');
    });
    
    if (allSuggestions.length > 5) {
      console.log(`   ... and ${allSuggestions.length - 5} more suggestions\n`);
    }
  }
  
  // File statistics
  const oldestFile = analyses.reduce((oldest, current) => 
    current.lastModified < oldest.lastModified ? current : oldest
  );
  
  console.log(chalk.cyan('📈 DOCUMENTATION STATISTICS:'));
  console.log('-----------------------------');
  console.log(`Oldest File: ${oldestFile.file} (${oldestFile.lastModified.toLocaleDateString()})`);
  
  const totalLines = analyses.reduce((sum, a) => sum + (a.stats?.lines || 0), 0);
  const totalWords = analyses.reduce((sum, a) => sum + (a.stats?.words || 0), 0);
  
  console.log(`Total Lines: ${totalLines.toLocaleString()}`);
  console.log(`Total Words: ${totalWords.toLocaleString()}`);
  console.log(`Average Words per File: ${Math.round(totalWords / totalFiles)}`);
}

/**
 * Main analysis function
 */
async function analyzeDocumentationAccuracy() {
  console.log(chalk.blue('🔍 Analyzing documentation accuracy...\n'));
  
  const markdownFiles = getMarkdownFiles();
  console.log(`📁 Found ${markdownFiles.length} documentation files\n`);
  
  const analyses = markdownFiles.map(analyzeMarkdownFile);
  
  displayResults(analyses);
  
  return analyses;
}

// Export for use in other tools
export { analyzeDocumentationAccuracy };
export default analyzeDocumentationAccuracy;

// Run if called directly
if (import.meta.url === `file://${process.argv[1]}`) {
  analyzeDocumentationAccuracy().catch(console.error);
}
