#!/usr/bin/env node

/**
 * Documentation Archiver for ADHD Trading Dashboard
 * 
 * Archives problematic documentation files with extensive broken references
 * while preserving historical context and creating clean, accurate replacements.
 */

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';
import chalk from 'chalk';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);
const ROOT_DIR = path.join(__dirname, '../..');

// Files to archive (high broken reference count)
const FILES_TO_ARCHIVE = [
  'docs/migration/LEGACY_DOCS.md',
  'augment-instructions/comprehensive-tasks.md',
  'augment-instructions/immediate-tasks.md',
  'augment-instructions/moderate-tasks.md',
  'augment-instructions/quick-tasks.md',
  'augment-instructions/TASK_SUMMARY.md',
  'augment-instructions/individual-tasks/',
  'TRADE_LOGGING_CLEANUP_COMPLETE.md',
  'TRADING_ANALYSIS_CLEANUP_COMPLETE.md',
  'IMPORT_FEATURE_INTEGRATION_GUIDE.md'
];

// Core files to rewrite
const CORE_FILES_TO_REWRITE = [
  'README.md',
  'docs/GETTING_STARTED.md',
  'docs/ARCHITECTURE.md', 
  'docs/DEVELOPMENT.md',
  'CLAUDE.md'
];

/**
 * Create archive directory structure
 */
function createArchiveStructure() {
  const archiveDir = path.join(ROOT_DIR, 'docs/archive');
  const subdirs = [
    'legacy-docs',
    'task-instructions', 
    'cleanup-reports',
    'migration-guides'
  ];
  
  if (!fs.existsSync(archiveDir)) {
    fs.mkdirSync(archiveDir, { recursive: true });
  }
  
  subdirs.forEach(subdir => {
    const subdirPath = path.join(archiveDir, subdir);
    if (!fs.existsSync(subdirPath)) {
      fs.mkdirSync(subdirPath, { recursive: true });
    }
  });
  
  return archiveDir;
}

/**
 * Archive a file or directory
 */
function archiveItem(itemPath, archiveSubdir) {
  const fullPath = path.join(ROOT_DIR, itemPath);
  
  if (!fs.existsSync(fullPath)) {
    return { success: false, reason: 'File not found' };
  }
  
  const archiveDir = createArchiveStructure();
  const targetDir = path.join(archiveDir, archiveSubdir);
  const itemName = path.basename(itemPath);
  const targetPath = path.join(targetDir, itemName);
  
  try {
    if (fs.statSync(fullPath).isDirectory()) {
      // Copy directory recursively
      copyDirectoryRecursive(fullPath, targetPath);
    } else {
      // Copy file
      fs.copyFileSync(fullPath, targetPath);
    }
    
    // Remove original
    if (fs.statSync(fullPath).isDirectory()) {
      fs.rmSync(fullPath, { recursive: true, force: true });
    } else {
      fs.unlinkSync(fullPath);
    }
    
    return { 
      success: true, 
      originalPath: itemPath,
      archivePath: path.relative(ROOT_DIR, targetPath)
    };
  } catch (error) {
    return { 
      success: false, 
      reason: error.message,
      originalPath: itemPath
    };
  }
}

/**
 * Copy directory recursively
 */
function copyDirectoryRecursive(src, dest) {
  if (!fs.existsSync(dest)) {
    fs.mkdirSync(dest, { recursive: true });
  }
  
  const items = fs.readdirSync(src);
  
  for (const item of items) {
    const srcPath = path.join(src, item);
    const destPath = path.join(dest, item);
    
    if (fs.statSync(srcPath).isDirectory()) {
      copyDirectoryRecursive(srcPath, destPath);
    } else {
      fs.copyFileSync(srcPath, destPath);
    }
  }
}

/**
 * Create archive index file
 */
function createArchiveIndex(archivedItems) {
  const archiveDir = createArchiveStructure();
  const indexPath = path.join(archiveDir, 'README.md');
  
  const content = `# Documentation Archive

> **📚 Historical documentation preserved for reference**

This directory contains archived documentation that was moved due to extensive broken references or outdated content. The files are preserved for historical context but should not be used for current development.

## 📁 Archive Structure

### Legacy Documentation
- **legacy-docs/** - Old documentation with broken references
- **migration-guides/** - Historical migration documentation

### Task Instructions  
- **task-instructions/** - Outdated development task files
- **cleanup-reports/** - Historical cleanup and refactoring reports

## 📋 Archived Files

${archivedItems.map(item => `- \`${item.originalPath}\` → \`${item.archivePath}\``).join('\n')}

## ⚠️ Important Notes

- **Do not reference these files** in current documentation
- **Use current documentation** in the main \`docs/\` directory
- **Historical context only** - content may be severely outdated
- **Broken references expected** - files were archived due to accuracy issues

## 🔗 Current Documentation

For up-to-date documentation, see:

- **[Main Documentation](../README.md)** - Current documentation hub
- **[Getting Started](../GETTING_STARTED.md)** - Current setup guide  
- **[Architecture](../ARCHITECTURE.md)** - Current system design
- **[Development](../DEVELOPMENT.md)** - Current development workflow

---

**Archive Created:** ${new Date().toISOString()}
**Reason:** Extensive broken references and outdated content
`;

  fs.writeFileSync(indexPath, content, 'utf8');
  return indexPath;
}

/**
 * Main archiving function
 */
async function archiveProblematicDocumentation() {
  console.log(chalk.blue('📚 ARCHIVING PROBLEMATIC DOCUMENTATION'));
  console.log(chalk.blue('=====================================\n'));
  
  const archivedItems = [];
  const errors = [];
  
  // Archive files
  for (const filePath of FILES_TO_ARCHIVE) {
    console.log(chalk.yellow(`📦 Archiving: ${filePath}`));
    
    let archiveSubdir;
    if (filePath.includes('augment-instructions')) {
      archiveSubdir = 'task-instructions';
    } else if (filePath.includes('CLEANUP') || filePath.includes('INTEGRATION')) {
      archiveSubdir = 'cleanup-reports';
    } else if (filePath.includes('migration') || filePath.includes('LEGACY')) {
      archiveSubdir = 'legacy-docs';
    } else {
      archiveSubdir = 'migration-guides';
    }
    
    const result = archiveItem(filePath, archiveSubdir);
    
    if (result.success) {
      archivedItems.push(result);
      console.log(chalk.green(`  ✅ Archived to: ${result.archivePath}`));
    } else {
      errors.push(result);
      console.log(chalk.red(`  ❌ Failed: ${result.reason}`));
    }
  }
  
  // Create archive index
  if (archivedItems.length > 0) {
    const indexPath = createArchiveIndex(archivedItems);
    console.log(chalk.green(`\n📋 Created archive index: ${path.relative(ROOT_DIR, indexPath)}`));
  }
  
  // Summary
  console.log(chalk.blue('\n📊 ARCHIVE SUMMARY:'));
  console.log('-------------------');
  console.log(`Successfully Archived: ${archivedItems.length}`);
  console.log(`Errors: ${errors.length}`);
  console.log(`Total Files Processed: ${FILES_TO_ARCHIVE.length}`);
  
  if (errors.length > 0) {
    console.log(chalk.red('\n❌ ERRORS:'));
    errors.forEach(error => {
      console.log(`  - ${error.originalPath}: ${error.reason}`);
    });
  }
  
  return { archivedItems, errors };
}

// Export for use in other tools
export { archiveProblematicDocumentation };
export default archiveProblematicDocumentation;

// Run if called directly
if (import.meta.url === `file://${process.argv[1]}`) {
  archiveProblematicDocumentation().catch(console.error);
}
