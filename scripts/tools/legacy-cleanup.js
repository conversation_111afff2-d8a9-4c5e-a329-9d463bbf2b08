#!/usr/bin/env node

/**
 * Legacy Tool Cleanup for ADHD Trading Dashboard
 * 
 * Identifies and optionally removes legacy development tools that have been
 * replaced by the new comprehensive development tools suite.
 */

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';
import chalk from 'chalk';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);
const ROOT_DIR = path.join(__dirname, '../..');

// Legacy tools that should be cleaned up
const LEGACY_TOOLS = [
  {
    file: 'dependency-analyzer.js',
    reason: 'Replaced by scripts/tools/data-flow-visualizer.js',
    replacement: 'yarn analyze:data-flow'
  },
  {
    file: 'dependency-magnet-analyzer.js', 
    reason: 'Functionality integrated into new analysis tools',
    replacement: 'yarn analyze:components'
  },
  {
    file: 'priority-refactor-analyzer.js',
    reason: 'Functionality integrated into new analysis tools', 
    replacement: 'yarn analyze:performance'
  },
  {
    file: 'schema-validator.js',
    reason: 'Could be moved to scripts/tools/ if still needed',
    replacement: 'Consider moving to scripts/tools/'
  }
];

// Other files that might be outdated
const POTENTIAL_CLEANUP = [
  'cli.js',
  'server.js',
  'babel.config.js'
];

/**
 * Check if legacy tools exist and analyze their usage
 */
function analyzeLegacyTools() {
  console.log(chalk.blue('🧹 LEGACY TOOL CLEANUP ANALYSIS'));
  console.log(chalk.blue('=================================\n'));

  const results = {
    legacyTools: [],
    potentialCleanup: [],
    recommendations: []
  };

  // Check legacy tools
  console.log(chalk.yellow('📋 LEGACY DEVELOPMENT TOOLS:'));
  console.log('-----------------------------');

  LEGACY_TOOLS.forEach((tool, index) => {
    const filePath = path.join(ROOT_DIR, tool.file);
    const exists = fs.existsSync(filePath);
    
    console.log(`${index + 1}. ${tool.file}`);
    console.log(`   Status: ${exists ? chalk.red('EXISTS') : chalk.green('ALREADY REMOVED')}`);
    console.log(`   Reason: ${tool.reason}`);
    console.log(`   Replacement: ${chalk.cyan(tool.replacement)}`);
    
    if (exists) {
      const stats = fs.statSync(filePath);
      console.log(`   Size: ${(stats.size / 1024).toFixed(1)}KB`);
      results.legacyTools.push({
        ...tool,
        path: filePath,
        size: stats.size
      });
    }
    console.log('');
  });

  // Check potential cleanup files
  console.log(chalk.yellow('🔍 POTENTIAL CLEANUP FILES:'));
  console.log('----------------------------');

  POTENTIAL_CLEANUP.forEach((file, index) => {
    const filePath = path.join(ROOT_DIR, file);
    const exists = fs.existsSync(filePath);
    
    if (exists) {
      const stats = fs.statSync(filePath);
      console.log(`${index + 1}. ${file}`);
      console.log(`   Size: ${(stats.size / 1024).toFixed(1)}KB`);
      console.log(`   Last Modified: ${stats.mtime.toLocaleDateString()}`);
      
      results.potentialCleanup.push({
        file,
        path: filePath,
        size: stats.size,
        lastModified: stats.mtime
      });
      console.log('');
    }
  });

  // Generate recommendations
  if (results.legacyTools.length > 0) {
    results.recommendations.push({
      type: 'CLEANUP',
      priority: 'HIGH',
      action: 'Remove legacy development tools',
      files: results.legacyTools.map(t => t.file),
      benefit: 'Reduce codebase clutter and confusion'
    });
  }

  if (results.potentialCleanup.length > 0) {
    results.recommendations.push({
      type: 'REVIEW',
      priority: 'MEDIUM', 
      action: 'Review potential cleanup files',
      files: results.potentialCleanup.map(t => t.file),
      benefit: 'Ensure no unused configuration files'
    });
  }

  return results;
}

/**
 * Display cleanup recommendations
 */
function displayRecommendations(results) {
  if (results.recommendations.length === 0) {
    console.log(chalk.green('✅ No cleanup needed - codebase is clean!\n'));
    return;
  }

  console.log(chalk.green('💡 CLEANUP RECOMMENDATIONS:'));
  console.log('----------------------------');

  results.recommendations.forEach((rec, index) => {
    console.log(`${index + 1}. [${rec.priority}] ${rec.action}`);
    console.log(`   Type: ${rec.type}`);
    console.log(`   Files: ${rec.files.join(', ')}`);
    console.log(`   Benefit: ${rec.benefit}`);
    console.log('');
  });

  // Safe removal commands
  if (results.legacyTools.length > 0) {
    console.log(chalk.cyan('🔧 SAFE REMOVAL COMMANDS:'));
    console.log('-------------------------');
    console.log('# Remove legacy tools (backup first!)');
    results.legacyTools.forEach(tool => {
      console.log(`git mv ${tool.file} ${tool.file}.backup  # Backup first`);
    });
    console.log('');
    console.log('# After confirming everything works:');
    results.legacyTools.forEach(tool => {
      console.log(`rm ${tool.file}.backup`);
    });
    console.log('');
  }

  // Migration guide
  console.log(chalk.yellow('📖 MIGRATION GUIDE:'));
  console.log('-------------------');
  console.log('Old Command → New Command');
  LEGACY_TOOLS.forEach(tool => {
    if (tool.replacement.startsWith('yarn')) {
      console.log(`node ${tool.file} → ${chalk.cyan(tool.replacement)}`);
    }
  });
  console.log('');
}

/**
 * Check for references to legacy tools
 */
function checkReferences(results) {
  console.log(chalk.yellow('🔍 CHECKING FOR REFERENCES:'));
  console.log('----------------------------');

  const filesToCheck = [
    'package.json',
    'README.md',
    'docs/**/*.md',
    '.github/**/*.yml'
  ];

  // This is a simplified check - in a real implementation,
  // you'd want to recursively search through files
  const packageJsonPath = path.join(ROOT_DIR, 'package.json');
  if (fs.existsSync(packageJsonPath)) {
    const packageJson = JSON.parse(fs.readFileSync(packageJsonPath, 'utf8'));
    
    // Check scripts
    const scripts = packageJson.scripts || {};
    const referencedTools = [];
    
    Object.entries(scripts).forEach(([scriptName, command]) => {
      LEGACY_TOOLS.forEach(tool => {
        if (command.includes(tool.file)) {
          referencedTools.push({
            script: scriptName,
            command,
            tool: tool.file
          });
        }
      });
    });

    if (referencedTools.length > 0) {
      console.log(chalk.red('⚠️  Found references in package.json:'));
      referencedTools.forEach(ref => {
        console.log(`   Script "${ref.script}": ${ref.command}`);
        console.log(`   References: ${ref.tool}`);
      });
      console.log('');
    } else {
      console.log(chalk.green('✅ No references found in package.json'));
    }
  }
}

/**
 * Main cleanup analysis
 */
async function runCleanupAnalysis() {
  console.log(chalk.blue('🔍 Analyzing legacy tools and cleanup opportunities...\n'));

  const results = analyzeLegacyTools();
  checkReferences(results);
  displayRecommendations(results);

  // Summary
  const totalLegacySize = results.legacyTools.reduce((sum, tool) => sum + tool.size, 0);
  const totalPotentialSize = results.potentialCleanup.reduce((sum, file) => sum + file.size, 0);

  console.log(chalk.blue('📊 CLEANUP SUMMARY:'));
  console.log('-------------------');
  console.log(`Legacy Tools: ${results.legacyTools.length} files (${(totalLegacySize / 1024).toFixed(1)}KB)`);
  console.log(`Potential Cleanup: ${results.potentialCleanup.length} files (${(totalPotentialSize / 1024).toFixed(1)}KB)`);
  console.log(`Total Potential Savings: ${((totalLegacySize + totalPotentialSize) / 1024).toFixed(1)}KB`);

  return results;
}

// Export for use in other tools
export { runCleanupAnalysis };
export default runCleanupAnalysis;

// Run if called directly
if (import.meta.url === `file://${process.argv[1]}`) {
  runCleanupAnalysis().catch(console.error);
}
