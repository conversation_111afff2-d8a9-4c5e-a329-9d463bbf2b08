#!/usr/bin/env node

/**
 * Data Flow Visualizer for ADHD Trading Dashboard
 *
 * Analyzes and visualizes the data flow architecture:
 * 1. Component → Context → Service → Storage flow
 * 2. State management patterns
 * 3. Data dependencies and relationships
 * 4. Performance bottlenecks in data flow
 */

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';
import chalk from 'chalk';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Configuration
const DASHBOARD_SRC = path.join(__dirname, '../../packages/dashboard/src');
const SHARED_SRC = path.join(__dirname, '../../packages/shared/src');

// Analysis results
const dataFlowMap = {
  components: new Map(),
  contexts: new Map(),
  services: new Map(),
  hooks: new Map(),
  flows: [],
  bottlenecks: [],
  recommendations: [],
};

/**
 * Get all TypeScript/React files
 */
function getAllFiles(dir, files = []) {
  if (!fs.existsSync(dir)) return files;

  const items = fs.readdirSync(dir);

  for (const item of items) {
    const fullPath = path.join(dir, item);
    const stat = fs.statSync(fullPath);

    if (stat.isDirectory() && !item.startsWith('.') && item !== 'node_modules') {
      getAllFiles(fullPath, files);
    } else if (item.match(/\.(tsx?|jsx?)$/)) {
      files.push(fullPath);
    }
  }

  return files;
}

/**
 * Analyze data flow patterns in a file
 */
function analyzeFileDataFlow(filePath) {
  try {
    const content = fs.readFileSync(filePath, 'utf8');
    const relativePath = path.relative(process.cwd(), filePath);

    const analysis = {
      file: relativePath,
      type: getFileType(filePath, content),
      dataPatterns: extractDataPatterns(content),
      dependencies: extractDependencies(content),
      stateUsage: extractStateUsage(content),
      performance: analyzePerformance(content),
    };

    return analysis;
  } catch (error) {
    console.warn(`Error analyzing ${filePath}: ${error.message}`);
    return null;
  }
}

/**
 * Determine file type based on path and content
 */
function getFileType(filePath, content) {
  if (filePath.includes('/context/') || content.includes('createContext')) {
    return 'context';
  }
  if (filePath.includes('/hooks/') || content.includes('use')) {
    return 'hook';
  }
  if (filePath.includes('/services/') || content.includes('Service')) {
    return 'service';
  }
  if (content.includes('export default') && content.includes('return')) {
    return 'component';
  }
  return 'utility';
}

/**
 * Extract data flow patterns
 */
function extractDataPatterns(content) {
  const patterns = {
    contextUsage: (content.match(/useContext\(/g) || []).length,
    hookUsage: (content.match(/use\w+\(/g) || []).length,
    stateUpdates: (content.match(/setState|dispatch/g) || []).length,
    asyncCalls: (content.match(/await|\.then\(/g) || []).length,
    storageAccess: (content.match(/tradeStorage|IndexedDB/g) || []).length,
  };

  return patterns;
}

/**
 * Extract dependencies
 */
function extractDependencies(content) {
  const dependencies = [];

  // Import statements
  const importRegex = /import\s+.*from\s+['"`]([^'"`]+)['"`]/g;
  let match;
  while ((match = importRegex.exec(content)) !== null) {
    dependencies.push({
      type: 'import',
      source: match[1],
      isInternal: match[1].startsWith('@adhd-trading-dashboard') || match[1].startsWith('.'),
    });
  }

  // Service usage
  const serviceRegex = /(tradeStorage|tradeStorageService)\.\w+/g;
  while ((match = serviceRegex.exec(content)) !== null) {
    dependencies.push({
      type: 'service',
      source: 'tradeStorageService',
      method: match[0],
    });
  }

  return dependencies;
}

/**
 * Extract state usage patterns
 */
function extractStateUsage(content) {
  return {
    useState: (content.match(/useState\(/g) || []).length,
    useEffect: (content.match(/useEffect\(/g) || []).length,
    useCallback: (content.match(/useCallback\(/g) || []).length,
    useMemo: (content.match(/useMemo\(/g) || []).length,
    useSelector: (content.match(/useSelector\(/g) || []).length,
    useStore: (content.match(/useStore\(/g) || []).length,
  };
}

/**
 * Analyze performance implications
 */
function analyzePerformance(content) {
  const issues = [];

  // Check for potential performance issues
  if (content.includes('useEffect') && !content.includes('dependencies')) {
    issues.push('Missing useEffect dependencies');
  }

  if ((content.match(/useState\(/g) || []).length > 5) {
    issues.push('High local state usage - consider context');
  }

  if (content.includes('JSON.parse') && content.includes('JSON.stringify')) {
    issues.push('Frequent serialization - performance concern');
  }

  return {
    issues,
    complexity: calculateComplexity(content),
  };
}

/**
 * Calculate complexity score
 */
function calculateComplexity(content) {
  const lines = content.split('\n').length;
  const functions = (content.match(/function|=>/g) || []).length;
  const conditionals = (content.match(/if|switch|ternary|\?/g) || []).length;

  return Math.round(lines / 10 + functions + conditionals * 2);
}

/**
 * Build data flow graph
 */
function buildDataFlowGraph(analyses) {
  const graph = {
    nodes: new Map(),
    edges: [],
  };

  // Create nodes
  analyses.forEach((analysis) => {
    if (analysis) {
      graph.nodes.set(analysis.file, {
        ...analysis,
        id: analysis.file,
        label: path.basename(analysis.file),
      });
    }
  });

  // Create edges based on dependencies
  analyses.forEach((analysis) => {
    if (analysis && analysis.dependencies) {
      analysis.dependencies.forEach((dep) => {
        if (dep.isInternal) {
          graph.edges.push({
            from: analysis.file,
            to: dep.source,
            type: dep.type,
            weight: 1,
          });
        }
      });
    }
  });

  return graph;
}

/**
 * Identify bottlenecks
 */
function identifyBottlenecks(graph) {
  const bottlenecks = [];

  // Find nodes with high in-degree (many dependencies)
  const inDegree = new Map();
  graph.edges.forEach((edge) => {
    inDegree.set(edge.to, (inDegree.get(edge.to) || 0) + 1);
  });

  // Find nodes with high complexity and high usage
  graph.nodes.forEach((node, id) => {
    const degree = inDegree.get(id) || 0;
    const complexity = node.performance.complexity;

    if (degree > 5 && complexity > 20) {
      bottlenecks.push({
        file: id,
        type: 'High Usage + High Complexity',
        degree,
        complexity,
        severity: 'HIGH',
      });
    } else if (degree > 8) {
      bottlenecks.push({
        file: id,
        type: 'Dependency Magnet',
        degree,
        complexity,
        severity: 'MEDIUM',
      });
    }
  });

  return bottlenecks.sort((a, b) => b.degree - a.degree);
}

/**
 * Generate recommendations
 */
function generateRecommendations(graph, bottlenecks) {
  const recommendations = [];

  // Bottleneck recommendations
  bottlenecks.forEach((bottleneck) => {
    if (bottleneck.severity === 'HIGH') {
      recommendations.push({
        type: 'CRITICAL',
        target: bottleneck.file,
        issue: 'High complexity component with many dependencies',
        solution: 'Split into smaller components, extract business logic to hooks',
      });
    }
  });

  // Pattern recommendations
  const contextFiles = Array.from(graph.nodes.values()).filter((n) => n.type === 'context');
  const componentFiles = Array.from(graph.nodes.values()).filter((n) => n.type === 'component');

  if (contextFiles.length < 3 && componentFiles.length > 20) {
    recommendations.push({
      type: 'ARCHITECTURE',
      target: 'State Management',
      issue: 'Low context usage with many components',
      solution: 'Consider more React Context usage for shared state',
    });
  }

  return recommendations;
}

/**
 * Generate Mermaid diagram for data flow
 */
function generateDataFlowMermaid(graph, bottlenecks) {
  const lines = ['graph TD'];

  // Add nodes with styling based on type
  graph.nodes.forEach((node, id) => {
    const shortId = id.replace(/[^a-zA-Z0-9]/g, '_');
    const label = path.basename(id);
    const isBottleneck = bottlenecks.some((b) => b.file === id);

    if (node.type === 'service') {
      lines.push(`    ${shortId}[${label}]:::service`);
    } else if (node.type === 'context') {
      lines.push(`    ${shortId}[${label}]:::context`);
    } else if (node.type === 'hook') {
      lines.push(`    ${shortId}[${label}]:::hook`);
    } else if (isBottleneck) {
      lines.push(`    ${shortId}[${label}]:::bottleneck`);
    } else {
      lines.push(`    ${shortId}[${label}]:::component`);
    }
  });

  // Add edges (limit to most important connections)
  const importantEdges = graph.edges
    .filter((edge) => {
      const fromNode = graph.nodes.get(edge.from);
      const toNode = graph.nodes.get(edge.to);
      return (
        fromNode &&
        toNode &&
        (fromNode.type === 'service' ||
          toNode.type === 'service' ||
          fromNode.type === 'context' ||
          toNode.type === 'context')
      );
    })
    .slice(0, 20); // Limit for readability

  importantEdges.forEach((edge) => {
    const fromId = edge.from.replace(/[^a-zA-Z0-9]/g, '_');
    const toId = edge.to.replace(/[^a-zA-Z0-9]/g, '_');
    lines.push(`    ${fromId} --> ${toId}`);
  });

  // Add styling
  lines.push('');
  lines.push('    classDef service fill:#e1f5fe,stroke:#01579b,stroke-width:2px');
  lines.push('    classDef context fill:#f3e5f5,stroke:#4a148c,stroke-width:2px');
  lines.push('    classDef hook fill:#e8f5e8,stroke:#1b5e20,stroke-width:2px');
  lines.push('    classDef component fill:#fff3e0,stroke:#e65100,stroke-width:1px');
  lines.push('    classDef bottleneck fill:#ffebee,stroke:#c62828,stroke-width:3px');

  return lines.join('\n');
}

/**
 * Generate trade data lifecycle diagram
 */
function generateTradeLifecycleMermaid() {
  return `graph LR
    A[User Input] --> B[TradeForm Component]
    B --> C[useTradeSubmission Hook]
    C --> D[TradeStorageService]
    D --> E[IndexedDB]

    E --> F[Data Retrieval]
    F --> G[useTradingDashboard Hook]
    G --> H[TradingDashboardContext]
    H --> I[Dashboard Components]

    E --> J[Trade Journal]
    J --> K[useTradeJournal Hook]
    K --> L[TradeList Component]

    subgraph "Storage Layer"
        D
        E
    end

    subgraph "State Management"
        C
        G
        H
        K
    end

    subgraph "UI Layer"
        B
        I
        L
    end

    classDef storage fill:#e1f5fe,stroke:#01579b,stroke-width:2px
    classDef state fill:#f3e5f5,stroke:#4a148c,stroke-width:2px
    classDef ui fill:#e8f5e8,stroke:#1b5e20,stroke-width:2px

    class D,E storage
    class C,G,H,K state
    class B,I,L ui`;
}

/**
 * Display results
 */
function displayResults(graph, bottlenecks, recommendations) {
  console.log(chalk.blue('🌊 DATA FLOW ANALYSIS RESULTS'));
  console.log(chalk.blue('==============================\n'));

  // Summary
  console.log(chalk.yellow('📊 SUMMARY:'));
  console.log(`   Total Files: ${graph.nodes.size}`);
  console.log(`   Data Flow Connections: ${graph.edges.length}`);
  console.log(`   Bottlenecks Found: ${bottlenecks.length}`);
  console.log(`   Recommendations: ${recommendations.length}\n`);

  // File type breakdown
  const typeBreakdown = new Map();
  graph.nodes.forEach((node) => {
    typeBreakdown.set(node.type, (typeBreakdown.get(node.type) || 0) + 1);
  });

  console.log(chalk.yellow('📁 FILE TYPE BREAKDOWN:'));
  typeBreakdown.forEach((count, type) => {
    console.log(`   ${type}: ${count} files`);
  });
  console.log('');

  // Bottlenecks
  if (bottlenecks.length > 0) {
    console.log(chalk.red('🚨 PERFORMANCE BOTTLENECKS:'));
    console.log('----------------------------');
    bottlenecks.slice(0, 5).forEach((bottleneck, index) => {
      console.log(`${index + 1}. ${bottleneck.file}`);
      console.log(`   Type: ${bottleneck.type}`);
      console.log(`   Dependencies: ${bottleneck.degree}`);
      console.log(`   Complexity: ${bottleneck.complexity}`);
      console.log(`   Severity: ${bottleneck.severity}\n`);
    });
  }

  // Recommendations
  if (recommendations.length > 0) {
    console.log(chalk.green('💡 RECOMMENDATIONS:'));
    console.log('--------------------');
    recommendations.forEach((rec, index) => {
      console.log(`${index + 1}. [${rec.type}] ${rec.target}`);
      console.log(`   Issue: ${rec.issue}`);
      console.log(`   Solution: ${rec.solution}\n`);
    });
  }
}

/**
 * Main analysis function
 */
async function analyzeDataFlow() {
  console.log(chalk.blue('🔍 Analyzing Data Flow Architecture...\n'));

  // Get all files
  const allFiles = [...getAllFiles(DASHBOARD_SRC), ...getAllFiles(SHARED_SRC)];

  console.log(`📁 Found ${allFiles.length} files to analyze\n`);

  // Analyze each file
  const analyses = allFiles.map(analyzeFileDataFlow).filter(Boolean);

  // Build data flow graph
  const graph = buildDataFlowGraph(analyses);

  // Identify bottlenecks
  const bottlenecks = identifyBottlenecks(graph);

  // Generate recommendations
  const recommendations = generateRecommendations(graph, bottlenecks);

  // Display results
  displayResults(graph, bottlenecks, recommendations);

  // Generate diagrams
  const dataFlowDiagram = generateDataFlowMermaid(graph, bottlenecks);
  const tradeLifecycleDiagram = generateTradeLifecycleMermaid();

  return {
    graph,
    bottlenecks,
    recommendations,
    diagrams: {
      dataFlow: dataFlowDiagram,
      tradeLifecycle: tradeLifecycleDiagram,
    },
  };
}

// Export for use in other tools
export { analyzeDataFlow, generateDataFlowMermaid, generateTradeLifecycleMermaid };
export default analyzeDataFlow;

// Run if called directly
if (import.meta.url === `file://${process.argv[1]}`) {
  analyzeDataFlow().catch(console.error);
}
