#!/usr/bin/env node

/**
 * Documentation Fixer for ADHD Trading Dashboard
 *
 * Automatically fixes common documentation issues:
 * 1. Broken file references
 * 2. Invalid script references
 * 3. Outdated path references
 * 4. Package.json script mismatches
 */

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';
import chalk from 'chalk';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);
const ROOT_DIR = path.join(__dirname, '../..');

// Get package.json scripts for validation
const packageJson = JSON.parse(fs.readFileSync(path.join(ROOT_DIR, 'package.json'), 'utf8'));
const validScripts = Object.keys(packageJson.scripts || {});

// Common file path corrections
const PATH_CORRECTIONS = {
  // Fix double paths first
  'packages/dashboard/packages/dashboard/src/': 'packages/dashboard/src/',
  'packages/shared/packages/dashboard/src/': 'packages/shared/src/',
  // Fix backtick issues
  'packages/shared`**': 'packages/shared/',
  'packages/dashboard`**': 'packages/dashboard/',
  'packages/shared/src/components/`': 'packages/shared/src/components/',
  'packages/dashboard/src/features/`': 'packages/dashboard/src/features/',
  'packages/shared/src`': 'packages/shared/src/',
  'packages/dashboard/src`': 'packages/dashboard/src/',
  // Fix src/ references (only if not already prefixed with packages/)
  'src/components/`': 'packages/shared/src/components/',
  'src/features/`': 'packages/dashboard/src/features/',
  'src/`': 'packages/dashboard/src/',
};

// Script corrections
const SCRIPT_CORRECTIONS = {
  'test:e2e:ui': 'test:e2e',
  'health:analyze': 'analyze',
  'health:scripts': 'analyze',
  'health:cleanup': 'cleanup:legacy',
  'health:cleanup:dry': 'cleanup:legacy',
  install: '',
  'deploy:staging': '',
  global: '',
  workspace: '',
  'check-versions': 'deps:check',
  'fix-versions': 'deps:sync',
  diagnostics: 'health',
  deploy: '',
  'deploy:all': '',
  'deploy:gh-pages': '',
  start: 'dev',
  'type-check': 'test',
};

/**
 * Fix file references in content
 */
function fixFileReferences(content) {
  let fixedContent = content;
  let fixes = [];

  // Apply corrections in order (double paths first, then others)
  Object.entries(PATH_CORRECTIONS).forEach(([oldPath, newPath]) => {
    if (fixedContent.includes(oldPath)) {
      const regex = new RegExp(oldPath.replace(/[.*+?^${}()|[\]\\]/g, '\\$&'), 'g');
      const beforeCount = (fixedContent.match(regex) || []).length;
      fixedContent = fixedContent.replace(regex, newPath);
      const afterCount = (fixedContent.match(regex) || []).length;

      if (beforeCount > afterCount) {
        fixes.push(`${oldPath} → ${newPath} (${beforeCount - afterCount} replacements)`);
      }
    }
  });

  // Additional smart fixes for src/ references that aren't already prefixed
  const srcPattern = /(?<!packages\/[^\/]+\/)src\//g;
  if (srcPattern.test(fixedContent)) {
    const beforeCount = (fixedContent.match(srcPattern) || []).length;
    fixedContent = fixedContent.replace(srcPattern, 'packages/dashboard/src/');
    fixes.push(`Standalone src/ → packages/dashboard/src/ (${beforeCount} replacements)`);
  }

  return { content: fixedContent, fixes };
}

/**
 * Fix script references in content
 */
function fixScriptReferences(content) {
  let fixedContent = content;
  let fixes = [];

  Object.entries(SCRIPT_CORRECTIONS).forEach(([oldScript, newScript]) => {
    const patterns = [
      `yarn ${oldScript}`,
      `npm run ${oldScript}`,
      `\`yarn ${oldScript}\``,
      `\`npm run ${oldScript}\``,
    ];

    patterns.forEach((pattern) => {
      if (fixedContent.includes(pattern)) {
        if (newScript) {
          const newPattern = pattern.replace(oldScript, newScript);
          fixedContent = fixedContent.replace(
            new RegExp(pattern.replace(/[.*+?^${}()|[\]\\]/g, '\\$&'), 'g'),
            newPattern
          );
          fixes.push(`${pattern} → ${newPattern}`);
        } else {
          // Remove invalid scripts
          const lines = fixedContent.split('\n');
          const filteredLines = lines.filter((line) => !line.includes(pattern));
          if (filteredLines.length !== lines.length) {
            fixedContent = filteredLines.join('\n');
            fixes.push(`Removed: ${pattern}`);
          }
        }
      }
    });
  });

  return { content: fixedContent, fixes };
}

/**
 * Remove broken file references that don't exist
 */
function removeBrokenReferences(content, filePath) {
  const lines = content.split('\n');
  const fixedLines = [];
  let fixes = [];

  for (const line of lines) {
    let shouldKeep = true;

    // Check for file references in various formats
    const fileRefPatterns = [
      /`([^`]+\.(tsx?|jsx?|json|md))`/g,
      /\[.*?\]\(([^)]+\.(tsx?|jsx?|json|md))\)/g,
    ];

    for (const pattern of fileRefPatterns) {
      let match;
      while ((match = pattern.exec(line)) !== null) {
        const ref = match[1] || match[0];
        if (ref && !ref.startsWith('http')) {
          const fullPath = path.resolve(path.dirname(filePath), ref);
          if (!fs.existsSync(fullPath)) {
            // Check if it's a commonly broken reference we should remove
            if (
              ref.includes('TradeForm') ||
              ref.includes('TradeAnalysis') ||
              ref.includes('components/trade-') ||
              ref.includes('src/features/') ||
              (ref.includes('packages/') && !fs.existsSync(path.join(ROOT_DIR, ref)))
            ) {
              shouldKeep = false;
              fixes.push(`Removed broken reference: ${ref}`);
              break;
            }
          }
        }
      }
      if (!shouldKeep) break;
    }

    if (shouldKeep) {
      fixedLines.push(line);
    }
  }

  return { content: fixedLines.join('\n'), fixes };
}

/**
 * Fix a single documentation file
 */
function fixDocumentationFile(filePath) {
  try {
    const content = fs.readFileSync(filePath, 'utf8');
    const relativePath = path.relative(ROOT_DIR, filePath);

    let fixedContent = content;
    let allFixes = [];

    // Apply fixes
    const fileRefFix = fixFileReferences(fixedContent);
    fixedContent = fileRefFix.content;
    allFixes.push(...fileRefFix.fixes);

    const scriptRefFix = fixScriptReferences(fixedContent);
    fixedContent = scriptRefFix.content;
    allFixes.push(...scriptRefFix.fixes);

    const brokenRefFix = removeBrokenReferences(fixedContent, filePath);
    fixedContent = brokenRefFix.content;
    allFixes.push(...brokenRefFix.fixes);

    // Only write if changes were made
    if (fixedContent !== content) {
      fs.writeFileSync(filePath, fixedContent, 'utf8');
      return {
        file: relativePath,
        fixed: true,
        fixes: allFixes,
      };
    }

    return {
      file: relativePath,
      fixed: false,
      fixes: [],
    };
  } catch (error) {
    return {
      file: path.relative(ROOT_DIR, filePath),
      fixed: false,
      error: error.message,
      fixes: [],
    };
  }
}

/**
 * Get all markdown files
 */
function getMarkdownFiles(dir = ROOT_DIR) {
  const files = [];

  function traverse(currentDir) {
    if (!fs.existsSync(currentDir)) return;

    const items = fs.readdirSync(currentDir);

    for (const item of items) {
      const fullPath = path.join(currentDir, item);
      const stat = fs.statSync(fullPath);

      if (stat.isDirectory() && !item.startsWith('.') && item !== 'node_modules') {
        traverse(fullPath);
      } else if (item.endsWith('.md')) {
        files.push(fullPath);
      }
    }
  }

  traverse(dir);
  return files;
}

/**
 * Main documentation fixing function
 */
async function fixAllDocumentation() {
  console.log(chalk.blue('🔧 FIXING DOCUMENTATION ISSUES'));
  console.log(chalk.blue('================================\n'));

  const markdownFiles = getMarkdownFiles();
  console.log(`📁 Found ${markdownFiles.length} documentation files\n`);

  const results = markdownFiles.map(fixDocumentationFile);

  const fixedFiles = results.filter((r) => r.fixed);
  const errorFiles = results.filter((r) => r.error);

  // Display results
  console.log(chalk.green('✅ FIXED FILES:'));
  console.log('----------------');
  fixedFiles.forEach((result, index) => {
    console.log(`${index + 1}. ${result.file}`);
    result.fixes.slice(0, 3).forEach((fix) => {
      console.log(`   - ${fix}`);
    });
    if (result.fixes.length > 3) {
      console.log(`   ... and ${result.fixes.length - 3} more fixes`);
    }
    console.log('');
  });

  if (errorFiles.length > 0) {
    console.log(chalk.red('❌ FILES WITH ERRORS:'));
    console.log('----------------------');
    errorFiles.forEach((result, index) => {
      console.log(`${index + 1}. ${result.file}: ${result.error}`);
    });
    console.log('');
  }

  // Summary
  console.log(chalk.blue('📊 SUMMARY:'));
  console.log('------------');
  console.log(`Total Files: ${markdownFiles.length}`);
  console.log(`Fixed Files: ${fixedFiles.length}`);
  console.log(`Error Files: ${errorFiles.length}`);
  console.log(`Total Fixes Applied: ${fixedFiles.reduce((sum, r) => sum + r.fixes.length, 0)}`);

  return results;
}

// Export for use in other tools
export { fixAllDocumentation };
export default fixAllDocumentation;

// Run if called directly
if (import.meta.url === `file://${process.argv[1]}`) {
  fixAllDocumentation().catch(console.error);
}
