#!/usr/bin/env node

/**
 * Documentation Rewriter for ADHD Trading Dashboard
 * 
 * Creates fresh, accurate versions of core documentation files
 * based on the actual current codebase structure and functionality.
 */

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';
import chalk from 'chalk';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);
const ROOT_DIR = path.join(__dirname, '../..');

/**
 * Get actual package.json scripts for accurate documentation
 */
function getPackageScripts() {
  const packagePath = path.join(ROOT_DIR, 'package.json');
  const packageJson = JSON.parse(fs.readFileSync(packagePath, 'utf8'));
  return Object.keys(packageJson.scripts || {});
}

/**
 * Get actual file structure for accurate documentation
 */
function getActualStructure() {
  const structure = {
    packages: {
      shared: fs.existsSync(path.join(ROOT_DIR, 'packages/shared')),
      dashboard: fs.existsSync(path.join(ROOT_DIR, 'packages/dashboard'))
    },
    docs: fs.existsSync(path.join(ROOT_DIR, 'docs')),
    scripts: fs.existsSync(path.join(ROOT_DIR, 'scripts'))
  };
  
  return structure;
}

/**
 * Create new README.md
 */
function createNewReadme() {
  const scripts = getPackageScripts();
  const structure = getActualStructure();
  
  return `# ADHD Trading Dashboard

> **🏎️ A high-performance React trading dashboard with Formula 1-inspired design**

A modern monorepo trading analysis and journaling system built with React, TypeScript, and a sleek Formula 1 racing theme. Designed for traders who need fast, reliable tools for performance analysis and trade management.

## 🚀 Quick Start

\`\`\`bash
# Install dependencies
yarn install

# Start development server
yarn dev

# Build for production  
yarn build
\`\`\`

The app will be available at [http://localhost:3000](http://localhost:3000)

## 📁 Project Structure

This is a **monorepo** with clear separation of concerns:

\`\`\`
📦 adhd-trading-dashboard-lib/
├── 📁 packages/
│   ├── 📁 shared/          # Reusable components, utilities, theme
│   └── 📁 dashboard/       # Main trading dashboard application
├── 📁 docs/               # 📚 Complete documentation
├── 📁 scripts/            # Development and build tools
└── 📄 package.json        # Root workspace configuration
\`\`\`

### Package Dependencies

\`\`\`
shared → dashboard
\`\`\`

- **shared**: Foundation components, API layer, theme system, utilities
- **dashboard**: Trading features, pages, routing, application logic

## ✨ Features

- **🏎️ Formula 1 Theme**: High-performance design inspired by F1 racing
- **📊 Trade Analysis**: Comprehensive performance tracking and analytics
- **📝 Trade Journal**: Detailed trade logging with setup classification
- **🎯 Daily Guide**: Market overview and trading plan management
- **⚡ Real-time Data**: Fast, responsive interface for live trading
- **🔧 Developer Tools**: Enhanced development experience with hot reload
- **📱 Responsive**: Works on desktop and mobile devices
- **🎨 Atomic Design**: Scalable component architecture

## 🛠️ Available Scripts

${scripts.includes('dev') ? '- `yarn dev` - Start development server with hot reload' : ''}
${scripts.includes('build') ? '- `yarn build` - Build for production' : ''}
${scripts.includes('test') ? '- `yarn test` - Run test suite' : ''}
${scripts.includes('lint') ? '- `yarn lint` - Lint code' : ''}
${scripts.includes('analyze') ? '- `yarn analyze` - Analyze code architecture' : ''}
${scripts.includes('health') ? '- `yarn health` - Check system health' : ''}
${scripts.includes('docs:check') ? '- `yarn docs:check` - Validate documentation' : ''}

## 📚 Documentation

Complete documentation is available in the [\`docs/\`](./docs/) directory:

- **[📖 Getting Started](./docs/GETTING_STARTED.md)** - Setup and installation
- **[🏗️ Architecture](./docs/ARCHITECTURE.md)** - System design and structure  
- **[🔧 Development](./docs/DEVELOPMENT.md)** - Development workflow and tools
- **[📦 Packages](./docs/packages/)** - Package-specific documentation

## 🏁 Technology Stack

- **Framework**: React 18 + TypeScript
- **Styling**: styled-components with F1 racing theme
- **Build**: Vite (development) + TypeScript (production)
- **Testing**: Vitest (unit) + Playwright (E2E)
- **Package Management**: Yarn workspaces
- **State Management**: React Context + custom hooks
- **Data Storage**: IndexedDB for client-side persistence

## 🤝 Contributing

1. **Fork the repository**
2. **Create a feature branch**: \`git checkout -b feature/amazing-feature\`
3. **Make your changes** following our [development guide](./docs/DEVELOPMENT.md)
4. **Run tests**: \`yarn test\`
5. **Commit changes**: \`git commit -m 'feat: add amazing feature'\`
6. **Push to branch**: \`git push origin feature/amazing-feature\`
7. **Open a Pull Request**

## 📄 License

MIT License - see [LICENSE](./LICENSE) file for details.

---

**Built with ❤️ for traders who demand performance and precision**
`;
}

/**
 * Create new CLAUDE.md (AI assistant instructions)
 */
function createNewClaude() {
  const scripts = getPackageScripts();
  
  return `# ADHD Trading Dashboard - AI Assistant Guide

> **🤖 Instructions for AI assistants working with this codebase**

## 📋 Project Overview

This is a **React TypeScript monorepo** for a trading dashboard with Formula 1-inspired design. The project follows modern development practices with strict typing, atomic design principles, and comprehensive tooling.

## 🏗️ Architecture

### Monorepo Structure
- **\`packages/shared/\`** - Reusable components, hooks, theme system, utilities
- **\`packages/dashboard/\`** - Main trading application with features and pages

### Dependency Flow
\`\`\`
shared → dashboard
\`\`\`

### Key Patterns
- **Atomic Design**: Components organized as atoms → molecules → organisms
- **Feature-Based**: Features isolated in \`packages/dashboard/src/features/\`
- **TypeScript First**: Strict typing throughout the codebase
- **Context State**: React Context for state management

## 🛠️ Development Commands

${scripts.map(script => `- \`yarn ${script}\``).join('\n')}

## 📁 Important Directories

\`\`\`
packages/
├── shared/src/
│   ├── components/     # Atomic design components
│   ├── hooks/         # Custom React hooks  
│   ├── theme/         # F1-inspired theme system
│   ├── types/         # TypeScript type definitions
│   └── utils/         # Utility functions
└── dashboard/src/
    ├── features/      # Trading features (trade-journal, trade-analysis, etc.)
    ├── pages/         # Route-level components
    ├── layouts/       # Application layouts
    └── routes/        # Route definitions
\`\`\`

## 🎯 Development Guidelines

### Code Quality
- Use TypeScript for all new code
- Follow atomic design principles for components
- Implement proper error boundaries
- Write tests for critical functionality

### Architecture Principles
- Keep features isolated and self-contained
- Use shared components from the shared package
- Follow the established folder structure
- Maintain clear separation of concerns

### Performance
- Use React.memo for expensive components
- Implement proper loading states
- Optimize bundle size with code splitting
- Use IndexedDB for client-side data persistence

## 🔧 Common Tasks

### Adding New Components
1. Determine atomic level (atom/molecule/organism)
2. Place in appropriate package (\`shared\` for reusable, \`dashboard\` for specific)
3. Follow TypeScript interface patterns
4. Include proper JSDoc documentation

### Adding New Features
1. Create feature directory in \`packages/dashboard/src/features/\`
2. Include: components, hooks, types, state (if needed)
3. Export through feature index file
4. Add route configuration if needed

### Debugging Issues
- Use \`yarn health\` for system diagnostics
- Check \`yarn analyze\` for architecture insights
- Validate documentation with \`yarn docs:check\`

## 📚 Documentation

- All documentation is in \`docs/\` directory
- Follow established documentation standards
- Update docs when making architectural changes
- Use \`yarn docs:check\` to validate accuracy

## ⚠️ Important Notes

- **Node.js v18 LTS** recommended for compatibility
- **Yarn workspaces** - always use yarn, not npm
- **ES Modules** - project uses ES module syntax
- **Exact versions** - no ^ in package.json dependencies

The codebase follows disciplined patterns and comprehensive tooling for maintainable, high-performance trading software.
`;
}

/**
 * Rewrite core documentation files
 */
async function rewriteCoreDocumentation() {
  console.log(chalk.blue('📝 REWRITING CORE DOCUMENTATION'));
  console.log(chalk.blue('=================================\n'));
  
  const rewrites = [
    {
      path: 'README.md',
      content: createNewReadme(),
      description: 'Main project README with current structure and scripts'
    },
    {
      path: 'CLAUDE.md', 
      content: createNewClaude(),
      description: 'AI assistant instructions with accurate project info'
    }
  ];
  
  const results = [];
  
  for (const rewrite of rewrites) {
    const fullPath = path.join(ROOT_DIR, rewrite.path);
    
    try {
      // Backup existing file
      if (fs.existsSync(fullPath)) {
        const backupPath = `${fullPath}.backup.${Date.now()}`;
        fs.copyFileSync(fullPath, backupPath);
        console.log(chalk.yellow(`📦 Backed up: ${rewrite.path} → ${path.basename(backupPath)}`));
      }
      
      // Write new content
      fs.writeFileSync(fullPath, rewrite.content, 'utf8');
      
      results.push({
        path: rewrite.path,
        success: true,
        description: rewrite.description
      });
      
      console.log(chalk.green(`✅ Rewrote: ${rewrite.path}`));
      console.log(chalk.gray(`   ${rewrite.description}`));
      
    } catch (error) {
      results.push({
        path: rewrite.path,
        success: false,
        error: error.message
      });
      
      console.log(chalk.red(`❌ Failed: ${rewrite.path} - ${error.message}`));
    }
  }
  
  // Summary
  console.log(chalk.blue('\n📊 REWRITE SUMMARY:'));
  console.log('-------------------');
  console.log(`Successfully Rewritten: ${results.filter(r => r.success).length}`);
  console.log(`Errors: ${results.filter(r => !r.success).length}`);
  console.log(`Total Files: ${rewrites.length}`);
  
  return results;
}

// Export for use in other tools
export { rewriteCoreDocumentation };
export default rewriteCoreDocumentation;

// Run if called directly
if (import.meta.url === `file://${process.argv[1]}`) {
  rewriteCoreDocumentation().catch(console.error);
}
