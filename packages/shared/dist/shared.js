"use strict";Object.defineProperty(exports,Symbol.toStringTag,{value:"Module"});const b=require("react"),s=require("styled-components"),Zt=require("react-dom");var et=(e=>(e.LONG="LONG",e.SHORT="SHORT",e))(et||{}),rt=(e=>(e.OPEN="OPEN",e.CLOSED="CLOSED",e.CANCELED="CANCELED",e.REJECTED="REJECTED",e.PENDING="PENDING",e))(rt||{}),tt=(e=>(e.MARKET="MARKET",e.LIMIT="LIMIT",e.STOP="STOP",e.STOP_LIMIT="STOP_LIMIT",e))(tt||{}),ot=(e=>(e.BUY="BUY",e.SELL="SELL",e))(ot||{}),st=(e=>(e.PENDING="PENDING",e.FILLED="FILLED",e.PARTIALLY_FILLED="PARTIALLY_FILLED",e.CANCELED="CANCELED",e.REJECTED="REJECTED",e))(st||{}),nt=(e=>(e.GTC="GTC",e.IOC="IOC",e.FOK="FOK",e.DAY="DAY",e))(nt||{}),G=(e=>(e.LONDON="london",e.NEW_YORK_AM="new-york-am",e.NEW_YORK_PM="new-york-pm",e.ASIA="asia",e.PRE_MARKET="pre-market",e.AFTER_HOURS="after-hours",e.OVERNIGHT="overnight",e))(G||{}),M=(e=>(e.MORNING_BREAKOUT="morning-breakout",e.MID_MORNING_REVERSION="mid-morning-reversion",e.PRE_LUNCH="pre-lunch",e.LUNCH_MACRO_EXTENDED="lunch-macro-extended",e.LUNCH_MACRO="lunch-macro",e.POST_LUNCH="post-lunch",e.PRE_CLOSE="pre-close",e.POWER_HOUR="power-hour",e.MOC="moc",e.LONDON_OPEN="london-open",e.LONDON_NY_OVERLAP="london-ny-overlap",e.CUSTOM="custom",e))(M||{});const ve={constant:{parentArrays:["NWOG","Old-NWOG","NDOG","Old-NDOG","Monthly-FVG","Weekly-FVG","Daily-FVG","15min-Top/Bottom-FVG","1h-Top/Bottom-FVG"],fvgTypes:["Strong-FVG","AM-FPFVG","PM-FPFVG","Asia-FPFVG","Premarket-FPFVG","MNOR-FVG","Macro-FVG","News-FVG","Top/Bottom-FVG"]},action:{liquidityEvents:["None","London-H/L","Premarket-H/L","09:30-Opening-Range-H/L","Lunch-H/L","Prev-Day-H/L","Prev-Week-H/L","Monthly-H/L","Macro-H/L"]},variable:{rdTypes:["None","True-RD","IMM-RD","Dispersed-RD","Wide-Gap-RD"]},entry:{methods:["Simple-Entry","Complex-Entry","Complex-Entry/Mini"]}},eo=["RD-Cont","FVG-RD","Combined"];var o={},ro={get exports(){return o},set exports(e){o=e}},Ce={};/**
 * @license React
 * react-jsx-runtime.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Nr;function to(){if(Nr)return Ce;Nr=1;var e=b,r=Symbol.for("react.element"),t=Symbol.for("react.fragment"),n=Object.prototype.hasOwnProperty,i=e.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ReactCurrentOwner,c={key:!0,ref:!0,__self:!0,__source:!0};function a(p,f,u){var d,m={},h=null,y=null;u!==void 0&&(h=""+u),f.key!==void 0&&(h=""+f.key),f.ref!==void 0&&(y=f.ref);for(d in f)n.call(f,d)&&!c.hasOwnProperty(d)&&(m[d]=f[d]);if(p&&p.defaultProps)for(d in f=p.defaultProps,f)m[d]===void 0&&(m[d]=f[d]);return{$$typeof:r,type:p,key:h,ref:y,props:m,_owner:i.current}}return Ce.Fragment=t,Ce.jsx=a,Ce.jsxs=a,Ce}var Ie={};/**
 * @license React
 * react-jsx-runtime.development.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Rr;function oo(){return Rr||(Rr=1,process.env.NODE_ENV!=="production"&&function(){var e=b,r=Symbol.for("react.element"),t=Symbol.for("react.portal"),n=Symbol.for("react.fragment"),i=Symbol.for("react.strict_mode"),c=Symbol.for("react.profiler"),a=Symbol.for("react.provider"),p=Symbol.for("react.context"),f=Symbol.for("react.forward_ref"),u=Symbol.for("react.suspense"),d=Symbol.for("react.suspense_list"),m=Symbol.for("react.memo"),h=Symbol.for("react.lazy"),y=Symbol.for("react.offscreen"),g=Symbol.iterator,x="@@iterator";function C(l){if(l===null||typeof l!="object")return null;var v=g&&l[g]||l[x];return typeof v=="function"?v:null}var S=e.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED;function w(l){{for(var v=arguments.length,I=new Array(v>1?v-1:0),P=1;P<v;P++)I[P-1]=arguments[P];L("error",l,I)}}function L(l,v,I){{var P=S.ReactDebugCurrentFrame,B=P.getStackAddendum();B!==""&&(v+="%s",I=I.concat([B]));var Y=I.map(function(z){return String(z)});Y.unshift("Warning: "+v),Function.prototype.apply.call(console[l],console,Y)}}var A=!1,O=!1,R=!1,j=!1,_=!1,k;k=Symbol.for("react.module.reference");function U(l){return!!(typeof l=="string"||typeof l=="function"||l===n||l===c||_||l===i||l===u||l===d||j||l===y||A||O||R||typeof l=="object"&&l!==null&&(l.$$typeof===h||l.$$typeof===m||l.$$typeof===a||l.$$typeof===p||l.$$typeof===f||l.$$typeof===k||l.getModuleId!==void 0))}function V(l,v,I){var P=l.displayName;if(P)return P;var B=v.displayName||v.name||"";return B!==""?I+"("+B+")":I}function N(l){return l.displayName||"Context"}function H(l){if(l==null)return null;if(typeof l.tag=="number"&&w("Received an unexpected object in getComponentNameFromType(). This is likely a bug in React. Please file an issue."),typeof l=="function")return l.displayName||l.name||null;if(typeof l=="string")return l;switch(l){case n:return"Fragment";case t:return"Portal";case c:return"Profiler";case i:return"StrictMode";case u:return"Suspense";case d:return"SuspenseList"}if(typeof l=="object")switch(l.$$typeof){case p:var v=l;return N(v)+".Consumer";case a:var I=l;return N(I._context)+".Provider";case f:return V(l,l.render,"ForwardRef");case m:var P=l.displayName||null;return P!==null?P:H(l.type)||"Memo";case h:{var B=l,Y=B._payload,z=B._init;try{return H(z(Y))}catch{return null}}}return null}var ee=Object.assign,te=0,de,oe,D,X,he,q,pe;function gr(){}gr.__reactDisabledLog=!0;function kt(){{if(te===0){de=console.log,oe=console.info,D=console.warn,X=console.error,he=console.group,q=console.groupCollapsed,pe=console.groupEnd;var l={configurable:!0,enumerable:!0,value:gr,writable:!0};Object.defineProperties(console,{info:l,log:l,warn:l,error:l,group:l,groupCollapsed:l,groupEnd:l})}te++}}function Nt(){{if(te--,te===0){var l={configurable:!0,enumerable:!0,writable:!0};Object.defineProperties(console,{log:ee({},l,{value:de}),info:ee({},l,{value:oe}),warn:ee({},l,{value:D}),error:ee({},l,{value:X}),group:ee({},l,{value:he}),groupCollapsed:ee({},l,{value:q}),groupEnd:ee({},l,{value:pe})})}te<0&&w("disabledDepth fell below zero. This is a bug in React. Please file an issue.")}}var Ye=S.ReactCurrentDispatcher,We;function Re(l,v,I){{if(We===void 0)try{throw Error()}catch(B){var P=B.stack.trim().match(/\n( *(at )?)/);We=P&&P[1]||""}return`
`+We+l}}var Ge=!1,_e;{var Rt=typeof WeakMap=="function"?WeakMap:Map;_e=new Rt}function mr(l,v){if(!l||Ge)return"";{var I=_e.get(l);if(I!==void 0)return I}var P;Ge=!0;var B=Error.prepareStackTrace;Error.prepareStackTrace=void 0;var Y;Y=Ye.current,Ye.current=null,kt();try{if(v){var z=function(){throw Error()};if(Object.defineProperty(z.prototype,"props",{set:function(){throw Error()}}),typeof Reflect=="object"&&Reflect.construct){try{Reflect.construct(z,[])}catch(fe){P=fe}Reflect.construct(l,[],z)}else{try{z.call()}catch(fe){P=fe}l.call(z.prototype)}}else{try{throw Error()}catch(fe){P=fe}l()}}catch(fe){if(fe&&P&&typeof fe.stack=="string"){for(var $=fe.stack.split(`
`),se=P.stack.split(`
`),J=$.length-1,Z=se.length-1;J>=1&&Z>=0&&$[J]!==se[Z];)Z--;for(;J>=1&&Z>=0;J--,Z--)if($[J]!==se[Z]){if(J!==1||Z!==1)do if(J--,Z--,Z<0||$[J]!==se[Z]){var ae=`
`+$[J].replace(" at new "," at ");return l.displayName&&ae.includes("<anonymous>")&&(ae=ae.replace("<anonymous>",l.displayName)),typeof l=="function"&&_e.set(l,ae),ae}while(J>=1&&Z>=0);break}}}finally{Ge=!1,Ye.current=Y,Nt(),Error.prepareStackTrace=B}var ye=l?l.displayName||l.name:"",kr=ye?Re(ye):"";return typeof l=="function"&&_e.set(l,kr),kr}function _t(l,v,I){return mr(l,!1)}function Lt(l){var v=l.prototype;return!!(v&&v.isReactComponent)}function Le(l,v,I){if(l==null)return"";if(typeof l=="function")return mr(l,Lt(l));if(typeof l=="string")return Re(l);switch(l){case u:return Re("Suspense");case d:return Re("SuspenseList")}if(typeof l=="object")switch(l.$$typeof){case f:return _t(l.render);case m:return Le(l.type,v,I);case h:{var P=l,B=P._payload,Y=P._init;try{return Le(Y(B),v,I)}catch{}}}return""}var Me=Object.prototype.hasOwnProperty,hr={},xr=S.ReactDebugCurrentFrame;function Pe(l){if(l){var v=l._owner,I=Le(l.type,l._source,v?v.type:null);xr.setExtraStackFrame(I)}else xr.setExtraStackFrame(null)}function Mt(l,v,I,P,B){{var Y=Function.call.bind(Me);for(var z in l)if(Y(l,z)){var $=void 0;try{if(typeof l[z]!="function"){var se=Error((P||"React class")+": "+I+" type `"+z+"` is invalid; it must be a function, usually from the `prop-types` package, but received `"+typeof l[z]+"`.This often happens because of typos such as `PropTypes.function` instead of `PropTypes.func`.");throw se.name="Invariant Violation",se}$=l[z](v,z,P,I,null,"SECRET_DO_NOT_PASS_THIS_OR_YOU_WILL_BE_FIRED")}catch(J){$=J}$&&!($ instanceof Error)&&(Pe(B),w("%s: type specification of %s `%s` is invalid; the type checker function must return `null` or an `Error` but returned a %s. You may have forgotten to pass an argument to the type checker creator (arrayOf, instanceOf, objectOf, oneOf, oneOfType, and shape all require an argument).",P||"React class",I,z,typeof $),Pe(null)),$ instanceof Error&&!($.message in hr)&&(hr[$.message]=!0,Pe(B),w("Failed %s type: %s",I,$.message),Pe(null))}}}var Pt=Array.isArray;function Ke(l){return Pt(l)}function Dt(l){{var v=typeof Symbol=="function"&&Symbol.toStringTag,I=v&&l[Symbol.toStringTag]||l.constructor.name||"Object";return I}}function Ot(l){try{return br(l),!1}catch{return!0}}function br(l){return""+l}function yr(l){if(Ot(l))return w("The provided key is an unsupported type %s. This value must be coerced to a string before before using it here.",Dt(l)),br(l)}var we=S.ReactCurrentOwner,$t={key:!0,ref:!0,__self:!0,__source:!0},vr,Sr,Qe;Qe={};function At(l){if(Me.call(l,"ref")){var v=Object.getOwnPropertyDescriptor(l,"ref").get;if(v&&v.isReactWarning)return!1}return l.ref!==void 0}function zt(l){if(Me.call(l,"key")){var v=Object.getOwnPropertyDescriptor(l,"key").get;if(v&&v.isReactWarning)return!1}return l.key!==void 0}function Ft(l,v){if(typeof l.ref=="string"&&we.current&&v&&we.current.stateNode!==v){var I=H(we.current.type);Qe[I]||(w('Component "%s" contains the string ref "%s". Support for string refs will be removed in a future major release. This case cannot be automatically converted to an arrow function. We ask you to manually fix this case by using useRef() or createRef() instead. Learn more about using refs safely here: https://reactjs.org/link/strict-mode-string-ref',H(we.current.type),l.ref),Qe[I]=!0)}}function Bt(l,v){{var I=function(){vr||(vr=!0,w("%s: `key` is not a prop. Trying to access it will result in `undefined` being returned. If you need to access the same value within the child component, you should pass it as a different prop. (https://reactjs.org/link/special-props)",v))};I.isReactWarning=!0,Object.defineProperty(l,"key",{get:I,configurable:!0})}}function qt(l,v){{var I=function(){Sr||(Sr=!0,w("%s: `ref` is not a prop. Trying to access it will result in `undefined` being returned. If you need to access the same value within the child component, you should pass it as a different prop. (https://reactjs.org/link/special-props)",v))};I.isReactWarning=!0,Object.defineProperty(l,"ref",{get:I,configurable:!0})}}var Ht=function(l,v,I,P,B,Y,z){var $={$$typeof:r,type:l,key:v,ref:I,props:z,_owner:Y};return $._store={},Object.defineProperty($._store,"validated",{configurable:!1,enumerable:!1,writable:!0,value:!1}),Object.defineProperty($,"_self",{configurable:!1,enumerable:!1,writable:!1,value:P}),Object.defineProperty($,"_source",{configurable:!1,enumerable:!1,writable:!1,value:B}),Object.freeze&&(Object.freeze($.props),Object.freeze($)),$};function Ut(l,v,I,P,B){{var Y,z={},$=null,se=null;I!==void 0&&(yr(I),$=""+I),zt(v)&&(yr(v.key),$=""+v.key),At(v)&&(se=v.ref,Ft(v,B));for(Y in v)Me.call(v,Y)&&!$t.hasOwnProperty(Y)&&(z[Y]=v[Y]);if(l&&l.defaultProps){var J=l.defaultProps;for(Y in J)z[Y]===void 0&&(z[Y]=J[Y])}if($||se){var Z=typeof l=="function"?l.displayName||l.name||"Unknown":l;$&&Bt(z,Z),se&&qt(z,Z)}return Ht(l,$,se,B,P,we.current,z)}}var Xe=S.ReactCurrentOwner,wr=S.ReactDebugCurrentFrame;function be(l){if(l){var v=l._owner,I=Le(l.type,l._source,v?v.type:null);wr.setExtraStackFrame(I)}else wr.setExtraStackFrame(null)}var Je;Je=!1;function Ze(l){return typeof l=="object"&&l!==null&&l.$$typeof===r}function Cr(){{if(Xe.current){var l=H(Xe.current.type);if(l)return`

Check the render method of \``+l+"`."}return""}}function Vt(l){{if(l!==void 0){var v=l.fileName.replace(/^.*[\\\/]/,""),I=l.lineNumber;return`

Check your code at `+v+":"+I+"."}return""}}var Ir={};function Yt(l){{var v=Cr();if(!v){var I=typeof l=="string"?l:l.displayName||l.name;I&&(v=`

Check the top-level render call using <`+I+">.")}return v}}function Er(l,v){{if(!l._store||l._store.validated||l.key!=null)return;l._store.validated=!0;var I=Yt(v);if(Ir[I])return;Ir[I]=!0;var P="";l&&l._owner&&l._owner!==Xe.current&&(P=" It was passed a child from "+H(l._owner.type)+"."),be(l),w('Each child in a list should have a unique "key" prop.%s%s See https://reactjs.org/link/warning-keys for more information.',I,P),be(null)}}function Tr(l,v){{if(typeof l!="object")return;if(Ke(l))for(var I=0;I<l.length;I++){var P=l[I];Ze(P)&&Er(P,v)}else if(Ze(l))l._store&&(l._store.validated=!0);else if(l){var B=C(l);if(typeof B=="function"&&B!==l.entries)for(var Y=B.call(l),z;!(z=Y.next()).done;)Ze(z.value)&&Er(z.value,v)}}}function Wt(l){{var v=l.type;if(v==null||typeof v=="string")return;var I;if(typeof v=="function")I=v.propTypes;else if(typeof v=="object"&&(v.$$typeof===f||v.$$typeof===m))I=v.propTypes;else return;if(I){var P=H(v);Mt(I,l.props,"prop",P,l)}else if(v.PropTypes!==void 0&&!Je){Je=!0;var B=H(v);w("Component %s declared `PropTypes` instead of `propTypes`. Did you misspell the property assignment?",B||"Unknown")}typeof v.getDefaultProps=="function"&&!v.getDefaultProps.isReactClassApproved&&w("getDefaultProps is only used on classic React.createClass definitions. Use a static property named `defaultProps` instead.")}}function Gt(l){{for(var v=Object.keys(l.props),I=0;I<v.length;I++){var P=v[I];if(P!=="children"&&P!=="key"){be(l),w("Invalid prop `%s` supplied to `React.Fragment`. React.Fragment can only have `key` and `children` props.",P),be(null);break}}l.ref!==null&&(be(l),w("Invalid attribute `ref` supplied to `React.Fragment`."),be(null))}}function jr(l,v,I,P,B,Y){{var z=U(l);if(!z){var $="";(l===void 0||typeof l=="object"&&l!==null&&Object.keys(l).length===0)&&($+=" You likely forgot to export your component from the file it's defined in, or you might have mixed up default and named imports.");var se=Vt(B);se?$+=se:$+=Cr();var J;l===null?J="null":Ke(l)?J="array":l!==void 0&&l.$$typeof===r?(J="<"+(H(l.type)||"Unknown")+" />",$=" Did you accidentally export a JSX literal instead of a component?"):J=typeof l,w("React.jsx: type is invalid -- expected a string (for built-in components) or a class/function (for composite components) but got: %s.%s",J,$)}var Z=Ut(l,v,I,B,Y);if(Z==null)return Z;if(z){var ae=v.children;if(ae!==void 0)if(P)if(Ke(ae)){for(var ye=0;ye<ae.length;ye++)Tr(ae[ye],l);Object.freeze&&Object.freeze(ae)}else w("React.jsx: Static children should always be an array. You are likely explicitly calling React.jsxs or React.jsxDEV. Use the Babel transform instead.");else Tr(ae,l)}return l===n?Gt(Z):Wt(Z),Z}}function Kt(l,v,I){return jr(l,v,I,!0)}function Qt(l,v,I){return jr(l,v,I,!1)}var Xt=Qt,Jt=Kt;Ie.Fragment=n,Ie.jsx=Xt,Ie.jsxs=Jt}()),Ie}(function(e){process.env.NODE_ENV==="production"?e.exports=to():e.exports=oo()})(ro);const so={small:s.css(["padding:",";font-size:",";min-height:20px;min-width:",";"],({theme:e})=>`${e.spacing.xxs} ${e.spacing.xs}`,({theme:e})=>e.fontSizes.xs,({dot:e})=>e?"8px":"20px"),medium:s.css(["padding:",";font-size:",";min-height:24px;min-width:",";"],({theme:e})=>`${e.spacing.xs} ${e.spacing.sm}`,({theme:e})=>e.fontSizes.sm,({dot:e})=>e?"10px":"24px"),large:s.css(["padding:",";font-size:",";min-height:32px;min-width:",";"],({theme:e})=>`${e.spacing.sm} ${e.spacing.md}`,({theme:e})=>e.fontSizes.md,({dot:e})=>e?"12px":"32px")},no=(e,r,t=!1)=>s.css(["",""],({theme:n})=>{let i,c,a;switch(e){case"primary":i=r?n.colors.primary:`${n.colors.primary}20`,c=r?n.colors.textInverse:n.colors.primary,a=n.colors.primary;break;case"secondary":i=r?n.colors.secondary:`${n.colors.secondary}20`,c=r?n.colors.textInverse:n.colors.secondary,a=n.colors.secondary;break;case"success":i=r?n.colors.success:`${n.colors.success}20`,c=r?n.colors.textInverse:n.colors.success,a=n.colors.success;break;case"warning":i=r?n.colors.warning:`${n.colors.warning}20`,c=r?n.colors.textInverse:n.colors.warning,a=n.colors.warning;break;case"error":i=r?n.colors.error:`${n.colors.error}20`,c=r?n.colors.textInverse:n.colors.error,a=n.colors.error;break;case"info":i=r?n.colors.info:`${n.colors.info}20`,c=r?n.colors.textInverse:n.colors.info,a=n.colors.info;break;case"neutral":i=r?n.colors.textSecondary:`${n.colors.textSecondary}10`,c=r?n.colors.textInverse:n.colors.textSecondary,a=n.colors.textSecondary;break;default:i=r?n.colors.textSecondary:`${n.colors.textSecondary}20`,c=r?n.colors.textInverse:n.colors.textSecondary,a=n.colors.textSecondary}return t?`
          background-color: transparent;
          color: ${a};
          border: 1px solid ${a};
        `:`
        background-color: ${i};
        color: ${c};
        border: 1px solid transparent;
      `}),it=s.span.withConfig({displayName:"IconContainer",componentId:"sc-10uskub-0"})(["display:flex;align-items:center;justify-content:center;"]),io=s(it).withConfig({displayName:"StartIcon",componentId:"sc-10uskub-1"})(["margin-right:",";"],({theme:e})=>e.spacing.xxs),ao=s(it).withConfig({displayName:"EndIcon",componentId:"sc-10uskub-2"})(["margin-left:",";"],({theme:e})=>e.spacing.xxs),co=s.span.withConfig({displayName:"StyledBadge",componentId:"sc-10uskub-3"})(["display:",";align-items:center;justify-content:center;border-radius:",";font-weight:",";white-space:nowrap;"," "," "," "," ",""],({inline:e})=>e?"inline-flex":"flex",({theme:e,rounded:r,dot:t})=>t?"50%":r?"9999px":e.borderRadius.sm,({theme:e})=>e.fontWeights.medium,({size:e})=>so[e],({variant:e,solid:r,outlined:t})=>no(e,r,t||!1),({dot:e})=>e&&s.css(["padding:0;height:8px;width:8px;"]),({counter:e})=>e&&s.css(["min-width:1.5em;height:1.5em;padding:0 0.5em;border-radius:1em;"]),({clickable:e})=>e&&s.css(["cursor:pointer;transition:opacity ",";&:hover{opacity:0.8;}&:active{opacity:0.6;}"],({theme:r})=>r.transitions.fast)),Ne=({children:e,variant:r="default",size:t="medium",solid:n=!1,className:i="",style:c,onClick:a,rounded:p=!1,dot:f=!1,counter:u=!1,outlined:d=!1,startIcon:m,endIcon:h,max:y,inline:g=!0})=>{let x=e;return u&&typeof e=="number"&&y!==void 0&&e>y&&(x=`${y}+`),o.jsx(co,{variant:r,size:t,solid:n,clickable:!!a,className:i,style:c,onClick:a,rounded:p,dot:f,counter:u,outlined:d,inline:g,children:!f&&o.jsxs(o.Fragment,{children:[m&&o.jsx(io,{children:m}),x,h&&o.jsx(ao,{children:h})]})})},lo=s.keyframes(["0%{transform:rotate(0deg);}100%{transform:rotate(360deg);}"]),uo=s.div.withConfig({displayName:"LoadingSpinner",componentId:"sc-1rze74q-0"})(["width:16px;height:16px;border:2px solid rgba(255,255,255,0.3);border-radius:50%;border-top-color:#fff;animation:"," 0.8s linear infinite;margin-right:",";"],lo,({theme:e})=>e.spacing.xs),po={small:s.css(["padding:",";font-size:",";min-height:32px;"],({theme:e})=>`${e.spacing.xxs} ${e.spacing.sm}`,({theme:e})=>e.fontSizes.xs),medium:s.css(["padding:",";font-size:",";min-height:40px;"],({theme:e})=>`${e.spacing.xs} ${e.spacing.md}`,({theme:e})=>e.fontSizes.sm),large:s.css(["padding:",";font-size:",";min-height:48px;"],({theme:e})=>`${e.spacing.sm} ${e.spacing.lg}`,({theme:e})=>e.fontSizes.md)},fo={primary:s.css(["background-color:",";color:",";border:none;&:hover:not(:disabled){background-color:",";transform:translateY(-1px);box-shadow:0 4px 6px rgba(0,0,0,0.1);}&:active:not(:disabled){background-color:",";transform:translateY(0);box-shadow:none;}"],({theme:e})=>e.colors.primary,({theme:e})=>e.colors.textPrimary||e.colors.textInverse||"#fff",({theme:e})=>e.colors.primaryDark,({theme:e})=>e.colors.primaryDark),secondary:s.css(["background-color:",";color:",";border:none;&:hover:not(:disabled){background-color:",";transform:translateY(-1px);box-shadow:0 4px 6px rgba(0,0,0,0.1);}&:active:not(:disabled){background-color:",";transform:translateY(0);box-shadow:none;}"],({theme:e})=>e.colors.secondary,({theme:e})=>e.colors.textPrimary||e.colors.textInverse||"#fff",({theme:e})=>e.colors.secondaryDark,({theme:e})=>e.colors.secondaryDark),outline:s.css(["background-color:transparent;color:",";border:1px solid ",";&:hover:not(:disabled){background-color:rgba(225,6,0,0.05);transform:translateY(-1px);}&:active:not(:disabled){background-color:rgba(225,6,0,0.1);transform:translateY(0);}"],({theme:e})=>e.colors.primary,({theme:e})=>e.colors.primary),text:s.css(["background-color:transparent;color:",";border:none;padding-left:",";padding-right:",";&:hover:not(:disabled){background-color:rgba(225,6,0,0.05);}&:active:not(:disabled){background-color:rgba(225,6,0,0.1);}"],({theme:e})=>e.colors.primary,({theme:e})=>e.spacing.xs,({theme:e})=>e.spacing.xs),success:s.css(["background-color:",";color:",";border:none;&:hover:not(:disabled){background-color:","dd;transform:translateY(-1px);box-shadow:0 4px 6px rgba(0,0,0,0.1);}&:active:not(:disabled){transform:translateY(0);box-shadow:none;}"],({theme:e})=>e.colors.success,({theme:e})=>e.colors.textInverse||"#fff",({theme:e})=>e.colors.success),danger:s.css(["background-color:",";color:",";border:none;&:hover:not(:disabled){background-color:","dd;transform:translateY(-1px);box-shadow:0 4px 6px rgba(0,0,0,0.1);}&:active:not(:disabled){transform:translateY(0);box-shadow:none;}"],({theme:e})=>e.colors.error,({theme:e})=>e.colors.textInverse||"#fff",({theme:e})=>e.colors.error)},go=s.button.withConfig({displayName:"StyledButton",componentId:"sc-1rze74q-1"})(["display:inline-flex;align-items:center;justify-content:center;border-radius:",";font-weight:",";cursor:pointer;transition:all ",";position:relative;overflow:hidden;"," "," "," &:disabled{opacity:0.6;cursor:not-allowed;box-shadow:none;transform:translateY(0);}"," ",""],({theme:e})=>e.borderRadius.sm,({theme:e})=>{var r;return((r=e.fontWeights)==null?void 0:r.medium)||500},({theme:e})=>{var r;return((r=e.transitions)==null?void 0:r.fast)||"0.2s ease"},({size:e="medium"})=>po[e],({variant:e="primary"})=>fo[e],({fullWidth:e})=>e&&s.css(["width:100%;"]),({$hasStartIcon:e})=>e&&s.css(["& > *:first-child{margin-right:",";}"],({theme:r})=>r.spacing.xs),({$hasEndIcon:e})=>e&&s.css(["& > *:last-child{margin-left:",";}"],({theme:r})=>r.spacing.xs)),mo=s.div.withConfig({displayName:"ButtonContent",componentId:"sc-1rze74q-2"})(["display:flex;align-items:center;justify-content:center;"]),ie=({children:e,variant:r="primary",disabled:t=!1,loading:n=!1,size:i="medium",fullWidth:c=!1,startIcon:a,endIcon:p,onClick:f,className:u="",type:d="button",...m})=>o.jsx(go,{variant:r,disabled:t||n,size:i,fullWidth:c,onClick:f,className:u,type:d,$hasStartIcon:!!a&&!n,$hasEndIcon:!!p&&!n,...m,children:o.jsxs(mo,{children:[n&&o.jsx(uo,{}),!n&&a,e,!n&&p]})}),ho=s.div.withConfig({displayName:"InputWrapper",componentId:"sc-uv3rzi-0"})(["display:flex;flex-direction:column;width:",";position:relative;"],({fullWidth:e})=>e?"100%":"auto"),xo=s.label.withConfig({displayName:"Label",componentId:"sc-uv3rzi-1"})(["font-size:",";color:",";margin-bottom:",";font-weight:",";"],({theme:e})=>e.fontSizes.sm,({theme:e})=>e.colors.textSecondary,({theme:e})=>e.spacing.xxs,({theme:e})=>{var r;return((r=e.fontWeights)==null?void 0:r.medium)||500}),bo=s.div.withConfig({displayName:"InputContainer",componentId:"sc-uv3rzi-2"})(["display:flex;align-items:center;position:relative;width:100%;border-radius:",";border:1px solid ",";background-color:",";transition:all ",";"," "," ",""],({theme:e})=>e.borderRadius.sm,({theme:e,hasError:r,hasSuccess:t,isFocused:n})=>r?e.colors.error:t?e.colors.success:n?e.colors.primary:e.colors.border,({theme:e})=>e.colors.surface,({theme:e})=>{var r;return((r=e.transitions)==null?void 0:r.fast)||"0.2s ease"},({disabled:e,theme:r})=>e&&s.css(["opacity:0.6;background-color:",";cursor:not-allowed;"],r.colors.background),({isFocused:e,theme:r,hasError:t,hasSuccess:n})=>e&&s.css(["box-shadow:0 0 0 2px ",";"],t?`${r.colors.error}33`:n?`${r.colors.success}33`:`${r.colors.primary}33`),({$size:e})=>{switch(e){case"small":return s.css(["height:32px;"]);case"large":return s.css(["height:48px;"]);default:return s.css(["height:40px;"])}}),_r=s.div.withConfig({displayName:"IconContainer",componentId:"sc-uv3rzi-3"})(["display:flex;align-items:center;justify-content:center;padding:0 ",";color:",";"],({theme:e})=>e.spacing.xs,({theme:e})=>e.colors.textSecondary),yo=s.input.withConfig({displayName:"StyledInput",componentId:"sc-uv3rzi-4"})(["flex:1;border:none;background:transparent;color:",";width:100%;outline:none;&:disabled{cursor:not-allowed;}&::placeholder{color:",";}"," "," ",""],({theme:e})=>e.colors.textPrimary,({theme:e})=>e.colors.textDisabled,({hasStartIcon:e})=>e&&s.css(["padding-left:0;"]),({hasEndIcon:e})=>e&&s.css(["padding-right:0;"]),({$size:e,theme:r})=>e==="small"?s.css(["font-size:",";padding:"," ",";"],r.fontSizes.xs,r.spacing.xxs,r.spacing.xs):e==="large"?s.css(["font-size:",";padding:"," ",";"],r.fontSizes.md,r.spacing.sm,r.spacing.md):s.css(["font-size:",";padding:"," ",";"],r.fontSizes.sm,r.spacing.xs,r.spacing.sm)),vo=s.button.withConfig({displayName:"ClearButton",componentId:"sc-uv3rzi-5"})(["background:none;border:none;cursor:pointer;color:",";padding:0 ",";display:flex;align-items:center;justify-content:center;&:hover{color:",";}&:focus{outline:none;}"],({theme:e})=>e.colors.textDisabled,({theme:e})=>e.spacing.xs,({theme:e})=>e.colors.textSecondary),So=s.div.withConfig({displayName:"HelperTextContainer",componentId:"sc-uv3rzi-6"})(["display:flex;justify-content:space-between;margin-top:",";font-size:",";color:",";"],({theme:e})=>e.spacing.xxs,({theme:e})=>e.fontSizes.xs,({theme:e,hasError:r,hasSuccess:t})=>r?e.colors.error:t?e.colors.success:e.colors.textSecondary),xe=({value:e,onChange:r,placeholder:t="",disabled:n=!1,error:i="",type:c="text",name:a="",id:p="",className:f="",required:u=!1,autoComplete:d="",label:m="",helperText:h="",startIcon:y,endIcon:g,loading:x=!1,success:C=!1,clearable:S=!1,onClear:w,maxLength:L,showCharCount:A=!1,size:O="medium",fullWidth:R=!1,...j})=>{const[_,k]=b.useState(!1),U=b.useRef(null),V=()=>{w?w():r(""),U.current&&U.current.focus()},N=oe=>{k(!0),j.onFocus&&j.onFocus(oe)},H=oe=>{k(!1),j.onBlur&&j.onBlur(oe)},ee=S&&e&&!n,te=(e==null?void 0:e.length)||0,de=A||L!==void 0&&L>0;return o.jsxs(ho,{className:f,fullWidth:R,children:[m&&o.jsxs(xo,{htmlFor:p,children:[m,u&&" *"]}),o.jsxs(bo,{hasError:!!i,hasSuccess:!!C,disabled:!!n,$size:O,hasStartIcon:!!y,hasEndIcon:!!(g||ee),isFocused:!!_,children:[y&&o.jsx(_r,{children:y}),o.jsx(yo,{ref:U,type:c,value:e,onChange:oe=>r(oe.target.value),placeholder:t,disabled:!!(n||x),name:a,id:p,required:!!u,autoComplete:d,hasStartIcon:!!y,hasEndIcon:!!(g||ee),$size:O,maxLength:L,onFocus:N,onBlur:H,...j}),ee&&o.jsx(vo,{type:"button",onClick:V,tabIndex:-1,children:"✕"}),g&&o.jsx(_r,{children:g})]}),(i||h||de)&&o.jsxs(So,{hasError:!!i,hasSuccess:!!C,children:[o.jsx("div",{children:i||h}),de&&o.jsxs("div",{children:[te,L!==void 0&&`/${L}`]})]})]})},Lr={small:s.css(["height:100px;"]),medium:s.css(["height:200px;"]),large:s.css(["height:300px;"]),custom:e=>s.css(["height:",";width:",";"],e.customHeight,e.customWidth||"100%")},wo={default:s.css(["background-color:",";border-radius:",";"],({theme:e})=>e.colors.background,({theme:e})=>e.borderRadius.md),card:s.css(["background-color:",";border-radius:",";box-shadow:",";"],({theme:e})=>e.colors.surface,({theme:e})=>e.borderRadius.md,({theme:e})=>e.shadows.sm),text:s.css(["background-color:transparent;height:auto !important;min-height:1.5em;"]),list:s.css(["background-color:",";border-radius:",";margin-bottom:",";"],({theme:e})=>e.colors.background,({theme:e})=>e.borderRadius.sm,({theme:e})=>e.spacing.sm)},Co=s.keyframes(["0%{transform:rotate(0deg);}100%{transform:rotate(360deg);}"]),Io=s.div.withConfig({displayName:"Container",componentId:"sc-12vczt5-0"})(["display:flex;flex-direction:column;align-items:center;justify-content:center;"," ",""],({size:e,customHeight:r,customWidth:t})=>e==="custom"?Lr.custom({customHeight:r,customWidth:t}):Lr[e],({variant:e})=>wo[e]),Eo=s.div.withConfig({displayName:"Spinner",componentId:"sc-12vczt5-1"})(["width:32px;height:32px;border:3px solid ",";border-top:3px solid ",";border-radius:50%;animation:"," 1s linear infinite;margin-bottom:",";"],({theme:e})=>e.colors.background,({theme:e})=>e.colors.primary,Co,({theme:e})=>e.spacing.sm),To=s.div.withConfig({displayName:"Text",componentId:"sc-12vczt5-2"})(["color:",";font-size:",";"],({theme:e})=>e.colors.textSecondary,({theme:e})=>e.fontSizes.sm),at=({variant:e="default",size:r="medium",height:t="200px",width:n="",text:i="Loading...",showSpinner:c=!0,className:a=""})=>o.jsxs(Io,{variant:e,size:r,customHeight:t,customWidth:n,className:a,children:[c&&o.jsx(Eo,{}),i&&o.jsx(To,{children:i})]}),jo=s.div.withConfig({displayName:"SelectWrapper",componentId:"sc-wvk2um-0"})(["display:flex;flex-direction:column;width:",";position:relative;"],({fullWidth:e})=>e?"100%":"auto"),ko=s.label.withConfig({displayName:"Label",componentId:"sc-wvk2um-1"})(["font-size:",";color:",";margin-bottom:",";font-weight:",";"],({theme:e})=>e.fontSizes.sm,({theme:e})=>e.colors.textSecondary,({theme:e})=>e.spacing.xxs,({theme:e})=>{var r;return((r=e.fontWeights)==null?void 0:r.medium)||500}),No=s.div.withConfig({displayName:"SelectContainer",componentId:"sc-wvk2um-2"})(["display:flex;align-items:center;position:relative;width:100%;border-radius:",";border:1px solid ",";background-color:",";transition:all ",";"," "," ",""],({theme:e})=>e.borderRadius.sm,({theme:e,hasError:r,hasSuccess:t,isFocused:n})=>r?e.colors.error:t?e.colors.success:n?e.colors.primary:e.colors.border,({theme:e})=>e.colors.surface,({theme:e})=>{var r;return((r=e.transitions)==null?void 0:r.fast)||"0.2s ease"},({disabled:e,theme:r})=>e&&s.css(["opacity:0.6;background-color:",";cursor:not-allowed;"],r.colors.background),({isFocused:e,theme:r,hasError:t,hasSuccess:n})=>e&&s.css(["box-shadow:0 0 0 2px ",";"],t?`${r.colors.error}33`:n?`${r.colors.success}33`:`${r.colors.primary}33`),({$size:e})=>{switch(e){case"small":return s.css(["height:32px;"]);case"large":return s.css(["height:48px;"]);default:return s.css(["height:40px;"])}}),Ro=s.div.withConfig({displayName:"IconContainer",componentId:"sc-wvk2um-3"})(["display:flex;align-items:center;justify-content:center;padding:0 ",";color:",";"],({theme:e})=>e.spacing.xs,({theme:e})=>e.colors.textSecondary),_o=s.select.withConfig({displayName:"StyledSelect",componentId:"sc-wvk2um-4"})(["flex:1;border:none;background:transparent;color:",`;width:100%;outline:none;appearance:none;background-image:url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");background-repeat:no-repeat;background-position:right `," center;background-size:16px;padding-right:",";&:disabled{cursor:not-allowed;}"," ",""],({theme:e})=>e.colors.textPrimary,({theme:e})=>e.spacing.sm,({theme:e})=>e.spacing.xl,({hasStartIcon:e})=>e&&s.css(["padding-left:0;"]),({$size:e,theme:r})=>e==="small"?s.css(["font-size:",";padding:"," ",";"],r.fontSizes.xs,r.spacing.xxs,r.spacing.xs):e==="large"?s.css(["font-size:",";padding:"," ",";"],r.fontSizes.md,r.spacing.sm,r.spacing.md):s.css(["font-size:",";padding:"," ",";"],r.fontSizes.sm,r.spacing.xs,r.spacing.sm)),Lo=s.div.withConfig({displayName:"HelperTextContainer",componentId:"sc-wvk2um-5"})(["display:flex;justify-content:space-between;margin-top:",";font-size:",";color:",";"],({theme:e})=>e.spacing.xxs,({theme:e})=>e.fontSizes.xs,({theme:e,hasError:r,hasSuccess:t})=>r?e.colors.error:t?e.colors.success:e.colors.textSecondary),Mo=s.optgroup.withConfig({displayName:"OptionGroup",componentId:"sc-wvk2um-6"})(["font-weight:",";color:",";"],({theme:e})=>{var r;return((r=e.fontWeights)==null?void 0:r.medium)||500},({theme:e})=>e.colors.textPrimary),Se=({options:e,value:r,onChange:t,disabled:n=!1,error:i="",name:c="",id:a="",className:p="",required:f=!1,placeholder:u="",label:d="",helperText:m="",size:h="medium",fullWidth:y=!0,loading:g=!1,success:x=!1,startIcon:C,...S})=>{const[w,L]=b.useState(!1),A=k=>{L(!0),S.onFocus&&S.onFocus(k)},O=k=>{L(!1),S.onBlur&&S.onBlur(k)},R={},j=[];e.forEach(k=>{k.group?(R[k.group]||(R[k.group]=[]),R[k.group].push(k)):j.push(k)});const _=Object.keys(R).length>0;return o.jsxs(jo,{className:p,fullWidth:y,children:[d&&o.jsxs(ko,{htmlFor:a,children:[d,f&&" *"]}),o.jsxs(No,{hasError:!!i,hasSuccess:!!x,disabled:!!(n||g),$size:h,hasStartIcon:!!C,isFocused:!!w,children:[C&&o.jsx(Ro,{children:C}),o.jsxs(_o,{value:r,onChange:k=>t(k.target.value),disabled:!!(n||g),name:c,id:a,required:!!f,hasStartIcon:!!C,$size:h,onFocus:A,onBlur:O,...S,children:[u&&o.jsx("option",{value:"",disabled:!0,children:u}),_?o.jsxs(o.Fragment,{children:[j.map(k=>o.jsx("option",{value:k.value,disabled:k.disabled,children:k.label},k.value)),Object.entries(R).map(([k,U])=>o.jsx(Mo,{label:k,children:U.map(V=>o.jsx("option",{value:V.value,disabled:V.disabled,children:V.label},V.value))},k))]}):e.map(k=>o.jsx("option",{value:k.value,disabled:k.disabled,children:k.label},k.value))]})]}),(i||m)&&o.jsx(Lo,{hasError:!!i,hasSuccess:!!x,children:o.jsx("div",{children:i||m})})]})},Mr={small:"8px",medium:"12px",large:"16px"},Po={small:s.css(["font-size:",";margin-left:",";"],({theme:e})=>e.fontSizes.xs,({theme:e})=>e.spacing.xs),medium:s.css(["font-size:",";margin-left:",";"],({theme:e})=>e.fontSizes.sm,({theme:e})=>e.spacing.sm),large:s.css(["font-size:",";margin-left:",";"],({theme:e})=>e.fontSizes.md,({theme:e})=>e.spacing.md)},Do=s.css(["@keyframes pulse{0%{transform:scale(0.95);box-shadow:0 0 0 0 rgba(var(--pulse-color),0.7);}70%{transform:scale(1);box-shadow:0 0 0 6px rgba(var(--pulse-color),0);}100%{transform:scale(0.95);box-shadow:0 0 0 0 rgba(var(--pulse-color),0);}}animation:pulse 2s infinite;"]),Oo=s.div.withConfig({displayName:"Container",componentId:"sc-gwj3m-0"})(["display:inline-flex;align-items:center;"]),$o=s.div.withConfig({displayName:"Indicator",componentId:"sc-gwj3m-1"})(["border-radius:50%;width:",";height:",";",""],({size:e})=>Mr[e],({size:e})=>Mr[e],({status:e,theme:r,pulse:t})=>{let n,i;switch(e){case"success":n=r.colors.success,i="76, 175, 80";break;case"error":n=r.colors.error,i="244, 67, 54";break;case"warning":n=r.colors.warning,i="255, 152, 0";break;case"info":n=r.colors.info,i="33, 150, 243";break;default:n=r.colors.textSecondary,i="158, 158, 158"}return s.css(["background-color:",";",""],n,t&&s.css(["--pulse-color:",";",""],i,Do))}),Ao=s.span.withConfig({displayName:"Label",componentId:"sc-gwj3m-2"})([""," ",""],({size:e})=>Po[e],({status:e,theme:r})=>{let t;switch(e){case"success":t=r.colors.success;break;case"error":t=r.colors.error;break;case"warning":t=r.colors.warning;break;case"info":t=r.colors.info;break;default:t=r.colors.textSecondary}return s.css(["color:",";font-weight:",";"],t,r.fontWeights.medium)}),zo=({status:e,size:r="medium",pulse:t=!1,showLabel:n=!1,label:i="",className:c=""})=>{const a=i||e.charAt(0).toUpperCase()+e.slice(1);return o.jsxs(Oo,{className:c,children:[o.jsx($o,{status:e,size:r,pulse:t}),n&&o.jsx(Ao,{status:e,size:r,children:a})]})},Fo={small:s.css(["padding:",";font-size:",";"],({theme:e})=>`${e.spacing.xxs} ${e.spacing.xs}`,({theme:e})=>e.fontSizes.xs),medium:s.css(["padding:",";font-size:",";"],({theme:e})=>`${e.spacing.xs} ${e.spacing.sm}`,({theme:e})=>e.fontSizes.sm),large:s.css(["padding:",";font-size:",";"],({theme:e})=>`${e.spacing.sm} ${e.spacing.md}`,({theme:e})=>e.fontSizes.md)},Bo=e=>s.css(["",""],({theme:r})=>{let t,n,i;switch(e){case"primary":t=`${r.colors.primary}10`,n=r.colors.primary,i=`${r.colors.primary}30`;break;case"secondary":t=`${r.colors.secondary}10`,n=r.colors.secondary,i=`${r.colors.secondary}30`;break;case"success":t=`${r.colors.success}10`,n=r.colors.success,i=`${r.colors.success}30`;break;case"warning":t=`${r.colors.warning}10`,n=r.colors.warning,i=`${r.colors.warning}30`;break;case"error":t=`${r.colors.error}10`,n=r.colors.error,i=`${r.colors.error}30`;break;case"info":t=`${r.colors.info}10`,n=r.colors.info,i=`${r.colors.info}30`;break;default:t=`${r.colors.textSecondary}10`,n=r.colors.textSecondary,i=`${r.colors.textSecondary}30`}return`
        background-color: ${t};
        color: ${n};
        border: 1px solid ${i};
      `}),qo=s.span.withConfig({displayName:"StyledTag",componentId:"sc-11nmnw9-0"})(["display:inline-flex;align-items:center;border-radius:",";font-weight:",";"," "," ",""],({theme:e})=>e.borderRadius.pill,({theme:e})=>e.fontWeights.medium,({size:e})=>Fo[e],({variant:e})=>Bo(e),({clickable:e})=>e&&s.css(["cursor:pointer;transition:opacity ",";&:hover{opacity:0.8;}&:active{opacity:0.6;}"],({theme:r})=>r.transitions.fast)),Ho=s.button.withConfig({displayName:"RemoveButton",componentId:"sc-11nmnw9-1"})(["display:inline-flex;align-items:center;justify-content:center;background:none;border:none;cursor:pointer;color:inherit;opacity:0.7;margin-left:",";padding:0;"," &:hover{opacity:1;}"],({theme:e})=>e.spacing.xs,({size:e,theme:r})=>{const t={small:"12px",medium:"14px",large:"16px"};return`
      width: ${t[e]};
      height: ${t[e]};
      font-size: ${r.fontSizes.xs};
    `}),Uo=({children:e,variant:r="default",size:t="medium",removable:n=!1,onRemove:i,className:c="",onClick:a})=>{const p=f=>{f.stopPropagation(),i==null||i()};return o.jsxs(qo,{variant:r,size:t,clickable:!!a,className:c,onClick:a,children:[e,n&&o.jsx(Ho,{size:t,onClick:p,children:"×"})]})},Vo=s.div.withConfig({displayName:"TimePickerContainer",componentId:"sc-v5w9zw-0"})(["display:flex;flex-direction:column;gap:",";"],({theme:e})=>e.spacing.xs),Yo=s.label.withConfig({displayName:"Label",componentId:"sc-v5w9zw-1"})(["font-size:",";font-weight:",";color:",";"],({theme:e})=>e.fontSizes.sm,({theme:e})=>e.fontWeights.medium,({theme:e})=>e.colors.textPrimary),Wo=s.input.withConfig({displayName:"TimeInput",componentId:"sc-v5w9zw-2"})(["padding:",";border:1px solid ",";border-radius:",";font-size:",";color:",";background-color:",";transition:border-color ",";&:focus{outline:none;border-color:",";}&:disabled{background-color:",";cursor:not-allowed;}"],({theme:e})=>e.spacing.sm,({theme:e})=>e.colors.border,({theme:e})=>e.borderRadius.sm,({theme:e})=>e.fontSizes.md,({theme:e})=>e.colors.textPrimary,({theme:e})=>e.colors.surface,({theme:e})=>e.transitions.fast,({theme:e})=>e.colors.primary,({theme:e})=>e.colors.chartGrid),Go=({id:e,name:r,value:t,onChange:n,label:i,required:c=!1,disabled:a=!1,className:p,placeholder:f="HH:MM",min:u,max:d})=>o.jsxs(Vo,{className:p,children:[i&&o.jsxs(Yo,{htmlFor:e,children:[i,c&&o.jsx("span",{style:{color:"red"},children:" *"})]}),o.jsx(Wo,{id:e,name:r,type:"time",value:t,onChange:n,required:c,disabled:a,placeholder:f,min:u,max:d})]}),Ko=Go,Qo=s.div.withConfig({displayName:"SelectContainer",componentId:"sc-w0dp8e-0"})(["display:flex;flex-direction:column;gap:",";"],({theme:e})=>e.spacing.xs),Xo=s.label.withConfig({displayName:"Label",componentId:"sc-w0dp8e-1"})(["font-size:",";font-weight:",";color:",";"],({theme:e})=>e.fontSizes.sm,({theme:e})=>e.fontWeights.medium,({theme:e})=>e.colors.textPrimary),Jo=s.select.withConfig({displayName:"Select",componentId:"sc-w0dp8e-2"})(["padding:",";border:1px solid ",";border-radius:",";font-size:",";color:",";background-color:",";transition:border-color ",";&:focus{outline:none;border-color:",";}&:disabled{background-color:",";cursor:not-allowed;}"],({theme:e})=>e.spacing.sm,({theme:e})=>e.colors.border,({theme:e})=>e.borderRadius.sm,({theme:e})=>e.fontSizes.md,({theme:e})=>e.colors.textPrimary,({theme:e})=>e.colors.surface,({theme:e})=>e.transitions.fast,({theme:e})=>e.colors.primary,({theme:e})=>e.colors.chartGrid),Zo=({id:e,name:r,value:t,onChange:n,options:i,label:c,required:a=!1,disabled:p=!1,className:f,placeholder:u})=>o.jsxs(Qo,{className:f,children:[c&&o.jsxs(Xo,{htmlFor:e,children:[c,a&&o.jsx("span",{style:{color:"red"},children:" *"})]}),o.jsxs(Jo,{id:e,name:r,value:t,onChange:n,required:a,disabled:p,children:[u&&o.jsx("option",{value:"",disabled:!0,children:u}),i.map(d=>o.jsx("option",{value:d.value,children:d.label},d.value))]})]}),es=Zo,rs=s.span.withConfig({displayName:"StyledLoadingCell",componentId:"sc-1i0qdjp-0"})(["display:inline-flex;align-items:center;justify-content:flex-end;opacity:0.6;position:relative;"," border-radius:",";&::after{content:'';position:absolute;top:0;left:0;right:0;bottom:0;background:linear-gradient(90deg,transparent,rgba(255,255,255,0.2),transparent);animation:shimmer 1.5s infinite;}@keyframes shimmer{0%{transform:translateX(-100%);}100%{transform:translateX(100%);}}"],({$size:e,theme:r})=>{var t,n,i,c,a,p,f,u,d;switch(e){case"small":return s.css(["font-size:",";padding:"," ",";"],((t=r.fontSizes)==null?void 0:t.xs)||"12px",((n=r.spacing)==null?void 0:n.xxs)||"2px",((i=r.spacing)==null?void 0:i.xs)||"4px");case"large":return s.css(["font-size:",";padding:"," ",";"],((c=r.fontSizes)==null?void 0:c.lg)||"18px",((a=r.spacing)==null?void 0:a.sm)||"8px",((p=r.spacing)==null?void 0:p.md)||"12px");default:return s.css(["font-size:",";padding:"," ",";"],((f=r.fontSizes)==null?void 0:f.sm)||"14px",((u=r.spacing)==null?void 0:u.xs)||"4px",((d=r.spacing)==null?void 0:d.sm)||"8px")}},({theme:e})=>{var r;return((r=e.borderRadius)==null?void 0:r.sm)||"4px"}),ts=s.span.withConfig({displayName:"LoadingPlaceholder",componentId:"sc-1i0qdjp-1"})(["display:inline-block;width:",";height:1em;background-color:currentColor;opacity:0.3;border-radius:2px;"],({$width:e})=>e||"60px"),os=e=>{const{size:r="medium",width:t,className:n,"aria-label":i}=e;return o.jsx(rs,{className:n,$size:r,$width:t,"aria-label":i||"Loading data",role:"cell","aria-busy":"true",children:o.jsx(ts,{$width:t})})},ss=os,ns=s.keyframes(["0%{transform:rotate(0deg);}100%{transform:rotate(360deg);}"]),is=s.keyframes(["0%{background-position:0% 0%;}100%{background-position:100% 100%;}"]),as=s.div.withConfig({displayName:"StyledSpinner",componentId:"sc-1hoaoss-0"})(["display:inline-block;position:relative;"," &::before{content:'';position:absolute;top:0;left:0;right:0;bottom:0;border-radius:50%;border:2px solid transparent;"," animation:"," ","s linear infinite;}",""],({$size:e})=>{switch(e){case"xs":return s.css(["width:16px;height:16px;"]);case"sm":return s.css(["width:20px;height:20px;"]);case"md":return s.css(["width:32px;height:32px;"]);case"lg":return s.css(["width:48px;height:48px;"]);case"xl":return s.css(["width:64px;height:64px;"]);default:return s.css(["width:32px;height:32px;"])}},({$variant:e,theme:r})=>{var t,n,i,c,a,p;switch(e){case"primary":return s.css(["border-top-color:",";border-right-color:",";"],((t=r.colors)==null?void 0:t.primary)||"#dc2626",((n=r.colors)==null?void 0:n.primary)||"#dc2626");case"secondary":return s.css(["border-top-color:",";border-right-color:",";"],((i=r.colors)==null?void 0:i.textSecondary)||"#9ca3af",((c=r.colors)==null?void 0:c.textSecondary)||"#9ca3af");case"white":return s.css(["border-top-color:#ffffff;border-right-color:#ffffff;"]);case"red":return s.css(["border-top-color:#dc2626;border-right-color:#dc2626;"]);default:return s.css(["border-top-color:",";border-right-color:",";"],((a=r.colors)==null?void 0:a.primary)||"#dc2626",((p=r.colors)==null?void 0:p.primary)||"#dc2626")}},ns,({$speed:e})=>1/e,({$showStripes:e,$variant:r,theme:t})=>e&&s.css(["&::after{content:'';position:absolute;top:2px;left:2px;right:2px;bottom:2px;border-radius:50%;background:",";background-size:8px 8px;animation:"," ","s linear infinite;}"],r==="red"||r==="primary"?"linear-gradient(45deg, transparent 25%, rgba(220, 38, 38, 0.3) 25%, rgba(220, 38, 38, 0.3) 50%, transparent 50%, transparent 75%, rgba(220, 38, 38, 0.3) 75%)":"linear-gradient(45deg, transparent 25%, rgba(156, 163, 175, 0.3) 25%, rgba(156, 163, 175, 0.3) 50%, transparent 50%, transparent 75%, rgba(156, 163, 175, 0.3) 75%)",is,n=>2/n.$speed)),cs=s.div.withConfig({displayName:"SpinnerContainer",componentId:"sc-1hoaoss-1"})(["display:inline-flex;align-items:center;justify-content:center;"]),ls=e=>{const{size:r="md",variant:t="primary",className:n,"aria-label":i,speed:c=1,showStripes:a=!1}=e;return o.jsx(cs,{className:n,children:o.jsx(as,{$size:r,$variant:t,$speed:c,$showStripes:a,role:"status","aria-label":i||"Loading","aria-live":"polite"})})},ds=ls,us={none:s.css(["padding:0;"]),small:s.css(["padding:",";"],({theme:e})=>e.spacing.sm),medium:s.css(["padding:",";"],({theme:e})=>e.spacing.md),large:s.css(["padding:",";"],({theme:e})=>e.spacing.lg)},ps={default:s.css(["background-color:",";"],({theme:e})=>e.colors.surface),primary:s.css(["background-color:","10;border-color:","30;"],({theme:e})=>e.colors.primary,({theme:e})=>e.colors.primary),secondary:s.css(["background-color:","10;border-color:","30;"],({theme:e})=>e.colors.secondary,({theme:e})=>e.colors.secondary),outlined:s.css(["background-color:transparent;border:1px solid ",";"],({theme:e})=>e.colors.border),elevated:s.css(["background-color:",";box-shadow:",";border:none;"],({theme:e})=>e.colors.surface,({theme:e})=>e.shadows.md)},fs=s.div.withConfig({displayName:"CardContainer",componentId:"sc-mv9m67-0"})(["border-radius:",";overflow:hidden;transition:all ",";position:relative;"," "," "," ",""],({theme:e})=>e.borderRadius.md,({theme:e})=>e.transitions.fast,({bordered:e,theme:r})=>e&&s.css(["border:1px solid ",";"],r.colors.border),({padding:e})=>us[e],({variant:e})=>ps[e],({clickable:e})=>e&&s.css(["cursor:pointer;&:hover{transform:translateY(-2px);box-shadow:",";}&:active{transform:translateY(0);}"],({theme:r})=>r.shadows.md)),gs=s.div.withConfig({displayName:"CardHeader",componentId:"sc-mv9m67-1"})(["display:flex;justify-content:space-between;align-items:flex-start;margin-bottom:",";"],({theme:e})=>e.spacing.md),ms=s.div.withConfig({displayName:"HeaderContent",componentId:"sc-mv9m67-2"})(["flex:1;"]),hs=s.h3.withConfig({displayName:"CardTitle",componentId:"sc-mv9m67-3"})(["margin:0;font-size:",";font-weight:",";color:",";"],({theme:e})=>e.fontSizes.lg,({theme:e})=>e.fontWeights.semibold,({theme:e})=>e.colors.textPrimary),xs=s.div.withConfig({displayName:"CardSubtitle",componentId:"sc-mv9m67-4"})(["margin-top:",";font-size:",";color:",";"],({theme:e})=>e.spacing.xs,({theme:e})=>e.fontSizes.sm,({theme:e})=>e.colors.textSecondary),bs=s.div.withConfig({displayName:"ActionsContainer",componentId:"sc-mv9m67-5"})(["display:flex;gap:",";"],({theme:e})=>e.spacing.sm),ys=s.div.withConfig({displayName:"CardContent",componentId:"sc-mv9m67-6"})([""]),vs=s.div.withConfig({displayName:"CardFooter",componentId:"sc-mv9m67-7"})(["margin-top:",";padding-top:",";border-top:1px solid ",";"],({theme:e})=>e.spacing.md,({theme:e})=>e.spacing.md,({theme:e})=>e.colors.border),Ss=s.div.withConfig({displayName:"LoadingOverlay",componentId:"sc-mv9m67-8"})(["position:absolute;top:0;left:0;right:0;bottom:0;background-color:",";display:flex;align-items:center;justify-content:center;z-index:1;"],({theme:e})=>`${e.colors.background}80`),ws=s.div.withConfig({displayName:"ErrorContainer",componentId:"sc-mv9m67-9"})(["padding:",";background-color:","10;border-radius:",";color:",";margin-bottom:",";"],({theme:e})=>e.spacing.md,({theme:e})=>e.colors.error,({theme:e})=>e.borderRadius.sm,({theme:e})=>e.colors.error,({theme:e})=>e.spacing.md),Cs=s.div.withConfig({displayName:"LoadingSpinner",componentId:"sc-mv9m67-10"})(["width:32px;height:32px;border:3px solid ",";border-top:3px solid ",";border-radius:50%;animation:spin 1s linear infinite;@keyframes spin{0%{transform:rotate(0deg);}100%{transform:rotate(360deg);}}"],({theme:e})=>e.colors.background,({theme:e})=>e.colors.primary),ct=({children:e,title:r="",subtitle:t="",bordered:n=!0,variant:i="default",padding:c="medium",className:a="",footer:p,actions:f,isLoading:u=!1,hasError:d=!1,errorMessage:m="An error occurred",clickable:h=!1,onClick:y,...g})=>{const x=r||t||f;return o.jsxs(fs,{bordered:n,variant:i,padding:c,clickable:h,className:a,onClick:h?y:void 0,...g,children:[u&&o.jsx(Ss,{children:o.jsx(Cs,{})}),x&&o.jsxs(gs,{children:[o.jsxs(ms,{children:[r&&o.jsx(hs,{children:r}),t&&o.jsx(xs,{children:t})]}),f&&o.jsx(bs,{children:f})]}),d&&o.jsx(ws,{children:o.jsx("p",{children:m})}),o.jsx(ys,{children:e}),p&&o.jsx(vs,{children:p})]})},Is=s.h3.withConfig({displayName:"Title",componentId:"sc-1jsjvya-0"})(["margin:0 0 "," 0;color:",";font-weight:",";",""],({theme:e})=>e.spacing.sm,({theme:e})=>e.colors.textPrimary,({theme:e})=>e.fontWeights.semibold,({size:e,theme:r})=>{const t={small:r.fontSizes.md,medium:r.fontSizes.lg,large:r.fontSizes.xl};return s.css(["font-size:",";"],t[e])}),Es=s.p.withConfig({displayName:"Description",componentId:"sc-1jsjvya-1"})(["margin:0 0 "," 0;color:",";",""],({theme:e})=>e.spacing.lg,({theme:e})=>e.colors.textSecondary,({size:e,theme:r})=>{const t={small:r.fontSizes.sm,medium:r.fontSizes.md,large:r.fontSizes.lg};return s.css(["font-size:",";"],t[e])}),Ts={default:s.css(["background-color:transparent;"]),compact:s.css(["background-color:transparent;text-align:left;align-items:flex-start;"]),card:s.css(["background-color:",";border-radius:",";box-shadow:",";"],({theme:e})=>e.colors.surface,({theme:e})=>e.borderRadius.md,({theme:e})=>e.shadows.sm)},js=s.div.withConfig({displayName:"Container",componentId:"sc-1jsjvya-2"})(["display:flex;flex-direction:column;align-items:center;justify-content:center;text-align:center;width:100%;"," ",""],({variant:e})=>Ts[e],({size:e,theme:r})=>{switch(e){case"small":return s.css(["padding:",";min-height:120px;"],r.spacing.md);case"large":return s.css(["padding:",";min-height:300px;"],r.spacing.xl);default:return s.css(["padding:",";min-height:200px;"],r.spacing.lg)}}),ks=s.div.withConfig({displayName:"IconContainer",componentId:"sc-1jsjvya-3"})(["margin-bottom:",";",""],({theme:e})=>e.spacing.md,({size:e,theme:r})=>{const t={small:"32px",medium:"48px",large:"64px"};return s.css(["font-size:",";svg{width:",";height:",";color:",";}"],t[e],t[e],t[e],r.colors.textSecondary)}),Ns=s.div.withConfig({displayName:"ActionContainer",componentId:"sc-1jsjvya-4"})(["margin-top:",";"],({theme:e})=>e.spacing.md),Rs=s.div.withConfig({displayName:"ChildrenContainer",componentId:"sc-1jsjvya-5"})(["margin-top:",";width:100%;"],({theme:e})=>e.spacing.lg),tr=({title:e="",description:r="",icon:t,actionText:n="",onAction:i,variant:c="default",size:a="medium",className:p="",children:f})=>o.jsxs(js,{variant:c,size:a,className:p,children:[t&&o.jsx(ks,{size:a,children:t}),e&&o.jsx(Is,{size:a,children:e}),r&&o.jsx(Es,{size:a,children:r}),n&&i&&o.jsx(Ns,{children:o.jsx(ie,{variant:"primary",size:a==="small"?"small":"medium",onClick:i,children:n})}),f&&o.jsx(Rs,{children:f})]}),Pr=s.div.withConfig({displayName:"ErrorContainer",componentId:"sc-jxqb9h-0"})(["padding:1.5rem;margin:",";border-radius:0.5rem;background-color:",";color:#ffffff;",""],e=>e.isAppLevel?"0":"1rem 0",e=>e.isAppLevel?"#1a1f2c":"#f44336",e=>e.isAppLevel&&`
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    min-height: 100vh;
    width: 100%;
    max-width: 600px;
    margin: 0 auto;
  `),_s=s.div.withConfig({displayName:"ErrorCard",componentId:"sc-jxqb9h-1"})(["background-color:#252a37;border-radius:0.5rem;padding:2rem;width:100%;box-shadow:0 4px 6px rgba(0,0,0,0.1);"]),Dr=s.h3.withConfig({displayName:"ErrorTitle",componentId:"sc-jxqb9h-2"})(["margin-top:0;font-size:",";font-weight:700;text-align:",";"],e=>e.isAppLevel?"1.5rem":"1.25rem",e=>e.isAppLevel?"center":"left"),De=s.p.withConfig({displayName:"ErrorMessage",componentId:"sc-jxqb9h-3"})(["margin-bottom:1rem;text-align:",";"],e=>e.isAppLevel?"center":"left"),Or=s.details.withConfig({displayName:"ErrorDetails",componentId:"sc-jxqb9h-4"})(["margin-bottom:1rem;summary{cursor:pointer;color:#2196f3;font-weight:500;margin-bottom:0.5rem;}"]),$r=s.pre.withConfig({displayName:"ErrorStack",componentId:"sc-jxqb9h-5"})(["font-size:0.875rem;background-color:rgba(0,0,0,0.1);padding:0.5rem;border-radius:0.25rem;overflow:auto;max-height:200px;"]),Ls=s.div.withConfig({displayName:"ButtonContainer",componentId:"sc-jxqb9h-6"})(["display:flex;gap:0.5rem;justify-content:flex-start;"]),lt=s.button.withConfig({displayName:"RetryButton",componentId:"sc-jxqb9h-7"})(["background-color:#ffffff;color:#f44336;border:none;border-radius:0.25rem;padding:0.5rem 1rem;font-weight:700;cursor:pointer;transition:background-color 0.2s;&:hover{background-color:#f5f5f5;}"]),Ms=s.button.withConfig({displayName:"SkipButton",componentId:"sc-jxqb9h-8"})(["padding:0.5rem 1rem;background-color:transparent;color:#ffffff;border:1px solid #ffffff;border-radius:0.25rem;font-size:0.875rem;font-weight:500;cursor:pointer;transition:all 0.2s;&:hover{background-color:rgba(255,255,255,0.1);}"]),Ps=s(lt).withConfig({displayName:"ReloadButton",componentId:"sc-jxqb9h-9"})(["margin-top:1rem;width:100%;"]),Ds=({error:e,resetError:r,isAppLevel:t,name:n,onSkip:i})=>{const c=()=>{window.location.reload()};return t?o.jsx(Pr,{isAppLevel:!0,children:o.jsxs(_s,{children:[o.jsx(Dr,{isAppLevel:!0,children:"Something went wrong"}),o.jsx(De,{isAppLevel:!0,children:"We're sorry, but an unexpected error has occurred. Please try reloading the application."}),o.jsxs(Or,{children:[o.jsx("summary",{children:"Technical Details"}),o.jsx(De,{children:e.message}),e.stack&&o.jsx($r,{children:e.stack})]}),o.jsx(Ps,{onClick:c,children:"Reload Application"})]})}):o.jsxs(Pr,{children:[o.jsx(Dr,{children:n?`Error in ${n}`:"Something went wrong"}),o.jsx(De,{children:n?`We encountered a problem while loading ${n}. You can try again${i?" or skip this feature":""}.`:"An unexpected error occurred. Please try again."}),o.jsxs(Or,{children:[o.jsx("summary",{children:"Technical Details"}),o.jsx(De,{children:e.message}),e.stack&&o.jsx($r,{children:e.stack})]}),o.jsxs(Ls,{children:[o.jsx(lt,{onClick:r,children:"Try Again"}),i&&o.jsx(Ms,{onClick:i,children:"Skip This Feature"})]})]})};class dt extends b.Component{constructor(r){super(r),this.resetError=()=>{this.setState({hasError:!1,error:null})},this.state={hasError:!1,error:null}}static getDerivedStateFromError(r){return{hasError:!0,error:r}}componentDidCatch(r,t){const{name:n}=this.props,i=n?`ErrorBoundary(${n})`:"ErrorBoundary";console.error(`Error caught by ${i}:`,r,t),this.props.onError&&this.props.onError(r,t)}componentDidUpdate(r){this.state.hasError&&this.props.resetOnPropsChange&&r.children!==this.props.children&&this.resetError()}componentWillUnmount(){this.state.hasError&&this.props.resetOnUnmount&&this.resetError()}render(){const{hasError:r,error:t}=this.state,{children:n,fallback:i,name:c,isFeatureBoundary:a,onSkip:p}=this.props;return r&&t?typeof i=="function"?i({error:t,resetError:this.resetError}):i||o.jsx(Ds,{error:t,resetError:this.resetError,isAppLevel:!a,name:c,onSkip:p}):n}}const nr=({isAppLevel:e=!1,isFeatureBoundary:r=!1,children:t,...n})=>{const i=e?"app":r?"feature":"component",c={resetOnPropsChange:i!=="app",resetOnUnmount:i!=="app",isFeatureBoundary:i==="feature"};return o.jsx(dt,{...c,...n,children:t})},Os=e=>o.jsx(nr,{isAppLevel:!0,...e}),$s=({featureName:e,children:r,...t})=>o.jsx(nr,{isFeatureBoundary:!0,name:e,children:r,...t}),As=s.div.withConfig({displayName:"TabContainer",componentId:"sc-lgz9vh-0"})(["display:flex;flex-direction:column;width:100%;"]),zs=s.div.withConfig({displayName:"TabList",componentId:"sc-lgz9vh-1"})(["display:flex;border-bottom:1px solid ",";margin-bottom:",";"],({theme:e})=>e.colors.border,({theme:e})=>e.spacing.md),Fs=s.button.withConfig({displayName:"TabButton",componentId:"sc-lgz9vh-2"})(["padding:"," ",";background:none;border:none;border-bottom:2px solid ",";color:",";font-weight:",";cursor:pointer;transition:all ",";&:hover{color:",";}&:focus{outline:none;color:",";}"],({theme:e})=>e.spacing.sm,({theme:e})=>e.spacing.md,({active:e,theme:r})=>e?r.colors.primary:"transparent",({active:e,theme:r})=>e?r.colors.primary:r.colors.textSecondary,({active:e,theme:r})=>e?r.fontWeights.semibold:r.fontWeights.regular,({theme:e})=>e.transitions.fast,({theme:e})=>e.colors.primary,({theme:e})=>e.colors.primary),Bs=s.div.withConfig({displayName:"TabContent",componentId:"sc-lgz9vh-3"})(["padding:"," 0;"],({theme:e})=>e.spacing.sm),qs=({tabs:e,defaultTab:r,className:t,activeTab:n,onTabClick:i})=>{var u;const[c,a]=b.useState(r||e[0].id),p=n!==void 0?n:c,f=(d,m)=>{d.preventDefault(),d.stopPropagation(),i?i(m):a(m)};return o.jsxs(As,{className:t,children:[o.jsx(zs,{children:e.map(d=>o.jsx(Fs,{active:p===d.id,onClick:m=>f(m,d.id),type:"button",form:"",tabIndex:0,"data-tab-id":d.id,children:d.label},d.id))}),o.jsx(Bs,{children:(u=e.find(d=>d.id===p))==null?void 0:u.content})]})},Hs=qs,ut={required:(e="This field is required")=>({validate:r=>typeof r=="string"?r.trim().length>0:typeof r=="number"?!isNaN(r):Array.isArray(r)?r.length>0:r!=null&&r!==void 0,message:e}),email:(e="Please enter a valid email address")=>({validate:r=>/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(r),message:e}),minLength:(e,r)=>({validate:t=>t.length>=e,message:r||`Must be at least ${e} characters`}),maxLength:(e,r)=>({validate:t=>t.length<=e,message:r||`Must be no more than ${e} characters`}),min:(e,r)=>({validate:t=>t>=e,message:r||`Must be at least ${e}`}),max:(e,r)=>({validate:t=>t<=e,message:r||`Must be no more than ${e}`}),pattern:(e,r)=>({validate:t=>e.test(t),message:r})},pt=(e={})=>{const{initialValue:r="",required:t=!1,type:n="text",validationRules:i=[],validateOnChange:c=!1,validateOnBlur:a=!0,transform:p}=e,f=b.useMemo(()=>{const _=[...i];return t&&!i.some(k=>k.message.toLowerCase().includes("required"))&&_.unshift(ut.required()),_},[t,i]),[u,d]=b.useState(r),[m,h]=b.useState(null),[y,g]=b.useState(!1),[x,C]=b.useState(!1),S=b.useMemo(()=>u!==r,[u,r]),w=b.useMemo(()=>m===null&&!x,[m,x]),L=b.useMemo(()=>m===null&&!x,[m,x]),A=b.useCallback(async()=>{C(!0);try{for(const _ of f)if(!_.validate(u))return h(_.message),C(!1),!1;return h(null),C(!1),!0}catch{return h("Validation error occurred"),C(!1),!1}},[u,f]),O=b.useCallback(()=>{d(r),h(null),g(!1),C(!1)},[r]),R=b.useCallback(_=>{let k;n==="number"?k=parseFloat(_.target.value)||0:k=_.target.value,p&&(k=p(k)),d(k),c&&y&&setTimeout(()=>A(),0)},[n,p,c,y,A]),j=b.useCallback(_=>{g(!0),a&&A()},[a,A]);return{value:u,error:m,touched:y,dirty:S,valid:w,isValid:L,validating:x,setValue:d,setError:h,setTouched:g,validate:A,reset:O,handleChange:R,handleBlur:j}},Us=s.div.withConfig({displayName:"FieldContainer",componentId:"sc-oh07s1-0"})(["display:flex;flex-direction:column;gap:",";width:100%;margin-bottom:",";"],({theme:e})=>{var r;return((r=e.spacing)==null?void 0:r.xs)||"4px"},({theme:e})=>{var r;return((r=e.spacing)==null?void 0:r.md)||"12px"}),Vs=s.label.withConfig({displayName:"Label",componentId:"sc-oh07s1-1"})(["font-size:",";font-weight:",";color:",";",""],({theme:e})=>{var r;return((r=e.fontSizes)==null?void 0:r.sm)||"14px"},({theme:e})=>{var r;return((r=e.fontWeights)==null?void 0:r.medium)||"500"},({theme:e})=>{var r;return((r=e.colors)==null?void 0:r.textPrimary)||"#ffffff"},({$required:e})=>e&&s.css(["&::after{content:' *';color:",";}"],({theme:r})=>{var t;return((t=r.colors)==null?void 0:t.error)||"#dc2626"})),ir=s.css(["width:100%;border:1px solid ",";border-radius:",";background-color:",";color:",";font-size:",";padding:",";transition:",";&:focus{outline:none;border-color:",";box-shadow:0 0 0 2px ",";}&:disabled{background-color:",";color:",";cursor:not-allowed;}&::placeholder{color:",";}"],({theme:e,$hasError:r})=>{var t,n;return r?((t=e.colors)==null?void 0:t.error)||"#dc2626":((n=e.colors)==null?void 0:n.border)||"#4b5563"},({theme:e})=>{var r;return((r=e.borderRadius)==null?void 0:r.sm)||"4px"},({theme:e})=>{var r;return((r=e.colors)==null?void 0:r.surface)||"#1f2937"},({theme:e})=>{var r;return((r=e.colors)==null?void 0:r.textPrimary)||"#ffffff"},({theme:e,$size:r})=>{var t,n,i;switch(r){case"sm":return((t=e.fontSizes)==null?void 0:t.sm)||"14px";case"lg":return((n=e.fontSizes)==null?void 0:n.lg)||"18px";default:return((i=e.fontSizes)==null?void 0:i.md)||"16px"}},({theme:e,$size:r})=>{var t,n,i,c,a,p;switch(r){case"sm":return`${((t=e.spacing)==null?void 0:t.xs)||"4px"} ${((n=e.spacing)==null?void 0:n.sm)||"8px"}`;case"lg":return`${((i=e.spacing)==null?void 0:i.md)||"12px"} ${((c=e.spacing)==null?void 0:c.lg)||"16px"}`;default:return`${((a=e.spacing)==null?void 0:a.sm)||"8px"} ${((p=e.spacing)==null?void 0:p.md)||"12px"}`}},({theme:e})=>{var r;return((r=e.transitions)==null?void 0:r.fast)||"all 0.2s ease"},({theme:e})=>{var r;return((r=e.colors)==null?void 0:r.primary)||"#dc2626"},({theme:e})=>{var r;return(r=e.colors)!=null&&r.primary?`${e.colors.primary}20`:"rgba(220, 38, 38, 0.2)"},({theme:e})=>{var r;return((r=e.colors)==null?void 0:r.chartGrid)||"#374151"},({theme:e})=>{var r;return((r=e.colors)==null?void 0:r.textSecondary)||"#9ca3af"},({theme:e})=>{var r;return((r=e.colors)==null?void 0:r.textSecondary)||"#9ca3af"}),Ys=s.input.withConfig({displayName:"StyledInput",componentId:"sc-oh07s1-2"})(["",""],ir),Ws=s.textarea.withConfig({displayName:"StyledTextarea",componentId:"sc-oh07s1-3"})([""," resize:vertical;min-height:80px;"],ir),Gs=s.select.withConfig({displayName:"StyledSelect",componentId:"sc-oh07s1-4"})([""," cursor:pointer;"],ir),Ks=s.div.withConfig({displayName:"ErrorMessage",componentId:"sc-oh07s1-5"})(["font-size:",";color:",";"],({theme:e})=>{var r;return((r=e.fontSizes)==null?void 0:r.xs)||"12px"},({theme:e})=>{var r;return((r=e.colors)==null?void 0:r.error)||"#dc2626"}),Qs=s.div.withConfig({displayName:"HelpText",componentId:"sc-oh07s1-6"})(["font-size:",";color:",";"],({theme:e})=>{var r;return((r=e.fontSizes)==null?void 0:r.xs)||"12px"},({theme:e})=>{var r;return((r=e.colors)==null?void 0:r.textSecondary)||"#9ca3af"}),Xs=e=>{const{name:r,label:t,placeholder:n,disabled:i=!1,className:c,size:a="md",helpText:p,inputType:f="input",options:u=[],rows:d=4,onChange:m,onBlur:h,...y}=e,g=pt({...y,validateOnBlur:!0});b.useEffect(()=>{m&&m(g.value)},[g.value,m]);const x=w=>{g.handleBlur(w),h&&h()},C={id:r,name:r,value:g.value,onChange:g.handleChange,onBlur:x,disabled:i,placeholder:n,$hasError:!!g.error,$disabled:i,$size:a},S=()=>{switch(f){case"textarea":return o.jsx(Ws,{...C,rows:d});case"select":return o.jsxs(Gs,{...C,children:[n&&o.jsx("option",{value:"",disabled:!0,children:n}),u.map(w=>o.jsx("option",{value:w.value,children:w.label},w.value))]});default:return o.jsx(Ys,{...C,type:y.type||"text"})}};return o.jsxs(Us,{className:c,children:[t&&o.jsx(Vs,{htmlFor:r,$required:!!y.required,children:t}),S(),g.error&&g.touched&&o.jsx(Ks,{role:"alert",children:g.error}),p&&!g.error&&o.jsx(Qs,{children:p})]})},Js=Xs,Zs={string:e=>(r,t)=>{const n=String(r[e]||""),i=String(t[e]||"");return n.localeCompare(i)},number:e=>(r,t)=>{const n=Number(r[e])||0,i=Number(t[e])||0;return n-i},date:e=>(r,t)=>{const n=new Date(r[e]).getTime(),i=new Date(t[e]).getTime();return n-i},boolean:e=>(r,t)=>{const n=!!r[e],i=!!t[e];return Number(n)-Number(i)}},ft=({data:e,columns:r,defaultSort:t})=>{const[n,i]=b.useState(t?{field:t.field,direction:t.direction}:null),c=b.useCallback(d=>{const m=r.find(h=>h.field===d);m!=null&&m.sortable&&i(h=>{var y;if((h==null?void 0:h.field)===d)return{field:d,direction:h.direction==="asc"?"desc":"asc"};{const g=typeof((y=e[0])==null?void 0:y[d])=="number"?"desc":"asc";return{field:d,direction:g}}})},[r,e]),a=b.useMemo(()=>{if(!n)return e;const d=r.find(h=>h.field===n.field);return d?[...e].sort((h,y)=>{let g=0;if(d.sortFn)g=d.sortFn(h,y);else{const x=h[n.field],C=y[n.field];typeof x=="string"&&typeof C=="string"?g=x.localeCompare(C):typeof x=="number"&&typeof C=="number"?g=x-C:g=String(x).localeCompare(String(C))}return n.direction==="asc"?g:-g}):e},[e,n,r]),p=b.useCallback(d=>!n||n.field!==d?null:n.direction==="asc"?"↑":"↓",[n]),f=b.useCallback(d=>(n==null?void 0:n.field)===d,[n]),u=b.useCallback(d=>(n==null?void 0:n.field)===d?n.direction:null,[n]);return{sortedData:a,sortConfig:n,handleSort:c,getSortIcon:p,isSorted:f,getSortDirection:u}},Ar=s.div.withConfig({displayName:"Container",componentId:"sc-13j9udn-0"})(["overflow-x:auto;border-radius:",";border:1px solid ",";"],({theme:e})=>{var r;return((r=e.borderRadius)==null?void 0:r.md)||"8px"},({theme:e})=>{var r;return((r=e.colors)==null?void 0:r.border)||"#4b5563"}),en=s.table.withConfig({displayName:"Table",componentId:"sc-13j9udn-1"})(["width:100%;border-collapse:collapse;font-size:",";"],({theme:e,$size:r})=>{var t,n,i;switch(r){case"sm":return((t=e.fontSizes)==null?void 0:t.xs)||"12px";case"lg":return((n=e.fontSizes)==null?void 0:n.md)||"16px";default:return((i=e.fontSizes)==null?void 0:i.sm)||"14px"}}),rn=s.thead.withConfig({displayName:"TableHead",componentId:"sc-13j9udn-2"})(["background-color:",";border-bottom:2px solid ",";"],({theme:e})=>{var r;return((r=e.colors)==null?void 0:r.surface)||"#1f2937"},({theme:e})=>{var r;return((r=e.colors)==null?void 0:r.border)||"#4b5563"}),tn=s.tbody.withConfig({displayName:"TableBody",componentId:"sc-13j9udn-3"})([""]),zr=s.tr.withConfig({displayName:"TableRow",componentId:"sc-13j9udn-4"})([""," "," "," border-bottom:1px solid ",";"],({$striped:e,theme:r})=>{var t;return e&&s.css(["&:nth-child(even){background-color:",";}"],((t=r.colors)==null?void 0:t.background)||"#0f0f0f")},({$hoverable:e,theme:r})=>{var t;return e&&s.css(["&:hover{background-color:",";}"],((t=r.colors)==null?void 0:t.surface)||"#1f2937")},({$clickable:e})=>e&&s.css(["cursor:pointer;"]),({theme:e})=>{var r;return((r=e.colors)==null?void 0:r.border)||"#4b5563"}),on=s.th.withConfig({displayName:"TableHeaderCell",componentId:"sc-13j9udn-5"})(["text-align:left;font-weight:",";color:",";cursor:",";user-select:none;transition:",";padding:",";&:hover{","}&:focus{outline:2px solid ",";outline-offset:-2px;}"],({theme:e})=>{var r;return((r=e.fontWeights)==null?void 0:r.semibold)||"600"},({theme:e,$active:r})=>{var t,n;return r?((t=e.colors)==null?void 0:t.primary)||"#dc2626":((n=e.colors)==null?void 0:n.textPrimary)||"#ffffff"},({$sortable:e})=>e?"pointer":"default",({theme:e})=>{var r;return((r=e.transitions)==null?void 0:r.fast)||"all 0.2s ease"},({theme:e,$size:r})=>{var t,n,i,c,a,p;switch(r){case"sm":return`${((t=e.spacing)==null?void 0:t.xs)||"4px"} ${((n=e.spacing)==null?void 0:n.sm)||"8px"}`;case"lg":return`${((i=e.spacing)==null?void 0:i.md)||"12px"} ${((c=e.spacing)==null?void 0:c.lg)||"16px"}`;default:return`${((a=e.spacing)==null?void 0:a.sm)||"8px"} ${((p=e.spacing)==null?void 0:p.md)||"12px"}`}},({$sortable:e,theme:r})=>{var t;return e&&s.css(["color:",";"],((t=r.colors)==null?void 0:t.primary)||"#dc2626")},({theme:e})=>{var r;return((r=e.colors)==null?void 0:r.primary)||"#dc2626"}),sn=s.td.withConfig({displayName:"TableCell",componentId:"sc-13j9udn-6"})(["padding:",";color:",";"],({theme:e,$size:r})=>{var t,n,i,c,a,p;switch(r){case"sm":return`${((t=e.spacing)==null?void 0:t.xs)||"4px"} ${((n=e.spacing)==null?void 0:n.sm)||"8px"}`;case"lg":return`${((i=e.spacing)==null?void 0:i.md)||"12px"} ${((c=e.spacing)==null?void 0:c.lg)||"16px"}`;default:return`${((a=e.spacing)==null?void 0:a.sm)||"8px"} ${((p=e.spacing)==null?void 0:p.md)||"12px"}`}},({theme:e})=>{var r;return((r=e.colors)==null?void 0:r.textPrimary)||"#ffffff"}),nn=s.span.withConfig({displayName:"SortIcon",componentId:"sc-13j9udn-7"})(["display:inline-block;margin-left:",";font-size:",";&::after{content:'","';}"],({theme:e})=>{var r;return((r=e.spacing)==null?void 0:r.xs)||"4px"},({theme:e})=>{var r;return((r=e.fontSizes)==null?void 0:r.sm)||"14px"},({$direction:e})=>e==="asc"?"↑":"↓"),an=s.div.withConfig({displayName:"EmptyState",componentId:"sc-13j9udn-8"})(["padding:",";text-align:center;color:",";font-style:italic;"],({theme:e,$size:r})=>{var t,n,i;switch(r){case"sm":return((t=e.spacing)==null?void 0:t.md)||"12px";case"lg":return((n=e.spacing)==null?void 0:n.xl)||"24px";default:return((i=e.spacing)==null?void 0:i.lg)||"16px"}},({theme:e})=>{var r;return((r=e.colors)==null?void 0:r.textSecondary)||"#9ca3af"}),cn=({data:e,columns:r,className:t,emptyMessage:n="No data available",defaultSort:i,renderCell:c,onRowClick:a,size:p="md",striped:f=!0,hoverable:u=!0})=>{const{sortedData:d,handleSort:m,getSortIcon:h,isSorted:y}=ft({data:e,columns:r,defaultSort:i});return e.length===0?o.jsx(Ar,{className:t,children:o.jsx(an,{$size:p,children:n})}):o.jsx(Ar,{className:t,children:o.jsxs(en,{$size:p,$striped:f,$hoverable:u,children:[o.jsx(rn,{children:o.jsx(zr,{$striped:!1,$hoverable:!1,$clickable:!1,children:r.map(g=>o.jsxs(on,{$sortable:g.sortable||!1,$active:y(g.field),$size:p,onClick:()=>g.sortable&&m(g.field),tabIndex:g.sortable?0:-1,onKeyDown:x=>{g.sortable&&(x.key==="Enter"||x.key===" ")&&(x.preventDefault(),m(g.field))},role:g.sortable?"button":void 0,"aria-sort":y(g.field)?h(g.field)==="↑"?"ascending":"descending":void 0,children:[g.label,y(g.field)&&o.jsx(nn,{$direction:h(g.field)==="↑"?"asc":"desc"})]},String(g.field)))})}),o.jsx(tn,{children:d.map((g,x)=>o.jsx(zr,{$striped:f,$hoverable:u,$clickable:!!a,onClick:()=>a==null?void 0:a(g,x),tabIndex:a?0:-1,onKeyDown:C=>{a&&(C.key==="Enter"||C.key===" ")&&(C.preventDefault(),a(g,x))},role:a?"button":void 0,children:r.map(C=>{const S=g[C.field];return o.jsx(sn,{$size:p,children:c?c(S,g,C):String(S)},String(C.field))})},x))})]})})},ln=cn,dn=s.div.withConfig({displayName:"FieldContainer",componentId:"sc-i922jg-0"})(["display:flex;flex-direction:column;margin-bottom:",";"],({theme:e})=>e.spacing.md),un=s.label.withConfig({displayName:"Label",componentId:"sc-i922jg-1"})(["font-size:",";font-weight:500;margin-bottom:",";color:",";.required-indicator{color:",";margin-left:",";}"],({theme:e})=>e.fontSizes.sm,({theme:e})=>e.spacing.xxs,({theme:e,hasError:r})=>r?e.colors.error:e.colors.textPrimary,({theme:e})=>e.colors.error,({theme:e})=>e.spacing.xxs),pn=s.div.withConfig({displayName:"HelperText",componentId:"sc-i922jg-2"})(["font-size:",";color:",";margin-top:",";"],({theme:e})=>e.fontSizes.xs,({theme:e,hasError:r})=>r?e.colors.error:e.colors.textSecondary,({theme:e})=>e.spacing.xxs),fn=({children:e,label:r,helperText:t,required:n=!1,error:i,className:c,id:a,...p})=>{const f=a||`field-${Math.random().toString(36).substr(2,9)}`,u=b.Children.map(e,d=>b.isValidElement(d)?b.cloneElement(d,{id:f,required:n,error:i}):d);return o.jsxs(dn,{className:c,...p,children:[o.jsxs(un,{htmlFor:f,hasError:!!i,children:[r,n&&o.jsx("span",{className:"required-indicator",children:"*"})]}),u,(t||i)&&o.jsx(pn,{hasError:!!i,children:i||t})]})},gn=s.keyframes(["from{opacity:0;}to{opacity:1;}"]),mn=s.keyframes(["from{transform:translateY(-20px);opacity:0;}to{transform:translateY(0);opacity:1;}"]),hn=s.div.withConfig({displayName:"Backdrop",componentId:"sc-1cuqxtr-0"})(["position:fixed;top:0;left:0;right:0;bottom:0;background-color:rgba(0,0,0,0.5);display:flex;align-items:center;justify-content:center;z-index:",";animation:"," 0.2s ease-out;"],({zIndex:e})=>e||1e3,gn),xn=s.div.withConfig({displayName:"ModalContainer",componentId:"sc-1cuqxtr-1"})(["background-color:",";border-radius:",";box-shadow:",";display:flex;flex-direction:column;max-height:",";width:",";max-width:95vw;animation:"," 0.2s ease-out;position:relative;"," ",""],({theme:e})=>e.colors.surface,({theme:e})=>e.borderRadius.md,({theme:e})=>e.shadows.lg,({size:e})=>e==="fullscreen"?"100vh":"90vh",({size:e})=>{switch(e){case"small":return"400px";case"medium":return"600px";case"large":return"800px";case"fullscreen":return"100vw";default:return"600px"}},mn,({size:e})=>e==="fullscreen"&&s.css(["height:100vh;border-radius:0;"]),({centered:e})=>e&&s.css(["margin:auto;"])),bn=s.div.withConfig({displayName:"ModalHeader",componentId:"sc-1cuqxtr-2"})(["display:flex;justify-content:space-between;align-items:center;padding:",";border-bottom:1px solid ",";"],({theme:e})=>`${e.spacing.md} ${e.spacing.lg}`,({theme:e})=>e.colors.border),yn=s.h3.withConfig({displayName:"ModalTitle",componentId:"sc-1cuqxtr-3"})(["margin:0;font-size:",";font-weight:",";color:",";"],({theme:e})=>e.fontSizes.lg,({theme:e})=>e.fontWeights.semibold,({theme:e})=>e.colors.textPrimary),vn=s.button.withConfig({displayName:"CloseButton",componentId:"sc-1cuqxtr-4"})(["background:none;border:none;cursor:pointer;font-size:",";color:",";padding:0;display:flex;align-items:center;justify-content:center;width:32px;height:32px;border-radius:",";&:hover{background-color:",";}&:focus{outline:none;box-shadow:0 0 0 2px ","33;}"],({theme:e})=>e.fontSizes.xl,({theme:e})=>e.colors.textSecondary,({theme:e})=>e.borderRadius.sm,({theme:e})=>e.colors.background,({theme:e})=>e.colors.primary),Sn=s.div.withConfig({displayName:"ModalContent",componentId:"sc-1cuqxtr-5"})(["padding:",";",""],({theme:e})=>e.spacing.lg,({scrollable:e})=>e&&s.css(["overflow-y:auto;flex:1;"])),wn=s.div.withConfig({displayName:"ModalFooter",componentId:"sc-1cuqxtr-6"})(["display:flex;justify-content:flex-end;gap:",";padding:",";border-top:1px solid ",";"],({theme:e})=>e.spacing.md,({theme:e})=>`${e.spacing.md} ${e.spacing.lg}`,({theme:e})=>e.colors.border),Cn=({isOpen:e,title:r="",children:t,onClose:n,size:i="medium",closeOnOutsideClick:c=!0,showCloseButton:a=!0,footer:p,hasFooter:f=!0,primaryActionText:u="",onPrimaryAction:d,primaryActionDisabled:m=!1,primaryActionLoading:h=!1,secondaryActionText:y="",onSecondaryAction:g,secondaryActionDisabled:x=!1,className:C="",zIndex:S=1e3,centered:w=!0,scrollable:L=!0})=>{const A=b.useRef(null);b.useEffect(()=>{const _=k=>{k.key==="Escape"&&e&&c&&n()};return document.addEventListener("keydown",_),()=>{document.removeEventListener("keydown",_)}},[e,n,c]);const O=_=>{A.current&&!A.current.contains(_.target)&&c&&n()};b.useEffect(()=>(e?document.body.style.overflow="hidden":document.body.style.overflow="",()=>{document.body.style.overflow=""}),[e]);const R=o.jsxs(o.Fragment,{children:[y&&o.jsx(ie,{variant:"outline",onClick:g,disabled:x,children:y}),u&&o.jsx(ie,{onClick:d,disabled:m,loading:h,children:u})]});if(!e)return null;const j=o.jsx(hn,{onClick:O,zIndex:S,children:o.jsxs(xn,{ref:A,size:i,className:C,centered:w,scrollable:L,onClick:_=>_.stopPropagation(),children:[(r||a)&&o.jsxs(bn,{children:[r&&o.jsx(yn,{children:r}),a&&o.jsx(vn,{onClick:n,"aria-label":"Close",children:"×"})]}),o.jsx(Sn,{scrollable:L,children:t}),f&&(p||u||y)&&o.jsx(wn,{children:p||R})]})});return Zt.createPortal(j,document.body)},In=s.div.withConfig({displayName:"TableContainer",componentId:"sc-4as3uq-0"})(["width:100%;overflow:auto;"," ",""],({height:e})=>e&&`height: ${e};`,({scrollable:e})=>e&&"overflow-x: auto;"),En=s.table.withConfig({displayName:"StyledTable",componentId:"sc-4as3uq-1"})(["width:100%;border-collapse:separate;border-spacing:0;font-size:",";"," ",""],({theme:e})=>e.fontSizes.sm,({bordered:e,theme:r})=>e&&s.css(["border:1px solid ",";border-radius:",";"],r.colors.border,r.borderRadius.sm),({compact:e,theme:r})=>e?s.css(["th,td{padding:"," ",";}"],r.spacing.xs,r.spacing.sm):s.css(["th,td{padding:"," ",";}"],r.spacing.sm,r.spacing.md)),Tn=s.thead.withConfig({displayName:"TableHeader",componentId:"sc-4as3uq-2"})(["",""],({stickyHeader:e})=>e&&s.css(["position:sticky;top:0;z-index:1;"])),jn=s.tr.withConfig({displayName:"TableHeaderRow",componentId:"sc-4as3uq-3"})(["background-color:",";"],({theme:e})=>e.colors.background),kn=s.th.withConfig({displayName:"TableHeaderCell",componentId:"sc-4as3uq-4"})(["text-align:",";font-weight:",";color:",";border-bottom:1px solid ",";white-space:nowrap;"," "," ",""],({align:e})=>e||"left",({theme:e})=>e.fontWeights.semibold,({theme:e})=>e.colors.textSecondary,({theme:e})=>e.colors.border,({width:e})=>e&&`width: ${e};`,({sortable:e})=>e&&s.css(["cursor:pointer;user-select:none;&:hover{background-color:","aa;}"],({theme:r})=>r.colors.background),({isSorted:e,theme:r})=>e&&s.css(["color:",";"],r.colors.primary)),Nn=s.span.withConfig({displayName:"SortIcon",componentId:"sc-4as3uq-5"})(["display:inline-block;margin-left:",";&::after{content:'","';}"],({theme:e})=>e.spacing.xs,({direction:e})=>e==="asc"?"↑":e==="desc"?"↓":"↕"),Rn=s.tbody.withConfig({displayName:"TableBody",componentId:"sc-4as3uq-6"})([""]),_n=s.tr.withConfig({displayName:"TableRow",componentId:"sc-4as3uq-7"})([""," "," "," ",""],({striped:e,theme:r,isSelected:t})=>e&&!t&&s.css(["&:nth-child(even){background-color:","50;}"],r.colors.background),({hoverable:e,theme:r,isSelected:t})=>e&&!t&&s.css(["&:hover{background-color:","aa;}"],r.colors.background),({isSelected:e,theme:r})=>e&&s.css(["background-color:","15;"],r.colors.primary),({isClickable:e})=>e&&s.css(["cursor:pointer;"])),Ln=s.td.withConfig({displayName:"TableCell",componentId:"sc-4as3uq-8"})(["text-align:",";border-bottom:1px solid ",";color:",";"],({align:e})=>e||"left",({theme:e})=>e.colors.border,({theme:e})=>e.colors.textPrimary),Mn=s.div.withConfig({displayName:"EmptyState",componentId:"sc-4as3uq-9"})(["padding:",";text-align:center;color:",";"],({theme:e})=>e.spacing.xl,({theme:e})=>e.colors.textSecondary),Pn=s.div.withConfig({displayName:"PaginationContainer",componentId:"sc-4as3uq-10"})(["display:flex;justify-content:space-between;align-items:center;padding:"," 0;font-size:",";"],({theme:e})=>e.spacing.md,({theme:e})=>e.fontSizes.sm),Dn=s.div.withConfig({displayName:"PageInfo",componentId:"sc-4as3uq-11"})(["color:",";"],({theme:e})=>e.colors.textSecondary),On=s.div.withConfig({displayName:"PaginationControls",componentId:"sc-4as3uq-12"})(["display:flex;gap:",";"],({theme:e})=>e.spacing.xs),$n=s.div.withConfig({displayName:"PageSizeSelector",componentId:"sc-4as3uq-13"})(["display:flex;align-items:center;gap:",";margin-right:",";"],({theme:e})=>e.spacing.sm,({theme:e})=>e.spacing.md),An=s.div.withConfig({displayName:"LoadingOverlay",componentId:"sc-4as3uq-14"})(["position:absolute;top:0;left:0;right:0;bottom:0;background-color:",";display:flex;align-items:center;justify-content:center;z-index:1;"],({theme:e})=>`${e.colors.background}80`),zn=s.div.withConfig({displayName:"LoadingSpinner",componentId:"sc-4as3uq-15"})(["width:32px;height:32px;border:3px solid ",";border-top:3px solid ",";border-radius:50%;animation:spin 1s linear infinite;@keyframes spin{0%{transform:rotate(0deg);}100%{transform:rotate(360deg);}}"],({theme:e})=>e.colors.background,({theme:e})=>e.colors.primary);function Fn({columns:e,data:r,isLoading:t=!1,bordered:n=!0,striped:i=!0,hoverable:c=!0,compact:a=!1,stickyHeader:p=!1,height:f,onRowClick:u,isRowSelected:d,onSort:m,sortColumn:h,sortDirection:y,pagination:g=!1,currentPage:x=1,pageSize:C=10,totalRows:S=0,onPageChange:w,onPageSizeChange:L,className:A,emptyMessage:O="No data available",scrollable:R=!0}){const j=b.useMemo(()=>e.filter(N=>!N.hidden),[e]),_=b.useMemo(()=>Math.ceil(S/C),[S,C]),k=b.useMemo(()=>{if(!g)return r;const N=(x-1)*C,H=N+C;return S>0&&r.length<=C?r:r.slice(N,H)},[r,g,x,C,S]),U=N=>{if(!m)return;m(N,h===N&&y==="asc"?"desc":"asc")},V=N=>{N<1||N>_||!w||w(N)};return o.jsxs("div",{style:{position:"relative"},children:[t&&o.jsx(An,{children:o.jsx(zn,{})}),o.jsx(In,{height:f,scrollable:R,children:o.jsxs(En,{bordered:n,striped:i,compact:a,className:A,children:[o.jsx(Tn,{stickyHeader:p,children:o.jsx(jn,{children:j.map(N=>o.jsxs(kn,{sortable:N.sortable,isSorted:h===N.id,align:N.align,width:N.width,onClick:()=>N.sortable&&U(N.id),children:[N.header,N.sortable&&o.jsx(Nn,{direction:h===N.id?y:void 0})]},N.id))})}),o.jsx(Rn,{children:k.length>0?k.map((N,H)=>o.jsx(_n,{hoverable:c,striped:i,isSelected:d?d(N,H):!1,isClickable:!!u,onClick:()=>u&&u(N,H),children:j.map(ee=>o.jsx(Ln,{align:ee.align,children:ee.cell(N,H)},ee.id))},H)):o.jsx("tr",{children:o.jsx("td",{colSpan:j.length,children:o.jsx(Mn,{children:O})})})})]})}),g&&_>0&&o.jsxs(Pn,{children:[o.jsxs(Dn,{children:["Showing ",Math.min((x-1)*C+1,S)," to"," ",Math.min(x*C,S)," of ",S," entries"]}),o.jsxs("div",{style:{display:"flex",alignItems:"center"},children:[L&&o.jsxs($n,{children:[o.jsx("span",{children:"Show"}),o.jsx("select",{value:C,onChange:N=>L(Number(N.target.value)),style:{padding:"4px 8px",borderRadius:"4px",border:"1px solid #ccc"},children:[10,25,50,100].map(N=>o.jsx("option",{value:N,children:N},N))}),o.jsx("span",{children:"entries"})]}),o.jsxs(On,{children:[o.jsx(ie,{size:"small",variant:"outline",onClick:()=>V(1),disabled:x===1,children:"First"}),o.jsx(ie,{size:"small",variant:"outline",onClick:()=>V(x-1),disabled:x===1,children:"Prev"}),o.jsx(ie,{size:"small",variant:"outline",onClick:()=>V(x+1),disabled:x===_,children:"Next"}),o.jsx(ie,{size:"small",variant:"outline",onClick:()=>V(_),disabled:x===_,children:"Last"})]})]})]})]})}const ce={[M.MORNING_BREAKOUT]:{type:M.MORNING_BREAKOUT,name:"9:50-10:10 Macro",timeRange:{start:"09:50:00",end:"10:10:00"},description:"Morning breakout period - high volatility after market open",characteristics:["High Volume","Breakout Setups","Gap Fills","Opening Range"],volatilityLevel:5,volumeLevel:5,isHighProbability:!0},[M.MID_MORNING_REVERSION]:{type:M.MID_MORNING_REVERSION,name:"10:50-11:10 Macro",timeRange:{start:"10:50:00",end:"11:10:00"},description:"Mid-morning reversion period - mean reversion opportunities",characteristics:["Mean Reversion","Pullback Setups","Support/Resistance Tests"],volatilityLevel:3,volumeLevel:3,isHighProbability:!0},[M.PRE_LUNCH]:{type:M.PRE_LUNCH,name:"11:50-12:10 Macro",timeRange:{start:"11:50:00",end:"12:10:00"},description:"Pre-lunch macro window - specific high-activity period within lunch session",characteristics:["Consolidation","Range Trading","Pre-Lunch Activity"],volatilityLevel:2,volumeLevel:2,isHighProbability:!1,parentMacro:M.LUNCH_MACRO_EXTENDED},[M.LUNCH_MACRO_EXTENDED]:{type:M.LUNCH_MACRO_EXTENDED,name:"Lunch Macro (11:30-13:30)",timeRange:{start:"11:30:00",end:"13:30:00"},description:"Extended lunch period spanning late morning through early afternoon",characteristics:["Multi-Session","Lunch Trading","Lower Volume","Transition Period"],volatilityLevel:2,volumeLevel:2,isHighProbability:!1,isMultiSession:!0,spansSessions:[G.NEW_YORK_AM,G.NEW_YORK_PM],subPeriods:[]},[M.LUNCH_MACRO]:{type:M.LUNCH_MACRO,name:"Lunch Macro (12:00-13:30)",timeRange:{start:"12:00:00",end:"13:30:00"},description:"Traditional lunch time trading - typically lower volume",characteristics:["Low Volume","Range Bound","Choppy Price Action"],volatilityLevel:2,volumeLevel:1,isHighProbability:!1},[M.POST_LUNCH]:{type:M.POST_LUNCH,name:"13:50-14:10 Macro",timeRange:{start:"13:50:00",end:"14:10:00"},description:"Post-lunch macro window",characteristics:["Volume Pickup","Trend Resumption"],volatilityLevel:3,volumeLevel:3,isHighProbability:!1},[M.PRE_CLOSE]:{type:M.PRE_CLOSE,name:"14:50-15:10 Macro",timeRange:{start:"14:50:00",end:"15:10:00"},description:"Pre-close macro window",characteristics:["Institutional Activity","Position Adjustments"],volatilityLevel:3,volumeLevel:4,isHighProbability:!1},[M.POWER_HOUR]:{type:M.POWER_HOUR,name:"15:15-15:45 Macro (Power Hour)",timeRange:{start:"15:15:00",end:"15:45:00"},description:"Last hour macro - high activity before close",characteristics:["High Volume","Institutional Flows","EOD Positioning"],volatilityLevel:4,volumeLevel:5,isHighProbability:!0},[M.MOC]:{type:M.MOC,name:"MOC (Market on Close)",timeRange:{start:"15:45:00",end:"16:00:00"},description:"Market on close period",characteristics:["MOC Orders","Final Positioning","High Volume"],volatilityLevel:4,volumeLevel:5,isHighProbability:!1},[M.LONDON_OPEN]:{type:M.LONDON_OPEN,name:"London Open",timeRange:{start:"08:00:00",end:"09:00:00"},description:"London market opening hour",characteristics:["European Activity","Currency Moves","News Reactions"],volatilityLevel:4,volumeLevel:4,isHighProbability:!0},[M.LONDON_NY_OVERLAP]:{type:M.LONDON_NY_OVERLAP,name:"London/NY Overlap",timeRange:{start:"14:00:00",end:"16:00:00"},description:"London and New York session overlap",characteristics:["Highest Volume","Major Moves","Cross-Market Activity"],volatilityLevel:5,volumeLevel:5,isHighProbability:!0},[M.CUSTOM]:{type:M.CUSTOM,name:"Custom Period",timeRange:{start:"00:00:00",end:"23:59:59"},description:"User-defined custom time period",characteristics:["Custom"],volatilityLevel:3,volumeLevel:3,isHighProbability:!1}},Bn=()=>{const e=Object.values(qn).map(i=>({id:i.type,...i})),t=[{...ce[M.LUNCH_MACRO_EXTENDED],id:"lunch-macro-extended",subPeriods:[{...ce[M.PRE_LUNCH],id:"pre-lunch-sub"}]}],n={};return e.forEach(i=>{i.macroPeriods.forEach(c=>{n[c.type]={...c,parentSession:i.type}})}),t.forEach(i=>{n[i.type]={...i,spansSessions:i.spansSessions}}),{sessions:e,sessionsByType:e.reduce((i,c)=>(i[c.type]=c,i),{}),macrosByType:n,multiSessionMacros:t}},qn={[G.NEW_YORK_AM]:{type:G.NEW_YORK_AM,name:"New York AM Session",timeRange:{start:"09:30:00",end:"12:00:00"},description:"New York morning session - high activity and volatility",timezone:"America/New_York",characteristics:["High Volume","Trend Development","Breakout Opportunities"],color:"#dc2626",macroPeriods:[{...ce[M.MORNING_BREAKOUT],id:"morning-breakout"},{...ce[M.MID_MORNING_REVERSION],id:"mid-morning-reversion"},{...ce[M.PRE_LUNCH],id:"pre-lunch"}]},[G.NEW_YORK_PM]:{type:G.NEW_YORK_PM,name:"New York PM Session",timeRange:{start:"12:00:00",end:"16:00:00"},description:"New York afternoon session - institutional activity increases toward close",timezone:"America/New_York",characteristics:["Institutional Flows","EOD Positioning","Power Hour Activity"],color:"#dc2626",macroPeriods:[{...ce[M.LUNCH_MACRO],id:"lunch-macro"},{...ce[M.POST_LUNCH],id:"post-lunch"},{...ce[M.PRE_CLOSE],id:"pre-close"},{...ce[M.POWER_HOUR],id:"power-hour"},{...ce[M.MOC],id:"moc"}]},[G.LONDON]:{type:G.LONDON,name:"London Session",timeRange:{start:"08:00:00",end:"16:00:00"},description:"London trading session - European market activity",timezone:"Europe/London",characteristics:["European Activity","Currency Focus","News-Driven"],color:"#1f2937",macroPeriods:[{...ce[M.LONDON_OPEN],id:"london-open"},{...ce[M.LONDON_NY_OVERLAP],id:"london-ny-overlap"}]},[G.ASIA]:{type:G.ASIA,name:"Asia Session",timeRange:{start:"18:00:00",end:"03:00:00"},description:"Asian trading session - typically lower volatility",timezone:"Asia/Tokyo",characteristics:["Lower Volume","Range Trading","News Reactions"],color:"#4b5563",macroPeriods:[]},[G.PRE_MARKET]:{type:G.PRE_MARKET,name:"Pre-Market",timeRange:{start:"04:00:00",end:"09:30:00"},description:"Pre-market trading hours",timezone:"America/New_York",characteristics:["Low Volume","News Reactions","Gap Setups"],color:"#6b7280",macroPeriods:[]},[G.AFTER_HOURS]:{type:G.AFTER_HOURS,name:"After Hours",timeRange:{start:"16:00:00",end:"20:00:00"},description:"After-hours trading",timezone:"America/New_York",characteristics:["Low Volume","Earnings Reactions","News-Driven"],color:"#6b7280",macroPeriods:[]},[G.OVERNIGHT]:{type:G.OVERNIGHT,name:"Overnight",timeRange:{start:"20:00:00",end:"04:00:00"},description:"Overnight session",timezone:"America/New_York",characteristics:["Very Low Volume","Futures Activity"],color:"#374151",macroPeriods:[]}};class re{static getSessionHierarchy(){return this.hierarchy||(this.hierarchy=this.buildHierarchy()),this.hierarchy}static buildHierarchy(){return Bn()}static timeToMinutes(r){const[t,n,i=0]=r.split(":").map(Number);return t*60+n+i/60}static minutesToTime(r){const t=Math.floor(r/60),n=Math.floor(r%60),i=Math.floor(r%1*60);return`${t.toString().padStart(2,"0")}:${n.toString().padStart(2,"0")}:${i.toString().padStart(2,"0")}`}static isTimeInRange(r,t){const n=this.timeToMinutes(r),i=this.timeToMinutes(t.start),c=this.timeToMinutes(t.end);return c<i?n>=i||n<=c:n>=i&&n<=c}static validateTime(r){var c;if(!/^([0-1]?[0-9]|2[0-3]):[0-5][0-9](:[0-5][0-9])?$/.test(r))return{isValid:!1,error:"Invalid time format. Use HH:MM or HH:MM:SS format."};const n=this.getSessionHierarchy(),i=[];for(const[a,p]of Object.entries(n.macrosByType))this.isTimeInRange(r,p.timeRange)&&i.push({type:a,macro:p,isSubPeriod:!!p.parentMacro});if(i.length>0){const p=i.sort((u,d)=>{if(u.isSubPeriod&&!d.isSubPeriod)return-1;if(!u.isSubPeriod&&d.isSubPeriod)return 1;const m=this.timeToMinutes(u.macro.timeRange.end)-this.timeToMinutes(u.macro.timeRange.start),h=this.timeToMinutes(d.macro.timeRange.end)-this.timeToMinutes(d.macro.timeRange.start);return m-h})[0],f=i.length>1;return{isValid:!0,suggestedMacro:p.type,suggestedSession:p.macro.parentSession||((c=p.macro.spansSessions)==null?void 0:c[0]),warning:f?`Time falls within ${i.length} overlapping macro periods. Suggesting most specific: ${p.macro.name}`:void 0}}for(const a of n.sessions)if(this.isTimeInRange(r,a.timeRange))return{isValid:!0,suggestedSession:a.type,warning:"Time falls within session but not in a specific macro period."};return{isValid:!0,warning:"Time does not fall within any defined session or macro period."}}static getSession(r){return this.getSessionHierarchy().sessionsByType[r]||null}static getMacroPeriod(r){return this.getSessionHierarchy().macrosByType[r]||null}static getMacroPeriodsForSession(r){const t=this.getSession(r);return(t==null?void 0:t.macroPeriods)||[]}static createSessionSelection(r,t,n){if(t){const i=this.getMacroPeriod(t);return{session:i==null?void 0:i.parentSession,macroPeriod:t,displayLabel:(i==null?void 0:i.name)||"Unknown Macro",selectionType:"macro"}}if(r){const i=this.getSession(r);return{session:r,displayLabel:(i==null?void 0:i.name)||"Unknown Session",selectionType:"session"}}return n?{customTimeRange:n,displayLabel:`${n.start} - ${n.end}`,selectionType:"custom"}:{displayLabel:"No Selection",selectionType:"custom"}}static filterSessions(r={}){var c,a;const t=this.getSessionHierarchy();let n=[...t.sessions],i=Object.values(t.macrosByType);return r.activeOnly&&(n=n.filter(p=>p.isActive)),(c=r.sessionTypes)!=null&&c.length&&(n=n.filter(p=>r.sessionTypes.includes(p.type))),(a=r.macroTypes)!=null&&a.length&&(i=i.filter(p=>r.macroTypes.includes(p.type))),r.highProbabilityOnly&&(i=i.filter(p=>p.isHighProbability)),r.minVolatility!==void 0&&(i=i.filter(p=>p.volatilityLevel>=r.minVolatility)),r.maxVolatility!==void 0&&(i=i.filter(p=>p.volatilityLevel<=r.maxVolatility)),{sessions:n,macros:i}}static getCurrentSession(){const r=new Date,t=`${r.getHours().toString().padStart(2,"0")}:${r.getMinutes().toString().padStart(2,"0")}:00`,n=this.validateTime(t);return n.suggestedMacro?this.createSessionSelection(n.suggestedSession,n.suggestedMacro):n.suggestedSession?this.createSessionSelection(n.suggestedSession):null}static timeRangesOverlap(r,t){const n=this.timeToMinutes(r.start),i=this.timeToMinutes(r.end),c=this.timeToMinutes(t.start),a=this.timeToMinutes(t.end);return Math.max(n,c)<Math.min(i,a)}static getDisplayOptions(){const r=this.getSessionHierarchy(),t=r.sessions.map(i=>({value:i.type,label:i.name,group:"Sessions"})),n=Object.values(r.macrosByType).map(i=>{var c;return{value:i.type,label:i.name,group:((c=r.sessionsByType[i.parentSession])==null?void 0:c.name)||"Other",parentSession:i.parentSession}});return{sessionOptions:t,macroOptions:n}}static getOverlappingMacros(r){const t=this.getSessionHierarchy(),n=[];for(const[i,c]of Object.entries(t.macrosByType))this.isTimeInRange(r,c.timeRange)&&n.push({type:i,macro:c,isSubPeriod:!!c.parentMacro,isMultiSession:!!c.spansSessions});return n.sort((i,c)=>{if(i.isSubPeriod&&!c.isSubPeriod)return-1;if(!i.isSubPeriod&&c.isSubPeriod)return 1;const a=this.timeToMinutes(i.macro.timeRange.end)-this.timeToMinutes(i.macro.timeRange.start),p=this.timeToMinutes(c.macro.timeRange.end)-this.timeToMinutes(c.macro.timeRange.start);return a-p})}static getMultiSessionMacros(){return this.getSessionHierarchy().multiSessionMacros||[]}static hasSubPeriods(r){const t=this.getMacroPeriod(r);return!!(t!=null&&t.subPeriods&&t.subPeriods.length>0)}static getSubPeriods(r){const t=this.getMacroPeriod(r);return(t==null?void 0:t.subPeriods)||[]}static convertLegacySession(r){const n={"NY Open":{session:G.NEW_YORK_AM},"London Open":{session:G.LONDON},"Lunch Macro":{macro:M.LUNCH_MACRO_EXTENDED},"Lunch Macro (11:30-13:30)":{macro:M.LUNCH_MACRO_EXTENDED},"Lunch Macro (12:00-13:30)":{macro:M.LUNCH_MACRO},MOC:{macro:M.MOC},Overnight:{session:G.OVERNIGHT},"Pre-Market":{session:G.PRE_MARKET},"After Hours":{session:G.AFTER_HOURS},"Power Hour":{macro:M.POWER_HOUR},"10:50-11:10":{macro:M.MID_MORNING_REVERSION},"11:50-12:10":{macro:M.PRE_LUNCH},"15:15-15:45":{macro:M.POWER_HOUR}}[r];return n?this.createSessionSelection(n.session,n.macro):null}}re.hierarchy=null;const gt=(e={})=>{const{initialSelection:r,autoDetectCurrent:t=!1,filterOptions:n={},onSelectionChange:i,validateTimes:c=!0}=e,[a,p]=b.useState(r||{displayLabel:"No Selection",selectionType:"custom"}),f=b.useMemo(()=>re.getCurrentSession(),[]),u=b.useMemo(()=>f!==null,[f]),{availableSessions:d,availableMacros:m}=b.useMemo(()=>{const{sessions:R,macros:j}=re.filterSessions(n),{sessionOptions:_,macroOptions:k}=re.getDisplayOptions(),U=_.filter(N=>R.some(H=>H.type===N.value)),V=k.filter(N=>j.some(H=>H.type===N.value));return{availableSessions:U,availableMacros:V}},[n]),h=b.useMemo(()=>{const R=re.getSessionHierarchy();return d.map(j=>{R.sessionsByType[j.value];const _=m.filter(k=>k.parentSession===j.value).map(k=>({value:k.value,label:k.label}));return{session:j.value,sessionLabel:j.label,macros:_}})},[d,m]);b.useEffect(()=>{t&&f&&!r&&p(f)},[t,f,r]),b.useEffect(()=>{i==null||i(a)},[a,i]);const y=b.useCallback(R=>{const j=re.createSessionSelection(R);p(j)},[]),g=b.useCallback(R=>{const j=re.createSessionSelection(void 0,R);p(j)},[]),x=b.useCallback(R=>{const j=re.createSessionSelection(void 0,void 0,R);p(j)},[]),C=b.useCallback(()=>{p({displayLabel:"No Selection",selectionType:"custom"})},[]),S=b.useCallback(R=>c?re.validateTime(R):{isValid:!0},[c]),w=b.useMemo(()=>{if(a.selectionType==="session"&&a.session)return re.getSession(a.session)!==null;if(a.selectionType==="macro"&&a.macroPeriod)return re.getMacroPeriod(a.macroPeriod)!==null;if(a.selectionType==="custom"&&a.customTimeRange){const R=S(a.customTimeRange.start),j=S(a.customTimeRange.end);return R.isValid&&j.isValid}return a.selectionType==="custom"&&!a.customTimeRange},[a,S]),L=b.useCallback(R=>re.getSession(R),[]),A=b.useCallback(R=>re.getMacroPeriod(R),[]),O=b.useCallback(R=>re.convertLegacySession(R),[]);return{selection:a,selectSession:y,selectMacro:g,selectCustomRange:x,clearSelection:C,validateTime:S,isValidSelection:w,availableSessions:d,availableMacros:m,hierarchicalOptions:h,currentSession:f,isCurrentSessionActive:u,getSessionDetails:L,getMacroDetails:A,convertLegacySession:O}},Hn=s.div.withConfig({displayName:"Container",componentId:"sc-1reqqnl-0"})(["display:flex;flex-direction:column;gap:",";"],({theme:e})=>{var r;return((r=e.spacing)==null?void 0:r.sm)||"8px"}),Un=s.div.withConfig({displayName:"SelectorContainer",componentId:"sc-1reqqnl-1"})(["position:relative;border:1px solid ",";border-radius:",";background:",";transition:all 0.2s ease;opacity:",";pointer-events:",";&:hover{border-color:","40;}&:focus-within{border-color:",";box-shadow:0 0 0 3px ","20;}"],({theme:e,hasError:r})=>{var t,n;return r?((t=e.colors)==null?void 0:t.error)||"#ef4444":((n=e.colors)==null?void 0:n.border)||"#4b5563"},({theme:e})=>{var r;return((r=e.borderRadius)==null?void 0:r.md)||"6px"},({theme:e})=>{var r;return((r=e.colors)==null?void 0:r.surface)||"#1f2937"},({disabled:e})=>e?.6:1,({disabled:e})=>e?"none":"auto",({theme:e,hasError:r})=>{var t,n;return r?((t=e.colors)==null?void 0:t.error)||"#ef4444":((n=e.colors)==null?void 0:n.primary)||"#dc2626"},({theme:e})=>{var r;return((r=e.colors)==null?void 0:r.primary)||"#dc2626"},({theme:e})=>{var r;return((r=e.colors)==null?void 0:r.primary)||"#dc2626"}),Vn=s.div.withConfig({displayName:"SelectedValue",componentId:"sc-1reqqnl-2"})(["padding:",";color:",";font-size:",";cursor:pointer;display:flex;align-items:center;justify-content:space-between;"],({theme:e})=>{var r;return((r=e.spacing)==null?void 0:r.md)||"12px"},({theme:e})=>{var r;return((r=e.colors)==null?void 0:r.textPrimary)||"#ffffff"},({theme:e})=>{var r;return((r=e.fontSizes)==null?void 0:r.md)||"1rem"}),Yn=s.div.withConfig({displayName:"DropdownIcon",componentId:"sc-1reqqnl-3"})(["transition:transform 0.2s ease;transform:",";color:",";"],({isOpen:e})=>e?"rotate(180deg)":"rotate(0deg)",({theme:e})=>{var r;return((r=e.colors)==null?void 0:r.textSecondary)||"#9ca3af"}),Wn=s.div.withConfig({displayName:"DropdownMenu",componentId:"sc-1reqqnl-4"})(["position:absolute;top:100%;left:0;right:0;z-index:1000;background:",";border:1px solid ",";border-radius:",";box-shadow:0 10px 25px -5px rgba(0,0,0,0.3);max-height:400px;overflow-y:auto;display:",";"],({theme:e})=>{var r;return((r=e.colors)==null?void 0:r.surface)||"#1f2937"},({theme:e})=>{var r;return((r=e.colors)==null?void 0:r.border)||"#4b5563"},({theme:e})=>{var r;return((r=e.borderRadius)==null?void 0:r.md)||"6px"},({isOpen:e})=>e?"block":"none"),Gn=s.div.withConfig({displayName:"MultiSessionGroup",componentId:"sc-1reqqnl-5"})(["border-bottom:1px solid ",";background:",";&:last-child{border-bottom:none;}"],({theme:e})=>{var r;return((r=e.colors)==null?void 0:r.border)||"#4b5563"},({theme:e})=>{var r;return((r=e.colors)==null?void 0:r.background)||"#111827"}),Kn=s.div.withConfig({displayName:"MultiSessionHeader",componentId:"sc-1reqqnl-6"})(["padding:",";background:",";color:",";font-weight:600;cursor:pointer;display:flex;align-items:center;justify-content:space-between;transition:background-color 0.2s ease;border-left:3px solid ",";&:hover{background:","40;}"],({theme:e})=>{var r;return((r=e.spacing)==null?void 0:r.md)||"12px"},({theme:e,isSelected:r})=>{var t,n;return r?((t=e.colors)==null?void 0:t.primary)||"#dc2626":((n=e.colors)==null?void 0:n.surface)||"#1f2937"},({theme:e,isSelected:r})=>{var t;return r?"#ffffff":((t=e.colors)==null?void 0:t.textPrimary)||"#ffffff"},({theme:e})=>{var r;return((r=e.colors)==null?void 0:r.warning)||"#f59e0b"},({theme:e,isSelected:r})=>{var t,n;return r?((t=e.colors)==null?void 0:t.primary)||"#dc2626":((n=e.colors)==null?void 0:n.border)||"#4b5563"}),Qn=s.div.withConfig({displayName:"MultiSessionIndicator",componentId:"sc-1reqqnl-7"})(["display:inline-flex;align-items:center;gap:",";font-size:",";color:",";font-weight:500;"],({theme:e})=>{var r;return((r=e.spacing)==null?void 0:r.xs)||"4px"},({theme:e})=>{var r;return((r=e.fontSizes)==null?void 0:r.xs)||"0.75rem"},({theme:e})=>{var r;return((r=e.colors)==null?void 0:r.warning)||"#f59e0b"}),Xn=s.div.withConfig({displayName:"SessionGroup",componentId:"sc-1reqqnl-8"})(["border-bottom:1px solid ",";&:last-child{border-bottom:none;}"],({theme:e})=>{var r;return((r=e.colors)==null?void 0:r.border)||"#4b5563"}),Jn=s.div.withConfig({displayName:"SessionHeader",componentId:"sc-1reqqnl-9"})(["padding:",";background:",";color:",";font-weight:600;cursor:pointer;display:flex;align-items:center;justify-content:space-between;transition:background-color 0.2s ease;&:hover{background:","40;}"],({theme:e})=>{var r;return((r=e.spacing)==null?void 0:r.md)||"12px"},({theme:e,isSelected:r})=>{var t;return r?((t=e.colors)==null?void 0:t.primary)||"#dc2626":"transparent"},({theme:e,isSelected:r})=>{var t;return r?"#ffffff":((t=e.colors)==null?void 0:t.textPrimary)||"#ffffff"},({theme:e,isSelected:r})=>{var t,n;return r?((t=e.colors)==null?void 0:t.primary)||"#dc2626":((n=e.colors)==null?void 0:n.border)||"#4b5563"}),Zn=s.div.withConfig({displayName:"MacroList",componentId:"sc-1reqqnl-10"})(["background:",";"],({theme:e})=>{var r;return((r=e.colors)==null?void 0:r.background)||"#111827"}),ei=s.div.withConfig({displayName:"MacroItem",componentId:"sc-1reqqnl-11"})(["padding:"," ",";color:",";cursor:pointer;font-size:",";transition:all 0.2s ease;border-left:3px solid ",";&:hover{background:","20;color:",";}"],({theme:e})=>{var r;return((r=e.spacing)==null?void 0:r.sm)||"8px"},({theme:e})=>{var r;return((r=e.spacing)==null?void 0:r.lg)||"24px"},({theme:e,isSelected:r})=>{var t,n;return r?((t=e.colors)==null?void 0:t.primary)||"#dc2626":((n=e.colors)==null?void 0:n.textSecondary)||"#9ca3af"},({theme:e})=>{var r;return((r=e.fontSizes)==null?void 0:r.sm)||"0.875rem"},({theme:e,isSelected:r})=>{var t;return r?((t=e.colors)==null?void 0:t.primary)||"#dc2626":"transparent"},({theme:e})=>{var r;return((r=e.colors)==null?void 0:r.border)||"#4b5563"},({theme:e})=>{var r;return((r=e.colors)==null?void 0:r.textPrimary)||"#ffffff"}),ri=s.div.withConfig({displayName:"CurrentSessionIndicator",componentId:"sc-1reqqnl-12"})(["display:inline-flex;align-items:center;gap:",";font-size:",";color:",";font-weight:500;"],({theme:e})=>{var r;return((r=e.spacing)==null?void 0:r.xs)||"4px"},({theme:e})=>{var r;return((r=e.fontSizes)==null?void 0:r.xs)||"0.75rem"},({theme:e})=>{var r;return((r=e.colors)==null?void 0:r.success)||"#10b981"}),ti=s.div.withConfig({displayName:"ErrorMessage",componentId:"sc-1reqqnl-13"})(["color:",";font-size:",";margin-top:",";"],({theme:e})=>{var r;return((r=e.colors)==null?void 0:r.error)||"#ef4444"},({theme:e})=>{var r;return((r=e.fontSizes)==null?void 0:r.sm)||"0.875rem"},({theme:e})=>{var r;return((r=e.spacing)==null?void 0:r.xs)||"4px"}),oi=({value:e,onChange:r,showMacroPeriods:t=!0,showCurrentSession:n=!0,allowCustomRange:i=!1,placeholder:c="Select session or macro period",disabled:a=!1,error:p,className:f})=>{const[u,d]=b.useState(!1),{hierarchicalOptions:m,currentSession:h,isCurrentSessionActive:y,selectSession:g,selectMacro:x}=gt({onSelectionChange:r}),C=b.useMemo(()=>re.getMultiSessionMacros(),[]),S=b.useMemo(()=>e!=null&&e.displayLabel?e.displayLabel:c,[e,c]),w=j=>{g(j),d(!1)},L=j=>{x(j),d(!1)},A=j=>(e==null?void 0:e.session)===j&&(e==null?void 0:e.selectionType)==="session",O=j=>(e==null?void 0:e.macroPeriod)===j&&(e==null?void 0:e.selectionType)==="macro",R=j=>(h==null?void 0:h.session)===j;return o.jsxs(Hn,{className:f,hasError:!!p,children:[o.jsxs(Un,{hasError:!!p,disabled:a,onClick:()=>!a&&d(!u),children:[o.jsxs(Vn,{children:[o.jsx("span",{children:S}),o.jsx(Yn,{isOpen:u,children:"▼"})]}),o.jsxs(Wn,{isOpen:u,children:[t&&C.length>0&&o.jsx(Gn,{children:C.map(j=>o.jsxs(Kn,{isSelected:O(j.type),onClick:_=>{_.stopPropagation(),L(j.type)},children:[o.jsx("span",{children:j.name}),o.jsx(Qn,{children:"🌐 MULTI-SESSION"})]},j.type))}),m.map(({session:j,sessionLabel:_,macros:k})=>o.jsxs(Xn,{children:[o.jsxs(Jn,{isSelected:A(j),onClick:U=>{U.stopPropagation(),w(j)},children:[o.jsx("span",{children:_}),n&&R(j)&&o.jsx(ri,{children:"🔴 LIVE"})]}),t&&k.length>0&&o.jsx(Zn,{children:k.map(({value:U,label:V})=>o.jsxs(ei,{isSelected:O(U),onClick:N=>{N.stopPropagation(),L(U)},children:[V,re.hasSubPeriods(U)&&o.jsx("span",{style:{marginLeft:"8px",fontSize:"0.75rem",opacity:.7},children:"📋 Has sub-periods"})]},U))})]},j))]})]}),p&&o.jsx(ti,{children:p})]})},E={DATE:"date",SYMBOL:"symbol",DIRECTION:"direction",MODEL_TYPE:"model_type",SESSION:"session",ENTRY_PRICE:"entry_price",EXIT_PRICE:"exit_price",R_MULTIPLE:"r_multiple",ACHIEVED_PL:"achieved_pl",WIN_LOSS:"win_loss",PATTERN_QUALITY:"pattern_quality_rating",ENTRY_TIME:"entry_time",EXIT_TIME:"exit_time"},ar=s.span.withConfig({displayName:"ProfitLossCell",componentId:"sc-14bks31-0"})(["color:",";font-weight:",";"],({isProfit:e,theme:r})=>e?r.colors.success||"#10b981":r.colors.error||"#ef4444",({theme:e})=>{var r;return((r=e.fontWeights)==null?void 0:r.semibold)||600}),mt=s(Ne).withConfig({displayName:"DirectionBadge",componentId:"sc-14bks31-1"})(["background-color:",";color:white;"],({direction:e,theme:r})=>e==="Long"?r.colors.success||"#10b981":r.colors.error||"#ef4444"),ht=s.span.withConfig({displayName:"QualityRating",componentId:"sc-14bks31-2"})(["color:",";font-weight:",";"],({rating:e,theme:r})=>e>=4?r.colors.success||"#10b981":e>=3?r.colors.warning||"#f59e0b":r.colors.error||"#ef4444",({theme:e})=>{var r;return((r=e.fontWeights)==null?void 0:r.semibold)||600}),cr=s.span.withConfig({displayName:"RMultipleCell",componentId:"sc-14bks31-3"})(["color:",";font-weight:",";"],({rMultiple:e,theme:r})=>e>0?r.colors.success||"#10b981":r.colors.error||"#ef4444",({theme:e})=>{var r;return((r=e.fontWeights)==null?void 0:r.semibold)||600}),ke=e=>e==null?"-":new Intl.NumberFormat("en-US",{style:"currency",currency:"USD",minimumFractionDigits:2}).format(e),lr=e=>{try{return new Date(e).toLocaleDateString("en-US",{year:"numeric",month:"short",day:"numeric"})}catch{return e}},or=e=>e||"-",xt=()=>[{id:E.DATE,header:"Date",sortable:!0,width:"100px",cell:e=>lr(e.trade[E.DATE])},{id:E.SYMBOL,header:"Symbol",sortable:!0,width:"80px",cell:e=>e.trade.market||"MNQ"},{id:E.DIRECTION,header:"Direction",sortable:!0,width:"80px",align:"center",cell:e=>o.jsx(mt,{direction:e.trade[E.DIRECTION],size:"small",children:e.trade[E.DIRECTION]})},{id:E.MODEL_TYPE,header:"Model",sortable:!0,width:"120px",cell:e=>e.trade[E.MODEL_TYPE]||"-"},{id:E.SESSION,header:"Session",sortable:!0,width:"120px",cell:e=>e.trade[E.SESSION]||"-"},{id:E.ENTRY_PRICE,header:"Entry",sortable:!0,width:"100px",align:"right",cell:e=>ke(e.trade[E.ENTRY_PRICE])},{id:E.EXIT_PRICE,header:"Exit",sortable:!0,width:"100px",align:"right",cell:e=>ke(e.trade[E.EXIT_PRICE])},{id:E.R_MULTIPLE,header:"R Multiple",sortable:!0,width:"100px",align:"right",cell:e=>o.jsx(cr,{rMultiple:e.trade[E.R_MULTIPLE]||0,children:e.trade[E.R_MULTIPLE]?`${e.trade[E.R_MULTIPLE].toFixed(2)}R`:"-"})},{id:E.ACHIEVED_PL,header:"P&L",sortable:!0,width:"100px",align:"right",cell:e=>o.jsx(ar,{isProfit:(e.trade[E.ACHIEVED_PL]||0)>0,children:ke(e.trade[E.ACHIEVED_PL])})},{id:E.WIN_LOSS,header:"Result",sortable:!0,width:"80px",align:"center",cell:e=>o.jsx(Ne,{variant:e.trade[E.WIN_LOSS]==="Win"?"success":"error",size:"small",children:e.trade[E.WIN_LOSS]||"-"})},{id:E.PATTERN_QUALITY,header:"Quality",sortable:!0,width:"80px",align:"center",cell:e=>o.jsx(ht,{rating:e.trade[E.PATTERN_QUALITY]||0,children:e.trade[E.PATTERN_QUALITY]?`${e.trade[E.PATTERN_QUALITY]}/5`:"-"})},{id:E.ENTRY_TIME,header:"Entry Time",sortable:!0,width:"100px",align:"center",cell:e=>or(e.trade[E.ENTRY_TIME])},{id:E.EXIT_TIME,header:"Exit Time",sortable:!0,width:"100px",align:"center",cell:e=>or(e.trade[E.EXIT_TIME])}],bt=()=>[{id:E.DATE,header:"Date",sortable:!0,width:"90px",cell:e=>lr(e.trade[E.DATE])},{id:E.SYMBOL,header:"Symbol",sortable:!0,width:"60px",cell:e=>e.trade.market||"MNQ"},{id:E.DIRECTION,header:"Dir",sortable:!0,width:"50px",align:"center",cell:e=>o.jsx(mt,{direction:e.trade[E.DIRECTION],size:"small",children:e.trade[E.DIRECTION].charAt(0)})},{id:E.R_MULTIPLE,header:"R",sortable:!0,width:"60px",align:"right",cell:e=>o.jsx(cr,{rMultiple:e.trade[E.R_MULTIPLE]||0,children:e.trade[E.R_MULTIPLE]?`${e.trade[E.R_MULTIPLE].toFixed(1)}R`:"-"})},{id:E.ACHIEVED_PL,header:"P&L",sortable:!0,width:"80px",align:"right",cell:e=>o.jsx(ar,{isProfit:(e.trade[E.ACHIEVED_PL]||0)>0,children:ke(e.trade[E.ACHIEVED_PL])})},{id:E.WIN_LOSS,header:"Result",sortable:!0,width:"60px",align:"center",cell:e=>o.jsx(Ne,{variant:e.trade[E.WIN_LOSS]==="Win"?"success":"error",size:"small",children:e.trade[E.WIN_LOSS]==="Win"?"W":e.trade[E.WIN_LOSS]==="Loss"?"L":"-"})}],yt=()=>[{id:E.DATE,header:"Date",sortable:!0,width:"100px",cell:e=>lr(e.trade[E.DATE])},{id:E.MODEL_TYPE,header:"Model",sortable:!0,width:"120px",cell:e=>e.trade[E.MODEL_TYPE]||"-"},{id:E.SESSION,header:"Session",sortable:!0,width:"120px",cell:e=>e.trade[E.SESSION]||"-"},{id:E.R_MULTIPLE,header:"R Multiple",sortable:!0,width:"100px",align:"right",cell:e=>o.jsx(cr,{rMultiple:e.trade[E.R_MULTIPLE]||0,children:e.trade[E.R_MULTIPLE]?`${e.trade[E.R_MULTIPLE].toFixed(2)}R`:"-"})},{id:E.ACHIEVED_PL,header:"P&L",sortable:!0,width:"100px",align:"right",cell:e=>o.jsx(ar,{isProfit:(e.trade[E.ACHIEVED_PL]||0)>0,children:ke(e.trade[E.ACHIEVED_PL])})},{id:E.PATTERN_QUALITY,header:"Quality",sortable:!0,width:"80px",align:"center",cell:e=>o.jsx(ht,{rating:e.trade[E.PATTERN_QUALITY]||0,children:e.trade[E.PATTERN_QUALITY]?`${e.trade[E.PATTERN_QUALITY]}/5`:"-"})},{id:E.WIN_LOSS,header:"Result",sortable:!0,width:"80px",align:"center",cell:e=>o.jsx(Ne,{variant:e.trade[E.WIN_LOSS]==="Win"?"success":"error",size:"small",children:e.trade[E.WIN_LOSS]||"-"})}],si=s.tr.withConfig({displayName:"TableRow",componentId:"sc-uyrnn-0"})([""," "," "," "," ",""],({striped:e,theme:r,isSelected:t})=>{var n;return e&&!t&&s.css(["&:nth-child(even){background-color:","50;}"],((n=r.colors)==null?void 0:n.background)||"#f8f9fa")},({hoverable:e,theme:r,isSelected:t})=>{var n;return e&&!t&&s.css(["&:hover{background-color:","aa;}"],((n=r.colors)==null?void 0:n.background)||"#f8f9fa")},({isSelected:e,theme:r})=>{var t;return e&&s.css(["background-color:","15;"],((t=r.colors)==null?void 0:t.primary)||"#3b82f6")},({isClickable:e})=>e&&s.css(["cursor:pointer;"]),({isExpanded:e,theme:r})=>{var t;return e&&s.css(["border-bottom:2px solid ",";"],((t=r.colors)==null?void 0:t.primary)||"#3b82f6")}),Fr=s.td.withConfig({displayName:"TableCell",componentId:"sc-uyrnn-1"})(["text-align:",";border-bottom:1px solid ",";color:",";padding:"," ",";vertical-align:middle;"],({align:e})=>e||"left",({theme:e})=>{var r;return((r=e.colors)==null?void 0:r.border)||"#e5e7eb"},({theme:e})=>{var r;return((r=e.colors)==null?void 0:r.textPrimary)||"#111827"},({theme:e})=>{var r;return((r=e.spacing)==null?void 0:r.sm)||"12px"},({theme:e})=>{var r;return((r=e.spacing)==null?void 0:r.md)||"16px"}),ni=s.tr.withConfig({displayName:"ExpandedRow",componentId:"sc-uyrnn-2"})(["display:",";"],({isVisible:e})=>e?"table-row":"none"),ii=s.td.withConfig({displayName:"ExpandedCell",componentId:"sc-uyrnn-3"})(["padding:0;border-bottom:1px solid ",";"],({theme:e})=>{var r;return((r=e.colors)==null?void 0:r.border)||"#e5e7eb"}),ai=s.div.withConfig({displayName:"ExpandedContent",componentId:"sc-uyrnn-4"})(["padding:",";background-color:","30;border-left:3px solid ",";"],({theme:e})=>{var r;return((r=e.spacing)==null?void 0:r.md)||"16px"},({theme:e})=>{var r;return((r=e.colors)==null?void 0:r.background)||"#f8f9fa"},({theme:e})=>{var r;return((r=e.colors)==null?void 0:r.primary)||"#3b82f6"}),ci=s.button.withConfig({displayName:"ExpandButton",componentId:"sc-uyrnn-5"})(["background:none;border:none;cursor:pointer;padding:",";color:",";font-size:",";display:flex;align-items:center;justify-content:center;border-radius:",";transition:all 0.2s ease;&:hover{background-color:",";color:",";}&:focus{outline:2px solid ",";outline-offset:2px;}"],({theme:e})=>{var r;return((r=e.spacing)==null?void 0:r.xs)||"8px"},({theme:e})=>{var r;return((r=e.colors)==null?void 0:r.textSecondary)||"#6b7280"},({theme:e})=>{var r;return((r=e.fontSizes)==null?void 0:r.sm)||"14px"},({theme:e})=>{var r;return((r=e.borderRadius)==null?void 0:r.sm)||"4px"},({theme:e})=>{var r;return((r=e.colors)==null?void 0:r.background)||"#f8f9fa"},({theme:e})=>{var r;return((r=e.colors)==null?void 0:r.primary)||"#3b82f6"},({theme:e})=>{var r;return((r=e.colors)==null?void 0:r.primary)||"#3b82f6"}),li=s.span.withConfig({displayName:"ExpandIcon",componentId:"sc-uyrnn-6"})(["display:inline-block;transition:transform 0.2s ease;transform:",";&::after{content:'▶';}"],({isExpanded:e})=>e?"rotate(90deg)":"rotate(0deg)"),di=s.div.withConfig({displayName:"TradeDetails",componentId:"sc-uyrnn-7"})(["display:grid;grid-template-columns:repeat(auto-fit,minmax(200px,1fr));gap:",";"],({theme:e})=>{var r;return((r=e.spacing)==null?void 0:r.md)||"16px"}),Ee=s.div.withConfig({displayName:"DetailGroup",componentId:"sc-uyrnn-8"})(["display:flex;flex-direction:column;gap:",";"],({theme:e})=>{var r;return((r=e.spacing)==null?void 0:r.xs)||"8px"}),Te=s.span.withConfig({displayName:"DetailLabel",componentId:"sc-uyrnn-9"})(["font-size:",";font-weight:",";color:",";"],({theme:e})=>{var r;return((r=e.fontSizes)==null?void 0:r.sm)||"14px"},({theme:e})=>{var r;return((r=e.fontWeights)==null?void 0:r.medium)||500},({theme:e})=>{var r;return((r=e.colors)==null?void 0:r.textSecondary)||"#6b7280"}),ne=s.span.withConfig({displayName:"DetailValue",componentId:"sc-uyrnn-10"})(["font-size:",";color:",";"],({theme:e})=>{var r;return((r=e.fontSizes)==null?void 0:r.sm)||"14px"},({theme:e})=>{var r;return((r=e.colors)==null?void 0:r.textPrimary)||"#111827"}),ui=({trade:e})=>o.jsxs(di,{children:[e.fvg_details&&o.jsxs(Ee,{children:[o.jsx(Te,{children:"FVG Details"}),o.jsxs(ne,{children:["Type: ",e.fvg_details.rd_type||"-"]}),o.jsxs(ne,{children:["Entry Version: ",e.fvg_details.entry_version||"-"]}),o.jsxs(ne,{children:["Draw on Liquidity: ",e.fvg_details.draw_on_liquidity||"-"]})]}),e.setup&&o.jsxs(Ee,{children:[o.jsx(Te,{children:"Setup Classification"}),o.jsxs(ne,{children:["Primary: ",e.setup.primary_setup||"-"]}),o.jsxs(ne,{children:["Secondary: ",e.setup.secondary_setup||"-"]}),o.jsxs(ne,{children:["Liquidity: ",e.setup.liquidity_taken||"-"]})]}),e.analysis&&o.jsxs(Ee,{children:[o.jsx(Te,{children:"Analysis"}),o.jsxs(ne,{children:["DOL Target: ",e.analysis.dol_target_type||"-"]}),o.jsxs(ne,{children:["Path Quality: ",e.analysis.path_quality||"-"]}),o.jsxs(ne,{children:["Clustering: ",e.analysis.clustering||"-"]})]}),o.jsxs(Ee,{children:[o.jsx(Te,{children:"Timing"}),o.jsxs(ne,{children:["Entry: ",e.trade.entry_time||"-"]}),o.jsxs(ne,{children:["Exit: ",e.trade.exit_time||"-"]}),o.jsxs(ne,{children:["FVG: ",e.trade.fvg_time||"-"]}),o.jsxs(ne,{children:["RD: ",e.trade.rd_time||"-"]})]}),e.trade.notes&&o.jsxs(Ee,{style:{gridColumn:"1 / -1"},children:[o.jsx(Te,{children:"Notes"}),o.jsx(ne,{children:e.trade.notes})]})]}),vt=({trade:e,index:r,columns:t,isSelected:n=!1,hoverable:i=!0,striped:c=!0,expandable:a=!1,isExpanded:p=!1,onRowClick:f,onToggleExpand:u,expandedContent:d})=>{const[m,h]=b.useState(!1),y=p!==void 0?p:m,g=S=>{S.target.closest("button")||f==null||f(e,r)},x=S=>{S.stopPropagation(),u?u(e,r):h(!m)},C=t.filter(S=>!S.hidden);return o.jsxs(o.Fragment,{children:[o.jsxs(si,{hoverable:i,striped:c,isSelected:n,isClickable:!!f,isExpanded:y,onClick:g,children:[a&&o.jsx(Fr,{align:"center",style:{width:"40px",padding:"8px"},children:o.jsx(ci,{onClick:x,children:o.jsx(li,{isExpanded:y})})}),C.map(S=>o.jsx(Fr,{align:S.align,children:S.cell(e,r)},S.id))]}),a&&o.jsx(ni,{isVisible:y,children:o.jsx(ii,{colSpan:C.length+1,children:o.jsx(ai,{children:d||o.jsx(ui,{trade:e})})})})]})},ue={MODEL_TYPE:"model_type",WIN_LOSS:"win_loss",DATE_FROM:"dateFrom",DATE_TO:"dateTo",SESSION:"session",DIRECTION:"direction",MARKET:"market",MIN_R_MULTIPLE:"min_r_multiple",MAX_R_MULTIPLE:"max_r_multiple",MIN_PATTERN_QUALITY:"min_pattern_quality",MAX_PATTERN_QUALITY:"max_pattern_quality"},pi=s.div.withConfig({displayName:"FiltersContainer",componentId:"sc-32k3gq-0"})(["display:flex;flex-direction:column;gap:",";padding:",";background-color:",";border-radius:",";border:1px solid ",";"],({theme:e})=>{var r;return((r=e.spacing)==null?void 0:r.md)||"16px"},({theme:e})=>{var r;return((r=e.spacing)==null?void 0:r.md)||"16px"},({theme:e})=>{var r;return((r=e.colors)==null?void 0:r.background)||"#f8f9fa"},({theme:e})=>{var r;return((r=e.borderRadius)==null?void 0:r.md)||"8px"},({theme:e})=>{var r;return((r=e.colors)==null?void 0:r.border)||"#e5e7eb"}),Br=s.div.withConfig({displayName:"FilterRow",componentId:"sc-32k3gq-1"})(["display:flex;gap:",";align-items:end;flex-wrap:wrap;@media (max-width:768px){flex-direction:column;align-items:stretch;}"],({theme:e})=>{var r;return((r=e.spacing)==null?void 0:r.sm)||"12px"}),ge=s.div.withConfig({displayName:"FilterGroup",componentId:"sc-32k3gq-2"})(["display:flex;flex-direction:column;gap:",";min-width:120px;"],({theme:e})=>{var r;return((r=e.spacing)==null?void 0:r.xs)||"8px"}),me=s.label.withConfig({displayName:"FilterLabel",componentId:"sc-32k3gq-3"})(["font-size:",";font-weight:",";color:",";"],({theme:e})=>{var r;return((r=e.fontSizes)==null?void 0:r.sm)||"14px"},({theme:e})=>{var r;return((r=e.fontWeights)==null?void 0:r.medium)||500},({theme:e})=>{var r;return((r=e.colors)==null?void 0:r.textSecondary)||"#6b7280"}),fi=s.div.withConfig({displayName:"FilterActions",componentId:"sc-32k3gq-4"})(["display:flex;gap:",";align-items:center;margin-left:auto;@media (max-width:768px){margin-left:0;justify-content:flex-end;}"],({theme:e})=>{var r;return((r=e.spacing)==null?void 0:r.sm)||"12px"}),gi=s.div.withConfig({displayName:"AdvancedFilters",componentId:"sc-32k3gq-5"})(["display:",";flex-direction:column;gap:",";padding-top:",";border-top:1px solid ",";"],({isVisible:e})=>e?"flex":"none",({theme:e})=>{var r;return((r=e.spacing)==null?void 0:r.md)||"16px"},({theme:e})=>{var r;return((r=e.spacing)==null?void 0:r.md)||"16px"},({theme:e})=>{var r;return((r=e.colors)==null?void 0:r.border)||"#e5e7eb"}),qr=s.div.withConfig({displayName:"RangeInputGroup",componentId:"sc-32k3gq-6"})(["display:flex;gap:",";align-items:center;"],({theme:e})=>{var r;return((r=e.spacing)==null?void 0:r.xs)||"8px"}),Hr=s.span.withConfig({displayName:"RangeLabel",componentId:"sc-32k3gq-7"})(["font-size:",";color:",";"],({theme:e})=>{var r;return((r=e.fontSizes)==null?void 0:r.sm)||"14px"},({theme:e})=>{var r;return((r=e.colors)==null?void 0:r.textSecondary)||"#6b7280"}),St=({filters:e,onFiltersChange:r,onReset:t,isLoading:n=!1,showAdvanced:i=!1,onToggleAdvanced:c})=>{const a=(u,d)=>{r({...e,[u]:d})},p=()=>{r({}),t==null||t()},f=Object.values(e).some(u=>u!==void 0&&u!==""&&u!==null);return o.jsxs(pi,{children:[o.jsxs(Br,{children:[o.jsxs(ge,{children:[o.jsx(me,{children:"Date From"}),o.jsx(xe,{type:"date",value:e.dateFrom||"",onChange:u=>a(ue.DATE_FROM,u),disabled:n})]}),o.jsxs(ge,{children:[o.jsx(me,{children:"Date To"}),o.jsx(xe,{type:"date",value:e.dateTo||"",onChange:u=>a(ue.DATE_TO,u),disabled:n})]}),o.jsxs(ge,{children:[o.jsx(me,{children:"Model Type"}),o.jsx(Se,{options:[{value:"",label:"All Models"},{value:"RD-Cont",label:"RD-Cont"},{value:"FVG-RD",label:"FVG-RD"},{value:"True-RD",label:"True-RD"},{value:"IMM-RD",label:"IMM-RD"},{value:"Dispersed-RD",label:"Dispersed-RD"},{value:"Wide-Gap-RD",label:"Wide-Gap-RD"}],value:e.model_type||"",onChange:u=>a(ue.MODEL_TYPE,u),disabled:n})]}),o.jsxs(ge,{children:[o.jsx(me,{children:"Session"}),o.jsx(Se,{options:[{value:"",label:"All Sessions"},{value:"Pre-Market",label:"Pre-Market"},{value:"NY Open",label:"NY Open"},{value:"10:50-11:10",label:"10:50-11:10"},{value:"11:50-12:10",label:"11:50-12:10"},{value:"Lunch Macro",label:"Lunch Macro"},{value:"13:50-14:10",label:"13:50-14:10"},{value:"14:50-15:10",label:"14:50-15:10"},{value:"15:15-15:45",label:"15:15-15:45"},{value:"MOC",label:"MOC"},{value:"Post MOC",label:"Post MOC"}],value:e.session||"",onChange:u=>a(ue.SESSION,u),disabled:n})]}),o.jsxs(ge,{children:[o.jsx(me,{children:"Direction"}),o.jsx(Se,{options:[{value:"",label:"All Directions"},{value:"Long",label:"Long"},{value:"Short",label:"Short"}],value:e.direction||"",onChange:u=>a(ue.DIRECTION,u),disabled:n})]}),o.jsxs(ge,{children:[o.jsx(me,{children:"Result"}),o.jsx(Se,{options:[{value:"",label:"All Results"},{value:"Win",label:"Win"},{value:"Loss",label:"Loss"}],value:e.win_loss||"",onChange:u=>a(ue.WIN_LOSS,u),disabled:n})]}),o.jsxs(fi,{children:[c&&o.jsxs(ie,{variant:"outline",size:"small",onClick:c,disabled:n,children:[i?"Hide":"Show"," Advanced"]}),o.jsx(ie,{variant:"outline",size:"small",onClick:p,disabled:n||!f,children:"Reset"})]})]}),o.jsx(gi,{isVisible:i,children:o.jsxs(Br,{children:[o.jsxs(ge,{children:[o.jsx(me,{children:"Market"}),o.jsx(Se,{options:[{value:"",label:"All Markets"},{value:"MNQ",label:"MNQ"},{value:"NQ",label:"NQ"},{value:"ES",label:"ES"},{value:"MES",label:"MES"},{value:"YM",label:"YM"},{value:"MYM",label:"MYM"}],value:e.market||"",onChange:u=>a(ue.MARKET,u),disabled:n})]}),o.jsxs(ge,{children:[o.jsx(me,{children:"R Multiple Range"}),o.jsxs(qr,{children:[o.jsx(xe,{type:"number",placeholder:"Min",step:"0.1",value:e.min_r_multiple||"",onChange:u=>a(ue.MIN_R_MULTIPLE,u?Number(u):void 0),disabled:n,style:{width:"80px"}}),o.jsx(Hr,{children:"to"}),o.jsx(xe,{type:"number",placeholder:"Max",step:"0.1",value:e.max_r_multiple||"",onChange:u=>a(ue.MAX_R_MULTIPLE,u?Number(u):void 0),disabled:n,style:{width:"80px"}})]})]}),o.jsxs(ge,{children:[o.jsx(me,{children:"Pattern Quality Range"}),o.jsxs(qr,{children:[o.jsx(xe,{type:"number",placeholder:"Min",min:"1",max:"5",step:"0.1",value:e.min_pattern_quality||"",onChange:u=>a(ue.MIN_PATTERN_QUALITY,u?Number(u):void 0),disabled:n,style:{width:"80px"}}),o.jsx(Hr,{children:"to"}),o.jsx(xe,{type:"number",placeholder:"Max",min:"1",max:"5",step:"0.1",value:e.max_pattern_quality||"",onChange:u=>a(ue.MAX_PATTERN_QUALITY,u?Number(u):void 0),disabled:n,style:{width:"80px"}})]})]})]})})]})},mi=s.div.withConfig({displayName:"TableContainer",componentId:"sc-13oxwmo-0"})(["width:100%;overflow:auto;"," ",""],({height:e})=>e&&`height: ${e};`,({scrollable:e})=>e&&"overflow-x: auto;"),hi=s.table.withConfig({displayName:"StyledTable",componentId:"sc-13oxwmo-1"})(["width:100%;border-collapse:separate;border-spacing:0;font-size:",";"," ",""],({theme:e})=>{var r;return((r=e.fontSizes)==null?void 0:r.sm)||"14px"},({bordered:e,theme:r})=>{var t,n;return e&&s.css(["border:1px solid ",";border-radius:",";"],((t=r.colors)==null?void 0:t.border)||"#e5e7eb",((n=r.borderRadius)==null?void 0:n.sm)||"4px")},({compact:e,theme:r})=>{var t,n,i,c;return e?s.css(["th,td{padding:"," ",";}"],((t=r.spacing)==null?void 0:t.xs)||"8px",((n=r.spacing)==null?void 0:n.sm)||"12px"):s.css(["th,td{padding:"," ",";}"],((i=r.spacing)==null?void 0:i.sm)||"12px",((c=r.spacing)==null?void 0:c.md)||"16px")}),xi=s.thead.withConfig({displayName:"TableHeader",componentId:"sc-13oxwmo-2"})(["",""],({stickyHeader:e})=>e&&s.css(["position:sticky;top:0;z-index:1;"])),bi=s.tr.withConfig({displayName:"TableHeaderRow",componentId:"sc-13oxwmo-3"})(["background-color:",";"],({theme:e})=>{var r;return((r=e.colors)==null?void 0:r.background)||"#f8f9fa"}),Ur=s.th.withConfig({displayName:"TableHeaderCell",componentId:"sc-13oxwmo-4"})(["text-align:",";font-weight:",";color:",";border-bottom:1px solid ",";white-space:nowrap;"," "," ",""],({align:e})=>e||"left",({theme:e})=>{var r;return((r=e.fontWeights)==null?void 0:r.semibold)||600},({theme:e})=>{var r;return((r=e.colors)==null?void 0:r.textSecondary)||"#6b7280"},({theme:e})=>{var r;return((r=e.colors)==null?void 0:r.border)||"#e5e7eb"},({width:e})=>e&&`width: ${e};`,({sortable:e})=>e&&s.css(["cursor:pointer;user-select:none;&:hover{background-color:","aa;}"],({theme:r})=>{var t;return((t=r.colors)==null?void 0:t.background)||"#f8f9fa"}),({isSorted:e,theme:r})=>{var t;return e&&s.css(["color:",";"],((t=r.colors)==null?void 0:t.primary)||"#3b82f6")}),yi=s.span.withConfig({displayName:"SortIcon",componentId:"sc-13oxwmo-5"})(["display:inline-block;margin-left:",";&::after{content:'","';}"],({theme:e})=>{var r;return((r=e.spacing)==null?void 0:r.xs)||"8px"},({direction:e})=>e==="asc"?"↑":e==="desc"?"↓":"↕"),vi=s.tbody.withConfig({displayName:"TableBody",componentId:"sc-13oxwmo-6"})([""]),Si=s.div.withConfig({displayName:"EmptyState",componentId:"sc-13oxwmo-7"})(["padding:",";text-align:center;color:",";"],({theme:e})=>{var r;return((r=e.spacing)==null?void 0:r.xl)||"32px"},({theme:e})=>{var r;return((r=e.colors)==null?void 0:r.textSecondary)||"#6b7280"}),wi=s.div.withConfig({displayName:"LoadingOverlay",componentId:"sc-13oxwmo-8"})(["position:absolute;top:0;left:0;right:0;bottom:0;background-color:",";display:flex;align-items:center;justify-content:center;z-index:1;"],({theme:e})=>{var r;return`${((r=e.colors)==null?void 0:r.background)||"#ffffff"}80`}),Ci=s.div.withConfig({displayName:"LoadingSpinner",componentId:"sc-13oxwmo-9"})(["width:32px;height:32px;border:3px solid ",";border-top:3px solid ",";border-radius:50%;animation:spin 1s linear infinite;@keyframes spin{0%{transform:rotate(0deg);}100%{transform:rotate(360deg);}}"],({theme:e})=>{var r;return((r=e.colors)==null?void 0:r.background)||"#f8f9fa"},({theme:e})=>{var r;return((r=e.colors)==null?void 0:r.primary)||"#3b82f6"}),Ii=s.div.withConfig({displayName:"PaginationContainer",componentId:"sc-13oxwmo-10"})(["display:flex;justify-content:space-between;align-items:center;padding:"," 0;font-size:",";"],({theme:e})=>{var r;return((r=e.spacing)==null?void 0:r.md)||"16px"},({theme:e})=>{var r;return((r=e.fontSizes)==null?void 0:r.sm)||"14px"}),Ei=s.div.withConfig({displayName:"PageInfo",componentId:"sc-13oxwmo-11"})(["color:",";"],({theme:e})=>{var r;return((r=e.colors)==null?void 0:r.textSecondary)||"#6b7280"}),Ti=s.div.withConfig({displayName:"PaginationControls",componentId:"sc-13oxwmo-12"})(["display:flex;gap:",";"],({theme:e})=>{var r;return((r=e.spacing)==null?void 0:r.xs)||"8px"}),ji=({data:e,isLoading:r=!1,bordered:t=!0,striped:n=!0,hoverable:i=!0,compact:c=!1,stickyHeader:a=!1,height:p="",onRowClick:f,isRowSelected:u,onSort:d,sortColumn:m="",sortDirection:h="asc",pagination:y=!1,currentPage:g=1,pageSize:x=10,totalRows:C=0,onPageChange:S,onPageSizeChange:w,className:L="",emptyMessage:A="No trades available",scrollable:O=!0,showFilters:R=!1,filters:j={},onFiltersChange:_,columnPreset:k="default",customColumns:U,expandableRows:V=!1,renderExpandedContent:N})=>{const[H,ee]=b.useState(!1),te=b.useMemo(()=>{if(U)return U;switch(k){case"compact":return bt();case"performance":return yt();default:return xt()}},[U,k]),de=b.useMemo(()=>te.filter(q=>!q.hidden),[te]),oe=b.useMemo(()=>Math.ceil(C/x),[C,x]),D=b.useMemo(()=>{if(!y)return e;const q=(g-1)*x,pe=q+x;return C>0&&e.length<=x?e:e.slice(q,pe)},[e,y,g,x,C]),X=q=>{if(!d)return;d(q,m===q&&h==="asc"?"desc":"asc")},he=q=>{q<1||q>oe||!S||S(q)};return o.jsxs("div",{children:[R&&_&&o.jsx(St,{filters:j,onFiltersChange:_,isLoading:r,showAdvanced:H,onToggleAdvanced:()=>ee(!H)}),o.jsxs("div",{style:{position:"relative"},children:[r&&o.jsx(wi,{children:o.jsx(Ci,{})}),o.jsx(mi,{height:p,scrollable:O,children:o.jsxs(hi,{bordered:t,striped:n,compact:c,className:L,children:[o.jsx(xi,{stickyHeader:a,children:o.jsxs(bi,{children:[V&&o.jsx(Ur,{width:"40px",align:"center"}),de.map(q=>o.jsxs(Ur,{sortable:q.sortable,isSorted:m===q.id,align:q.align,width:q.width,onClick:()=>q.sortable&&X(q.id),children:[q.header,q.sortable&&o.jsx(yi,{direction:m===q.id?h:void 0})]},q.id))]})}),o.jsx(vi,{children:D.length>0?D.map((q,pe)=>o.jsx(vt,{trade:q,index:pe,columns:de,isSelected:u?u(q,pe):!1,hoverable:i,striped:n,expandable:V,onRowClick:f,expandedContent:N==null?void 0:N(q)},q.trade.id||pe)):o.jsx("tr",{children:o.jsx("td",{colSpan:de.length+(V?1:0),children:o.jsx(Si,{children:A})})})})]})}),y&&oe>0&&o.jsxs(Ii,{children:[o.jsxs(Ei,{children:["Showing ",Math.min((g-1)*x+1,C)," to"," ",Math.min(g*x,C)," of ",C," entries"]}),o.jsxs(Ti,{children:[o.jsx(ie,{size:"small",variant:"outline",onClick:()=>he(1),disabled:g===1,children:"First"}),o.jsx(ie,{size:"small",variant:"outline",onClick:()=>he(g-1),disabled:g===1,children:"Prev"}),o.jsx(ie,{size:"small",variant:"outline",onClick:()=>he(g+1),disabled:g===oe,children:"Next"}),o.jsx(ie,{size:"small",variant:"outline",onClick:()=>he(oe),disabled:g===oe,children:"Last"})]})]})]})]})},ki=s.div.withConfig({displayName:"HeaderActions",componentId:"sc-1l7c7gv-0"})(["display:flex;gap:",";"],({theme:e})=>e.spacing.sm),Ni=({title:e,children:r,isLoading:t=!1,hasError:n=!1,errorMessage:i="An error occurred while loading data",showRetry:c=!0,onRetry:a,isEmpty:p=!1,emptyMessage:f="No data available",emptyActionText:u,onEmptyAction:d,actionButton:m,className:h,...y})=>{const g=o.jsx(ki,{children:m});let x;return t?x=o.jsx(at,{variant:"card",text:"Loading data..."}):n?x=o.jsx(tr,{title:"Error",description:i,variant:"compact",actionText:c?"Retry":void 0,onAction:c?a:void 0}):p?x=o.jsx(tr,{title:"No Data",description:f,variant:"compact",actionText:u,onAction:d}):x=r,o.jsx(ct,{title:e,actions:g,className:h,...y,children:x})},Ri=s.div.withConfig({displayName:"SectionContainer",componentId:"sc-14y246p-0"})(["background:",";border:1px solid ",";border-radius:",";padding:",";margin-bottom:",";"],({theme:e})=>e.colors.surface,({theme:e})=>e.colors.border,({theme:e})=>e.borderRadius.md,({theme:e})=>e.spacing.lg,({theme:e})=>e.spacing.lg),_i=s.div.withConfig({displayName:"SectionHeader",componentId:"sc-14y246p-1"})(["display:flex;justify-content:space-between;align-items:center;margin-bottom:",";padding-bottom:",";border-bottom:1px solid ",";"],({theme:e})=>e.spacing.md,({theme:e})=>e.spacing.sm,({theme:e})=>e.colors.border),Li=s.h2.withConfig({displayName:"SectionTitle",componentId:"sc-14y246p-2"})(["color:",";font-size:",";font-weight:600;margin:0;"],({theme:e})=>e.colors.textPrimary,({theme:e})=>e.fontSizes.lg),Mi=s.div.withConfig({displayName:"SectionActions",componentId:"sc-14y246p-3"})(["display:flex;gap:",";"],({theme:e})=>e.spacing.sm),Pi=s.div.withConfig({displayName:"SectionContent",componentId:"sc-14y246p-4"})(["min-height:200px;"]),Vr=s.div.withConfig({displayName:"LoadingState",componentId:"sc-14y246p-5"})(["display:flex;align-items:center;justify-content:center;min-height:200px;color:",";"],({theme:e})=>e.colors.textSecondary),Di=s.div.withConfig({displayName:"ErrorState",componentId:"sc-14y246p-6"})(["display:flex;align-items:center;justify-content:center;min-height:200px;color:",";text-align:center;"],({theme:e})=>e.colors.danger),Oi=({name:e,title:r,children:t,actions:n,isLoading:i=!1,error:c=null,className:a,collapsible:p=!1,defaultCollapsed:f=!1})=>{const[u,d]=b.useState(f),m=()=>{p&&d(!u)},h=r||e.charAt(0).toUpperCase()+e.slice(1),y=()=>c?o.jsx(Di,{children:o.jsxs("div",{children:[o.jsxs("div",{children:["Error loading ",e]}),o.jsx("div",{style:{fontSize:"0.9em",marginTop:"8px"},children:c})]})}):i?o.jsxs(Vr,{children:["Loading ",e,"..."]}):t||o.jsxs(Vr,{children:["No ",e," data available"]});return o.jsxs(Ri,{className:a,"data-section":e,children:[o.jsxs(_i,{children:[o.jsxs(Li,{onClick:m,style:{cursor:p?"pointer":"default"},children:[h,p&&o.jsx("span",{style:{marginLeft:"8px",fontSize:"0.8em"},children:u?"▶":"▼"})]}),n&&o.jsx(Mi,{children:n})]}),!u&&o.jsx(Pi,{children:y()})]})},$i=Oi,Ai=s.div.withConfig({displayName:"Container",componentId:"sc-djltr5-0"})(["display:grid;grid-template-areas:'header header' 'sidebar content';grid-template-columns:",";grid-template-rows:auto 1fr;height:100vh;width:100%;overflow:hidden;transition:grid-template-columns "," ease;"],({sidebarCollapsed:e})=>e?"auto 1fr":"240px 1fr",({theme:e})=>e.transitions.normal),zi=s.header.withConfig({displayName:"HeaderContainer",componentId:"sc-djltr5-1"})(["grid-area:header;background-color:",";border-bottom:1px solid ",";padding:",";z-index:",";"],({theme:e})=>e.colors.headerBackground,({theme:e})=>e.colors.border,({theme:e})=>e.spacing.md,({theme:e})=>e.zIndex.fixed),Fi=s.aside.withConfig({displayName:"SidebarContainer",componentId:"sc-djltr5-2"})(["grid-area:sidebar;background-color:",";border-right:1px solid ",";overflow-y:auto;transition:width "," ease;width:",";"],({theme:e})=>e.colors.sidebarBackground,({theme:e})=>e.colors.border,({theme:e})=>e.transitions.normal,({collapsed:e})=>e?"60px":"240px"),Bi=s.main.withConfig({displayName:"ContentContainer",componentId:"sc-djltr5-3"})(["grid-area:content;overflow-y:auto;padding:",";background-color:",";"],({theme:e})=>e.spacing.md,({theme:e})=>e.colors.background),qi=({header:e,sidebar:r,children:t,sidebarCollapsed:n=!1,className:i})=>o.jsxs(Ai,{sidebarCollapsed:n,className:i,children:[o.jsx(zi,{children:e}),o.jsx(Fi,{collapsed:n,children:r}),o.jsx(Bi,{children:t})]}),Hi=s.div.withConfig({displayName:"BuilderContainer",componentId:"sc-5duzr2-0"})(["background:#1a1a1a;border:1px solid #4b5563;border-radius:8px;padding:24px;margin-bottom:16px;"]),Ui=s.h3.withConfig({displayName:"SectionTitle",componentId:"sc-5duzr2-1"})(["color:#ffffff;font-size:1.1rem;font-weight:600;margin-bottom:16px;border-bottom:2px solid #dc2626;padding-bottom:8px;"]),Vi=s.div.withConfig({displayName:"MatrixGrid",componentId:"sc-5duzr2-2"})(["display:grid;grid-template-columns:repeat(auto-fit,minmax(250px,1fr));gap:20px;margin-bottom:20px;"]),Oe=s.div.withConfig({displayName:"ElementSection",componentId:"sc-5duzr2-3"})(["background:#262626;border:1px solid #4b5563;border-radius:6px;padding:16px;"]),je=s.h4.withConfig({displayName:"ElementTitle",componentId:"sc-5duzr2-4"})(["color:#ffffff;font-size:0.9rem;font-weight:600;margin-bottom:12px;text-transform:uppercase;letter-spacing:0.5px;"]),$e=s.select.withConfig({displayName:"Select",componentId:"sc-5duzr2-5"})(["width:100%;padding:8px 12px;background:#0f0f0f;border:1px solid #4b5563;border-radius:4px;color:#ffffff;font-size:0.9rem;&:focus{outline:none;border-color:#dc2626;box-shadow:0 0 0 2px rgba(220,38,38,0.2);}option{background:#0f0f0f;color:#ffffff;}"]),Yi=s.div.withConfig({displayName:"PreviewContainer",componentId:"sc-5duzr2-6"})(["background:#0f0f0f;border:1px solid #4b5563;border-radius:6px;padding:16px;margin-top:16px;"]),Wi=s.div.withConfig({displayName:"PreviewText",componentId:"sc-5duzr2-7"})(["color:#ffffff;font-family:'Monaco','Menlo','Ubuntu Mono',monospace;font-size:0.9rem;line-height:1.4;min-height:20px;"]),Yr=s.span.withConfig({displayName:"RequiredIndicator",componentId:"sc-5duzr2-8"})(["color:#dc2626;margin-left:4px;"]),Wr=s.span.withConfig({displayName:"OptionalIndicator",componentId:"sc-5duzr2-9"})(["color:#9ca3af;font-size:0.8rem;margin-left:4px;"]),Gi=({onSetupChange:e,initialComponents:r})=>{const[t,n]=b.useState({constant:(r==null?void 0:r.constant)||"",action:(r==null?void 0:r.action)||"None",variable:(r==null?void 0:r.variable)||"None",entry:(r==null?void 0:r.entry)||""});b.useEffect(()=>{t.constant&&t.entry&&e(t)},[t,e]);const i=(a,p)=>{n(f=>({...f,[a]:p}))},c=()=>{const{constant:a,action:p,variable:f,entry:u}=t;if(!a||!u)return"Select required elements to see setup preview...";let d=a;return p&&p!=="None"&&(d+=` → ${p}`),f&&f!=="None"&&(d+=` → ${f}`),d+=` [${u}]`,d};return o.jsxs(Hi,{children:[o.jsx(Ui,{children:"Setup Construction Matrix"}),o.jsxs(Vi,{children:[o.jsxs(Oe,{children:[o.jsxs(je,{children:["Constant Element",o.jsx(Yr,{children:"*"})]}),o.jsxs($e,{value:t.constant,onChange:a=>i("constant",a.target.value),children:[o.jsx("option",{value:"",children:"Select Constant"}),ve.constant.parentArrays.map(a=>o.jsx("option",{value:a,children:a},a)),ve.constant.fvgTypes.map(a=>o.jsx("option",{value:a,children:a},a))]})]}),o.jsxs(Oe,{children:[o.jsxs(je,{children:["Action Element",o.jsx(Wr,{children:"(optional)"})]}),o.jsxs($e,{value:t.action,onChange:a=>i("action",a.target.value),children:[o.jsx("option",{value:"None",children:"None"}),ve.action.liquidityEvents.map(a=>o.jsx("option",{value:a,children:a},a))]})]}),o.jsxs(Oe,{children:[o.jsxs(je,{children:["Variable Element",o.jsx(Wr,{children:"(optional)"})]}),o.jsxs($e,{value:t.variable,onChange:a=>i("variable",a.target.value),children:[o.jsx("option",{value:"None",children:"None"}),ve.variable.rdTypes.map(a=>o.jsx("option",{value:a,children:a},a))]})]}),o.jsxs(Oe,{children:[o.jsxs(je,{children:["Entry Method",o.jsx(Yr,{children:"*"})]}),o.jsxs($e,{value:t.entry,onChange:a=>i("entry",a.target.value),children:[o.jsx("option",{value:"",children:"Select Entry Method"}),ve.entry.methods.map(a=>o.jsx("option",{value:a,children:a},a))]})]})]}),o.jsxs(Yi,{children:[o.jsx(je,{children:"Setup Preview"}),o.jsx(Wi,{children:c()})]})]})},Ki=Gi,Gr=s.div.withConfig({displayName:"MetricsContainer",componentId:"sc-opkdti-0"})(["display:grid;grid-template-columns:repeat(auto-fit,minmax(200px,1fr));gap:",";"],({theme:e})=>e.spacing.md),Kr=s.div.withConfig({displayName:"MetricCard",componentId:"sc-opkdti-1"})(["background:",";border:1px solid ",";border-radius:",";padding:",";"],({theme:e})=>e.colors.surface,({theme:e})=>e.colors.border,({theme:e})=>e.borderRadius.md,({theme:e})=>e.spacing.md),Qr=s.div.withConfig({displayName:"MetricLabel",componentId:"sc-opkdti-2"})(["color:",";font-size:",";margin-bottom:",";"],({theme:e})=>e.colors.textSecondary,({theme:e})=>e.fontSizes.sm,({theme:e})=>e.spacing.xs),Xr=s.div.withConfig({displayName:"MetricValue",componentId:"sc-opkdti-3"})(["color:",";font-size:",";font-weight:600;"],({theme:e,positive:r,negative:t})=>r?e.colors.success:t?e.colors.danger:e.colors.textPrimary,({theme:e})=>e.fontSizes.lg),Qi=({metrics:e,isLoading:r})=>r?o.jsx(Gr,{children:Array.from({length:4}).map((t,n)=>o.jsxs(Kr,{children:[o.jsx(Qr,{children:"Loading..."}),o.jsx(Xr,{children:"--"})]},n))}):o.jsx(Gr,{children:e.map((t,n)=>o.jsxs(Kr,{children:[o.jsx(Qr,{children:t.label}),o.jsx(Xr,{positive:t.positive,negative:t.negative,children:t.value})]},n))}),Xi=Qi,Ji=s.div.withConfig({displayName:"AnalysisContainer",componentId:"sc-tp1ymt-0"})(["background:",";border:1px solid ",";border-radius:",";padding:",";"],({theme:e})=>e.colors.surface,({theme:e})=>e.colors.border,({theme:e})=>e.borderRadius.md,({theme:e})=>e.spacing.lg),Zi=s.h3.withConfig({displayName:"AnalysisTitle",componentId:"sc-tp1ymt-1"})(["color:",";font-size:",";font-weight:600;margin-bottom:",";"],({theme:e})=>e.colors.textPrimary,({theme:e})=>e.fontSizes.lg,({theme:e})=>e.spacing.md),ea=s.div.withConfig({displayName:"AnalysisContent",componentId:"sc-tp1ymt-2"})(["color:",";line-height:1.6;"],({theme:e})=>e.colors.textSecondary),ra=({title:e="Trade Analysis",children:r,isLoading:t})=>o.jsxs(Ji,{children:[o.jsx(Zi,{children:e}),o.jsx(ea,{children:t?o.jsx("div",{children:"Loading analysis..."}):r||o.jsx("div",{children:"No analysis data available"})})]}),ta=ra,T={f1Red:"#dc2626",f1RedDark:"#b91c1c",f1RedLight:"#ef4444",f1Blue:"#1e5bc6",f1BlueDark:"#1a4da8",f1BlueLight:"#4a7dd8",white:"#ffffff",black:"#000000",gray50:"#f9fafb",gray100:"#f3f4f6",gray200:"#e5e7eb",gray300:"#d1d5db",gray400:"#9ca3af",gray500:"#6b7280",gray600:"#4b5563",gray700:"#374151",gray800:"#1f2937",gray900:"#111827",green:"#4caf50",greenDark:"#388e3c",greenLight:"#81c784",yellow:"#ffeb3b",yellowDark:"#fbc02d",yellowLight:"#fff59d",red:"#f44336",redDark:"#d32f2f",redLight:"#e57373",blue:"#2196f3",blueDark:"#1976d2",blueLight:"#64b5f6",purple:"#9c27b0",purpleDark:"#7b1fa2",purpleLight:"#ba68c8",whiteTransparent10:"rgba(255, 255, 255, 0.1)",blackTransparent10:"rgba(0, 0, 0, 0.1)"},F={background:"#0f0f0f",surface:"#1a1a1a",cardBackground:"#1a1a1a",border:"#333333",divider:"rgba(255, 255, 255, 0.1)",textPrimary:"#ffffff",textSecondary:"#aaaaaa",textDisabled:"#666666",textInverse:"#1a1f2c",success:T.green,warning:T.yellow,error:T.red,info:T.blue,chartGrid:"rgba(255, 255, 255, 0.1)",chartLine:T.f1Red,profit:T.green,loss:T.red,neutral:T.gray400,tooltipBackground:"rgba(37, 42, 55, 0.9)",modalBackground:"rgba(26, 31, 44, 0.8)"},K={background:"#f5f5f5",surface:"#ffffff",cardBackground:"#ffffff",border:"#e0e0e0",divider:"rgba(0, 0, 0, 0.1)",textPrimary:"#333333",textSecondary:"#666666",textDisabled:"#999999",textInverse:"#ffffff",success:T.green,warning:T.yellow,error:T.red,info:T.blue,chartGrid:"rgba(0, 0, 0, 0.1)",chartLine:T.f1Red,profit:T.green,loss:T.red,neutral:T.gray400,tooltipBackground:"rgba(255, 255, 255, 0.9)",modalBackground:"rgba(255, 255, 255, 0.8)"},Q={xxs:"4px",xs:"8px",sm:"12px",md:"16px",lg:"24px",xl:"32px",xxl:"48px"},le={xs:"0.75rem",sm:"0.875rem",md:"1rem",lg:"1.125rem",xl:"1.25rem",xxl:"1.5rem",xxxl:"2.5rem",h1:"2.5rem",h2:"2rem",h3:"1.75rem",h4:"1.5rem",h5:"1.25rem",h6:"1rem"},Ae={light:300,regular:400,medium:500,semibold:600,bold:700},ze={tight:1.25,normal:1.5,relaxed:1.75},Fe={body:"'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif",heading:"'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif",mono:"SFMono-Regular, Menlo, Monaco, Consolas, 'Liberation Mono', 'Courier New', monospace"},Be={xs:"480px",sm:"640px",md:"768px",lg:"1024px",xl:"1280px"},qe={xs:"2px",sm:"4px",md:"6px",lg:"8px",xl:"12px",pill:"9999px",circle:"50%"},He={sm:"0 1px 3px rgba(0, 0, 0, 0.1)",md:"0 4px 6px rgba(0, 0, 0, 0.1)",lg:"0 10px 15px rgba(0, 0, 0, 0.1)"},Ue={fast:"0.1s",normal:"0.3s",slow:"0.5s"},Ve={base:1,overlay:10,modal:20,popover:30,tooltip:40,fixed:100},oa=s.div.withConfig({displayName:"HeaderContainer",componentId:"sc-e71xhh-0"})(["display:flex;justify-content:space-between;align-items:center;padding:"," 0;border-bottom:2px solid #4b5563;margin-bottom:",";",""],({theme:e})=>{var r;return((r=e.spacing)==null?void 0:r.lg)||"16px"},({theme:e})=>{var r;return((r=e.spacing)==null?void 0:r.lg)||"16px"},({$variant:e})=>{switch(e){case"dashboard":return s.css(["padding:24px 0;margin-bottom:24px;"]);case"form":return s.css(["padding:16px 0;margin-bottom:16px;"]);default:return s.css(["padding:20px 0;margin-bottom:20px;"])}}),sa=s.div.withConfig({displayName:"TitleSection",componentId:"sc-e71xhh-1"})(["display:flex;flex-direction:column;gap:",";"],({theme:e})=>{var r;return((r=e.spacing)==null?void 0:r.xs)||"4px"}),na=s.h1.withConfig({displayName:"MainTitle",componentId:"sc-e71xhh-2"})(["font-weight:700;color:",";margin:0;letter-spacing:-0.025em;text-transform:uppercase;font-family:'Inter',-apple-system,BlinkMacSystemFont,sans-serif;"," span{color:",";}"],({theme:e})=>{var r;return((r=e.colors)==null?void 0:r.textPrimary)||"#ffffff"},({$variant:e})=>{switch(e){case"dashboard":return s.css(["font-size:",";"],le.xxxl);case"analysis":return s.css(["font-size:",";"],le.xxl);case"form":return s.css(["font-size:",";"],le.xl);default:return s.css(["font-size:",";"],le.xxl)}},({theme:e})=>{var r;return((r=e.colors)==null?void 0:r.primary)||"#dc2626"}),ia=s.div.withConfig({displayName:"Subtitle",componentId:"sc-e71xhh-3"})(["font-size:",";color:#9ca3af;font-weight:500;text-transform:uppercase;letter-spacing:0.05em;"],le.sm),aa=s.div.withConfig({displayName:"ActionsSection",componentId:"sc-e71xhh-4"})(["display:flex;align-items:center;gap:",";flex-wrap:wrap;"],({theme:e})=>{var r;return((r=e.spacing)==null?void 0:r.md)||"12px"}),ca=s.div.withConfig({displayName:"StatusIndicator",componentId:"sc-e71xhh-5"})(["display:flex;align-items:center;gap:",";padding:"," ",";border-radius:",";font-size:",";font-weight:600;text-transform:uppercase;letter-spacing:0.05em;",""],({theme:e})=>{var r;return((r=e.spacing)==null?void 0:r.xs)||"4px"},({theme:e})=>{var r;return((r=e.spacing)==null?void 0:r.xs)||"4px"},({theme:e})=>{var r;return((r=e.spacing)==null?void 0:r.sm)||"8px"},({theme:e})=>{var r;return((r=e.borderRadius)==null?void 0:r.sm)||"4px"},({theme:e})=>{var r;return((r=e.fontSizes)==null?void 0:r.xs)||"0.75rem"},({$isLive:e,$variant:r})=>e?s.css(["background:rgba(220,38,38,0.1);border:1px solid #dc2626;color:#dc2626;"]):r==="active"?s.css(["background:rgba(34,197,94,0.1);border:1px solid #22c55e;color:#22c55e;"]):s.css(["background:rgba(156,163,175,0.1);border:1px solid #9ca3af;color:#9ca3af;"])),la=s.div.withConfig({displayName:"StatusDot",componentId:"sc-e71xhh-6"})(["width:6px;height:6px;border-radius:50%;background:",";",""],({$isLive:e})=>e?"#dc2626":"#22c55e",({$isLive:e})=>e&&s.css(["animation:pulse 2s infinite;@keyframes pulse{0%,100%{opacity:1;}50%{opacity:0.5;}}"])),da=s.button.withConfig({displayName:"RefreshButton",componentId:"sc-e71xhh-7"})(["padding:"," ",";background:transparent;color:",";border:1px solid #4b5563;border-radius:",";cursor:pointer;font-weight:500;font-size:",";transition:all 0.2s ease;min-width:100px;position:relative;&:hover{background:#4b5563;color:",";border-color:",";}&:disabled{opacity:0.6;cursor:not-allowed;}",""],({theme:e})=>{var r;return((r=e.spacing)==null?void 0:r.sm)||"8px"},({theme:e})=>{var r;return((r=e.spacing)==null?void 0:r.md)||"12px"},({theme:e})=>{var r;return((r=e.colors)==null?void 0:r.textSecondary)||"#9ca3af"},({theme:e})=>{var r;return((r=e.borderRadius)==null?void 0:r.sm)||"4px"},({theme:e})=>{var r;return((r=e.fontSizes)==null?void 0:r.sm)||"0.875rem"},({theme:e})=>{var r;return((r=e.colors)==null?void 0:r.textPrimary)||"#ffffff"},({theme:e})=>{var r;return((r=e.colors)==null?void 0:r.primary)||"#dc2626"},({$isRefreshing:e})=>e&&s.css(["&::after{content:'';position:absolute;top:50%;left:50%;width:16px;height:16px;margin:-8px 0 0 -8px;border:2px solid transparent;border-top:2px solid currentColor;border-radius:50%;animation:spin 1s linear infinite;}@keyframes spin{0%{transform:rotate(0deg);}100%{transform:rotate(360deg);}}"])),ua=s.div.withConfig({displayName:"CustomActions",componentId:"sc-e71xhh-8"})(["display:flex;align-items:center;gap:",";"],({theme:e})=>{var r;return((r=e.spacing)==null?void 0:r.sm)||"8px"}),pa=e=>{const{title:r,subtitle:t,isLive:n=!1,liveText:i="LIVE SESSION",statusText:c,onRefresh:a,isRefreshing:p=!1,actions:f,variant:u="dashboard",className:d}=e,m=n?i:c;return o.jsxs(oa,{$variant:u,className:d,children:[o.jsxs(sa,{children:[o.jsx(na,{$variant:u,children:u==="dashboard"?o.jsxs(o.Fragment,{children:["🏎️ ",r.replace("Trading","TRADING").replace("Dashboard","DASHBOARD")]}):r}),t&&o.jsx(ia,{children:t})]}),o.jsxs(aa,{children:[m&&o.jsxs(ca,{$isLive:n,$variant:!n&&c?"active":void 0,children:[o.jsx(la,{$isLive:n}),m]}),a&&o.jsx(da,{onClick:a,disabled:p,$isRefreshing:p,children:p?"Refreshing...":"Refresh"}),f&&o.jsx(ua,{children:f})]})]})},er=s.div.withConfig({displayName:"Container",componentId:"sc-vuv4tf-0"})(["display:flex;flex-direction:column;width:100%;max-width:",";margin:0 auto;min-height:",";"," "," "," ",""],({$maxWidth:e})=>typeof e=="number"?`${e}px`:e,({$variant:e})=>e==="dashboard"?"100vh":"auto",({$padding:e})=>{const r={sm:Q.sm,md:Q.md,lg:Q.lg,xl:Q.xl};return s.css(["padding:",";"],r[e||"lg"])},({$background:e,theme:r})=>{const t={default:r.colors.background,surface:r.colors.surface,elevated:r.colors.elevated};return s.css(["background:",";"],t[e||"default"])},({$variant:e})=>{switch(e){case"dashboard":return s.css(["gap:24px;padding-top:0;"]);case"form":return s.css(["gap:16px;max-width:800px;"]);case"analysis":return s.css(["gap:20px;max-width:1400px;"]);case"settings":return s.css(["gap:16px;max-width:1000px;"]);default:return s.css(["gap:16px;"])}},({$animated:e})=>e&&s.css(["transition:all 0.3s ease-in-out;&.entering{opacity:0;transform:translateY(20px);}&.entered{opacity:1;transform:translateY(0);}"])),fa=s.div.withConfig({displayName:"LoadingContainer",componentId:"sc-vuv4tf-1"})(["display:flex;flex-direction:column;align-items:center;justify-content:center;min-height:400px;gap:16px;color:#9ca3af;"]),ga=s.div.withConfig({displayName:"LoadingSpinner",componentId:"sc-vuv4tf-2"})(["width:40px;height:40px;border:3px solid #4b5563;border-top:3px solid #dc2626;border-radius:50%;animation:spin 1s linear infinite;@keyframes spin{0%{transform:rotate(0deg);}100%{transform:rotate(360deg);}}"]),ma=s.div.withConfig({displayName:"ErrorContainer",componentId:"sc-vuv4tf-3"})(["display:flex;flex-direction:column;align-items:center;justify-content:center;padding:40px;background:rgba(244,67,54,0.1);border:1px solid #f44336;border-radius:8px;color:#f44336;text-align:center;gap:16px;"]),ha=s.div.withConfig({displayName:"ErrorIcon",componentId:"sc-vuv4tf-4"})(["font-size:48px;opacity:0.8;"]),xa=s.div.withConfig({displayName:"ErrorMessage",componentId:"sc-vuv4tf-5"})(["font-size:16px;font-weight:500;"]),ba=s.button.withConfig({displayName:"RetryButton",componentId:"sc-vuv4tf-6"})(["padding:8px 16px;background:#f44336;color:white;border:none;border-radius:4px;cursor:pointer;font-weight:500;transition:background 0.2s ease;&:hover{background:#d32f2f;}"]),Jr=()=>o.jsxs(fa,{children:[o.jsx(ga,{}),o.jsx("div",{children:"Loading..."})]}),ya=({error:e,onRetry:r})=>o.jsxs(ma,{children:[o.jsx(ha,{children:"⚠️"}),o.jsx(xa,{children:e}),r&&o.jsx(ba,{onClick:r,children:"Retry"})]}),va=e=>{const{children:r,variant:t="dashboard",maxWidth:n="100%",padding:i="lg",isLoading:c=!1,error:a=null,loadingFallback:p,errorFallback:f,className:u,animated:d=!0,background:m="default"}=e,h={$variant:t,$maxWidth:n,$padding:i,$animated:d,$background:m};return a?o.jsx(er,{...h,className:u,children:f||o.jsx(ya,{error:a})}):c?o.jsx(er,{...h,className:u,children:p||o.jsx(Jr,{})}):o.jsx(er,{...h,className:u,children:o.jsx(b.Suspense,{fallback:p||o.jsx(Jr,{}),children:r})})},Sa=s.form.withConfig({displayName:"FormContainer",componentId:"sc-1gwzj6e-0"})(["display:flex;flex-direction:column;gap:",";background:",";border-radius:",";border:1px solid ",";position:relative;"," "," ",""],({theme:e})=>{var r;return((r=e.spacing)==null?void 0:r.md)||"12px"},({theme:e})=>{var r;return((r=e.colors)==null?void 0:r.surface)||"#1f2937"},({theme:e})=>{var r;return((r=e.borderRadius)==null?void 0:r.lg)||"8px"},({theme:e})=>{var r;return((r=e.colors)==null?void 0:r.border)||"#4b5563"},({$variant:e})=>{switch(e){case"quick":return s.css(["padding:",";max-width:600px;"],Q.lg);case"detailed":return s.css(["padding:",";max-width:800px;"],Q.xl);case"modal":return s.css(["padding:",";max-width:500px;margin:0 auto;"],Q.lg);case"inline":return s.css(["padding:",";background:transparent;border:none;"],Q.md);default:return s.css(["padding:",";"],Q.lg)}},({$showAccent:e,theme:r})=>{var t,n,i,c,a;return e&&s.css(["&::before{content:'';position:absolute;top:0;left:0;right:0;height:3px;background:linear-gradient( 90deg,",",",","," );border-radius:"," "," 0 0;}"],((t=r.colors)==null?void 0:t.primary)||"#dc2626",((n=r.colors)==null?void 0:n.primaryDark)||"#b91c1c",((i=r.colors)==null?void 0:i.primary)||"#dc2626",((c=r.borderRadius)==null?void 0:c.lg)||"8px",((a=r.borderRadius)==null?void 0:a.lg)||"8px")},({$disabled:e})=>e&&s.css(["opacity:0.6;pointer-events:none;"])),wa=s.div.withConfig({displayName:"FormHeader",componentId:"sc-1gwzj6e-1"})(["display:flex;flex-direction:column;gap:",";margin-bottom:",";"],({theme:e})=>{var r;return((r=e.spacing)==null?void 0:r.xs)||"4px"},({theme:e})=>{var r;return((r=e.spacing)==null?void 0:r.md)||"12px"}),Ca=s.h3.withConfig({displayName:"FormTitle",componentId:"sc-1gwzj6e-2"})(["font-size:",";font-weight:700;color:",";margin:0;text-transform:uppercase;letter-spacing:0.025em;display:flex;align-items:center;gap:",";"],({theme:e})=>{var r;return((r=e.fontSizes)==null?void 0:r.lg)||"1.125rem"},({theme:e})=>{var r;return((r=e.colors)==null?void 0:r.textPrimary)||"#ffffff"},({theme:e})=>{var r;return((r=e.spacing)==null?void 0:r.sm)||"8px"}),Ia=s.div.withConfig({displayName:"FormSubtitle",componentId:"sc-1gwzj6e-3"})(["font-size:",";color:",";font-weight:500;"],({theme:e})=>{var r;return((r=e.fontSizes)==null?void 0:r.sm)||"0.875rem"},({theme:e})=>{var r;return((r=e.colors)==null?void 0:r.textSecondary)||"#9ca3af"}),Ea=s.div.withConfig({displayName:"FormContent",componentId:"sc-1gwzj6e-4"})(["display:flex;flex-direction:column;gap:",";"],({theme:e})=>{var r;return((r=e.spacing)==null?void 0:r.md)||"12px"}),Zr=s.div.withConfig({displayName:"FormMessage",componentId:"sc-1gwzj6e-5"})(["padding:"," ",";border-radius:",";font-size:",";font-weight:500;display:flex;align-items:center;gap:",";",""],({theme:e})=>{var r;return((r=e.spacing)==null?void 0:r.sm)||"8px"},({theme:e})=>{var r;return((r=e.spacing)==null?void 0:r.md)||"12px"},({theme:e})=>{var r;return((r=e.borderRadius)==null?void 0:r.sm)||"4px"},({theme:e})=>{var r;return((r=e.fontSizes)==null?void 0:r.sm)||"0.875rem"},({theme:e})=>{var r;return((r=e.spacing)==null?void 0:r.xs)||"4px"},({$type:e})=>{switch(e){case"error":return s.css(["background:rgba(244,67,54,0.1);border:1px solid #f44336;color:#f44336;"]);case"success":return s.css(["background:rgba(34,197,94,0.1);border:1px solid #22c55e;color:#22c55e;"]);case"info":return s.css(["background:rgba(59,130,246,0.1);border:1px solid #3b82f6;color:#3b82f6;"])}}),Ta=s.div.withConfig({displayName:"LoadingOverlay",componentId:"sc-1gwzj6e-6"})(["position:absolute;top:0;left:0;right:0;bottom:0;background:rgba(0,0,0,0.5);display:flex;align-items:center;justify-content:center;border-radius:",";z-index:10;"],({theme:e})=>{var r;return((r=e.borderRadius)==null?void 0:r.lg)||"8px"}),ja=s.div.withConfig({displayName:"LoadingSpinner",componentId:"sc-1gwzj6e-7"})(["width:32px;height:32px;border:3px solid #4b5563;border-top:3px solid #dc2626;border-radius:50%;animation:spin 1s linear infinite;@keyframes spin{0%{transform:rotate(0deg);}100%{transform:rotate(360deg);}}"]),ka=s.div.withConfig({displayName:"AutoSaveIndicator",componentId:"sc-1gwzj6e-8"})(["position:absolute;top:8px;right:8px;font-size:",";color:",";opacity:",";transition:opacity 0.3s ease;"],({theme:e})=>{var r;return((r=e.fontSizes)==null?void 0:r.xs)||"0.75rem"},({theme:e})=>{var r;return((r=e.colors)==null?void 0:r.textSecondary)||"#9ca3af"},({$visible:e})=>e?1:0),Na=e=>{const{children:r,onSubmit:t,title:n,subtitle:i,isSubmitting:c=!1,error:a=null,success:p=null,variant:f="quick",showAccent:u=!0,className:d,disabled:m=!1,autoSave:h=!1,autoSaveInterval:y=3e4}=e,g=async x=>{x.preventDefault(),t&&!c&&!m&&await t(x)};return o.jsxs(Sa,{$variant:f,$showAccent:u,$disabled:m,className:d,onSubmit:g,noValidate:!0,children:[c&&o.jsx(Ta,{children:o.jsx(ja,{})}),h&&o.jsx(ka,{$visible:!c,children:"Auto-save enabled"}),(n||i)&&o.jsxs(wa,{children:[n&&o.jsx(Ca,{children:n}),i&&o.jsx(Ia,{children:i})]}),a&&o.jsxs(Zr,{$type:"error",children:["⚠️ ",a]}),p&&o.jsxs(Zr,{$type:"success",children:["✅ ",p]}),o.jsx(Ea,{children:r})]})},Ra=s.div.withConfig({displayName:"FieldContainer",componentId:"sc-sq94oz-0"})(["display:flex;flex-direction:column;gap:",";"],({$size:e})=>({sm:Q.xs,md:Q.sm,lg:Q.md})[e||"md"]),_a=s.label.withConfig({displayName:"Label",componentId:"sc-sq94oz-1"})(["font-size:",";font-weight:600;color:",";display:flex;align-items:center;gap:",";"," ",""],({theme:e})=>{var r;return((r=e.fontSizes)==null?void 0:r.sm)||"0.875rem"},({theme:e})=>{var r;return((r=e.colors)==null?void 0:r.textPrimary)||"#ffffff"},({theme:e})=>{var r;return((r=e.spacing)==null?void 0:r.xs)||"4px"},({$variant:e})=>{switch(e){case"trading":return s.css(["text-transform:uppercase;letter-spacing:0.025em;"]);case"analysis":return s.css(["font-weight:500;"]);default:return s.css([""])}},({$required:e,theme:r})=>{var t;return e&&s.css(["&::after{content:'*';color:",";margin-left:2px;}"],((t=r.colors)==null?void 0:t.primary)||"#dc2626")}),La=s.div.withConfig({displayName:"InputContainer",componentId:"sc-sq94oz-2"})(["position:relative;display:flex;align-items:center;",""],({$disabled:e})=>e&&s.css(["opacity:0.6;pointer-events:none;"])),dr=s.css(["width:100%;border:1px solid ",";border-radius:",";background:",";color:",";font-family:inherit;transition:all 0.2s ease;"," &:focus{outline:none;border-color:",";box-shadow:0 0 0 2px rgba(220,38,38,0.2);}&:disabled{background:#374151;color:#9ca3af;cursor:not-allowed;}&::placeholder{color:#6b7280;}"],({$hasError:e,theme:r})=>{var t;return e?((t=r.colors)==null?void 0:t.error)||"#f44336":"#4b5563"},({theme:e})=>{var r;return((r=e.borderRadius)==null?void 0:r.sm)||"4px"},({theme:e})=>{var r;return((r=e.colors)==null?void 0:r.background)||"#111827"},({theme:e})=>{var r;return((r=e.colors)==null?void 0:r.textPrimary)||"#ffffff"},({$size:e})=>({sm:s.css(["padding:"," ",";font-size:",";"],Q.xs,Q.sm,le.sm),md:s.css(["padding:"," ",";font-size:",";"],Q.sm,Q.md,le.md),lg:s.css(["padding:"," ",";font-size:",";"],Q.md,Q.lg,le.lg)})[e||"md"],({theme:e})=>{var r;return((r=e.colors)==null?void 0:r.primary)||"#dc2626"}),Ma=s.input.withConfig({displayName:"Input",componentId:"sc-sq94oz-3"})(["",""],dr),Pa=s.select.withConfig({displayName:"Select",componentId:"sc-sq94oz-4"})([""," cursor:pointer;option{background:",";color:",";}"],dr,({theme:e})=>{var r;return((r=e.colors)==null?void 0:r.background)||"#111827"},({theme:e})=>{var r;return((r=e.colors)==null?void 0:r.textPrimary)||"#ffffff"}),Da=s.textarea.withConfig({displayName:"TextArea",componentId:"sc-sq94oz-5"})([""," resize:vertical;min-height:80px;font-family:inherit;"],dr),Oa=s.div.withConfig({displayName:"PrefixContainer",componentId:"sc-sq94oz-6"})(["position:absolute;left:12px;display:flex;align-items:center;color:",";pointer-events:none;z-index:1;"],({theme:e})=>{var r;return((r=e.colors)==null?void 0:r.textSecondary)||"#9ca3af"}),$a=s.div.withConfig({displayName:"SuffixContainer",componentId:"sc-sq94oz-7"})(["position:absolute;right:12px;display:flex;align-items:center;color:",";"],({theme:e})=>{var r;return((r=e.colors)==null?void 0:r.textSecondary)||"#9ca3af"}),Aa=s.div.withConfig({displayName:"ErrorMessage",componentId:"sc-sq94oz-8"})(["font-size:",";color:",";font-weight:500;display:flex;align-items:center;gap:",";"],({theme:e})=>{var r;return((r=e.fontSizes)==null?void 0:r.xs)||"0.75rem"},({theme:e})=>{var r;return((r=e.colors)==null?void 0:r.error)||"#f44336"},({theme:e})=>{var r;return((r=e.spacing)==null?void 0:r.xs)||"4px"}),za=s.div.withConfig({displayName:"HelpText",componentId:"sc-sq94oz-9"})(["font-size:",";color:",";"],({theme:e})=>{var r;return((r=e.fontSizes)==null?void 0:r.xs)||"0.75rem"},({theme:e})=>{var r;return((r=e.colors)==null?void 0:r.textSecondary)||"#9ca3af"}),Fa=s.div.withConfig({displayName:"ValidationIndicator",componentId:"sc-sq94oz-10"})(["position:absolute;right:8px;display:flex;align-items:center;"," ",""],({$validating:e})=>e&&s.css(["&::after{content:'';width:12px;height:12px;border:2px solid #4b5563;border-top:2px solid #dc2626;border-radius:50%;animation:spin 1s linear infinite;}@keyframes spin{0%{transform:rotate(0deg);}100%{transform:rotate(360deg);}}"]),({$valid:e,$validating:r})=>!r&&s.css(["color:",";&::after{content:'","';}"],e?"#22c55e":"#f44336",e?"✓":"✗")),Ba=e=>{const{label:r,field:t,type:n="text",placeholder:i,required:c=!1,disabled:a=!1,helpText:p,options:f=[],inputProps:u={},className:d,size:m="md",variant:h="default",prefix:y,suffix:g}=e,x=!!(t.error&&t.touched),C=t.touched&&!t.validating,S=()=>{const w={id:u.id||r.toLowerCase().replace(/\s+/g,"-"),value:t.value,onChange:t.setValue,onBlur:()=>t.setTouched(!0),disabled:a,placeholder:i,$hasError:x,$size:m,...u};switch(n){case"select":return o.jsxs(Pa,{...w,children:[i&&o.jsx("option",{value:"",disabled:!0,children:i}),f.map(L=>o.jsx("option",{value:L.value,children:L.label},L.value))]});case"textarea":return o.jsx(Da,{...w});default:return o.jsx(Ma,{...w,type:n})}};return o.jsxs(Ra,{$size:m,className:d,children:[o.jsx(_a,{$required:c,$variant:h,htmlFor:u.id||r.toLowerCase().replace(/\s+/g,"-"),children:r}),o.jsxs(La,{$hasError:x,$disabled:a,children:[y&&o.jsx(Oa,{children:y}),S(),g&&o.jsx($a,{children:g}),C&&o.jsx(Fa,{$valid:t.valid,$validating:t.validating})]}),x&&o.jsxs(Aa,{children:["⚠️ ",t.error]}),p&&!x&&o.jsx(za,{children:p})]})},wt=(e=!1)=>{const[r,t]=b.useState(e),[n,i]=b.useState(null),[c,a]=b.useState(!1),p=b.useCallback(y=>{t(y),y&&(i(null),a(!1))},[]),f=b.useCallback(y=>{i(y),t(!1),a(!1)},[]),u=b.useCallback(()=>{i(null)},[]),d=b.useCallback(()=>{t(!1),i(null),a(!1)},[]),m=b.useCallback(async y=>{p(!0);try{const g=await y();return a(!0),t(!1),g}catch(g){const x=g instanceof Error?g.message:"An unexpected error occurred";throw f(x),g}},[p,f]),h=b.useCallback(y=>async(...g)=>{try{await m(()=>y(...g))}catch(x){console.error("Operation failed:",x)}},[m]);return{isLoading:r,error:n,isSuccess:c,isError:n!==null,setLoading:p,setError:f,clearError:u,reset:d,withLoading:m,withLoadingCallback:h}};function qa(e,r={}){const{fetchOnMount:t=!0,dependencies:n=[]}=r,[i,c]=b.useState({data:null,isLoading:!1,error:null,isInitialized:!1}),a=b.useCallback(async(...p)=>{c(f=>({...f,isLoading:!0,error:null}));try{const f=await e(...p);return c({data:f,isLoading:!1,error:null,isInitialized:!0}),f}catch(f){const u=f instanceof Error?f:new Error(String(f));throw c(d=>({...d,isLoading:!1,error:u,isInitialized:!0})),u}},[e]);return b.useEffect(()=>{t&&a()},[t,a,...n]),{...i,fetchData:a,refetch:()=>a()}}function Ha(e,r){const[t,n]=b.useState(e);return b.useEffect(()=>{const i=setTimeout(()=>{n(e)},r);return()=>{clearTimeout(i)}},[e,r]),t}function Ua(e={}){const{componentName:r,logToConsole:t=!0,reportToMonitoring:n=!0,onError:i}=e,[c,a]=b.useState(null),[p,f]=b.useState(!1),u=b.useCallback(h=>{if(a(h),f(!0),t){const y=r?`[${r}]`:"";console.error(`Error caught by useErrorHandler${y}:`,h)}i&&i(h)},[r,t,n,i]),d=b.useCallback(()=>{a(null),f(!1)},[]),m=b.useCallback(async h=>{try{return await h()}catch(y){u(y);return}},[u]);return b.useEffect(()=>()=>{a(null),f(!1)},[]),{error:c,hasError:p,handleError:u,resetError:d,tryExecute:m}}function sr(e,r){const t=()=>{if(typeof window>"u")return r;try{const a=window.localStorage.getItem(e);return a?JSON.parse(a):r}catch(a){return console.warn(`Error reading localStorage key "${e}":`,a),r}},[n,i]=b.useState(t),c=a=>{try{const p=a instanceof Function?a(n):a;i(p),typeof window<"u"&&window.localStorage.setItem(e,JSON.stringify(p))}catch(p){console.warn(`Error setting localStorage key "${e}":`,p)}};return b.useEffect(()=>{const a=p=>{p.key===e&&p.newValue&&i(JSON.parse(p.newValue))};return window.addEventListener("storage",a),()=>window.removeEventListener("storage",a)},[e]),[n,c]}function Va(e){const{totalItems:r,itemsPerPage:t=10,initialPage:n=1,persistKey:i}=e,[c,a]=i?sr(`${i}_page`,n):b.useState(n),[p,f]=i?sr(`${i}_itemsPerPage`,t):b.useState(t),u=b.useMemo(()=>Math.max(1,Math.ceil(r/p)),[r,p]),d=b.useMemo(()=>Math.min(Math.max(1,c),u),[c,u]);d!==c&&a(d);const m=(d-1)*p,h=Math.min(m+p-1,r-1),y=d>1,g=d<u,x=b.useMemo(()=>{const O=[];if(u<=5)for(let R=1;R<=u;R++)O.push(R);else{let R=Math.max(1,d-Math.floor(2.5));const j=Math.min(u,R+5-1);j===u&&(R=Math.max(1,j-5+1));for(let _=R;_<=j;_++)O.push(_)}return O},[d,u]),C=b.useCallback(()=>{g&&a(d+1)},[g,d,a]),S=b.useCallback(()=>{y&&a(d-1)},[y,d,a]),w=b.useCallback(A=>{const O=Math.min(Math.max(1,A),u);a(O)},[u,a]),L=b.useCallback(A=>{f(A),a(1)},[f,a]);return{currentPage:d,itemsPerPage:p,totalPages:u,hasPreviousPage:y,hasNextPage:g,startIndex:m,endIndex:h,pageRange:x,nextPage:C,previousPage:S,goToPage:w,setItemsPerPage:L}}const Ya=(e,r="$",t=!1)=>{const i=Math.abs(e).toLocaleString("en-US",{minimumFractionDigits:2,maximumFractionDigits:2});return e>0?t?`+${r}${i}`:`${r}${i}`:e<0?`-${r}${i}`:`${r}${i}`},Wa=(e,r={})=>{const{currency:t="$",showPositiveSign:n=!1,customAriaLabel:i}=r;return b.useMemo(()=>{if(e==null)return{formattedAmount:"",isProfit:!1,isLoss:!1,isNeutral:!1,isEmpty:!0,ariaLabel:i||"No profit/loss data available"};const c=e>0,a=e<0,p=e===0,f=Ya(e,t,n),u=`${c?"Profit":a?"Loss":"Breakeven"} of ${f}`;return{formattedAmount:f,isProfit:c,isLoss:a,isNeutral:p,isEmpty:!1,ariaLabel:i||u}},[e,t,n,i])},Ga=e=>e==null?!0:Array.isArray(e)?e.length===0:typeof e=="object"?Object.keys(e).length===0:typeof e=="string"?e.trim().length===0:!1,Ka=e=>{const{fetchData:r,initialData:t=null,fetchOnMount:n=!0,refreshInterval:i,isEmpty:c=Ga,transformError:a,dependencies:p=[]}=e,[f,u]=b.useState(t),[d,m]=b.useState(null),h=wt(),y=b.useMemo(()=>f===null||c(f),[f,c]),g=b.useCallback(async()=>{try{const w=await h.withLoading(r);u(w),m(new Date)}catch(w){const L=a&&w instanceof Error?a(w):w instanceof Error?w.message:"Failed to fetch data";h.setError(L),console.error("Data fetch failed:",w)}},[r,h,a]),x=b.useCallback(async()=>{await g()},[g]),C=b.useCallback(()=>{u(t),m(null),h.reset()},[t,h]),S=b.useCallback(w=>{u(w),m(new Date),h.clearError()},[h]);return b.useEffect(()=>{n&&g()},[n,g]),b.useEffect(()=>{p.length>0&&d!==null&&g()},p),b.useEffect(()=>{if(!i||i<=0)return;const w=setInterval(()=>{!h.isLoading&&!h.error&&g()},i);return()=>clearInterval(w)},[i,h.isLoading,h.error,g]),{data:f,isLoading:h.isLoading,error:h.error,isEmpty:y,isSuccess:h.isSuccess,isError:h.isError,lastFetched:d,refresh:x,clearError:h.clearError,reset:C,setData:S}},Qa=(e="en-US")=>b.useMemo(()=>({formatCurrency:(f,u={})=>{const{currency:d="USD",locale:m=e,minimumFractionDigits:h=2,maximumFractionDigits:y=2,showPositiveSign:g=!1}=u,C=new Intl.NumberFormat(m,{style:"currency",currency:d,minimumFractionDigits:h,maximumFractionDigits:y}).format(Math.abs(f));return f>0&&g?`+${C}`:f<0?`-${C}`:C},formatPercent:(f,u={})=>{const{locale:d=e,minimumFractionDigits:m=2,maximumFractionDigits:h=2,showPositiveSign:y=!1}=u,g=new Intl.NumberFormat(d,{style:"percent",minimumFractionDigits:m,maximumFractionDigits:h}),x=f>1?f/100:f,C=g.format(Math.abs(x));return x>0&&y?`+${C}`:x<0?`-${C}`:C},formatNumber:(f,u={})=>{const{locale:d=e,minimumFractionDigits:m=0,maximumFractionDigits:h=2,useGrouping:y=!0}=u;return new Intl.NumberFormat(d,{minimumFractionDigits:m,maximumFractionDigits:h,useGrouping:y}).format(f)},formatDate:(f,u="medium")=>{const d=typeof f=="string"?new Date(f):f;return new Intl.DateTimeFormat(e,{dateStyle:u}).format(d)},formatTime:(f,u="short")=>{const d=typeof f=="string"?new Date(f):f;return new Intl.DateTimeFormat(e,{timeStyle:u}).format(d)},formatRelativeTime:f=>{const u=typeof f=="string"?new Date(f):f,m=Math.floor((new Date().getTime()-u.getTime())/1e3);if(typeof Intl.RelativeTimeFormat<"u"){const x=new Intl.RelativeTimeFormat(e,{numeric:"auto"}),C=[{unit:"year",seconds:31536e3},{unit:"month",seconds:2592e3},{unit:"day",seconds:86400},{unit:"hour",seconds:3600},{unit:"minute",seconds:60},{unit:"second",seconds:1}];for(const S of C){const w=Math.floor(Math.abs(m)/S.seconds);if(w>=1)return x.format(m>0?-w:w,S.unit)}return x.format(0,"second")}const h=Math.abs(m),y=m<0;if(h<60)return y?"in a few seconds":"a few seconds ago";if(h<3600){const x=Math.floor(h/60);return y?`in ${x} minute${x>1?"s":""}`:`${x} minute${x>1?"s":""} ago`}if(h<86400){const x=Math.floor(h/3600);return y?`in ${x} hour${x>1?"s":""}`:`${x} hour${x>1?"s":""} ago`}const g=Math.floor(h/86400);return y?`in ${g} day${g>1?"s":""}`:`${g} day${g>1?"s":""} ago`}}),[e]),Ct={small:s.css(["font-size:",";padding:"," ",";"],({theme:e})=>{var r;return((r=e.fontSizes)==null?void 0:r.xs)||"12px"},({theme:e})=>{var r;return((r=e.spacing)==null?void 0:r.xxs)||"2px"},({theme:e})=>{var r;return((r=e.spacing)==null?void 0:r.xs)||"4px"}),medium:s.css(["font-size:",";padding:"," ",";"],({theme:e})=>{var r;return((r=e.fontSizes)==null?void 0:r.sm)||"14px"},({theme:e})=>{var r;return((r=e.spacing)==null?void 0:r.xs)||"4px"},({theme:e})=>{var r;return((r=e.spacing)==null?void 0:r.sm)||"8px"}),large:s.css(["font-size:",";padding:"," ",";"],({theme:e})=>{var r;return((r=e.fontSizes)==null?void 0:r.lg)||"18px"},({theme:e})=>{var r;return((r=e.spacing)==null?void 0:r.sm)||"8px"},({theme:e})=>{var r;return((r=e.spacing)==null?void 0:r.md)||"12px"})},It={profit:s.css(["color:",";background-color:",";border:1px solid ",";"],({theme:e})=>{var r,t;return((r=e.colors)==null?void 0:r.profit)||((t=e.colors)==null?void 0:t.success)||"#4caf50"},({theme:e})=>{var r;return(r=e.colors)!=null&&r.profit?`${e.colors.profit}15`:"rgba(76, 175, 80, 0.1)"},({theme:e})=>{var r;return(r=e.colors)!=null&&r.profit?`${e.colors.profit}30`:"rgba(76, 175, 80, 0.2)"}),loss:s.css(["color:",";background-color:",";border:1px solid ",";"],({theme:e})=>{var r,t;return((r=e.colors)==null?void 0:r.loss)||((t=e.colors)==null?void 0:t.error)||"#f44336"},({theme:e})=>{var r;return(r=e.colors)!=null&&r.loss?`${e.colors.loss}15`:"rgba(244, 67, 54, 0.1)"},({theme:e})=>{var r;return(r=e.colors)!=null&&r.loss?`${e.colors.loss}30`:"rgba(244, 67, 54, 0.2)"}),neutral:s.css(["color:",";background-color:",";border:1px solid ",";"],({theme:e})=>{var r,t;return((r=e.colors)==null?void 0:r.neutral)||((t=e.colors)==null?void 0:t.textSecondary)||"#757575"},({theme:e})=>{var r;return(r=e.colors)!=null&&r.neutral?`${e.colors.neutral}15`:"rgba(117, 117, 117, 0.1)"},({theme:e})=>{var r;return(r=e.colors)!=null&&r.neutral?`${e.colors.neutral}30`:"rgba(117, 117, 117, 0.2)"}),default:s.css(["color:",";background-color:transparent;border:1px solid transparent;"],({theme:e})=>{var r;return((r=e.colors)==null?void 0:r.textPrimary)||"#ffffff"})},Xa=s.css(["display:inline-flex;align-items:center;justify-content:flex-end;font-weight:",";font-family:",";transition:",";border-radius:",";&:hover{transform:translateY(-1px);box-shadow:",";}"],({theme:e})=>{var r;return((r=e.fontWeights)==null?void 0:r.semibold)||"600"},({theme:e})=>{var r;return((r=e.fontFamilies)==null?void 0:r.mono)||"monospace"},({theme:e})=>{var r;return((r=e.transitions)==null?void 0:r.fast)||"all 0.2s ease"},({theme:e})=>{var r;return((r=e.borderRadius)==null?void 0:r.sm)||"4px"},({theme:e})=>{var r;return((r=e.shadows)==null?void 0:r.sm)||"0 2px 4px rgba(0, 0, 0, 0.1)"}),Ja=s.css(["opacity:0.6;position:relative;&::after{content:'';position:absolute;top:0;left:0;right:0;bottom:0;background:linear-gradient(90deg,transparent,rgba(255,255,255,0.2),transparent);animation:shimmer 1.5s infinite;}@keyframes shimmer{0%{transform:translateX(-100%);}100%{transform:translateX(100%);}}"]),Za=e=>Ct[e],ec=(e,r,t)=>e?"profit":r?"loss":t?"neutral":"default",rc=e=>It[e],ur={name:"f1",colors:{primary:T.f1Red,primaryDark:T.f1RedDark,primaryLight:T.f1RedLight,secondary:T.f1Blue,secondaryDark:T.f1BlueDark,secondaryLight:T.f1BlueLight,accent:T.purple,accentDark:T.purpleDark,accentLight:T.purpleLight,success:F.success,warning:F.warning,error:F.error,danger:F.error,info:F.info,background:F.background,surface:F.surface,elevated:T.gray700,cardBackground:F.surface,border:F.border,divider:"rgba(255, 255, 255, 0.1)",textPrimary:F.textPrimary,textSecondary:F.textSecondary,textDisabled:F.textDisabled,textInverse:F.textInverse,chartGrid:F.chartGrid,chartLine:F.chartLine,chartAxis:T.gray400,chartTooltip:F.tooltipBackground,profit:F.profit,loss:F.loss,neutral:F.neutral,tabActive:T.f1Red,tabInactive:T.gray600,tooltipBackground:F.tooltipBackground,modalBackground:F.modalBackground,sidebarBackground:T.gray800,headerBackground:"rgba(0, 0, 0, 0.2)"},spacing:Q,breakpoints:Be,fontSizes:le,fontWeights:Ae,lineHeights:ze,fontFamilies:Fe,borderRadius:qe,shadows:He,transitions:Ue,zIndex:Ve},Et={name:"light",colors:{primary:T.f1Red,primaryDark:T.f1RedDark,primaryLight:T.f1RedLight,secondary:T.f1Blue,secondaryDark:T.f1BlueDark,secondaryLight:T.f1BlueLight,accent:T.purple,accentDark:T.purpleDark,accentLight:T.purpleLight,success:K.success,warning:K.warning,error:K.error,danger:K.error,info:K.info,background:K.background,surface:K.surface,elevated:T.gray100,cardBackground:K.surface,border:K.border,divider:T.blackTransparent10,textPrimary:K.textPrimary,textSecondary:K.textSecondary,textDisabled:K.textDisabled,textInverse:K.textInverse,chartGrid:K.chartGrid,chartLine:K.chartLine,chartAxis:T.gray600,chartTooltip:K.tooltipBackground,profit:K.profit,loss:K.loss,neutral:K.neutral,tabActive:T.f1Red,tabInactive:T.gray400,tooltipBackground:K.tooltipBackground,modalBackground:K.modalBackground,sidebarBackground:T.white,headerBackground:"rgba(0, 0, 0, 0.05)"},spacing:Q,breakpoints:Be,fontSizes:le,fontWeights:Ae,lineHeights:ze,fontFamilies:Fe,borderRadius:qe,shadows:He,transitions:Ue,zIndex:Ve},Tt={name:"dark",colors:{primary:T.f1Blue,primaryDark:T.f1BlueDark,primaryLight:T.f1BlueLight,secondary:T.f1Blue,secondaryDark:T.f1BlueDark,secondaryLight:T.f1BlueLight,accent:T.purple,accentDark:T.purpleDark,accentLight:T.purpleLight,success:F.success,warning:F.warning,error:F.error,danger:F.error,info:F.info,background:T.gray900,surface:T.gray800,elevated:T.gray700,cardBackground:T.gray800,border:T.gray700,divider:"rgba(255, 255, 255, 0.1)",textPrimary:T.white,textSecondary:T.gray300,textDisabled:T.gray500,textInverse:T.gray900,chartGrid:F.chartGrid,chartLine:T.f1Blue,chartAxis:T.gray400,chartTooltip:F.tooltipBackground,profit:F.profit,loss:F.loss,neutral:F.neutral,tabActive:T.f1Blue,tabInactive:T.gray600,tooltipBackground:"rgba(26, 32, 44, 0.9)",modalBackground:"rgba(26, 32, 44, 0.8)",sidebarBackground:T.gray900,headerBackground:"rgba(0, 0, 0, 0.3)"},spacing:Q,breakpoints:Be,fontSizes:le,fontWeights:Ae,lineHeights:ze,fontFamilies:Fe,borderRadius:qe,shadows:He,transitions:Ue,zIndex:Ve},tc=s.createGlobalStyle(["*,*::before,*::after{box-sizing:border-box;}html,body,div,span,applet,object,iframe,h1,h2,h3,h4,h5,h6,p,blockquote,pre,a,abbr,acronym,address,big,cite,code,del,dfn,em,img,ins,kbd,q,s,samp,small,strike,strong,sub,sup,tt,var,b,u,i,center,dl,dt,dd,ol,ul,li,fieldset,form,label,legend,table,caption,tbody,tfoot,thead,tr,th,td,article,aside,canvas,details,embed,figure,figcaption,footer,header,hgroup,menu,nav,output,ruby,section,summary,time,mark,audio,video{margin:0;padding:0;border:0;font-size:100%;font:inherit;vertical-align:baseline;}article,aside,details,figcaption,figure,footer,header,hgroup,menu,nav,section{display:block;}body{line-height:1.5;font-family:",";background-color:",";color:",";-webkit-font-smoothing:antialiased;-moz-osx-font-smoothing:grayscale;}ol,ul{list-style:none;}blockquote,q{quotes:none;}blockquote:before,blockquote:after,q:before,q:after{content:'';content:none;}table{border-collapse:collapse;border-spacing:0;}a{color:",";text-decoration:none;&:hover{text-decoration:underline;}}button,input,select,textarea{font-family:inherit;font-size:inherit;line-height:inherit;}::-webkit-scrollbar{width:8px;height:8px;}::-webkit-scrollbar-track{background:",";}::-webkit-scrollbar-thumb{background:",";border-radius:4px;}::-webkit-scrollbar-thumb:hover{background:",";}:focus{outline:2px solid ",";outline-offset:2px;}::selection{background-color:",";color:",";}"],({theme:e})=>e.fontFamilies.body,({theme:e})=>e.colors.background,({theme:e})=>e.colors.textPrimary,({theme:e})=>e.colors.primary,({theme:e})=>e.colors.background,({theme:e})=>e.colors.border,({theme:e})=>e.colors.primary,({theme:e})=>e.colors.primary,({theme:e})=>e.colors.primary,({theme:e})=>e.colors.textInverse),oc=tc,sc={f1:ur,light:Et,dark:Tt},pr=ur,rr=e=>sc[e]||pr,fr=b.createContext({theme:pr,setTheme:()=>{}}),nc=()=>b.useContext(fr),ic=({initialTheme:e=pr,persistTheme:r=!0,storageKey:t="adhd-dashboard-theme",children:n})=>{const[i,c]=b.useState(()=>{if(r&&typeof window<"u"){const u=window.localStorage.getItem(t);if(u)try{const d=rr(u);return d||JSON.parse(u)}catch(d){console.error("Failed to parse stored theme:",d)}}return typeof e=="string"?rr(e):e}),a=f=>{const u=typeof f=="string"?rr(f):f;c(u),r&&typeof window<"u"&&window.localStorage.setItem(t,u.name||JSON.stringify(u))},p=({children:f})=>o.jsxs(s.ThemeProvider,{theme:i,children:[o.jsx(oc,{}),f]});return o.jsx(fr.Provider,{value:{theme:i,setTheme:a},children:o.jsx(p,{children:n})})};function ac(e,r,t="StoreContext"){const n=b.createContext(void 0);n.displayName=t;const i=({children:u,initialState:d})=>{const[m,h]=b.useReducer(e,d||r),y=b.useMemo(()=>({state:m,dispatch:h}),[m]);return o.jsx(n.Provider,{value:y,children:u})};function c(){const u=b.useContext(n);if(u===void 0)throw new Error(`use${t} must be used within a ${t}Provider`);return u}function a(u){const{state:d}=c();return u(d)}function p(u){const{dispatch:d}=c();return b.useMemo(()=>(...m)=>{d(u(...m))},[d,u])}function f(u){const{dispatch:d}=c();return b.useMemo(()=>{const m={};for(const h in u)m[h]=(...y)=>{d(u[h](...y))};return m},[d,u])}return{Context:n,Provider:i,useStore:c,useSelector:a,useAction:p,useActions:f}}function cc(...e){const r=e.pop(),t=e;let n=null,i=null;return c=>{const a=t.map(p=>p(c));return(n===null||a.length!==n.length||a.some((p,f)=>p!==n[f]))&&(i=r(...a),n=a),i}}function lc(e,r){const{key:t,initialState:n,version:i=1,migrate:c,serialize:a=JSON.stringify,deserialize:p=JSON.parse,filter:f=S=>S,merge:u=(S,w)=>({...w,...S}),debug:d=!1}=r,m=()=>{try{const S=localStorage.getItem(t);if(S===null)return null;const{state:w,version:L}=p(S);return L!==i&&c?(d&&console.log(`Migrating state from version ${L} to ${i}`),c(w,L)):w}catch(S){return d&&console.error("Error loading state from local storage:",S),null}},h=S=>{try{const w=f(S),L=a({state:w,version:i});localStorage.setItem(t,L)}catch(w){d&&console.error("Error saving state to local storage:",w)}},y=()=>{try{localStorage.removeItem(t)}catch(S){d&&console.error("Error clearing state from local storage:",S)}},g=m(),x=g?u(g,n):n;return d&&g&&(console.log("Loaded persisted state:",g),console.log("Merged initial state:",x)),{reducer:(S,w)=>{const L=e(S,w);return h(L),L},initialState:x,clear:y}}function dc(e,r="$"){return`${r}${e.toFixed(2)}`}function uc(e,r=1){return`${(e*100).toFixed(r)}%`}function pc(e,r="short"){const t=typeof e=="string"?new Date(e):e;switch(r){case"medium":return t.toLocaleDateString("en-US",{year:"numeric",month:"short",day:"numeric"});case"long":return t.toLocaleDateString("en-US",{year:"numeric",month:"long",day:"numeric"});case"short":default:return t.toLocaleDateString("en-US",{year:"numeric",month:"2-digit",day:"2-digit"})}}function fc(e,r=50){return e.length<=r?e:`${e.substring(0,r-3)}...`}function gc(){return Math.random().toString(36).substring(2,9)}function mc(e,r){let t=null;return function(...n){const i=()=>{t=null,e(...n)};t&&clearTimeout(t),t=setTimeout(i,r)}}function hc(e,r){let t=!1;return function(...n){t||(e(...n),t=!0,setTimeout(()=>{t=!1},r))}}function xc(e={}){console.log("Monitoring service initialized",e)}function bc(e,r){console.error("Error captured by monitoring service:",e,r)}function yc(e){console.log("User set for monitoring service:",e)}function vc(e,r){const t=performance.now();return{name:e,startTime:t,finish:()=>{const i=performance.now()-t;console.log(`Transaction "${e}" finished in ${i.toFixed(2)}ms`,r)}}}const W={MODEL_TYPE:"model_type",WIN_LOSS:"win_loss",R_MULTIPLE:"r_multiple",DATE:"date",SESSION:"session",DIRECTION:"direction",MARKET:"market",ACHIEVED_PL:"achieved_pl",PATTERN_QUALITY_RATING:"pattern_quality_rating"};class Sc{constructor(){this.dbName="adhd-trading-dashboard",this.version=2,this.db=null,this.stores={trades:"trades",fvg_details:"trade_fvg_details",setups:"trade_setups",analysis:"trade_analysis",sessions:"trading_sessions"}}async initDB(){return this.db?this.db:new Promise((r,t)=>{const n=indexedDB.open(this.dbName,this.version);n.onupgradeneeded=i=>{var a;const c=i.target.result;if(!c.objectStoreNames.contains(this.stores.trades)){const p=c.createObjectStore(this.stores.trades,{keyPath:"id",autoIncrement:!0});p.createIndex(W.DATE,W.DATE,{unique:!1}),p.createIndex(W.MODEL_TYPE,W.MODEL_TYPE,{unique:!1}),p.createIndex(W.SESSION,W.SESSION,{unique:!1}),p.createIndex(W.WIN_LOSS,W.WIN_LOSS,{unique:!1}),p.createIndex(W.R_MULTIPLE,W.R_MULTIPLE,{unique:!1})}if(c.objectStoreNames.contains(this.stores.fvg_details)||c.createObjectStore(this.stores.fvg_details,{keyPath:"id",autoIncrement:!0}).createIndex("trade_id","trade_id",{unique:!1}),c.objectStoreNames.contains(this.stores.setups)||c.createObjectStore(this.stores.setups,{keyPath:"id",autoIncrement:!0}).createIndex("trade_id","trade_id",{unique:!1}),c.objectStoreNames.contains(this.stores.analysis)||c.createObjectStore(this.stores.analysis,{keyPath:"id",autoIncrement:!0}).createIndex("trade_id","trade_id",{unique:!1}),!c.objectStoreNames.contains(this.stores.sessions)){c.createObjectStore(this.stores.sessions,{keyPath:"id",autoIncrement:!0}).createIndex("name","name",{unique:!0});const f=[{name:"Pre-Market",start_time:"04:00:00",end_time:"09:30:00",description:"Pre-market trading hours"},{name:"NY Open",start_time:"09:30:00",end_time:"10:30:00",description:"New York opening hour"},{name:"10:50-11:10",start_time:"10:50:00",end_time:"11:10:00",description:"Mid-morning macro window"},{name:"11:50-12:10",start_time:"11:50:00",end_time:"12:10:00",description:"Pre-lunch macro window"},{name:"Lunch Macro",start_time:"12:00:00",end_time:"13:30:00",description:"Lunch time trading"},{name:"13:50-14:10",start_time:"13:50:00",end_time:"14:10:00",description:"Post-lunch macro window"},{name:"14:50-15:10",start_time:"14:50:00",end_time:"15:10:00",description:"Pre-close macro window"},{name:"15:15-15:45",start_time:"15:15:00",end_time:"15:45:00",description:"Late afternoon window"},{name:"MOC",start_time:"15:45:00",end_time:"16:00:00",description:"Market on close"},{name:"Post MOC",start_time:"16:00:00",end_time:"20:00:00",description:"After hours trading"}];(a=n.transaction)==null||a.addEventListener("complete",()=>{const d=c.transaction([this.stores.sessions],"readwrite").objectStore(this.stores.sessions);f.forEach(m=>d.add(m))})}},n.onsuccess=i=>{this.db=i.target.result,r(this.db)},n.onerror=i=>{console.error("Error opening IndexedDB:",i),t(new Error("Failed to open IndexedDB"))}})}async saveTradeWithDetails(r){try{const t=await this.initDB();return new Promise((n,i)=>{const c=t.transaction([this.stores.trades,this.stores.fvg_details,this.stores.setups,this.stores.analysis],"readwrite");c.onerror=u=>{console.error("Transaction error:",u),i(new Error("Failed to save trade with details"))};const a=c.objectStore(this.stores.trades),p={...r.trade,created_at:new Date().toISOString(),updated_at:new Date().toISOString()},f=a.add(p);f.onsuccess=()=>{const u=f.result,d=[];if(r.fvg_details){const m=c.objectStore(this.stores.fvg_details),h={...r.fvg_details,trade_id:u};d.push(new Promise((y,g)=>{const x=m.add(h);x.onsuccess=()=>y(),x.onerror=()=>g(new Error("Failed to save FVG details"))}))}if(r.setup){const m=c.objectStore(this.stores.setups),h={...r.setup,trade_id:u};d.push(new Promise((y,g)=>{const x=m.add(h);x.onsuccess=()=>y(),x.onerror=()=>g(new Error("Failed to save setup data"))}))}if(r.analysis){const m=c.objectStore(this.stores.analysis),h={...r.analysis,trade_id:u};d.push(new Promise((y,g)=>{const x=m.add(h);x.onsuccess=()=>y(),x.onerror=()=>g(new Error("Failed to save analysis data"))}))}c.oncomplete=()=>{n(u)}},f.onerror=u=>{console.error("Error saving trade:",u),i(new Error("Failed to save trade"))}})}catch(t){throw console.error("Error in saveTradeWithDetails:",t),new Error("Failed to save trade with details")}}async getTradeById(r){try{const t=await this.initDB();return new Promise((n,i)=>{const c=t.transaction([this.stores.trades,this.stores.fvg_details,this.stores.setups,this.stores.analysis],"readonly"),p=c.objectStore(this.stores.trades).get(r);p.onsuccess=()=>{const f=p.result;if(!f){n(null);return}const u={trade:f},h=c.objectStore(this.stores.fvg_details).index("trade_id").get(r);h.onsuccess=()=>{h.result&&(u.fvg_details=h.result);const x=c.objectStore(this.stores.setups).index("trade_id").get(r);x.onsuccess=()=>{x.result&&(u.setup=x.result);const w=c.objectStore(this.stores.analysis).index("trade_id").get(r);w.onsuccess=()=>{w.result&&(u.analysis=w.result),n(u)},w.onerror=L=>{console.error("Error getting analysis data:",L),n(u)}},x.onerror=C=>{console.error("Error getting setup data:",C),n(u)}},h.onerror=y=>{console.error("Error getting FVG details:",y),n(u)}},p.onerror=f=>{console.error("Error getting trade:",f),i(new Error("Failed to get trade"))}})}catch(t){return console.error("Error in getTradeById:",t),null}}async getPerformanceMetrics(){try{const r=await this.initDB();return new Promise((t,n)=>{const a=r.transaction([this.stores.trades],"readonly").objectStore(this.stores.trades).getAll();a.onsuccess=()=>{const p=a.result;if(p.length===0){t({totalTrades:0,winningTrades:0,losingTrades:0,winRate:0,profitFactor:0,averageWin:0,averageLoss:0,largestWin:0,largestLoss:0,totalPnl:0,maxDrawdown:0,maxDrawdownPercent:0,sharpeRatio:0,sortinoRatio:0,calmarRatio:0,averageRMultiple:0,expectancy:0,sqn:0,period:"all",startDate:"",endDate:""});return}const f=p.length,u=p.filter(D=>D[W.WIN_LOSS]==="Win").length,d=p.filter(D=>D[W.WIN_LOSS]==="Loss").length,m=f>0?u/f*100:0,h=p.filter(D=>D.achieved_pl!==void 0).map(D=>D.achieved_pl),y=h.reduce((D,X)=>D+X,0),g=h.filter(D=>D>0),x=h.filter(D=>D<0),C=g.length>0?g.reduce((D,X)=>D+X,0)/g.length:0,S=x.length>0?Math.abs(x.reduce((D,X)=>D+X,0)/x.length):0,w=g.length>0?Math.max(...g):0,L=x.length>0?Math.abs(Math.min(...x)):0,A=g.reduce((D,X)=>D+X,0),O=Math.abs(x.reduce((D,X)=>D+X,0)),R=O>0?A/O:0,j=p.filter(D=>D[W.R_MULTIPLE]!==void 0).map(D=>D[W.R_MULTIPLE]),_=j.length>0?j.reduce((D,X)=>D+X,0)/j.length:0,k=_*(m/100);let U=0,V=0,N=0;for(const D of p)if(D.achieved_pl!==void 0){U+=D.achieved_pl,U>V&&(V=U);const X=V-U;X>N&&(N=X)}const H=V>0?N/V*100:0,ee=j.length>0?Math.sqrt(j.length)*_/Math.sqrt(j.reduce((D,X)=>D+Math.pow(X-_,2),0)/j.length):0,te=p.map(D=>D.date).sort(),de=te.length>0?te[0]:"",oe=te.length>0?te[te.length-1]:"";t({totalTrades:f,winningTrades:u,losingTrades:d,winRate:m,profitFactor:R,averageWin:C,averageLoss:S,largestWin:w,largestLoss:L,totalPnl:y,maxDrawdown:N,maxDrawdownPercent:H,sharpeRatio:0,sortinoRatio:0,calmarRatio:0,averageRMultiple:_,expectancy:k,sqn:ee,period:"all",startDate:de,endDate:oe})},a.onerror=p=>{console.error("Error getting performance metrics:",p),n(new Error("Failed to get performance metrics"))}})}catch(r){throw console.error("Error in getPerformanceMetrics:",r),new Error("Failed to get performance metrics")}}async filterTrades(r){try{const t=await this.initDB();return new Promise((n,i)=>{const c=t.transaction([this.stores.trades,this.stores.fvg_details,this.stores.setups,this.stores.analysis],"readonly"),p=c.objectStore(this.stores.trades).getAll();p.onsuccess=async()=>{let f=p.result;r.dateFrom&&(f=f.filter(d=>d.date>=r.dateFrom)),r.dateTo&&(f=f.filter(d=>d.date<=r.dateTo)),r.model_type&&(f=f.filter(d=>d[W.MODEL_TYPE]===r.model_type)),r.session&&(f=f.filter(d=>d[W.SESSION]===r.session)),r.direction&&(f=f.filter(d=>d[W.DIRECTION]===r.direction)),r.win_loss&&(f=f.filter(d=>d[W.WIN_LOSS]===r.win_loss)),r.market&&(f=f.filter(d=>d[W.MARKET]===r.market)),r.min_r_multiple!==void 0&&(f=f.filter(d=>d[W.R_MULTIPLE]!==void 0&&d[W.R_MULTIPLE]>=r.min_r_multiple)),r.max_r_multiple!==void 0&&(f=f.filter(d=>d[W.R_MULTIPLE]!==void 0&&d[W.R_MULTIPLE]<=r.max_r_multiple)),r.min_pattern_quality!==void 0&&(f=f.filter(d=>d[W.PATTERN_QUALITY_RATING]!==void 0&&d[W.PATTERN_QUALITY_RATING]>=r.min_pattern_quality)),r.max_pattern_quality!==void 0&&(f=f.filter(d=>d[W.PATTERN_QUALITY_RATING]!==void 0&&d[W.PATTERN_QUALITY_RATING]<=r.max_pattern_quality));const u=[];for(const d of f){const m={trade:d},g=c.objectStore(this.stores.fvg_details).index("trade_id").get(d.id);await new Promise(O=>{g.onsuccess=()=>{g.result&&(m.fvg_details=g.result),O()},g.onerror=()=>O()});const S=c.objectStore(this.stores.setups).index("trade_id").get(d.id);await new Promise(O=>{S.onsuccess=()=>{S.result&&(m.setup=S.result),O()},S.onerror=()=>O()});const A=c.objectStore(this.stores.analysis).index("trade_id").get(d.id);await new Promise(O=>{A.onsuccess=()=>{A.result&&(m.analysis=A.result),O()},A.onerror=()=>O()}),u.push(m)}n(u)},p.onerror=f=>{console.error("Error filtering trades:",f),i(new Error("Failed to filter trades"))}})}catch(t){throw console.error("Error in filterTrades:",t),new Error("Failed to filter trades")}}async getAllTrades(){try{return await this.filterTrades({})}catch(r){return console.error("Error in getAllTrades:",r),[]}}async deleteTrade(r){try{const t=await this.initDB();return new Promise((n,i)=>{const c=t.transaction([this.stores.trades,this.stores.fvg_details,this.stores.setups,this.stores.analysis],"readwrite");c.onerror=S=>{console.error("Transaction error:",S),i(new Error("Failed to delete trade"))};const f=c.objectStore(this.stores.fvg_details).index("trade_id").openCursor(IDBKeyRange.only(r));f.onsuccess=S=>{const w=S.target.result;w&&(w.delete(),w.continue())};const m=c.objectStore(this.stores.setups).index("trade_id").openCursor(IDBKeyRange.only(r));m.onsuccess=S=>{const w=S.target.result;w&&(w.delete(),w.continue())};const g=c.objectStore(this.stores.analysis).index("trade_id").openCursor(IDBKeyRange.only(r));g.onsuccess=S=>{const w=S.target.result;w&&(w.delete(),w.continue())};const C=c.objectStore(this.stores.trades).delete(r);c.oncomplete=()=>{n()},C.onerror=S=>{console.error("Error deleting trade:",S),i(new Error("Failed to delete trade"))}})}catch(t){throw console.error("Error in deleteTrade:",t),new Error("Failed to delete trade")}}async updateTradeWithDetails(r,t){try{const n=await this.initDB();return new Promise((i,c)=>{const a=n.transaction([this.stores.trades,this.stores.fvg_details,this.stores.setups,this.stores.analysis],"readwrite");a.onerror=d=>{console.error("Transaction error:",d),c(new Error("Failed to update trade"))};const p=a.objectStore(this.stores.trades),f={...t.trade,id:r,updated_at:new Date().toISOString()},u=p.put(f);u.onsuccess=()=>{if(t.fvg_details){const d=a.objectStore(this.stores.fvg_details),m={...t.fvg_details,trade_id:r};d.put(m)}if(t.setup){const d=a.objectStore(this.stores.setups),m={...t.setup,trade_id:r};d.put(m)}if(t.analysis){const d=a.objectStore(this.stores.analysis),m={...t.analysis,trade_id:r};d.put(m)}},a.oncomplete=()=>{i()},u.onerror=d=>{console.error("Error updating trade:",d),c(new Error("Failed to update trade"))}})}catch(n){throw console.error("Error in updateTradeWithDetails:",n),new Error("Failed to update trade")}}}const jt=new Sc,wc=jt,Cc=jt;exports.AppErrorBoundary=Os;exports.Badge=Ne;exports.Button=ie;exports.Card=ct;exports.DashboardSection=$i;exports.DashboardTemplate=qi;exports.DataCard=Ni;exports.EmptyState=tr;exports.EnhancedFormField=Js;exports.ErrorBoundary=dt;exports.F1Container=va;exports.F1Form=Na;exports.F1FormField=Ba;exports.F1Header=pa;exports.FeatureErrorBoundary=$s;exports.FormField=fn;exports.HierarchicalSessionSelector=oi;exports.Input=xe;exports.LoadingCell=ss;exports.LoadingPlaceholder=at;exports.LoadingSpinner=ds;exports.MacroPeriodType=M;exports.Modal=Cn;exports.OrderSide=ot;exports.OrderStatus=st;exports.OrderType=tt;exports.SETUP_ELEMENTS=ve;exports.Select=Se;exports.SelectDropdown=es;exports.SessionType=G;exports.SessionUtils=re;exports.SetupBuilder=Ki;exports.SortableTable=ln;exports.StatusIndicator=zo;exports.TRADE_COLUMN_IDS=E;exports.TabPanel=Hs;exports.Table=Fn;exports.Tag=Uo;exports.ThemeContext=fr;exports.ThemeProvider=ic;exports.TimeInForce=nt;exports.TimePicker=Ko;exports.TradeAnalysis=ta;exports.TradeDirection=et;exports.TradeMetrics=Xi;exports.TradeStatus=rt;exports.TradeTable=ji;exports.TradeTableFilters=St;exports.TradeTableRow=vt;exports.UnifiedErrorBoundary=nr;exports.VALID_TRADING_MODELS=eo;exports.baseColors=T;exports.borderRadius=qe;exports.breakpoints=Be;exports.captureError=bc;exports.createSelector=cc;exports.createStoreContext=ac;exports.darkModeColors=F;exports.darkTheme=Tt;exports.debounce=mc;exports.f1Theme=ur;exports.fontFamilies=Fe;exports.fontSizes=le;exports.fontWeights=Ae;exports.formatCurrency=dc;exports.formatDate=pc;exports.formatPercentage=uc;exports.formatTime=or;exports.generateId=gc;exports.getCompactTradeTableColumns=bt;exports.getPerformanceTradeTableColumns=yt;exports.getProfitLossColors=rc;exports.getProfitLossSize=Za;exports.getProfitLossVariant=ec;exports.getTradeTableColumns=xt;exports.initMonitoring=xc;exports.lightModeColors=K;exports.lightTheme=Et;exports.lineHeights=ze;exports.persistState=lc;exports.profitLossBaseStyles=Xa;exports.profitLossColors=It;exports.profitLossLoadingStyles=Ja;exports.profitLossSizes=Ct;exports.setUser=yc;exports.shadows=He;exports.sortFunctions=Zs;exports.spacing=Q;exports.startTransaction=vc;exports.throttle=hc;exports.tradeStorage=Cc;exports.tradeStorageService=wc;exports.transitions=Ue;exports.truncateText=fc;exports.useAsyncData=qa;exports.useDataFormatting=Qa;exports.useDataSection=Ka;exports.useDebounce=Ha;exports.useErrorHandler=Ua;exports.useFormField=pt;exports.useLoadingState=wt;exports.useLocalStorage=sr;exports.usePagination=Va;exports.useProfitLossFormatting=Wa;exports.useSessionSelection=gt;exports.useSortableTable=ft;exports.useTheme=nc;exports.validationRules=ut;exports.zIndex=Ve;
