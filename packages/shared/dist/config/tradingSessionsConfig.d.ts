import { TradingSession, MacroPeriod, SessionType, MacroPeriodType, SessionHierarchy } from '../types/tradingSessions';
/**
 * Macro Period Definitions
 */
export declare const MACRO_PERIODS: Record<MacroPeriodType, Omit<MacroPeriod, 'id'>>;
/**
 * Trading Session Definitions
 */
/**
 * Build session hierarchy with overlapping macro support
 */
export declare const buildSessionHierarchy: () => SessionHierarchy;
/**
 * Build session hierarchy from database sessions (legacy support)
 */
export declare const buildSessionHierarchyFromDatabase: (dbSessions: Array<{
    name: string;
    start_time: string;
    end_time: string;
    description: string;
}>) => SessionHierarchy;
export declare const TRADING_SESSIONS: Record<SessionType, Omit<TradingSession, 'id'>>;
//# sourceMappingURL=tradingSessionsConfig.d.ts.map