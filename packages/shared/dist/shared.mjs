import we, { useState as Q, useRef as ot, Component as zt, use<PERSON>emo as U, use<PERSON><PERSON>back as F, useEffect as ue, Suspense as Ft, createContext as nt, useContext as st, useReducer as Bt } from "react";
import a, { css as g, keyframes as Ee, createGlobalStyle as qt, ThemeProvider as Ht } from "styled-components";
import { createPortal as Vt } from "react-dom";
var Ut = /* @__PURE__ */ ((e) => (e.LONG = "LONG", e.SHORT = "SHORT", e))(Ut || {}), Yt = /* @__PURE__ */ ((e) => (e.OPEN = "OPEN", e.CLOSED = "CLOSED", e.CANCELED = "CANCELED", e.REJECTED = "REJECTED", e.PENDING = "PENDING", e))(Yt || {}), Wt = /* @__PURE__ */ ((e) => (e.MARKET = "MARKET", e.LIMIT = "LIMIT", e.STOP = "STOP", e.STOP_LIMIT = "STOP_LIMIT", e))(Wt || {}), Gt = /* @__PURE__ */ ((e) => (e.BUY = "BUY", e.SELL = "SELL", e))(Gt || {}), Kt = /* @__PURE__ */ ((e) => (e.PENDING = "PENDING", e.FILLED = "FILLED", e.PARTIALLY_FILLED = "PARTIALLY_FILLED", e.CANCELED = "CANCELED", e.REJECTED = "REJECTED", e))(Kt || {}), Qt = /* @__PURE__ */ ((e) => (e.GTC = "GTC", e.IOC = "IOC", e.FOK = "FOK", e.DAY = "DAY", e))(Qt || {}), X = /* @__PURE__ */ ((e) => (e.LONDON = "london", e.NEW_YORK_AM = "new-york-am", e.NEW_YORK_PM = "new-york-pm", e.ASIA = "asia", e.PRE_MARKET = "pre-market", e.AFTER_HOURS = "after-hours", e.OVERNIGHT = "overnight", e))(X || {}), M = /* @__PURE__ */ ((e) => (e.MORNING_BREAKOUT = "morning-breakout", e.MID_MORNING_REVERSION = "mid-morning-reversion", e.PRE_LUNCH = "pre-lunch", e.LUNCH_MACRO_EXTENDED = "lunch-macro-extended", e.LUNCH_MACRO = "lunch-macro", e.POST_LUNCH = "post-lunch", e.PRE_CLOSE = "pre-close", e.POWER_HOUR = "power-hour", e.MOC = "moc", e.LONDON_OPEN = "london-open", e.LONDON_NY_OVERLAP = "london-ny-overlap", e.CUSTOM = "custom", e))(M || {});
const Te = {
  constant: {
    parentArrays: ["NWOG", "Old-NWOG", "NDOG", "Old-NDOG", "Monthly-FVG", "Weekly-FVG", "Daily-FVG", "15min-Top/Bottom-FVG", "1h-Top/Bottom-FVG"],
    fvgTypes: ["Strong-FVG", "AM-FPFVG", "PM-FPFVG", "Asia-FPFVG", "Premarket-FPFVG", "MNOR-FVG", "Macro-FVG", "News-FVG", "Top/Bottom-FVG"]
  },
  action: {
    liquidityEvents: ["None", "London-H/L", "Premarket-H/L", "09:30-Opening-Range-H/L", "Lunch-H/L", "Prev-Day-H/L", "Prev-Week-H/L", "Monthly-H/L", "Macro-H/L"]
  },
  variable: {
    rdTypes: ["None", "True-RD", "IMM-RD", "Dispersed-RD", "Wide-Gap-RD"]
  },
  entry: {
    methods: ["Simple-Entry", "Complex-Entry", "Complex-Entry/Mini"]
  }
}, $a = ["RD-Cont", "FVG-RD", "Combined"];
var o = {}, Xt = {
  get exports() {
    return o;
  },
  set exports(e) {
    o = e;
  }
}, Ne = {};
/**
 * @license React
 * react-jsx-runtime.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */
var kr;
function Jt() {
  if (kr)
    return Ne;
  kr = 1;
  var e = we, r = Symbol.for("react.element"), t = Symbol.for("react.fragment"), n = Object.prototype.hasOwnProperty, s = e.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ReactCurrentOwner, c = { key: !0, ref: !0, __self: !0, __source: !0 };
  function i(u, f, p) {
    var d, h = {}, x = null, y = null;
    p !== void 0 && (x = "" + p), f.key !== void 0 && (x = "" + f.key), f.ref !== void 0 && (y = f.ref);
    for (d in f)
      n.call(f, d) && !c.hasOwnProperty(d) && (h[d] = f[d]);
    if (u && u.defaultProps)
      for (d in f = u.defaultProps, f)
        h[d] === void 0 && (h[d] = f[d]);
    return { $$typeof: r, type: u, key: x, ref: y, props: h, _owner: s.current };
  }
  return Ne.Fragment = t, Ne.jsx = i, Ne.jsxs = i, Ne;
}
var ke = {};
/**
 * @license React
 * react-jsx-runtime.development.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */
var Rr;
function Zt() {
  return Rr || (Rr = 1, process.env.NODE_ENV !== "production" && function() {
    var e = we, r = Symbol.for("react.element"), t = Symbol.for("react.portal"), n = Symbol.for("react.fragment"), s = Symbol.for("react.strict_mode"), c = Symbol.for("react.profiler"), i = Symbol.for("react.provider"), u = Symbol.for("react.context"), f = Symbol.for("react.forward_ref"), p = Symbol.for("react.suspense"), d = Symbol.for("react.suspense_list"), h = Symbol.for("react.memo"), x = Symbol.for("react.lazy"), y = Symbol.for("react.offscreen"), m = Symbol.iterator, b = "@@iterator";
    function C(l) {
      if (l === null || typeof l != "object")
        return null;
      var v = m && l[m] || l[b];
      return typeof v == "function" ? v : null;
    }
    var w = e.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED;
    function S(l) {
      {
        for (var v = arguments.length, I = new Array(v > 1 ? v - 1 : 0), P = 1; P < v; P++)
          I[P - 1] = arguments[P];
        L("error", l, I);
      }
    }
    function L(l, v, I) {
      {
        var P = w.ReactDebugCurrentFrame, B = P.getStackAddendum();
        B !== "" && (v += "%s", I = I.concat([B]));
        var G = I.map(function(z) {
          return String(z);
        });
        G.unshift("Warning: " + v), Function.prototype.apply.call(console[l], console, G);
      }
    }
    var A = !1, O = !1, R = !1, T = !1, _ = !1, N;
    N = Symbol.for("react.module.reference");
    function Y(l) {
      return !!(typeof l == "string" || typeof l == "function" || l === n || l === c || _ || l === s || l === p || l === d || T || l === y || A || O || R || typeof l == "object" && l !== null && (l.$$typeof === x || l.$$typeof === h || l.$$typeof === i || l.$$typeof === u || l.$$typeof === f || // This needs to include all possible module reference object
      // types supported by any Flight configuration anywhere since
      // we don't know which Flight build this will end up being used
      // with.
      l.$$typeof === N || l.getModuleId !== void 0));
    }
    function W(l, v, I) {
      var P = l.displayName;
      if (P)
        return P;
      var B = v.displayName || v.name || "";
      return B !== "" ? I + "(" + B + ")" : I;
    }
    function k(l) {
      return l.displayName || "Context";
    }
    function V(l) {
      if (l == null)
        return null;
      if (typeof l.tag == "number" && S("Received an unexpected object in getComponentNameFromType(). This is likely a bug in React. Please file an issue."), typeof l == "function")
        return l.displayName || l.name || null;
      if (typeof l == "string")
        return l;
      switch (l) {
        case n:
          return "Fragment";
        case t:
          return "Portal";
        case c:
          return "Profiler";
        case s:
          return "StrictMode";
        case p:
          return "Suspense";
        case d:
          return "SuspenseList";
      }
      if (typeof l == "object")
        switch (l.$$typeof) {
          case u:
            var v = l;
            return k(v) + ".Consumer";
          case i:
            var I = l;
            return k(I._context) + ".Provider";
          case f:
            return W(l, l.render, "ForwardRef");
          case h:
            var P = l.displayName || null;
            return P !== null ? P : V(l.type) || "Memo";
          case x: {
            var B = l, G = B._payload, z = B._init;
            try {
              return V(z(G));
            } catch {
              return null;
            }
          }
        }
      return null;
    }
    var oe = Object.assign, ne = 0, fe, se, D, J, ve, H, he;
    function gr() {
    }
    gr.__reactDisabledLog = !0;
    function gt() {
      {
        if (ne === 0) {
          fe = console.log, se = console.info, D = console.warn, J = console.error, ve = console.group, H = console.groupCollapsed, he = console.groupEnd;
          var l = {
            configurable: !0,
            enumerable: !0,
            value: gr,
            writable: !0
          };
          Object.defineProperties(console, {
            info: l,
            log: l,
            warn: l,
            error: l,
            group: l,
            groupCollapsed: l,
            groupEnd: l
          });
        }
        ne++;
      }
    }
    function mt() {
      {
        if (ne--, ne === 0) {
          var l = {
            configurable: !0,
            enumerable: !0,
            writable: !0
          };
          Object.defineProperties(console, {
            log: oe({}, l, {
              value: fe
            }),
            info: oe({}, l, {
              value: se
            }),
            warn: oe({}, l, {
              value: D
            }),
            error: oe({}, l, {
              value: J
            }),
            group: oe({}, l, {
              value: ve
            }),
            groupCollapsed: oe({}, l, {
              value: H
            }),
            groupEnd: oe({}, l, {
              value: he
            })
          });
        }
        ne < 0 && S("disabledDepth fell below zero. This is a bug in React. Please file an issue.");
      }
    }
    var Ve = w.ReactCurrentDispatcher, Ue;
    function De(l, v, I) {
      {
        if (Ue === void 0)
          try {
            throw Error();
          } catch (B) {
            var P = B.stack.trim().match(/\n( *(at )?)/);
            Ue = P && P[1] || "";
          }
        return `
` + Ue + l;
      }
    }
    var Ye = !1, Oe;
    {
      var ht = typeof WeakMap == "function" ? WeakMap : Map;
      Oe = new ht();
    }
    function mr(l, v) {
      if (!l || Ye)
        return "";
      {
        var I = Oe.get(l);
        if (I !== void 0)
          return I;
      }
      var P;
      Ye = !0;
      var B = Error.prepareStackTrace;
      Error.prepareStackTrace = void 0;
      var G;
      G = Ve.current, Ve.current = null, gt();
      try {
        if (v) {
          var z = function() {
            throw Error();
          };
          if (Object.defineProperty(z.prototype, "props", {
            set: function() {
              throw Error();
            }
          }), typeof Reflect == "object" && Reflect.construct) {
            try {
              Reflect.construct(z, []);
            } catch (xe) {
              P = xe;
            }
            Reflect.construct(l, [], z);
          } else {
            try {
              z.call();
            } catch (xe) {
              P = xe;
            }
            l.call(z.prototype);
          }
        } else {
          try {
            throw Error();
          } catch (xe) {
            P = xe;
          }
          l();
        }
      } catch (xe) {
        if (xe && P && typeof xe.stack == "string") {
          for (var $ = xe.stack.split(`
`), ae = P.stack.split(`
`), Z = $.length - 1, te = ae.length - 1; Z >= 1 && te >= 0 && $[Z] !== ae[te]; )
            te--;
          for (; Z >= 1 && te >= 0; Z--, te--)
            if ($[Z] !== ae[te]) {
              if (Z !== 1 || te !== 1)
                do
                  if (Z--, te--, te < 0 || $[Z] !== ae[te]) {
                    var de = `
` + $[Z].replace(" at new ", " at ");
                    return l.displayName && de.includes("<anonymous>") && (de = de.replace("<anonymous>", l.displayName)), typeof l == "function" && Oe.set(l, de), de;
                  }
                while (Z >= 1 && te >= 0);
              break;
            }
        }
      } finally {
        Ye = !1, Ve.current = G, mt(), Error.prepareStackTrace = B;
      }
      var Ce = l ? l.displayName || l.name : "", Nr = Ce ? De(Ce) : "";
      return typeof l == "function" && Oe.set(l, Nr), Nr;
    }
    function xt(l, v, I) {
      return mr(l, !1);
    }
    function bt(l) {
      var v = l.prototype;
      return !!(v && v.isReactComponent);
    }
    function $e(l, v, I) {
      if (l == null)
        return "";
      if (typeof l == "function")
        return mr(l, bt(l));
      if (typeof l == "string")
        return De(l);
      switch (l) {
        case p:
          return De("Suspense");
        case d:
          return De("SuspenseList");
      }
      if (typeof l == "object")
        switch (l.$$typeof) {
          case f:
            return xt(l.render);
          case h:
            return $e(l.type, v, I);
          case x: {
            var P = l, B = P._payload, G = P._init;
            try {
              return $e(G(B), v, I);
            } catch {
            }
          }
        }
      return "";
    }
    var Ae = Object.prototype.hasOwnProperty, hr = {}, xr = w.ReactDebugCurrentFrame;
    function ze(l) {
      if (l) {
        var v = l._owner, I = $e(l.type, l._source, v ? v.type : null);
        xr.setExtraStackFrame(I);
      } else
        xr.setExtraStackFrame(null);
    }
    function yt(l, v, I, P, B) {
      {
        var G = Function.call.bind(Ae);
        for (var z in l)
          if (G(l, z)) {
            var $ = void 0;
            try {
              if (typeof l[z] != "function") {
                var ae = Error((P || "React class") + ": " + I + " type `" + z + "` is invalid; it must be a function, usually from the `prop-types` package, but received `" + typeof l[z] + "`.This often happens because of typos such as `PropTypes.function` instead of `PropTypes.func`.");
                throw ae.name = "Invariant Violation", ae;
              }
              $ = l[z](v, z, P, I, null, "SECRET_DO_NOT_PASS_THIS_OR_YOU_WILL_BE_FIRED");
            } catch (Z) {
              $ = Z;
            }
            $ && !($ instanceof Error) && (ze(B), S("%s: type specification of %s `%s` is invalid; the type checker function must return `null` or an `Error` but returned a %s. You may have forgotten to pass an argument to the type checker creator (arrayOf, instanceOf, objectOf, oneOf, oneOfType, and shape all require an argument).", P || "React class", I, z, typeof $), ze(null)), $ instanceof Error && !($.message in hr) && (hr[$.message] = !0, ze(B), S("Failed %s type: %s", I, $.message), ze(null));
          }
      }
    }
    var vt = Array.isArray;
    function We(l) {
      return vt(l);
    }
    function wt(l) {
      {
        var v = typeof Symbol == "function" && Symbol.toStringTag, I = v && l[Symbol.toStringTag] || l.constructor.name || "Object";
        return I;
      }
    }
    function St(l) {
      try {
        return br(l), !1;
      } catch {
        return !0;
      }
    }
    function br(l) {
      return "" + l;
    }
    function yr(l) {
      if (St(l))
        return S("The provided key is an unsupported type %s. This value must be coerced to a string before before using it here.", wt(l)), br(l);
    }
    var je = w.ReactCurrentOwner, Ct = {
      key: !0,
      ref: !0,
      __self: !0,
      __source: !0
    }, vr, wr, Ge;
    Ge = {};
    function It(l) {
      if (Ae.call(l, "ref")) {
        var v = Object.getOwnPropertyDescriptor(l, "ref").get;
        if (v && v.isReactWarning)
          return !1;
      }
      return l.ref !== void 0;
    }
    function Et(l) {
      if (Ae.call(l, "key")) {
        var v = Object.getOwnPropertyDescriptor(l, "key").get;
        if (v && v.isReactWarning)
          return !1;
      }
      return l.key !== void 0;
    }
    function jt(l, v) {
      if (typeof l.ref == "string" && je.current && v && je.current.stateNode !== v) {
        var I = V(je.current.type);
        Ge[I] || (S('Component "%s" contains the string ref "%s". Support for string refs will be removed in a future major release. This case cannot be automatically converted to an arrow function. We ask you to manually fix this case by using useRef() or createRef() instead. Learn more about using refs safely here: https://reactjs.org/link/strict-mode-string-ref', V(je.current.type), l.ref), Ge[I] = !0);
      }
    }
    function Tt(l, v) {
      {
        var I = function() {
          vr || (vr = !0, S("%s: `key` is not a prop. Trying to access it will result in `undefined` being returned. If you need to access the same value within the child component, you should pass it as a different prop. (https://reactjs.org/link/special-props)", v));
        };
        I.isReactWarning = !0, Object.defineProperty(l, "key", {
          get: I,
          configurable: !0
        });
      }
    }
    function Nt(l, v) {
      {
        var I = function() {
          wr || (wr = !0, S("%s: `ref` is not a prop. Trying to access it will result in `undefined` being returned. If you need to access the same value within the child component, you should pass it as a different prop. (https://reactjs.org/link/special-props)", v));
        };
        I.isReactWarning = !0, Object.defineProperty(l, "ref", {
          get: I,
          configurable: !0
        });
      }
    }
    var kt = function(l, v, I, P, B, G, z) {
      var $ = {
        // This tag allows us to uniquely identify this as a React Element
        $$typeof: r,
        // Built-in properties that belong on the element
        type: l,
        key: v,
        ref: I,
        props: z,
        // Record the component responsible for creating this element.
        _owner: G
      };
      return $._store = {}, Object.defineProperty($._store, "validated", {
        configurable: !1,
        enumerable: !1,
        writable: !0,
        value: !1
      }), Object.defineProperty($, "_self", {
        configurable: !1,
        enumerable: !1,
        writable: !1,
        value: P
      }), Object.defineProperty($, "_source", {
        configurable: !1,
        enumerable: !1,
        writable: !1,
        value: B
      }), Object.freeze && (Object.freeze($.props), Object.freeze($)), $;
    };
    function Rt(l, v, I, P, B) {
      {
        var G, z = {}, $ = null, ae = null;
        I !== void 0 && (yr(I), $ = "" + I), Et(v) && (yr(v.key), $ = "" + v.key), It(v) && (ae = v.ref, jt(v, B));
        for (G in v)
          Ae.call(v, G) && !Ct.hasOwnProperty(G) && (z[G] = v[G]);
        if (l && l.defaultProps) {
          var Z = l.defaultProps;
          for (G in Z)
            z[G] === void 0 && (z[G] = Z[G]);
        }
        if ($ || ae) {
          var te = typeof l == "function" ? l.displayName || l.name || "Unknown" : l;
          $ && Tt(z, te), ae && Nt(z, te);
        }
        return kt(l, $, ae, B, P, je.current, z);
      }
    }
    var Ke = w.ReactCurrentOwner, Sr = w.ReactDebugCurrentFrame;
    function Se(l) {
      if (l) {
        var v = l._owner, I = $e(l.type, l._source, v ? v.type : null);
        Sr.setExtraStackFrame(I);
      } else
        Sr.setExtraStackFrame(null);
    }
    var Qe;
    Qe = !1;
    function Xe(l) {
      return typeof l == "object" && l !== null && l.$$typeof === r;
    }
    function Cr() {
      {
        if (Ke.current) {
          var l = V(Ke.current.type);
          if (l)
            return `

Check the render method of \`` + l + "`.";
        }
        return "";
      }
    }
    function _t(l) {
      {
        if (l !== void 0) {
          var v = l.fileName.replace(/^.*[\\\/]/, ""), I = l.lineNumber;
          return `

Check your code at ` + v + ":" + I + ".";
        }
        return "";
      }
    }
    var Ir = {};
    function Lt(l) {
      {
        var v = Cr();
        if (!v) {
          var I = typeof l == "string" ? l : l.displayName || l.name;
          I && (v = `

Check the top-level render call using <` + I + ">.");
        }
        return v;
      }
    }
    function Er(l, v) {
      {
        if (!l._store || l._store.validated || l.key != null)
          return;
        l._store.validated = !0;
        var I = Lt(v);
        if (Ir[I])
          return;
        Ir[I] = !0;
        var P = "";
        l && l._owner && l._owner !== Ke.current && (P = " It was passed a child from " + V(l._owner.type) + "."), Se(l), S('Each child in a list should have a unique "key" prop.%s%s See https://reactjs.org/link/warning-keys for more information.', I, P), Se(null);
      }
    }
    function jr(l, v) {
      {
        if (typeof l != "object")
          return;
        if (We(l))
          for (var I = 0; I < l.length; I++) {
            var P = l[I];
            Xe(P) && Er(P, v);
          }
        else if (Xe(l))
          l._store && (l._store.validated = !0);
        else if (l) {
          var B = C(l);
          if (typeof B == "function" && B !== l.entries)
            for (var G = B.call(l), z; !(z = G.next()).done; )
              Xe(z.value) && Er(z.value, v);
        }
      }
    }
    function Pt(l) {
      {
        var v = l.type;
        if (v == null || typeof v == "string")
          return;
        var I;
        if (typeof v == "function")
          I = v.propTypes;
        else if (typeof v == "object" && (v.$$typeof === f || // Note: Memo only checks outer props here.
        // Inner props are checked in the reconciler.
        v.$$typeof === h))
          I = v.propTypes;
        else
          return;
        if (I) {
          var P = V(v);
          yt(I, l.props, "prop", P, l);
        } else if (v.PropTypes !== void 0 && !Qe) {
          Qe = !0;
          var B = V(v);
          S("Component %s declared `PropTypes` instead of `propTypes`. Did you misspell the property assignment?", B || "Unknown");
        }
        typeof v.getDefaultProps == "function" && !v.getDefaultProps.isReactClassApproved && S("getDefaultProps is only used on classic React.createClass definitions. Use a static property named `defaultProps` instead.");
      }
    }
    function Mt(l) {
      {
        for (var v = Object.keys(l.props), I = 0; I < v.length; I++) {
          var P = v[I];
          if (P !== "children" && P !== "key") {
            Se(l), S("Invalid prop `%s` supplied to `React.Fragment`. React.Fragment can only have `key` and `children` props.", P), Se(null);
            break;
          }
        }
        l.ref !== null && (Se(l), S("Invalid attribute `ref` supplied to `React.Fragment`."), Se(null));
      }
    }
    function Tr(l, v, I, P, B, G) {
      {
        var z = Y(l);
        if (!z) {
          var $ = "";
          (l === void 0 || typeof l == "object" && l !== null && Object.keys(l).length === 0) && ($ += " You likely forgot to export your component from the file it's defined in, or you might have mixed up default and named imports.");
          var ae = _t(B);
          ae ? $ += ae : $ += Cr();
          var Z;
          l === null ? Z = "null" : We(l) ? Z = "array" : l !== void 0 && l.$$typeof === r ? (Z = "<" + (V(l.type) || "Unknown") + " />", $ = " Did you accidentally export a JSX literal instead of a component?") : Z = typeof l, S("React.jsx: type is invalid -- expected a string (for built-in components) or a class/function (for composite components) but got: %s.%s", Z, $);
        }
        var te = Rt(l, v, I, B, G);
        if (te == null)
          return te;
        if (z) {
          var de = v.children;
          if (de !== void 0)
            if (P)
              if (We(de)) {
                for (var Ce = 0; Ce < de.length; Ce++)
                  jr(de[Ce], l);
                Object.freeze && Object.freeze(de);
              } else
                S("React.jsx: Static children should always be an array. You are likely explicitly calling React.jsxs or React.jsxDEV. Use the Babel transform instead.");
            else
              jr(de, l);
        }
        return l === n ? Mt(te) : Pt(te), te;
      }
    }
    function Dt(l, v, I) {
      return Tr(l, v, I, !0);
    }
    function Ot(l, v, I) {
      return Tr(l, v, I, !1);
    }
    var $t = Ot, At = Dt;
    ke.Fragment = n, ke.jsx = $t, ke.jsxs = At;
  }()), ke;
}
(function(e) {
  process.env.NODE_ENV === "production" ? e.exports = Jt() : e.exports = Zt();
})(Xt);
const eo = {
  small: g(["padding:", ";font-size:", ";min-height:20px;min-width:", ";"], ({
    theme: e
  }) => `${e.spacing.xxs} ${e.spacing.xs}`, ({
    theme: e
  }) => e.fontSizes.xs, ({
    dot: e
  }) => e ? "8px" : "20px"),
  medium: g(["padding:", ";font-size:", ";min-height:24px;min-width:", ";"], ({
    theme: e
  }) => `${e.spacing.xs} ${e.spacing.sm}`, ({
    theme: e
  }) => e.fontSizes.sm, ({
    dot: e
  }) => e ? "10px" : "24px"),
  large: g(["padding:", ";font-size:", ";min-height:32px;min-width:", ";"], ({
    theme: e
  }) => `${e.spacing.sm} ${e.spacing.md}`, ({
    theme: e
  }) => e.fontSizes.md, ({
    dot: e
  }) => e ? "12px" : "32px")
}, ro = (e, r, t = !1) => g(["", ""], ({
  theme: n
}) => {
  let s, c, i;
  switch (e) {
    case "primary":
      s = r ? n.colors.primary : `${n.colors.primary}20`, c = r ? n.colors.textInverse : n.colors.primary, i = n.colors.primary;
      break;
    case "secondary":
      s = r ? n.colors.secondary : `${n.colors.secondary}20`, c = r ? n.colors.textInverse : n.colors.secondary, i = n.colors.secondary;
      break;
    case "success":
      s = r ? n.colors.success : `${n.colors.success}20`, c = r ? n.colors.textInverse : n.colors.success, i = n.colors.success;
      break;
    case "warning":
      s = r ? n.colors.warning : `${n.colors.warning}20`, c = r ? n.colors.textInverse : n.colors.warning, i = n.colors.warning;
      break;
    case "error":
      s = r ? n.colors.error : `${n.colors.error}20`, c = r ? n.colors.textInverse : n.colors.error, i = n.colors.error;
      break;
    case "info":
      s = r ? n.colors.info : `${n.colors.info}20`, c = r ? n.colors.textInverse : n.colors.info, i = n.colors.info;
      break;
    case "neutral":
      s = r ? n.colors.textSecondary : `${n.colors.textSecondary}10`, c = r ? n.colors.textInverse : n.colors.textSecondary, i = n.colors.textSecondary;
      break;
    default:
      s = r ? n.colors.textSecondary : `${n.colors.textSecondary}20`, c = r ? n.colors.textInverse : n.colors.textSecondary, i = n.colors.textSecondary;
  }
  return t ? `
          background-color: transparent;
          color: ${i};
          border: 1px solid ${i};
        ` : `
        background-color: ${s};
        color: ${c};
        border: 1px solid transparent;
      `;
}), it = /* @__PURE__ */ a.span.withConfig({
  displayName: "IconContainer",
  componentId: "sc-10uskub-0"
})(["display:flex;align-items:center;justify-content:center;"]), to = /* @__PURE__ */ a(it).withConfig({
  displayName: "StartIcon",
  componentId: "sc-10uskub-1"
})(["margin-right:", ";"], ({
  theme: e
}) => e.spacing.xxs), oo = /* @__PURE__ */ a(it).withConfig({
  displayName: "EndIcon",
  componentId: "sc-10uskub-2"
})(["margin-left:", ";"], ({
  theme: e
}) => e.spacing.xxs), no = /* @__PURE__ */ a.span.withConfig({
  displayName: "StyledBadge",
  componentId: "sc-10uskub-3"
})(["display:", ";align-items:center;justify-content:center;border-radius:", ";font-weight:", ";white-space:nowrap;", " ", " ", " ", " ", ""], ({
  inline: e
}) => e ? "inline-flex" : "flex", ({
  theme: e,
  rounded: r,
  dot: t
}) => t ? "50%" : r ? "9999px" : e.borderRadius.sm, ({
  theme: e
}) => e.fontWeights.medium, ({
  size: e
}) => eo[e], ({
  variant: e,
  solid: r,
  outlined: t
}) => ro(e, r, t || !1), ({
  dot: e
}) => e && g(["padding:0;height:8px;width:8px;"]), ({
  counter: e
}) => e && g(["min-width:1.5em;height:1.5em;padding:0 0.5em;border-radius:1em;"]), ({
  clickable: e
}) => e && g(["cursor:pointer;transition:opacity ", ";&:hover{opacity:0.8;}&:active{opacity:0.6;}"], ({
  theme: r
}) => r.transitions.fast)), He = ({
  children: e,
  variant: r = "default",
  size: t = "medium",
  solid: n = !1,
  className: s = "",
  style: c,
  onClick: i,
  rounded: u = !1,
  dot: f = !1,
  counter: p = !1,
  outlined: d = !1,
  startIcon: h,
  endIcon: x,
  max: y,
  inline: m = !0
}) => {
  let b = e;
  return p && typeof e == "number" && y !== void 0 && e > y && (b = `${y}+`), /* @__PURE__ */ o.jsx(no, { variant: r, size: t, solid: n, clickable: !!i, className: s, style: c, onClick: i, rounded: u, dot: f, counter: p, outlined: d, inline: m, children: !f && /* @__PURE__ */ o.jsxs(o.Fragment, { children: [
    h && /* @__PURE__ */ o.jsx(to, { children: h }),
    b,
    x && /* @__PURE__ */ o.jsx(oo, { children: x })
  ] }) });
}, so = /* @__PURE__ */ Ee(["0%{transform:rotate(0deg);}100%{transform:rotate(360deg);}"]), io = /* @__PURE__ */ a.div.withConfig({
  displayName: "LoadingSpinner",
  componentId: "sc-1rze74q-0"
})(["width:16px;height:16px;border:2px solid rgba(255,255,255,0.3);border-radius:50%;border-top-color:#fff;animation:", " 0.8s linear infinite;margin-right:", ";"], so, ({
  theme: e
}) => e.spacing.xs), ao = {
  small: g(["padding:", ";font-size:", ";min-height:32px;"], ({
    theme: e
  }) => `${e.spacing.xxs} ${e.spacing.sm}`, ({
    theme: e
  }) => e.fontSizes.xs),
  medium: g(["padding:", ";font-size:", ";min-height:40px;"], ({
    theme: e
  }) => `${e.spacing.xs} ${e.spacing.md}`, ({
    theme: e
  }) => e.fontSizes.sm),
  large: g(["padding:", ";font-size:", ";min-height:48px;"], ({
    theme: e
  }) => `${e.spacing.sm} ${e.spacing.lg}`, ({
    theme: e
  }) => e.fontSizes.md)
}, co = {
  primary: g(["background-color:", ";color:", ";border:none;&:hover:not(:disabled){background-color:", ";transform:translateY(-1px);box-shadow:0 4px 6px rgba(0,0,0,0.1);}&:active:not(:disabled){background-color:", ";transform:translateY(0);box-shadow:none;}"], ({
    theme: e
  }) => e.colors.primary, ({
    theme: e
  }) => e.colors.textPrimary || e.colors.textInverse || "#fff", ({
    theme: e
  }) => e.colors.primaryDark, ({
    theme: e
  }) => e.colors.primaryDark),
  secondary: g(["background-color:", ";color:", ";border:none;&:hover:not(:disabled){background-color:", ";transform:translateY(-1px);box-shadow:0 4px 6px rgba(0,0,0,0.1);}&:active:not(:disabled){background-color:", ";transform:translateY(0);box-shadow:none;}"], ({
    theme: e
  }) => e.colors.secondary, ({
    theme: e
  }) => e.colors.textPrimary || e.colors.textInverse || "#fff", ({
    theme: e
  }) => e.colors.secondaryDark, ({
    theme: e
  }) => e.colors.secondaryDark),
  outline: g(["background-color:transparent;color:", ";border:1px solid ", ";&:hover:not(:disabled){background-color:rgba(225,6,0,0.05);transform:translateY(-1px);}&:active:not(:disabled){background-color:rgba(225,6,0,0.1);transform:translateY(0);}"], ({
    theme: e
  }) => e.colors.primary, ({
    theme: e
  }) => e.colors.primary),
  text: g(["background-color:transparent;color:", ";border:none;padding-left:", ";padding-right:", ";&:hover:not(:disabled){background-color:rgba(225,6,0,0.05);}&:active:not(:disabled){background-color:rgba(225,6,0,0.1);}"], ({
    theme: e
  }) => e.colors.primary, ({
    theme: e
  }) => e.spacing.xs, ({
    theme: e
  }) => e.spacing.xs),
  success: g(["background-color:", ";color:", ";border:none;&:hover:not(:disabled){background-color:", "dd;transform:translateY(-1px);box-shadow:0 4px 6px rgba(0,0,0,0.1);}&:active:not(:disabled){transform:translateY(0);box-shadow:none;}"], ({
    theme: e
  }) => e.colors.success, ({
    theme: e
  }) => e.colors.textInverse || "#fff", ({
    theme: e
  }) => e.colors.success),
  danger: g(["background-color:", ";color:", ";border:none;&:hover:not(:disabled){background-color:", "dd;transform:translateY(-1px);box-shadow:0 4px 6px rgba(0,0,0,0.1);}&:active:not(:disabled){transform:translateY(0);box-shadow:none;}"], ({
    theme: e
  }) => e.colors.error, ({
    theme: e
  }) => e.colors.textInverse || "#fff", ({
    theme: e
  }) => e.colors.error)
}, lo = /* @__PURE__ */ a.button.withConfig({
  displayName: "StyledButton",
  componentId: "sc-1rze74q-1"
})(["display:inline-flex;align-items:center;justify-content:center;border-radius:", ";font-weight:", ";cursor:pointer;transition:all ", ";position:relative;overflow:hidden;", " ", " ", " &:disabled{opacity:0.6;cursor:not-allowed;box-shadow:none;transform:translateY(0);}", " ", ""], ({
  theme: e
}) => e.borderRadius.sm, ({
  theme: e
}) => {
  var r;
  return ((r = e.fontWeights) == null ? void 0 : r.medium) || 500;
}, ({
  theme: e
}) => {
  var r;
  return ((r = e.transitions) == null ? void 0 : r.fast) || "0.2s ease";
}, ({
  size: e = "medium"
}) => ao[e], ({
  variant: e = "primary"
}) => co[e], ({
  fullWidth: e
}) => e && g(["width:100%;"]), ({
  $hasStartIcon: e
}) => e && g(["& > *:first-child{margin-right:", ";}"], ({
  theme: r
}) => r.spacing.xs), ({
  $hasEndIcon: e
}) => e && g(["& > *:last-child{margin-left:", ";}"], ({
  theme: r
}) => r.spacing.xs)), po = /* @__PURE__ */ a.div.withConfig({
  displayName: "ButtonContent",
  componentId: "sc-1rze74q-2"
})(["display:flex;align-items:center;justify-content:center;"]), le = ({
  children: e,
  variant: r = "primary",
  disabled: t = !1,
  loading: n = !1,
  size: s = "medium",
  fullWidth: c = !1,
  startIcon: i,
  endIcon: u,
  onClick: f,
  className: p = "",
  type: d = "button",
  ...h
}) => /* @__PURE__ */ o.jsx(lo, { variant: r, disabled: t || n, size: s, fullWidth: c, onClick: f, className: p, type: d, $hasStartIcon: !!i && !n, $hasEndIcon: !!u && !n, ...h, children: /* @__PURE__ */ o.jsxs(po, { children: [
  n && /* @__PURE__ */ o.jsx(io, {}),
  !n && i,
  e,
  !n && u
] }) }), uo = /* @__PURE__ */ a.div.withConfig({
  displayName: "InputWrapper",
  componentId: "sc-uv3rzi-0"
})(["display:flex;flex-direction:column;width:", ";position:relative;"], ({
  fullWidth: e
}) => e ? "100%" : "auto"), fo = /* @__PURE__ */ a.label.withConfig({
  displayName: "Label",
  componentId: "sc-uv3rzi-1"
})(["font-size:", ";color:", ";margin-bottom:", ";font-weight:", ";"], ({
  theme: e
}) => e.fontSizes.sm, ({
  theme: e
}) => e.colors.textSecondary, ({
  theme: e
}) => e.spacing.xxs, ({
  theme: e
}) => {
  var r;
  return ((r = e.fontWeights) == null ? void 0 : r.medium) || 500;
}), go = /* @__PURE__ */ a.div.withConfig({
  displayName: "InputContainer",
  componentId: "sc-uv3rzi-2"
})(["display:flex;align-items:center;position:relative;width:100%;border-radius:", ";border:1px solid ", ";background-color:", ";transition:all ", ";", " ", " ", ""], ({
  theme: e
}) => e.borderRadius.sm, ({
  theme: e,
  hasError: r,
  hasSuccess: t,
  isFocused: n
}) => r ? e.colors.error : t ? e.colors.success : n ? e.colors.primary : e.colors.border, ({
  theme: e
}) => e.colors.surface, ({
  theme: e
}) => {
  var r;
  return ((r = e.transitions) == null ? void 0 : r.fast) || "0.2s ease";
}, ({
  disabled: e,
  theme: r
}) => e && g(["opacity:0.6;background-color:", ";cursor:not-allowed;"], r.colors.background), ({
  isFocused: e,
  theme: r,
  hasError: t,
  hasSuccess: n
}) => e && g(["box-shadow:0 0 0 2px ", ";"], t ? `${r.colors.error}33` : n ? `${r.colors.success}33` : `${r.colors.primary}33`), ({
  $size: e
}) => {
  switch (e) {
    case "small":
      return g(["height:32px;"]);
    case "large":
      return g(["height:48px;"]);
    default:
      return g(["height:40px;"]);
  }
}), _r = /* @__PURE__ */ a.div.withConfig({
  displayName: "IconContainer",
  componentId: "sc-uv3rzi-3"
})(["display:flex;align-items:center;justify-content:center;padding:0 ", ";color:", ";"], ({
  theme: e
}) => e.spacing.xs, ({
  theme: e
}) => e.colors.textSecondary), mo = /* @__PURE__ */ a.input.withConfig({
  displayName: "StyledInput",
  componentId: "sc-uv3rzi-4"
})(["flex:1;border:none;background:transparent;color:", ";width:100%;outline:none;&:disabled{cursor:not-allowed;}&::placeholder{color:", ";}", " ", " ", ""], ({
  theme: e
}) => e.colors.textPrimary, ({
  theme: e
}) => e.colors.textDisabled, ({
  hasStartIcon: e
}) => e && g(["padding-left:0;"]), ({
  hasEndIcon: e
}) => e && g(["padding-right:0;"]), ({
  $size: e,
  theme: r
}) => e === "small" ? g(["font-size:", ";padding:", " ", ";"], r.fontSizes.xs, r.spacing.xxs, r.spacing.xs) : e === "large" ? g(["font-size:", ";padding:", " ", ";"], r.fontSizes.md, r.spacing.sm, r.spacing.md) : g(["font-size:", ";padding:", " ", ";"], r.fontSizes.sm, r.spacing.xs, r.spacing.sm)), ho = /* @__PURE__ */ a.button.withConfig({
  displayName: "ClearButton",
  componentId: "sc-uv3rzi-5"
})(["background:none;border:none;cursor:pointer;color:", ";padding:0 ", ";display:flex;align-items:center;justify-content:center;&:hover{color:", ";}&:focus{outline:none;}"], ({
  theme: e
}) => e.colors.textDisabled, ({
  theme: e
}) => e.spacing.xs, ({
  theme: e
}) => e.colors.textSecondary), xo = /* @__PURE__ */ a.div.withConfig({
  displayName: "HelperTextContainer",
  componentId: "sc-uv3rzi-6"
})(["display:flex;justify-content:space-between;margin-top:", ";font-size:", ";color:", ";"], ({
  theme: e
}) => e.spacing.xxs, ({
  theme: e
}) => e.fontSizes.xs, ({
  theme: e,
  hasError: r,
  hasSuccess: t
}) => r ? e.colors.error : t ? e.colors.success : e.colors.textSecondary), Ie = ({
  value: e,
  onChange: r,
  placeholder: t = "",
  disabled: n = !1,
  error: s = "",
  type: c = "text",
  name: i = "",
  id: u = "",
  className: f = "",
  required: p = !1,
  autoComplete: d = "",
  label: h = "",
  helperText: x = "",
  startIcon: y,
  endIcon: m,
  loading: b = !1,
  success: C = !1,
  clearable: w = !1,
  onClear: S,
  maxLength: L,
  showCharCount: A = !1,
  size: O = "medium",
  fullWidth: R = !1,
  ...T
}) => {
  const [_, N] = Q(!1), Y = ot(null), W = () => {
    S ? S() : r(""), Y.current && Y.current.focus();
  }, k = (se) => {
    N(!0), T.onFocus && T.onFocus(se);
  }, V = (se) => {
    N(!1), T.onBlur && T.onBlur(se);
  }, oe = w && e && !n, ne = (e == null ? void 0 : e.length) || 0, fe = A || L !== void 0 && L > 0;
  return /* @__PURE__ */ o.jsxs(uo, { className: f, fullWidth: R, children: [
    h && /* @__PURE__ */ o.jsxs(fo, { htmlFor: u, children: [
      h,
      p && " *"
    ] }),
    /* @__PURE__ */ o.jsxs(go, { hasError: !!s, hasSuccess: !!C, disabled: !!n, $size: O, hasStartIcon: !!y, hasEndIcon: !!(m || oe), isFocused: !!_, children: [
      y && /* @__PURE__ */ o.jsx(_r, { children: y }),
      /* @__PURE__ */ o.jsx(
        mo,
        {
          ref: Y,
          type: c,
          value: e,
          onChange: (se) => r(se.target.value),
          placeholder: t,
          disabled: !!(n || b),
          name: i,
          id: u,
          required: !!p,
          autoComplete: d,
          hasStartIcon: !!y,
          hasEndIcon: !!(m || oe),
          $size: O,
          maxLength: L,
          onFocus: k,
          onBlur: V,
          ...T
        }
      ),
      oe && /* @__PURE__ */ o.jsx(ho, { type: "button", onClick: W, tabIndex: -1, children: "✕" }),
      m && /* @__PURE__ */ o.jsx(_r, { children: m })
    ] }),
    (s || x || fe) && /* @__PURE__ */ o.jsxs(xo, { hasError: !!s, hasSuccess: !!C, children: [
      /* @__PURE__ */ o.jsx("div", { children: s || x }),
      fe && /* @__PURE__ */ o.jsxs("div", { children: [
        ne,
        L !== void 0 && `/${L}`
      ] })
    ] })
  ] });
}, Lr = {
  small: g(["height:100px;"]),
  medium: g(["height:200px;"]),
  large: g(["height:300px;"]),
  custom: (e) => g(["height:", ";width:", ";"], e.customHeight, e.customWidth || "100%")
}, bo = {
  default: g(["background-color:", ";border-radius:", ";"], ({
    theme: e
  }) => e.colors.background, ({
    theme: e
  }) => e.borderRadius.md),
  card: g(["background-color:", ";border-radius:", ";box-shadow:", ";"], ({
    theme: e
  }) => e.colors.surface, ({
    theme: e
  }) => e.borderRadius.md, ({
    theme: e
  }) => e.shadows.sm),
  text: g(["background-color:transparent;height:auto !important;min-height:1.5em;"]),
  list: g(["background-color:", ";border-radius:", ";margin-bottom:", ";"], ({
    theme: e
  }) => e.colors.background, ({
    theme: e
  }) => e.borderRadius.sm, ({
    theme: e
  }) => e.spacing.sm)
}, yo = /* @__PURE__ */ Ee(["0%{transform:rotate(0deg);}100%{transform:rotate(360deg);}"]), vo = /* @__PURE__ */ a.div.withConfig({
  displayName: "Container",
  componentId: "sc-12vczt5-0"
})(["display:flex;flex-direction:column;align-items:center;justify-content:center;", " ", ""], ({
  size: e,
  customHeight: r,
  customWidth: t
}) => e === "custom" ? Lr.custom({
  customHeight: r,
  customWidth: t
}) : Lr[e], ({
  variant: e
}) => bo[e]), wo = /* @__PURE__ */ a.div.withConfig({
  displayName: "Spinner",
  componentId: "sc-12vczt5-1"
})(["width:32px;height:32px;border:3px solid ", ";border-top:3px solid ", ";border-radius:50%;animation:", " 1s linear infinite;margin-bottom:", ";"], ({
  theme: e
}) => e.colors.background, ({
  theme: e
}) => e.colors.primary, yo, ({
  theme: e
}) => e.spacing.sm), So = /* @__PURE__ */ a.div.withConfig({
  displayName: "Text",
  componentId: "sc-12vczt5-2"
})(["color:", ";font-size:", ";"], ({
  theme: e
}) => e.colors.textSecondary, ({
  theme: e
}) => e.fontSizes.sm), Co = ({
  variant: e = "default",
  size: r = "medium",
  height: t = "200px",
  width: n = "",
  text: s = "Loading...",
  showSpinner: c = !0,
  className: i = ""
}) => /* @__PURE__ */ o.jsxs(vo, { variant: e, size: r, customHeight: t, customWidth: n, className: i, children: [
  c && /* @__PURE__ */ o.jsx(wo, {}),
  s && /* @__PURE__ */ o.jsx(So, { children: s })
] }), Io = /* @__PURE__ */ a.div.withConfig({
  displayName: "SelectWrapper",
  componentId: "sc-wvk2um-0"
})(["display:flex;flex-direction:column;width:", ";position:relative;"], ({
  fullWidth: e
}) => e ? "100%" : "auto"), Eo = /* @__PURE__ */ a.label.withConfig({
  displayName: "Label",
  componentId: "sc-wvk2um-1"
})(["font-size:", ";color:", ";margin-bottom:", ";font-weight:", ";"], ({
  theme: e
}) => e.fontSizes.sm, ({
  theme: e
}) => e.colors.textSecondary, ({
  theme: e
}) => e.spacing.xxs, ({
  theme: e
}) => {
  var r;
  return ((r = e.fontWeights) == null ? void 0 : r.medium) || 500;
}), jo = /* @__PURE__ */ a.div.withConfig({
  displayName: "SelectContainer",
  componentId: "sc-wvk2um-2"
})(["display:flex;align-items:center;position:relative;width:100%;border-radius:", ";border:1px solid ", ";background-color:", ";transition:all ", ";", " ", " ", ""], ({
  theme: e
}) => e.borderRadius.sm, ({
  theme: e,
  hasError: r,
  hasSuccess: t,
  isFocused: n
}) => r ? e.colors.error : t ? e.colors.success : n ? e.colors.primary : e.colors.border, ({
  theme: e
}) => e.colors.surface, ({
  theme: e
}) => {
  var r;
  return ((r = e.transitions) == null ? void 0 : r.fast) || "0.2s ease";
}, ({
  disabled: e,
  theme: r
}) => e && g(["opacity:0.6;background-color:", ";cursor:not-allowed;"], r.colors.background), ({
  isFocused: e,
  theme: r,
  hasError: t,
  hasSuccess: n
}) => e && g(["box-shadow:0 0 0 2px ", ";"], t ? `${r.colors.error}33` : n ? `${r.colors.success}33` : `${r.colors.primary}33`), ({
  $size: e
}) => {
  switch (e) {
    case "small":
      return g(["height:32px;"]);
    case "large":
      return g(["height:48px;"]);
    default:
      return g(["height:40px;"]);
  }
}), To = /* @__PURE__ */ a.div.withConfig({
  displayName: "IconContainer",
  componentId: "sc-wvk2um-3"
})(["display:flex;align-items:center;justify-content:center;padding:0 ", ";color:", ";"], ({
  theme: e
}) => e.spacing.xs, ({
  theme: e
}) => e.colors.textSecondary), No = /* @__PURE__ */ a.select.withConfig({
  displayName: "StyledSelect",
  componentId: "sc-wvk2um-4"
})(["flex:1;border:none;background:transparent;color:", `;width:100%;outline:none;appearance:none;background-image:url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");background-repeat:no-repeat;background-position:right `, " center;background-size:16px;padding-right:", ";&:disabled{cursor:not-allowed;}", " ", ""], ({
  theme: e
}) => e.colors.textPrimary, ({
  theme: e
}) => e.spacing.sm, ({
  theme: e
}) => e.spacing.xl, ({
  hasStartIcon: e
}) => e && g(["padding-left:0;"]), ({
  $size: e,
  theme: r
}) => e === "small" ? g(["font-size:", ";padding:", " ", ";"], r.fontSizes.xs, r.spacing.xxs, r.spacing.xs) : e === "large" ? g(["font-size:", ";padding:", " ", ";"], r.fontSizes.md, r.spacing.sm, r.spacing.md) : g(["font-size:", ";padding:", " ", ";"], r.fontSizes.sm, r.spacing.xs, r.spacing.sm)), ko = /* @__PURE__ */ a.div.withConfig({
  displayName: "HelperTextContainer",
  componentId: "sc-wvk2um-5"
})(["display:flex;justify-content:space-between;margin-top:", ";font-size:", ";color:", ";"], ({
  theme: e
}) => e.spacing.xxs, ({
  theme: e
}) => e.fontSizes.xs, ({
  theme: e,
  hasError: r,
  hasSuccess: t
}) => r ? e.colors.error : t ? e.colors.success : e.colors.textSecondary), Ro = /* @__PURE__ */ a.optgroup.withConfig({
  displayName: "OptionGroup",
  componentId: "sc-wvk2um-6"
})(["font-weight:", ";color:", ";"], ({
  theme: e
}) => {
  var r;
  return ((r = e.fontWeights) == null ? void 0 : r.medium) || 500;
}, ({
  theme: e
}) => e.colors.textPrimary), Re = ({
  options: e,
  value: r,
  onChange: t,
  disabled: n = !1,
  error: s = "",
  name: c = "",
  id: i = "",
  className: u = "",
  required: f = !1,
  placeholder: p = "",
  label: d = "",
  helperText: h = "",
  size: x = "medium",
  fullWidth: y = !0,
  loading: m = !1,
  success: b = !1,
  startIcon: C,
  ...w
}) => {
  const [S, L] = Q(!1), A = (N) => {
    L(!0), w.onFocus && w.onFocus(N);
  }, O = (N) => {
    L(!1), w.onBlur && w.onBlur(N);
  }, R = {}, T = [];
  e.forEach((N) => {
    N.group ? (R[N.group] || (R[N.group] = []), R[N.group].push(N)) : T.push(N);
  });
  const _ = Object.keys(R).length > 0;
  return /* @__PURE__ */ o.jsxs(Io, { className: u, fullWidth: y, children: [
    d && /* @__PURE__ */ o.jsxs(Eo, { htmlFor: i, children: [
      d,
      f && " *"
    ] }),
    /* @__PURE__ */ o.jsxs(jo, { hasError: !!s, hasSuccess: !!b, disabled: !!(n || m), $size: x, hasStartIcon: !!C, isFocused: !!S, children: [
      C && /* @__PURE__ */ o.jsx(To, { children: C }),
      /* @__PURE__ */ o.jsxs(
        No,
        {
          value: r,
          onChange: (N) => t(N.target.value),
          disabled: !!(n || m),
          name: c,
          id: i,
          required: !!f,
          hasStartIcon: !!C,
          $size: x,
          onFocus: A,
          onBlur: O,
          ...w,
          children: [
            p && /* @__PURE__ */ o.jsx("option", { value: "", disabled: !0, children: p }),
            _ ? /* @__PURE__ */ o.jsxs(o.Fragment, { children: [
              T.map((N) => /* @__PURE__ */ o.jsx("option", { value: N.value, disabled: N.disabled, children: N.label }, N.value)),
              Object.entries(R).map(([N, Y]) => /* @__PURE__ */ o.jsx(Ro, { label: N, children: Y.map((W) => /* @__PURE__ */ o.jsx("option", { value: W.value, disabled: W.disabled, children: W.label }, W.value)) }, N))
            ] }) : (
              // Render all options without groups
              e.map((N) => /* @__PURE__ */ o.jsx("option", { value: N.value, disabled: N.disabled, children: N.label }, N.value))
            )
          ]
        }
      )
    ] }),
    (s || h) && /* @__PURE__ */ o.jsx(ko, { hasError: !!s, hasSuccess: !!b, children: /* @__PURE__ */ o.jsx("div", { children: s || h }) })
  ] });
}, Pr = {
  small: "8px",
  medium: "12px",
  large: "16px"
}, _o = {
  small: g(["font-size:", ";margin-left:", ";"], ({
    theme: e
  }) => e.fontSizes.xs, ({
    theme: e
  }) => e.spacing.xs),
  medium: g(["font-size:", ";margin-left:", ";"], ({
    theme: e
  }) => e.fontSizes.sm, ({
    theme: e
  }) => e.spacing.sm),
  large: g(["font-size:", ";margin-left:", ";"], ({
    theme: e
  }) => e.fontSizes.md, ({
    theme: e
  }) => e.spacing.md)
}, Lo = /* @__PURE__ */ g(["@keyframes pulse{0%{transform:scale(0.95);box-shadow:0 0 0 0 rgba(var(--pulse-color),0.7);}70%{transform:scale(1);box-shadow:0 0 0 6px rgba(var(--pulse-color),0);}100%{transform:scale(0.95);box-shadow:0 0 0 0 rgba(var(--pulse-color),0);}}animation:pulse 2s infinite;"]), Po = /* @__PURE__ */ a.div.withConfig({
  displayName: "Container",
  componentId: "sc-gwj3m-0"
})(["display:inline-flex;align-items:center;"]), Mo = /* @__PURE__ */ a.div.withConfig({
  displayName: "Indicator",
  componentId: "sc-gwj3m-1"
})(["border-radius:50%;width:", ";height:", ";", ""], ({
  size: e
}) => Pr[e], ({
  size: e
}) => Pr[e], ({
  status: e,
  theme: r,
  pulse: t
}) => {
  let n, s;
  switch (e) {
    case "success":
      n = r.colors.success, s = "76, 175, 80";
      break;
    case "error":
      n = r.colors.error, s = "244, 67, 54";
      break;
    case "warning":
      n = r.colors.warning, s = "255, 152, 0";
      break;
    case "info":
      n = r.colors.info, s = "33, 150, 243";
      break;
    default:
      n = r.colors.textSecondary, s = "158, 158, 158";
  }
  return g(["background-color:", ";", ""], n, t && g(["--pulse-color:", ";", ""], s, Lo));
}), Do = /* @__PURE__ */ a.span.withConfig({
  displayName: "Label",
  componentId: "sc-gwj3m-2"
})(["", " ", ""], ({
  size: e
}) => _o[e], ({
  status: e,
  theme: r
}) => {
  let t;
  switch (e) {
    case "success":
      t = r.colors.success;
      break;
    case "error":
      t = r.colors.error;
      break;
    case "warning":
      t = r.colors.warning;
      break;
    case "info":
      t = r.colors.info;
      break;
    default:
      t = r.colors.textSecondary;
  }
  return g(["color:", ";font-weight:", ";"], t, r.fontWeights.medium);
}), Aa = ({
  status: e,
  size: r = "medium",
  pulse: t = !1,
  showLabel: n = !1,
  label: s = "",
  className: c = ""
}) => {
  const i = s || e.charAt(0).toUpperCase() + e.slice(1);
  return /* @__PURE__ */ o.jsxs(Po, { className: c, children: [
    /* @__PURE__ */ o.jsx(Mo, { status: e, size: r, pulse: t }),
    n && /* @__PURE__ */ o.jsx(Do, { status: e, size: r, children: i })
  ] });
}, Oo = {
  small: g(["padding:", ";font-size:", ";"], ({
    theme: e
  }) => `${e.spacing.xxs} ${e.spacing.xs}`, ({
    theme: e
  }) => e.fontSizes.xs),
  medium: g(["padding:", ";font-size:", ";"], ({
    theme: e
  }) => `${e.spacing.xs} ${e.spacing.sm}`, ({
    theme: e
  }) => e.fontSizes.sm),
  large: g(["padding:", ";font-size:", ";"], ({
    theme: e
  }) => `${e.spacing.sm} ${e.spacing.md}`, ({
    theme: e
  }) => e.fontSizes.md)
}, $o = (e) => g(["", ""], ({
  theme: r
}) => {
  let t, n, s;
  switch (e) {
    case "primary":
      t = `${r.colors.primary}10`, n = r.colors.primary, s = `${r.colors.primary}30`;
      break;
    case "secondary":
      t = `${r.colors.secondary}10`, n = r.colors.secondary, s = `${r.colors.secondary}30`;
      break;
    case "success":
      t = `${r.colors.success}10`, n = r.colors.success, s = `${r.colors.success}30`;
      break;
    case "warning":
      t = `${r.colors.warning}10`, n = r.colors.warning, s = `${r.colors.warning}30`;
      break;
    case "error":
      t = `${r.colors.error}10`, n = r.colors.error, s = `${r.colors.error}30`;
      break;
    case "info":
      t = `${r.colors.info}10`, n = r.colors.info, s = `${r.colors.info}30`;
      break;
    default:
      t = `${r.colors.textSecondary}10`, n = r.colors.textSecondary, s = `${r.colors.textSecondary}30`;
  }
  return `
        background-color: ${t};
        color: ${n};
        border: 1px solid ${s};
      `;
}), Ao = /* @__PURE__ */ a.span.withConfig({
  displayName: "StyledTag",
  componentId: "sc-11nmnw9-0"
})(["display:inline-flex;align-items:center;border-radius:", ";font-weight:", ";", " ", " ", ""], ({
  theme: e
}) => e.borderRadius.pill, ({
  theme: e
}) => e.fontWeights.medium, ({
  size: e
}) => Oo[e], ({
  variant: e
}) => $o(e), ({
  clickable: e
}) => e && g(["cursor:pointer;transition:opacity ", ";&:hover{opacity:0.8;}&:active{opacity:0.6;}"], ({
  theme: r
}) => r.transitions.fast)), zo = /* @__PURE__ */ a.button.withConfig({
  displayName: "RemoveButton",
  componentId: "sc-11nmnw9-1"
})(["display:inline-flex;align-items:center;justify-content:center;background:none;border:none;cursor:pointer;color:inherit;opacity:0.7;margin-left:", ";padding:0;", " &:hover{opacity:1;}"], ({
  theme: e
}) => e.spacing.xs, ({
  size: e,
  theme: r
}) => {
  const t = {
    small: "12px",
    medium: "14px",
    large: "16px"
  };
  return `
      width: ${t[e]};
      height: ${t[e]};
      font-size: ${r.fontSizes.xs};
    `;
}), za = ({
  children: e,
  variant: r = "default",
  size: t = "medium",
  removable: n = !1,
  onRemove: s,
  className: c = "",
  onClick: i
}) => {
  const u = (f) => {
    f.stopPropagation(), s == null || s();
  };
  return /* @__PURE__ */ o.jsxs(Ao, { variant: r, size: t, clickable: !!i, className: c, onClick: i, children: [
    e,
    n && /* @__PURE__ */ o.jsx(zo, { size: t, onClick: u, children: "×" })
  ] });
}, Fo = /* @__PURE__ */ a.div.withConfig({
  displayName: "TimePickerContainer",
  componentId: "sc-v5w9zw-0"
})(["display:flex;flex-direction:column;gap:", ";"], ({
  theme: e
}) => e.spacing.xs), Bo = /* @__PURE__ */ a.label.withConfig({
  displayName: "Label",
  componentId: "sc-v5w9zw-1"
})(["font-size:", ";font-weight:", ";color:", ";"], ({
  theme: e
}) => e.fontSizes.sm, ({
  theme: e
}) => e.fontWeights.medium, ({
  theme: e
}) => e.colors.textPrimary), qo = /* @__PURE__ */ a.input.withConfig({
  displayName: "TimeInput",
  componentId: "sc-v5w9zw-2"
})(["padding:", ";border:1px solid ", ";border-radius:", ";font-size:", ";color:", ";background-color:", ";transition:border-color ", ";&:focus{outline:none;border-color:", ";}&:disabled{background-color:", ";cursor:not-allowed;}"], ({
  theme: e
}) => e.spacing.sm, ({
  theme: e
}) => e.colors.border, ({
  theme: e
}) => e.borderRadius.sm, ({
  theme: e
}) => e.fontSizes.md, ({
  theme: e
}) => e.colors.textPrimary, ({
  theme: e
}) => e.colors.surface, ({
  theme: e
}) => e.transitions.fast, ({
  theme: e
}) => e.colors.primary, ({
  theme: e
}) => e.colors.chartGrid), Ho = ({
  id: e,
  name: r,
  value: t,
  onChange: n,
  label: s,
  required: c = !1,
  disabled: i = !1,
  className: u,
  placeholder: f = "HH:MM",
  min: p,
  max: d
}) => /* @__PURE__ */ o.jsxs(Fo, { className: u, children: [
  s && /* @__PURE__ */ o.jsxs(Bo, { htmlFor: e, children: [
    s,
    c && /* @__PURE__ */ o.jsx("span", { style: {
      color: "red"
    }, children: " *" })
  ] }),
  /* @__PURE__ */ o.jsx(qo, { id: e, name: r, type: "time", value: t, onChange: n, required: c, disabled: i, placeholder: f, min: p, max: d })
] }), Fa = Ho, Vo = /* @__PURE__ */ a.div.withConfig({
  displayName: "SelectContainer",
  componentId: "sc-w0dp8e-0"
})(["display:flex;flex-direction:column;gap:", ";"], ({
  theme: e
}) => e.spacing.xs), Uo = /* @__PURE__ */ a.label.withConfig({
  displayName: "Label",
  componentId: "sc-w0dp8e-1"
})(["font-size:", ";font-weight:", ";color:", ";"], ({
  theme: e
}) => e.fontSizes.sm, ({
  theme: e
}) => e.fontWeights.medium, ({
  theme: e
}) => e.colors.textPrimary), Yo = /* @__PURE__ */ a.select.withConfig({
  displayName: "Select",
  componentId: "sc-w0dp8e-2"
})(["padding:", ";border:1px solid ", ";border-radius:", ";font-size:", ";color:", ";background-color:", ";transition:border-color ", ";&:focus{outline:none;border-color:", ";}&:disabled{background-color:", ";cursor:not-allowed;}"], ({
  theme: e
}) => e.spacing.sm, ({
  theme: e
}) => e.colors.border, ({
  theme: e
}) => e.borderRadius.sm, ({
  theme: e
}) => e.fontSizes.md, ({
  theme: e
}) => e.colors.textPrimary, ({
  theme: e
}) => e.colors.surface, ({
  theme: e
}) => e.transitions.fast, ({
  theme: e
}) => e.colors.primary, ({
  theme: e
}) => e.colors.chartGrid), Wo = ({
  id: e,
  name: r,
  value: t,
  onChange: n,
  options: s,
  label: c,
  required: i = !1,
  disabled: u = !1,
  className: f,
  placeholder: p
}) => /* @__PURE__ */ o.jsxs(Vo, { className: f, children: [
  c && /* @__PURE__ */ o.jsxs(Uo, { htmlFor: e, children: [
    c,
    i && /* @__PURE__ */ o.jsx("span", { style: {
      color: "red"
    }, children: " *" })
  ] }),
  /* @__PURE__ */ o.jsxs(Yo, { id: e, name: r, value: t, onChange: n, required: i, disabled: u, children: [
    p && /* @__PURE__ */ o.jsx("option", { value: "", disabled: !0, children: p }),
    s.map((d) => /* @__PURE__ */ o.jsx("option", { value: d.value, children: d.label }, d.value))
  ] })
] }), Ba = Wo, Go = /* @__PURE__ */ a.span.withConfig({
  displayName: "StyledLoadingCell",
  componentId: "sc-1i0qdjp-0"
})(["display:inline-flex;align-items:center;justify-content:flex-end;opacity:0.6;position:relative;", " border-radius:", ";&::after{content:'';position:absolute;top:0;left:0;right:0;bottom:0;background:linear-gradient(90deg,transparent,rgba(255,255,255,0.2),transparent);animation:shimmer 1.5s infinite;}@keyframes shimmer{0%{transform:translateX(-100%);}100%{transform:translateX(100%);}}"], ({
  $size: e,
  theme: r
}) => {
  var t, n, s, c, i, u, f, p, d;
  switch (e) {
    case "small":
      return g(["font-size:", ";padding:", " ", ";"], ((t = r.fontSizes) == null ? void 0 : t.xs) || "12px", ((n = r.spacing) == null ? void 0 : n.xxs) || "2px", ((s = r.spacing) == null ? void 0 : s.xs) || "4px");
    case "large":
      return g(["font-size:", ";padding:", " ", ";"], ((c = r.fontSizes) == null ? void 0 : c.lg) || "18px", ((i = r.spacing) == null ? void 0 : i.sm) || "8px", ((u = r.spacing) == null ? void 0 : u.md) || "12px");
    default:
      return g(["font-size:", ";padding:", " ", ";"], ((f = r.fontSizes) == null ? void 0 : f.sm) || "14px", ((p = r.spacing) == null ? void 0 : p.xs) || "4px", ((d = r.spacing) == null ? void 0 : d.sm) || "8px");
  }
}, ({
  theme: e
}) => {
  var r;
  return ((r = e.borderRadius) == null ? void 0 : r.sm) || "4px";
}), Ko = /* @__PURE__ */ a.span.withConfig({
  displayName: "LoadingPlaceholder",
  componentId: "sc-1i0qdjp-1"
})(["display:inline-block;width:", ";height:1em;background-color:currentColor;opacity:0.3;border-radius:2px;"], ({
  $width: e
}) => e || "60px"), Qo = (e) => {
  const {
    size: r = "medium",
    width: t,
    className: n,
    "aria-label": s
  } = e;
  return /* @__PURE__ */ o.jsx(Go, { className: n, $size: r, $width: t, "aria-label": s || "Loading data", role: "cell", "aria-busy": "true", children: /* @__PURE__ */ o.jsx(Ko, { $width: t }) });
}, qa = Qo, Xo = /* @__PURE__ */ Ee(["0%{transform:rotate(0deg);}100%{transform:rotate(360deg);}"]), Jo = /* @__PURE__ */ Ee(["0%{background-position:0% 0%;}100%{background-position:100% 100%;}"]), Zo = /* @__PURE__ */ a.div.withConfig({
  displayName: "StyledSpinner",
  componentId: "sc-1hoaoss-0"
})(["display:inline-block;position:relative;", " &::before{content:'';position:absolute;top:0;left:0;right:0;bottom:0;border-radius:50%;border:2px solid transparent;", " animation:", " ", "s linear infinite;}", ""], ({
  $size: e
}) => {
  switch (e) {
    case "xs":
      return g(["width:16px;height:16px;"]);
    case "sm":
      return g(["width:20px;height:20px;"]);
    case "md":
      return g(["width:32px;height:32px;"]);
    case "lg":
      return g(["width:48px;height:48px;"]);
    case "xl":
      return g(["width:64px;height:64px;"]);
    default:
      return g(["width:32px;height:32px;"]);
  }
}, ({
  $variant: e,
  theme: r
}) => {
  var t, n, s, c, i, u;
  switch (e) {
    case "primary":
      return g(["border-top-color:", ";border-right-color:", ";"], ((t = r.colors) == null ? void 0 : t.primary) || "#dc2626", ((n = r.colors) == null ? void 0 : n.primary) || "#dc2626");
    case "secondary":
      return g(["border-top-color:", ";border-right-color:", ";"], ((s = r.colors) == null ? void 0 : s.textSecondary) || "#9ca3af", ((c = r.colors) == null ? void 0 : c.textSecondary) || "#9ca3af");
    case "white":
      return g(["border-top-color:#ffffff;border-right-color:#ffffff;"]);
    case "red":
      return g(["border-top-color:#dc2626;border-right-color:#dc2626;"]);
    default:
      return g(["border-top-color:", ";border-right-color:", ";"], ((i = r.colors) == null ? void 0 : i.primary) || "#dc2626", ((u = r.colors) == null ? void 0 : u.primary) || "#dc2626");
  }
}, Xo, ({
  $speed: e
}) => 1 / e, ({
  $showStripes: e,
  $variant: r,
  theme: t
}) => e && g(["&::after{content:'';position:absolute;top:2px;left:2px;right:2px;bottom:2px;border-radius:50%;background:", ";background-size:8px 8px;animation:", " ", "s linear infinite;}"], r === "red" || r === "primary" ? "linear-gradient(45deg, transparent 25%, rgba(220, 38, 38, 0.3) 25%, rgba(220, 38, 38, 0.3) 50%, transparent 50%, transparent 75%, rgba(220, 38, 38, 0.3) 75%)" : "linear-gradient(45deg, transparent 25%, rgba(156, 163, 175, 0.3) 25%, rgba(156, 163, 175, 0.3) 50%, transparent 50%, transparent 75%, rgba(156, 163, 175, 0.3) 75%)", Jo, (n) => 2 / n.$speed)), en = /* @__PURE__ */ a.div.withConfig({
  displayName: "SpinnerContainer",
  componentId: "sc-1hoaoss-1"
})(["display:inline-flex;align-items:center;justify-content:center;"]), rn = (e) => {
  const {
    size: r = "md",
    variant: t = "primary",
    className: n,
    "aria-label": s,
    speed: c = 1,
    showStripes: i = !1
  } = e;
  return /* @__PURE__ */ o.jsx(en, { className: n, children: /* @__PURE__ */ o.jsx(Zo, { $size: r, $variant: t, $speed: c, $showStripes: i, role: "status", "aria-label": s || "Loading", "aria-live": "polite" }) });
}, Ha = rn, tn = {
  none: g(["padding:0;"]),
  small: g(["padding:", ";"], ({
    theme: e
  }) => e.spacing.sm),
  medium: g(["padding:", ";"], ({
    theme: e
  }) => e.spacing.md),
  large: g(["padding:", ";"], ({
    theme: e
  }) => e.spacing.lg)
}, on = {
  default: g(["background-color:", ";"], ({
    theme: e
  }) => e.colors.surface),
  primary: g(["background-color:", "10;border-color:", "30;"], ({
    theme: e
  }) => e.colors.primary, ({
    theme: e
  }) => e.colors.primary),
  secondary: g(["background-color:", "10;border-color:", "30;"], ({
    theme: e
  }) => e.colors.secondary, ({
    theme: e
  }) => e.colors.secondary),
  outlined: g(["background-color:transparent;border:1px solid ", ";"], ({
    theme: e
  }) => e.colors.border),
  elevated: g(["background-color:", ";box-shadow:", ";border:none;"], ({
    theme: e
  }) => e.colors.surface, ({
    theme: e
  }) => e.shadows.md)
}, nn = /* @__PURE__ */ a.div.withConfig({
  displayName: "CardContainer",
  componentId: "sc-mv9m67-0"
})(["border-radius:", ";overflow:hidden;transition:all ", ";position:relative;", " ", " ", " ", ""], ({
  theme: e
}) => e.borderRadius.md, ({
  theme: e
}) => e.transitions.fast, ({
  bordered: e,
  theme: r
}) => e && g(["border:1px solid ", ";"], r.colors.border), ({
  padding: e
}) => tn[e], ({
  variant: e
}) => on[e], ({
  clickable: e
}) => e && g(["cursor:pointer;&:hover{transform:translateY(-2px);box-shadow:", ";}&:active{transform:translateY(0);}"], ({
  theme: r
}) => r.shadows.md)), sn = /* @__PURE__ */ a.div.withConfig({
  displayName: "CardHeader",
  componentId: "sc-mv9m67-1"
})(["display:flex;justify-content:space-between;align-items:flex-start;margin-bottom:", ";"], ({
  theme: e
}) => e.spacing.md), an = /* @__PURE__ */ a.div.withConfig({
  displayName: "HeaderContent",
  componentId: "sc-mv9m67-2"
})(["flex:1;"]), cn = /* @__PURE__ */ a.h3.withConfig({
  displayName: "CardTitle",
  componentId: "sc-mv9m67-3"
})(["margin:0;font-size:", ";font-weight:", ";color:", ";"], ({
  theme: e
}) => e.fontSizes.lg, ({
  theme: e
}) => e.fontWeights.semibold, ({
  theme: e
}) => e.colors.textPrimary), ln = /* @__PURE__ */ a.div.withConfig({
  displayName: "CardSubtitle",
  componentId: "sc-mv9m67-4"
})(["margin-top:", ";font-size:", ";color:", ";"], ({
  theme: e
}) => e.spacing.xs, ({
  theme: e
}) => e.fontSizes.sm, ({
  theme: e
}) => e.colors.textSecondary), dn = /* @__PURE__ */ a.div.withConfig({
  displayName: "ActionsContainer",
  componentId: "sc-mv9m67-5"
})(["display:flex;gap:", ";"], ({
  theme: e
}) => e.spacing.sm), pn = /* @__PURE__ */ a.div.withConfig({
  displayName: "CardContent",
  componentId: "sc-mv9m67-6"
})([""]), un = /* @__PURE__ */ a.div.withConfig({
  displayName: "CardFooter",
  componentId: "sc-mv9m67-7"
})(["margin-top:", ";padding-top:", ";border-top:1px solid ", ";"], ({
  theme: e
}) => e.spacing.md, ({
  theme: e
}) => e.spacing.md, ({
  theme: e
}) => e.colors.border), fn = /* @__PURE__ */ a.div.withConfig({
  displayName: "LoadingOverlay",
  componentId: "sc-mv9m67-8"
})(["position:absolute;top:0;left:0;right:0;bottom:0;background-color:", ";display:flex;align-items:center;justify-content:center;z-index:1;"], ({
  theme: e
}) => `${e.colors.background}80`), gn = /* @__PURE__ */ a.div.withConfig({
  displayName: "ErrorContainer",
  componentId: "sc-mv9m67-9"
})(["padding:", ";background-color:", "10;border-radius:", ";color:", ";margin-bottom:", ";"], ({
  theme: e
}) => e.spacing.md, ({
  theme: e
}) => e.colors.error, ({
  theme: e
}) => e.borderRadius.sm, ({
  theme: e
}) => e.colors.error, ({
  theme: e
}) => e.spacing.md), mn = /* @__PURE__ */ a.div.withConfig({
  displayName: "LoadingSpinner",
  componentId: "sc-mv9m67-10"
})(["width:32px;height:32px;border:3px solid ", ";border-top:3px solid ", ";border-radius:50%;animation:spin 1s linear infinite;@keyframes spin{0%{transform:rotate(0deg);}100%{transform:rotate(360deg);}}"], ({
  theme: e
}) => e.colors.background, ({
  theme: e
}) => e.colors.primary), hn = ({
  children: e,
  title: r = "",
  subtitle: t = "",
  bordered: n = !0,
  variant: s = "default",
  padding: c = "medium",
  className: i = "",
  footer: u,
  actions: f,
  isLoading: p = !1,
  hasError: d = !1,
  errorMessage: h = "An error occurred",
  clickable: x = !1,
  onClick: y,
  ...m
}) => {
  const b = r || t || f;
  return /* @__PURE__ */ o.jsxs(nn, { bordered: n, variant: s, padding: c, clickable: x, className: i, onClick: x ? y : void 0, ...m, children: [
    p && /* @__PURE__ */ o.jsx(fn, { children: /* @__PURE__ */ o.jsx(mn, {}) }),
    b && /* @__PURE__ */ o.jsxs(sn, { children: [
      /* @__PURE__ */ o.jsxs(an, { children: [
        r && /* @__PURE__ */ o.jsx(cn, { children: r }),
        t && /* @__PURE__ */ o.jsx(ln, { children: t })
      ] }),
      f && /* @__PURE__ */ o.jsx(dn, { children: f })
    ] }),
    d && /* @__PURE__ */ o.jsx(gn, { children: /* @__PURE__ */ o.jsx("p", { children: h }) }),
    /* @__PURE__ */ o.jsx(pn, { children: e }),
    u && /* @__PURE__ */ o.jsx(un, { children: u })
  ] });
}, xn = /* @__PURE__ */ a.h3.withConfig({
  displayName: "Title",
  componentId: "sc-1jsjvya-0"
})(["margin:0 0 ", " 0;color:", ";font-weight:", ";", ""], ({
  theme: e
}) => e.spacing.sm, ({
  theme: e
}) => e.colors.textPrimary, ({
  theme: e
}) => e.fontWeights.semibold, ({
  size: e,
  theme: r
}) => {
  const t = {
    small: r.fontSizes.md,
    medium: r.fontSizes.lg,
    large: r.fontSizes.xl
  };
  return g(["font-size:", ";"], t[e]);
}), bn = /* @__PURE__ */ a.p.withConfig({
  displayName: "Description",
  componentId: "sc-1jsjvya-1"
})(["margin:0 0 ", " 0;color:", ";", ""], ({
  theme: e
}) => e.spacing.lg, ({
  theme: e
}) => e.colors.textSecondary, ({
  size: e,
  theme: r
}) => {
  const t = {
    small: r.fontSizes.sm,
    medium: r.fontSizes.md,
    large: r.fontSizes.lg
  };
  return g(["font-size:", ";"], t[e]);
}), yn = {
  default: g(["background-color:transparent;"]),
  compact: g(["background-color:transparent;text-align:left;align-items:flex-start;"]),
  card: g(["background-color:", ";border-radius:", ";box-shadow:", ";"], ({
    theme: e
  }) => e.colors.surface, ({
    theme: e
  }) => e.borderRadius.md, ({
    theme: e
  }) => e.shadows.sm)
}, vn = /* @__PURE__ */ a.div.withConfig({
  displayName: "Container",
  componentId: "sc-1jsjvya-2"
})(["display:flex;flex-direction:column;align-items:center;justify-content:center;text-align:center;width:100%;", " ", ""], ({
  variant: e
}) => yn[e], ({
  size: e,
  theme: r
}) => {
  switch (e) {
    case "small":
      return g(["padding:", ";min-height:120px;"], r.spacing.md);
    case "large":
      return g(["padding:", ";min-height:300px;"], r.spacing.xl);
    default:
      return g(["padding:", ";min-height:200px;"], r.spacing.lg);
  }
}), wn = /* @__PURE__ */ a.div.withConfig({
  displayName: "IconContainer",
  componentId: "sc-1jsjvya-3"
})(["margin-bottom:", ";", ""], ({
  theme: e
}) => e.spacing.md, ({
  size: e,
  theme: r
}) => {
  const t = {
    small: "32px",
    medium: "48px",
    large: "64px"
  };
  return g(["font-size:", ";svg{width:", ";height:", ";color:", ";}"], t[e], t[e], t[e], r.colors.textSecondary);
}), Sn = /* @__PURE__ */ a.div.withConfig({
  displayName: "ActionContainer",
  componentId: "sc-1jsjvya-4"
})(["margin-top:", ";"], ({
  theme: e
}) => e.spacing.md), Cn = /* @__PURE__ */ a.div.withConfig({
  displayName: "ChildrenContainer",
  componentId: "sc-1jsjvya-5"
})(["margin-top:", ";width:100%;"], ({
  theme: e
}) => e.spacing.lg), Mr = ({
  title: e = "",
  description: r = "",
  icon: t,
  actionText: n = "",
  onAction: s,
  variant: c = "default",
  size: i = "medium",
  className: u = "",
  children: f
}) => /* @__PURE__ */ o.jsxs(vn, { variant: c, size: i, className: u, children: [
  t && /* @__PURE__ */ o.jsx(wn, { size: i, children: t }),
  e && /* @__PURE__ */ o.jsx(xn, { size: i, children: e }),
  r && /* @__PURE__ */ o.jsx(bn, { size: i, children: r }),
  n && s && /* @__PURE__ */ o.jsx(Sn, { children: /* @__PURE__ */ o.jsx(le, { variant: "primary", size: i === "small" ? "small" : "medium", onClick: s, children: n }) }),
  f && /* @__PURE__ */ o.jsx(Cn, { children: f })
] }), Dr = /* @__PURE__ */ a.div.withConfig({
  displayName: "ErrorContainer",
  componentId: "sc-jxqb9h-0"
})(["padding:1.5rem;margin:", ";border-radius:0.5rem;background-color:", ";color:#ffffff;", ""], (e) => e.isAppLevel ? "0" : "1rem 0", (e) => e.isAppLevel ? "#1a1f2c" : "#f44336", (e) => e.isAppLevel && `
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    min-height: 100vh;
    width: 100%;
    max-width: 600px;
    margin: 0 auto;
  `), In = /* @__PURE__ */ a.div.withConfig({
  displayName: "ErrorCard",
  componentId: "sc-jxqb9h-1"
})(["background-color:#252a37;border-radius:0.5rem;padding:2rem;width:100%;box-shadow:0 4px 6px rgba(0,0,0,0.1);"]), Or = /* @__PURE__ */ a.h3.withConfig({
  displayName: "ErrorTitle",
  componentId: "sc-jxqb9h-2"
})(["margin-top:0;font-size:", ";font-weight:700;text-align:", ";"], (e) => e.isAppLevel ? "1.5rem" : "1.25rem", (e) => e.isAppLevel ? "center" : "left"), Fe = /* @__PURE__ */ a.p.withConfig({
  displayName: "ErrorMessage",
  componentId: "sc-jxqb9h-3"
})(["margin-bottom:1rem;text-align:", ";"], (e) => e.isAppLevel ? "center" : "left"), $r = /* @__PURE__ */ a.details.withConfig({
  displayName: "ErrorDetails",
  componentId: "sc-jxqb9h-4"
})(["margin-bottom:1rem;summary{cursor:pointer;color:#2196f3;font-weight:500;margin-bottom:0.5rem;}"]), Ar = /* @__PURE__ */ a.pre.withConfig({
  displayName: "ErrorStack",
  componentId: "sc-jxqb9h-5"
})(["font-size:0.875rem;background-color:rgba(0,0,0,0.1);padding:0.5rem;border-radius:0.25rem;overflow:auto;max-height:200px;"]), En = /* @__PURE__ */ a.div.withConfig({
  displayName: "ButtonContainer",
  componentId: "sc-jxqb9h-6"
})(["display:flex;gap:0.5rem;justify-content:flex-start;"]), at = /* @__PURE__ */ a.button.withConfig({
  displayName: "RetryButton",
  componentId: "sc-jxqb9h-7"
})(["background-color:#ffffff;color:#f44336;border:none;border-radius:0.25rem;padding:0.5rem 1rem;font-weight:700;cursor:pointer;transition:background-color 0.2s;&:hover{background-color:#f5f5f5;}"]), jn = /* @__PURE__ */ a.button.withConfig({
  displayName: "SkipButton",
  componentId: "sc-jxqb9h-8"
})(["padding:0.5rem 1rem;background-color:transparent;color:#ffffff;border:1px solid #ffffff;border-radius:0.25rem;font-size:0.875rem;font-weight:500;cursor:pointer;transition:all 0.2s;&:hover{background-color:rgba(255,255,255,0.1);}"]), Tn = /* @__PURE__ */ a(at).withConfig({
  displayName: "ReloadButton",
  componentId: "sc-jxqb9h-9"
})(["margin-top:1rem;width:100%;"]), Nn = ({
  error: e,
  resetError: r,
  isAppLevel: t,
  name: n,
  onSkip: s
}) => {
  const c = () => {
    window.location.reload();
  };
  return t ? /* @__PURE__ */ o.jsx(Dr, { isAppLevel: !0, children: /* @__PURE__ */ o.jsxs(In, { children: [
    /* @__PURE__ */ o.jsx(Or, { isAppLevel: !0, children: "Something went wrong" }),
    /* @__PURE__ */ o.jsx(Fe, { isAppLevel: !0, children: "We're sorry, but an unexpected error has occurred. Please try reloading the application." }),
    /* @__PURE__ */ o.jsxs($r, { children: [
      /* @__PURE__ */ o.jsx("summary", { children: "Technical Details" }),
      /* @__PURE__ */ o.jsx(Fe, { children: e.message }),
      e.stack && /* @__PURE__ */ o.jsx(Ar, { children: e.stack })
    ] }),
    /* @__PURE__ */ o.jsx(Tn, { onClick: c, children: "Reload Application" })
  ] }) }) : /* @__PURE__ */ o.jsxs(Dr, { children: [
    /* @__PURE__ */ o.jsx(Or, { children: n ? `Error in ${n}` : "Something went wrong" }),
    /* @__PURE__ */ o.jsx(Fe, { children: n ? `We encountered a problem while loading ${n}. You can try again${s ? " or skip this feature" : ""}.` : "An unexpected error occurred. Please try again." }),
    /* @__PURE__ */ o.jsxs($r, { children: [
      /* @__PURE__ */ o.jsx("summary", { children: "Technical Details" }),
      /* @__PURE__ */ o.jsx(Fe, { children: e.message }),
      e.stack && /* @__PURE__ */ o.jsx(Ar, { children: e.stack })
    ] }),
    /* @__PURE__ */ o.jsxs(En, { children: [
      /* @__PURE__ */ o.jsx(at, { onClick: r, children: "Try Again" }),
      s && /* @__PURE__ */ o.jsx(jn, { onClick: s, children: "Skip This Feature" })
    ] })
  ] });
};
class kn extends zt {
  constructor(r) {
    super(r), this.resetError = () => {
      this.setState({
        hasError: !1,
        error: null
      });
    }, this.state = {
      hasError: !1,
      error: null
    };
  }
  static getDerivedStateFromError(r) {
    return {
      hasError: !0,
      error: r
    };
  }
  componentDidCatch(r, t) {
    const {
      name: n
    } = this.props, s = n ? `ErrorBoundary(${n})` : "ErrorBoundary";
    console.error(`Error caught by ${s}:`, r, t), this.props.onError && this.props.onError(r, t);
  }
  componentDidUpdate(r) {
    this.state.hasError && this.props.resetOnPropsChange && r.children !== this.props.children && this.resetError();
  }
  componentWillUnmount() {
    this.state.hasError && this.props.resetOnUnmount && this.resetError();
  }
  render() {
    const {
      hasError: r,
      error: t
    } = this.state, {
      children: n,
      fallback: s,
      name: c,
      isFeatureBoundary: i,
      onSkip: u
    } = this.props;
    return r && t ? typeof s == "function" ? s({
      error: t,
      resetError: this.resetError
    }) : s || /* @__PURE__ */ o.jsx(Nn, { error: t, resetError: this.resetError, isAppLevel: !i, name: c, onSkip: u }) : n;
  }
}
const ct = ({
  isAppLevel: e = !1,
  isFeatureBoundary: r = !1,
  children: t,
  ...n
}) => {
  const s = e ? "app" : r ? "feature" : "component", c = {
    resetOnPropsChange: s !== "app",
    // App-level boundaries should not reset on props change
    resetOnUnmount: s !== "app",
    // App-level boundaries should not reset on unmount
    isFeatureBoundary: s === "feature"
  };
  return /* @__PURE__ */ o.jsx(kn, { ...c, ...n, children: t });
}, Va = (e) => /* @__PURE__ */ o.jsx(ct, { isAppLevel: !0, ...e }), Ua = ({
  featureName: e,
  children: r,
  ...t
}) => /* @__PURE__ */ o.jsx(ct, { isFeatureBoundary: !0, name: e, children: r, ...t }), Rn = /* @__PURE__ */ a.div.withConfig({
  displayName: "TabContainer",
  componentId: "sc-lgz9vh-0"
})(["display:flex;flex-direction:column;width:100%;"]), _n = /* @__PURE__ */ a.div.withConfig({
  displayName: "TabList",
  componentId: "sc-lgz9vh-1"
})(["display:flex;border-bottom:1px solid ", ";margin-bottom:", ";"], ({
  theme: e
}) => e.colors.border, ({
  theme: e
}) => e.spacing.md), Ln = /* @__PURE__ */ a.button.withConfig({
  displayName: "TabButton",
  componentId: "sc-lgz9vh-2"
})(["padding:", " ", ";background:none;border:none;border-bottom:2px solid ", ";color:", ";font-weight:", ";cursor:pointer;transition:all ", ";&:hover{color:", ";}&:focus{outline:none;color:", ";}"], ({
  theme: e
}) => e.spacing.sm, ({
  theme: e
}) => e.spacing.md, ({
  active: e,
  theme: r
}) => e ? r.colors.primary : "transparent", ({
  active: e,
  theme: r
}) => e ? r.colors.primary : r.colors.textSecondary, ({
  active: e,
  theme: r
}) => e ? r.fontWeights.semibold : r.fontWeights.regular, ({
  theme: e
}) => e.transitions.fast, ({
  theme: e
}) => e.colors.primary, ({
  theme: e
}) => e.colors.primary), Pn = /* @__PURE__ */ a.div.withConfig({
  displayName: "TabContent",
  componentId: "sc-lgz9vh-3"
})(["padding:", " 0;"], ({
  theme: e
}) => e.spacing.sm), Mn = ({
  tabs: e,
  defaultTab: r,
  className: t,
  activeTab: n,
  onTabClick: s
}) => {
  var p;
  const [c, i] = Q(r || e[0].id), u = n !== void 0 ? n : c, f = (d, h) => {
    d.preventDefault(), d.stopPropagation(), s ? s(h) : i(h);
  };
  return /* @__PURE__ */ o.jsxs(Rn, { className: t, children: [
    /* @__PURE__ */ o.jsx(_n, { children: e.map((d) => /* @__PURE__ */ o.jsx(
      Ln,
      {
        active: u === d.id,
        onClick: (h) => f(h, d.id),
        type: "button",
        form: "",
        tabIndex: 0,
        "data-tab-id": d.id,
        children: d.label
      },
      d.id
    )) }),
    /* @__PURE__ */ o.jsx(Pn, { children: (p = e.find((d) => d.id === u)) == null ? void 0 : p.content })
  ] });
}, Ya = Mn, Dn = {
  required: (e = "This field is required") => ({
    validate: (r) => typeof r == "string" ? r.trim().length > 0 : typeof r == "number" ? !isNaN(r) : Array.isArray(r) ? r.length > 0 : r != null && r !== void 0,
    message: e
  }),
  email: (e = "Please enter a valid email address") => ({
    validate: (r) => /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(r),
    message: e
  }),
  minLength: (e, r) => ({
    validate: (t) => t.length >= e,
    message: r || `Must be at least ${e} characters`
  }),
  maxLength: (e, r) => ({
    validate: (t) => t.length <= e,
    message: r || `Must be no more than ${e} characters`
  }),
  min: (e, r) => ({
    validate: (t) => t >= e,
    message: r || `Must be at least ${e}`
  }),
  max: (e, r) => ({
    validate: (t) => t <= e,
    message: r || `Must be no more than ${e}`
  }),
  pattern: (e, r) => ({
    validate: (t) => e.test(t),
    message: r
  })
}, On = (e = {}) => {
  const {
    initialValue: r = "",
    required: t = !1,
    type: n = "text",
    validationRules: s = [],
    validateOnChange: c = !1,
    validateOnBlur: i = !0,
    transform: u
  } = e, f = U(() => {
    const _ = [...s];
    return t && !s.some((N) => N.message.toLowerCase().includes("required")) && _.unshift(Dn.required()), _;
  }, [t, s]), [p, d] = Q(r), [h, x] = Q(null), [y, m] = Q(!1), [b, C] = Q(!1), w = U(() => p !== r, [p, r]), S = U(() => h === null && !b, [h, b]), L = U(() => h === null && !b, [h, b]), A = F(async () => {
    C(!0);
    try {
      for (const _ of f)
        if (!_.validate(p))
          return x(_.message), C(!1), !1;
      return x(null), C(!1), !0;
    } catch {
      return x("Validation error occurred"), C(!1), !1;
    }
  }, [p, f]), O = F(() => {
    d(r), x(null), m(!1), C(!1);
  }, [r]), R = F((_) => {
    let N;
    n === "number" ? N = parseFloat(_.target.value) || 0 : N = _.target.value, u && (N = u(N)), d(N), c && y && setTimeout(() => A(), 0);
  }, [n, u, c, y, A]), T = F((_) => {
    m(!0), i && A();
  }, [i, A]);
  return {
    // State
    value: p,
    error: h,
    touched: y,
    dirty: w,
    valid: S,
    isValid: L,
    validating: b,
    // Actions
    setValue: d,
    setError: x,
    setTouched: m,
    validate: A,
    reset: O,
    handleChange: R,
    handleBlur: T
  };
}, $n = /* @__PURE__ */ a.div.withConfig({
  displayName: "FieldContainer",
  componentId: "sc-oh07s1-0"
})(["display:flex;flex-direction:column;gap:", ";width:100%;margin-bottom:", ";"], ({
  theme: e
}) => {
  var r;
  return ((r = e.spacing) == null ? void 0 : r.xs) || "4px";
}, ({
  theme: e
}) => {
  var r;
  return ((r = e.spacing) == null ? void 0 : r.md) || "12px";
}), An = /* @__PURE__ */ a.label.withConfig({
  displayName: "Label",
  componentId: "sc-oh07s1-1"
})(["font-size:", ";font-weight:", ";color:", ";", ""], ({
  theme: e
}) => {
  var r;
  return ((r = e.fontSizes) == null ? void 0 : r.sm) || "14px";
}, ({
  theme: e
}) => {
  var r;
  return ((r = e.fontWeights) == null ? void 0 : r.medium) || "500";
}, ({
  theme: e
}) => {
  var r;
  return ((r = e.colors) == null ? void 0 : r.textPrimary) || "#ffffff";
}, ({
  $required: e
}) => e && g(["&::after{content:' *';color:", ";}"], ({
  theme: r
}) => {
  var t;
  return ((t = r.colors) == null ? void 0 : t.error) || "#dc2626";
})), er = /* @__PURE__ */ g(["width:100%;border:1px solid ", ";border-radius:", ";background-color:", ";color:", ";font-size:", ";padding:", ";transition:", ";&:focus{outline:none;border-color:", ";box-shadow:0 0 0 2px ", ";}&:disabled{background-color:", ";color:", ";cursor:not-allowed;}&::placeholder{color:", ";}"], ({
  theme: e,
  $hasError: r
}) => {
  var t, n;
  return r ? ((t = e.colors) == null ? void 0 : t.error) || "#dc2626" : ((n = e.colors) == null ? void 0 : n.border) || "#4b5563";
}, ({
  theme: e
}) => {
  var r;
  return ((r = e.borderRadius) == null ? void 0 : r.sm) || "4px";
}, ({
  theme: e
}) => {
  var r;
  return ((r = e.colors) == null ? void 0 : r.surface) || "#1f2937";
}, ({
  theme: e
}) => {
  var r;
  return ((r = e.colors) == null ? void 0 : r.textPrimary) || "#ffffff";
}, ({
  theme: e,
  $size: r
}) => {
  var t, n, s;
  switch (r) {
    case "sm":
      return ((t = e.fontSizes) == null ? void 0 : t.sm) || "14px";
    case "lg":
      return ((n = e.fontSizes) == null ? void 0 : n.lg) || "18px";
    default:
      return ((s = e.fontSizes) == null ? void 0 : s.md) || "16px";
  }
}, ({
  theme: e,
  $size: r
}) => {
  var t, n, s, c, i, u;
  switch (r) {
    case "sm":
      return `${((t = e.spacing) == null ? void 0 : t.xs) || "4px"} ${((n = e.spacing) == null ? void 0 : n.sm) || "8px"}`;
    case "lg":
      return `${((s = e.spacing) == null ? void 0 : s.md) || "12px"} ${((c = e.spacing) == null ? void 0 : c.lg) || "16px"}`;
    default:
      return `${((i = e.spacing) == null ? void 0 : i.sm) || "8px"} ${((u = e.spacing) == null ? void 0 : u.md) || "12px"}`;
  }
}, ({
  theme: e
}) => {
  var r;
  return ((r = e.transitions) == null ? void 0 : r.fast) || "all 0.2s ease";
}, ({
  theme: e
}) => {
  var r;
  return ((r = e.colors) == null ? void 0 : r.primary) || "#dc2626";
}, ({
  theme: e
}) => {
  var r;
  return (r = e.colors) != null && r.primary ? `${e.colors.primary}20` : "rgba(220, 38, 38, 0.2)";
}, ({
  theme: e
}) => {
  var r;
  return ((r = e.colors) == null ? void 0 : r.chartGrid) || "#374151";
}, ({
  theme: e
}) => {
  var r;
  return ((r = e.colors) == null ? void 0 : r.textSecondary) || "#9ca3af";
}, ({
  theme: e
}) => {
  var r;
  return ((r = e.colors) == null ? void 0 : r.textSecondary) || "#9ca3af";
}), zn = /* @__PURE__ */ a.input.withConfig({
  displayName: "StyledInput",
  componentId: "sc-oh07s1-2"
})(["", ""], er), Fn = /* @__PURE__ */ a.textarea.withConfig({
  displayName: "StyledTextarea",
  componentId: "sc-oh07s1-3"
})(["", " resize:vertical;min-height:80px;"], er), Bn = /* @__PURE__ */ a.select.withConfig({
  displayName: "StyledSelect",
  componentId: "sc-oh07s1-4"
})(["", " cursor:pointer;"], er), qn = /* @__PURE__ */ a.div.withConfig({
  displayName: "ErrorMessage",
  componentId: "sc-oh07s1-5"
})(["font-size:", ";color:", ";"], ({
  theme: e
}) => {
  var r;
  return ((r = e.fontSizes) == null ? void 0 : r.xs) || "12px";
}, ({
  theme: e
}) => {
  var r;
  return ((r = e.colors) == null ? void 0 : r.error) || "#dc2626";
}), Hn = /* @__PURE__ */ a.div.withConfig({
  displayName: "HelpText",
  componentId: "sc-oh07s1-6"
})(["font-size:", ";color:", ";"], ({
  theme: e
}) => {
  var r;
  return ((r = e.fontSizes) == null ? void 0 : r.xs) || "12px";
}, ({
  theme: e
}) => {
  var r;
  return ((r = e.colors) == null ? void 0 : r.textSecondary) || "#9ca3af";
}), Vn = (e) => {
  const {
    name: r,
    label: t,
    placeholder: n,
    disabled: s = !1,
    className: c,
    size: i = "md",
    helpText: u,
    inputType: f = "input",
    options: p = [],
    rows: d = 4,
    onChange: h,
    onBlur: x,
    ...y
  } = e, m = On({
    ...y,
    validateOnBlur: !0
  });
  we.useEffect(() => {
    h && h(m.value);
  }, [m.value, h]);
  const b = (S) => {
    m.handleBlur(S), x && x();
  }, C = {
    id: r,
    name: r,
    value: m.value,
    onChange: m.handleChange,
    onBlur: b,
    disabled: s,
    placeholder: n,
    $hasError: !!m.error,
    $disabled: s,
    $size: i
  }, w = () => {
    switch (f) {
      case "textarea":
        return /* @__PURE__ */ o.jsx(Fn, { ...C, rows: d });
      case "select":
        return /* @__PURE__ */ o.jsxs(Bn, { ...C, children: [
          n && /* @__PURE__ */ o.jsx("option", { value: "", disabled: !0, children: n }),
          p.map((S) => /* @__PURE__ */ o.jsx("option", { value: S.value, children: S.label }, S.value))
        ] });
      default:
        return /* @__PURE__ */ o.jsx(zn, { ...C, type: y.type || "text" });
    }
  };
  return /* @__PURE__ */ o.jsxs($n, { className: c, children: [
    t && /* @__PURE__ */ o.jsx(An, { htmlFor: r, $required: !!y.required, children: t }),
    w(),
    m.error && m.touched && /* @__PURE__ */ o.jsx(qn, { role: "alert", children: m.error }),
    u && !m.error && /* @__PURE__ */ o.jsx(Hn, { children: u })
  ] });
}, Wa = Vn, Ga = {
  string: (e) => (r, t) => {
    const n = String(r[e] || ""), s = String(t[e] || "");
    return n.localeCompare(s);
  },
  number: (e) => (r, t) => {
    const n = Number(r[e]) || 0, s = Number(t[e]) || 0;
    return n - s;
  },
  date: (e) => (r, t) => {
    const n = new Date(r[e]).getTime(), s = new Date(t[e]).getTime();
    return n - s;
  },
  boolean: (e) => (r, t) => {
    const n = !!r[e], s = !!t[e];
    return Number(n) - Number(s);
  }
}, Un = ({
  data: e,
  columns: r,
  defaultSort: t
}) => {
  const [n, s] = Q(t ? {
    field: t.field,
    direction: t.direction
  } : null), c = F((d) => {
    const h = r.find((x) => x.field === d);
    h != null && h.sortable && s((x) => {
      var y;
      if ((x == null ? void 0 : x.field) === d)
        return {
          field: d,
          direction: x.direction === "asc" ? "desc" : "asc"
        };
      {
        const m = typeof ((y = e[0]) == null ? void 0 : y[d]) == "number" ? "desc" : "asc";
        return {
          field: d,
          direction: m
        };
      }
    });
  }, [r, e]), i = U(() => {
    if (!n)
      return e;
    const d = r.find((x) => x.field === n.field);
    return d ? [...e].sort((x, y) => {
      let m = 0;
      if (d.sortFn)
        m = d.sortFn(x, y);
      else {
        const b = x[n.field], C = y[n.field];
        typeof b == "string" && typeof C == "string" ? m = b.localeCompare(C) : typeof b == "number" && typeof C == "number" ? m = b - C : m = String(b).localeCompare(String(C));
      }
      return n.direction === "asc" ? m : -m;
    }) : e;
  }, [e, n, r]), u = F((d) => !n || n.field !== d ? null : n.direction === "asc" ? "↑" : "↓", [n]), f = F((d) => (n == null ? void 0 : n.field) === d, [n]), p = F((d) => (n == null ? void 0 : n.field) === d ? n.direction : null, [n]);
  return {
    sortedData: i,
    sortConfig: n,
    handleSort: c,
    getSortIcon: u,
    isSorted: f,
    getSortDirection: p
  };
}, zr = /* @__PURE__ */ a.div.withConfig({
  displayName: "Container",
  componentId: "sc-13j9udn-0"
})(["overflow-x:auto;border-radius:", ";border:1px solid ", ";"], ({
  theme: e
}) => {
  var r;
  return ((r = e.borderRadius) == null ? void 0 : r.md) || "8px";
}, ({
  theme: e
}) => {
  var r;
  return ((r = e.colors) == null ? void 0 : r.border) || "#4b5563";
}), Yn = /* @__PURE__ */ a.table.withConfig({
  displayName: "Table",
  componentId: "sc-13j9udn-1"
})(["width:100%;border-collapse:collapse;font-size:", ";"], ({
  theme: e,
  $size: r
}) => {
  var t, n, s;
  switch (r) {
    case "sm":
      return ((t = e.fontSizes) == null ? void 0 : t.xs) || "12px";
    case "lg":
      return ((n = e.fontSizes) == null ? void 0 : n.md) || "16px";
    default:
      return ((s = e.fontSizes) == null ? void 0 : s.sm) || "14px";
  }
}), Wn = /* @__PURE__ */ a.thead.withConfig({
  displayName: "TableHead",
  componentId: "sc-13j9udn-2"
})(["background-color:", ";border-bottom:2px solid ", ";"], ({
  theme: e
}) => {
  var r;
  return ((r = e.colors) == null ? void 0 : r.surface) || "#1f2937";
}, ({
  theme: e
}) => {
  var r;
  return ((r = e.colors) == null ? void 0 : r.border) || "#4b5563";
}), Gn = /* @__PURE__ */ a.tbody.withConfig({
  displayName: "TableBody",
  componentId: "sc-13j9udn-3"
})([""]), Fr = /* @__PURE__ */ a.tr.withConfig({
  displayName: "TableRow",
  componentId: "sc-13j9udn-4"
})(["", " ", " ", " border-bottom:1px solid ", ";"], ({
  $striped: e,
  theme: r
}) => {
  var t;
  return e && g(["&:nth-child(even){background-color:", ";}"], ((t = r.colors) == null ? void 0 : t.background) || "#0f0f0f");
}, ({
  $hoverable: e,
  theme: r
}) => {
  var t;
  return e && g(["&:hover{background-color:", ";}"], ((t = r.colors) == null ? void 0 : t.surface) || "#1f2937");
}, ({
  $clickable: e
}) => e && g(["cursor:pointer;"]), ({
  theme: e
}) => {
  var r;
  return ((r = e.colors) == null ? void 0 : r.border) || "#4b5563";
}), Kn = /* @__PURE__ */ a.th.withConfig({
  displayName: "TableHeaderCell",
  componentId: "sc-13j9udn-5"
})(["text-align:left;font-weight:", ";color:", ";cursor:", ";user-select:none;transition:", ";padding:", ";&:hover{", "}&:focus{outline:2px solid ", ";outline-offset:-2px;}"], ({
  theme: e
}) => {
  var r;
  return ((r = e.fontWeights) == null ? void 0 : r.semibold) || "600";
}, ({
  theme: e,
  $active: r
}) => {
  var t, n;
  return r ? ((t = e.colors) == null ? void 0 : t.primary) || "#dc2626" : ((n = e.colors) == null ? void 0 : n.textPrimary) || "#ffffff";
}, ({
  $sortable: e
}) => e ? "pointer" : "default", ({
  theme: e
}) => {
  var r;
  return ((r = e.transitions) == null ? void 0 : r.fast) || "all 0.2s ease";
}, ({
  theme: e,
  $size: r
}) => {
  var t, n, s, c, i, u;
  switch (r) {
    case "sm":
      return `${((t = e.spacing) == null ? void 0 : t.xs) || "4px"} ${((n = e.spacing) == null ? void 0 : n.sm) || "8px"}`;
    case "lg":
      return `${((s = e.spacing) == null ? void 0 : s.md) || "12px"} ${((c = e.spacing) == null ? void 0 : c.lg) || "16px"}`;
    default:
      return `${((i = e.spacing) == null ? void 0 : i.sm) || "8px"} ${((u = e.spacing) == null ? void 0 : u.md) || "12px"}`;
  }
}, ({
  $sortable: e,
  theme: r
}) => {
  var t;
  return e && g(["color:", ";"], ((t = r.colors) == null ? void 0 : t.primary) || "#dc2626");
}, ({
  theme: e
}) => {
  var r;
  return ((r = e.colors) == null ? void 0 : r.primary) || "#dc2626";
}), Qn = /* @__PURE__ */ a.td.withConfig({
  displayName: "TableCell",
  componentId: "sc-13j9udn-6"
})(["padding:", ";color:", ";"], ({
  theme: e,
  $size: r
}) => {
  var t, n, s, c, i, u;
  switch (r) {
    case "sm":
      return `${((t = e.spacing) == null ? void 0 : t.xs) || "4px"} ${((n = e.spacing) == null ? void 0 : n.sm) || "8px"}`;
    case "lg":
      return `${((s = e.spacing) == null ? void 0 : s.md) || "12px"} ${((c = e.spacing) == null ? void 0 : c.lg) || "16px"}`;
    default:
      return `${((i = e.spacing) == null ? void 0 : i.sm) || "8px"} ${((u = e.spacing) == null ? void 0 : u.md) || "12px"}`;
  }
}, ({
  theme: e
}) => {
  var r;
  return ((r = e.colors) == null ? void 0 : r.textPrimary) || "#ffffff";
}), Xn = /* @__PURE__ */ a.span.withConfig({
  displayName: "SortIcon",
  componentId: "sc-13j9udn-7"
})(["display:inline-block;margin-left:", ";font-size:", ";&::after{content:'", "';}"], ({
  theme: e
}) => {
  var r;
  return ((r = e.spacing) == null ? void 0 : r.xs) || "4px";
}, ({
  theme: e
}) => {
  var r;
  return ((r = e.fontSizes) == null ? void 0 : r.sm) || "14px";
}, ({
  $direction: e
}) => e === "asc" ? "↑" : "↓"), Jn = /* @__PURE__ */ a.div.withConfig({
  displayName: "EmptyState",
  componentId: "sc-13j9udn-8"
})(["padding:", ";text-align:center;color:", ";font-style:italic;"], ({
  theme: e,
  $size: r
}) => {
  var t, n, s;
  switch (r) {
    case "sm":
      return ((t = e.spacing) == null ? void 0 : t.md) || "12px";
    case "lg":
      return ((n = e.spacing) == null ? void 0 : n.xl) || "24px";
    default:
      return ((s = e.spacing) == null ? void 0 : s.lg) || "16px";
  }
}, ({
  theme: e
}) => {
  var r;
  return ((r = e.colors) == null ? void 0 : r.textSecondary) || "#9ca3af";
}), Zn = ({
  data: e,
  columns: r,
  className: t,
  emptyMessage: n = "No data available",
  defaultSort: s,
  renderCell: c,
  onRowClick: i,
  size: u = "md",
  striped: f = !0,
  hoverable: p = !0
}) => {
  const {
    sortedData: d,
    handleSort: h,
    getSortIcon: x,
    isSorted: y
  } = Un({
    data: e,
    columns: r,
    defaultSort: s
  });
  return e.length === 0 ? /* @__PURE__ */ o.jsx(zr, { className: t, children: /* @__PURE__ */ o.jsx(Jn, { $size: u, children: n }) }) : /* @__PURE__ */ o.jsx(zr, { className: t, children: /* @__PURE__ */ o.jsxs(Yn, { $size: u, $striped: f, $hoverable: p, children: [
    /* @__PURE__ */ o.jsx(Wn, { children: /* @__PURE__ */ o.jsx(Fr, { $striped: !1, $hoverable: !1, $clickable: !1, children: r.map((m) => /* @__PURE__ */ o.jsxs(Kn, { $sortable: m.sortable || !1, $active: y(m.field), $size: u, onClick: () => m.sortable && h(m.field), tabIndex: m.sortable ? 0 : -1, onKeyDown: (b) => {
      m.sortable && (b.key === "Enter" || b.key === " ") && (b.preventDefault(), h(m.field));
    }, role: m.sortable ? "button" : void 0, "aria-sort": y(m.field) ? x(m.field) === "↑" ? "ascending" : "descending" : void 0, children: [
      m.label,
      y(m.field) && /* @__PURE__ */ o.jsx(Xn, { $direction: x(m.field) === "↑" ? "asc" : "desc" })
    ] }, String(m.field))) }) }),
    /* @__PURE__ */ o.jsx(Gn, { children: d.map((m, b) => /* @__PURE__ */ o.jsx(Fr, { $striped: f, $hoverable: p, $clickable: !!i, onClick: () => i == null ? void 0 : i(m, b), tabIndex: i ? 0 : -1, onKeyDown: (C) => {
      i && (C.key === "Enter" || C.key === " ") && (C.preventDefault(), i(m, b));
    }, role: i ? "button" : void 0, children: r.map((C) => {
      const w = m[C.field];
      return /* @__PURE__ */ o.jsx(Qn, { $size: u, children: c ? c(w, m, C) : String(w) }, String(C.field));
    }) }, b)) })
  ] }) });
}, Ka = Zn, es = /* @__PURE__ */ a.div.withConfig({
  displayName: "FieldContainer",
  componentId: "sc-i922jg-0"
})(["display:flex;flex-direction:column;margin-bottom:", ";"], ({
  theme: e
}) => e.spacing.md), rs = /* @__PURE__ */ a.label.withConfig({
  displayName: "Label",
  componentId: "sc-i922jg-1"
})(["font-size:", ";font-weight:500;margin-bottom:", ";color:", ";.required-indicator{color:", ";margin-left:", ";}"], ({
  theme: e
}) => e.fontSizes.sm, ({
  theme: e
}) => e.spacing.xxs, ({
  theme: e,
  hasError: r
}) => r ? e.colors.error : e.colors.textPrimary, ({
  theme: e
}) => e.colors.error, ({
  theme: e
}) => e.spacing.xxs), ts = /* @__PURE__ */ a.div.withConfig({
  displayName: "HelperText",
  componentId: "sc-i922jg-2"
})(["font-size:", ";color:", ";margin-top:", ";"], ({
  theme: e
}) => e.fontSizes.xs, ({
  theme: e,
  hasError: r
}) => r ? e.colors.error : e.colors.textSecondary, ({
  theme: e
}) => e.spacing.xxs), Qa = ({
  children: e,
  label: r,
  helperText: t,
  required: n = !1,
  error: s,
  className: c,
  id: i,
  ...u
}) => {
  const f = i || `field-${Math.random().toString(36).substr(2, 9)}`, p = we.Children.map(e, (d) => we.isValidElement(d) ? we.cloneElement(d, {
    id: f,
    required: n,
    error: s
  }) : d);
  return /* @__PURE__ */ o.jsxs(es, { className: c, ...u, children: [
    /* @__PURE__ */ o.jsxs(rs, { htmlFor: f, hasError: !!s, children: [
      r,
      n && /* @__PURE__ */ o.jsx("span", { className: "required-indicator", children: "*" })
    ] }),
    p,
    (t || s) && /* @__PURE__ */ o.jsx(ts, { hasError: !!s, children: s || t })
  ] });
}, os = /* @__PURE__ */ Ee(["from{opacity:0;}to{opacity:1;}"]), ns = /* @__PURE__ */ Ee(["from{transform:translateY(-20px);opacity:0;}to{transform:translateY(0);opacity:1;}"]), ss = /* @__PURE__ */ a.div.withConfig({
  displayName: "Backdrop",
  componentId: "sc-1cuqxtr-0"
})(["position:fixed;top:0;left:0;right:0;bottom:0;background-color:rgba(0,0,0,0.5);display:flex;align-items:center;justify-content:center;z-index:", ";animation:", " 0.2s ease-out;"], ({
  zIndex: e
}) => e || 1e3, os), is = /* @__PURE__ */ a.div.withConfig({
  displayName: "ModalContainer",
  componentId: "sc-1cuqxtr-1"
})(["background-color:", ";border-radius:", ";box-shadow:", ";display:flex;flex-direction:column;max-height:", ";width:", ";max-width:95vw;animation:", " 0.2s ease-out;position:relative;", " ", ""], ({
  theme: e
}) => e.colors.surface, ({
  theme: e
}) => e.borderRadius.md, ({
  theme: e
}) => e.shadows.lg, ({
  size: e
}) => e === "fullscreen" ? "100vh" : "90vh", ({
  size: e
}) => {
  switch (e) {
    case "small":
      return "400px";
    case "medium":
      return "600px";
    case "large":
      return "800px";
    case "fullscreen":
      return "100vw";
    default:
      return "600px";
  }
}, ns, ({
  size: e
}) => e === "fullscreen" && g(["height:100vh;border-radius:0;"]), ({
  centered: e
}) => e && g(["margin:auto;"])), as = /* @__PURE__ */ a.div.withConfig({
  displayName: "ModalHeader",
  componentId: "sc-1cuqxtr-2"
})(["display:flex;justify-content:space-between;align-items:center;padding:", ";border-bottom:1px solid ", ";"], ({
  theme: e
}) => `${e.spacing.md} ${e.spacing.lg}`, ({
  theme: e
}) => e.colors.border), cs = /* @__PURE__ */ a.h3.withConfig({
  displayName: "ModalTitle",
  componentId: "sc-1cuqxtr-3"
})(["margin:0;font-size:", ";font-weight:", ";color:", ";"], ({
  theme: e
}) => e.fontSizes.lg, ({
  theme: e
}) => e.fontWeights.semibold, ({
  theme: e
}) => e.colors.textPrimary), ls = /* @__PURE__ */ a.button.withConfig({
  displayName: "CloseButton",
  componentId: "sc-1cuqxtr-4"
})(["background:none;border:none;cursor:pointer;font-size:", ";color:", ";padding:0;display:flex;align-items:center;justify-content:center;width:32px;height:32px;border-radius:", ";&:hover{background-color:", ";}&:focus{outline:none;box-shadow:0 0 0 2px ", "33;}"], ({
  theme: e
}) => e.fontSizes.xl, ({
  theme: e
}) => e.colors.textSecondary, ({
  theme: e
}) => e.borderRadius.sm, ({
  theme: e
}) => e.colors.background, ({
  theme: e
}) => e.colors.primary), ds = /* @__PURE__ */ a.div.withConfig({
  displayName: "ModalContent",
  componentId: "sc-1cuqxtr-5"
})(["padding:", ";", ""], ({
  theme: e
}) => e.spacing.lg, ({
  scrollable: e
}) => e && g(["overflow-y:auto;flex:1;"])), ps = /* @__PURE__ */ a.div.withConfig({
  displayName: "ModalFooter",
  componentId: "sc-1cuqxtr-6"
})(["display:flex;justify-content:flex-end;gap:", ";padding:", ";border-top:1px solid ", ";"], ({
  theme: e
}) => e.spacing.md, ({
  theme: e
}) => `${e.spacing.md} ${e.spacing.lg}`, ({
  theme: e
}) => e.colors.border), Xa = ({
  isOpen: e,
  title: r = "",
  children: t,
  onClose: n,
  size: s = "medium",
  closeOnOutsideClick: c = !0,
  showCloseButton: i = !0,
  footer: u,
  hasFooter: f = !0,
  primaryActionText: p = "",
  onPrimaryAction: d,
  primaryActionDisabled: h = !1,
  primaryActionLoading: x = !1,
  secondaryActionText: y = "",
  onSecondaryAction: m,
  secondaryActionDisabled: b = !1,
  className: C = "",
  zIndex: w = 1e3,
  centered: S = !0,
  // hasBackdrop = true, // Unused prop
  scrollable: L = !0
}) => {
  const A = ot(null);
  ue(() => {
    const _ = (N) => {
      N.key === "Escape" && e && c && n();
    };
    return document.addEventListener("keydown", _), () => {
      document.removeEventListener("keydown", _);
    };
  }, [e, n, c]);
  const O = (_) => {
    A.current && !A.current.contains(_.target) && c && n();
  };
  ue(() => (e ? document.body.style.overflow = "hidden" : document.body.style.overflow = "", () => {
    document.body.style.overflow = "";
  }), [e]);
  const R = /* @__PURE__ */ o.jsxs(o.Fragment, { children: [
    y && /* @__PURE__ */ o.jsx(le, { variant: "outline", onClick: m, disabled: b, children: y }),
    p && /* @__PURE__ */ o.jsx(le, { onClick: d, disabled: h, loading: x, children: p })
  ] });
  return e ? Vt(/* @__PURE__ */ o.jsx(ss, { onClick: O, zIndex: w, children: /* @__PURE__ */ o.jsxs(is, { ref: A, size: s, className: C, centered: S, scrollable: L, onClick: (_) => _.stopPropagation(), children: [
    (r || i) && /* @__PURE__ */ o.jsxs(as, { children: [
      r && /* @__PURE__ */ o.jsx(cs, { children: r }),
      i && /* @__PURE__ */ o.jsx(ls, { onClick: n, "aria-label": "Close", children: "×" })
    ] }),
    /* @__PURE__ */ o.jsx(ds, { scrollable: L, children: t }),
    f && (u || p || y) && /* @__PURE__ */ o.jsx(ps, { children: u || R })
  ] }) }), document.body) : null;
}, us = /* @__PURE__ */ a.div.withConfig({
  displayName: "TableContainer",
  componentId: "sc-4as3uq-0"
})(["width:100%;overflow:auto;", " ", ""], ({
  height: e
}) => e && `height: ${e};`, ({
  scrollable: e
}) => e && "overflow-x: auto;"), fs = /* @__PURE__ */ a.table.withConfig({
  displayName: "StyledTable",
  componentId: "sc-4as3uq-1"
})(["width:100%;border-collapse:separate;border-spacing:0;font-size:", ";", " ", ""], ({
  theme: e
}) => e.fontSizes.sm, ({
  bordered: e,
  theme: r
}) => e && g(["border:1px solid ", ";border-radius:", ";"], r.colors.border, r.borderRadius.sm), ({
  compact: e,
  theme: r
}) => e ? g(["th,td{padding:", " ", ";}"], r.spacing.xs, r.spacing.sm) : g(["th,td{padding:", " ", ";}"], r.spacing.sm, r.spacing.md)), gs = /* @__PURE__ */ a.thead.withConfig({
  displayName: "TableHeader",
  componentId: "sc-4as3uq-2"
})(["", ""], ({
  stickyHeader: e
}) => e && g(["position:sticky;top:0;z-index:1;"])), ms = /* @__PURE__ */ a.tr.withConfig({
  displayName: "TableHeaderRow",
  componentId: "sc-4as3uq-3"
})(["background-color:", ";"], ({
  theme: e
}) => e.colors.background), hs = /* @__PURE__ */ a.th.withConfig({
  displayName: "TableHeaderCell",
  componentId: "sc-4as3uq-4"
})(["text-align:", ";font-weight:", ";color:", ";border-bottom:1px solid ", ";white-space:nowrap;", " ", " ", ""], ({
  align: e
}) => e || "left", ({
  theme: e
}) => e.fontWeights.semibold, ({
  theme: e
}) => e.colors.textSecondary, ({
  theme: e
}) => e.colors.border, ({
  width: e
}) => e && `width: ${e};`, ({
  sortable: e
}) => e && g(["cursor:pointer;user-select:none;&:hover{background-color:", "aa;}"], ({
  theme: r
}) => r.colors.background), ({
  isSorted: e,
  theme: r
}) => e && g(["color:", ";"], r.colors.primary)), xs = /* @__PURE__ */ a.span.withConfig({
  displayName: "SortIcon",
  componentId: "sc-4as3uq-5"
})(["display:inline-block;margin-left:", ";&::after{content:'", "';}"], ({
  theme: e
}) => e.spacing.xs, ({
  direction: e
}) => e === "asc" ? "↑" : e === "desc" ? "↓" : "↕"), bs = /* @__PURE__ */ a.tbody.withConfig({
  displayName: "TableBody",
  componentId: "sc-4as3uq-6"
})([""]), ys = /* @__PURE__ */ a.tr.withConfig({
  displayName: "TableRow",
  componentId: "sc-4as3uq-7"
})(["", " ", " ", " ", ""], ({
  striped: e,
  theme: r,
  isSelected: t
}) => e && !t && g(["&:nth-child(even){background-color:", "50;}"], r.colors.background), ({
  hoverable: e,
  theme: r,
  isSelected: t
}) => e && !t && g(["&:hover{background-color:", "aa;}"], r.colors.background), ({
  isSelected: e,
  theme: r
}) => e && g(["background-color:", "15;"], r.colors.primary), ({
  isClickable: e
}) => e && g(["cursor:pointer;"])), vs = /* @__PURE__ */ a.td.withConfig({
  displayName: "TableCell",
  componentId: "sc-4as3uq-8"
})(["text-align:", ";border-bottom:1px solid ", ";color:", ";"], ({
  align: e
}) => e || "left", ({
  theme: e
}) => e.colors.border, ({
  theme: e
}) => e.colors.textPrimary), ws = /* @__PURE__ */ a.div.withConfig({
  displayName: "EmptyState",
  componentId: "sc-4as3uq-9"
})(["padding:", ";text-align:center;color:", ";"], ({
  theme: e
}) => e.spacing.xl, ({
  theme: e
}) => e.colors.textSecondary), Ss = /* @__PURE__ */ a.div.withConfig({
  displayName: "PaginationContainer",
  componentId: "sc-4as3uq-10"
})(["display:flex;justify-content:space-between;align-items:center;padding:", " 0;font-size:", ";"], ({
  theme: e
}) => e.spacing.md, ({
  theme: e
}) => e.fontSizes.sm), Cs = /* @__PURE__ */ a.div.withConfig({
  displayName: "PageInfo",
  componentId: "sc-4as3uq-11"
})(["color:", ";"], ({
  theme: e
}) => e.colors.textSecondary), Is = /* @__PURE__ */ a.div.withConfig({
  displayName: "PaginationControls",
  componentId: "sc-4as3uq-12"
})(["display:flex;gap:", ";"], ({
  theme: e
}) => e.spacing.xs), Es = /* @__PURE__ */ a.div.withConfig({
  displayName: "PageSizeSelector",
  componentId: "sc-4as3uq-13"
})(["display:flex;align-items:center;gap:", ";margin-right:", ";"], ({
  theme: e
}) => e.spacing.sm, ({
  theme: e
}) => e.spacing.md), js = /* @__PURE__ */ a.div.withConfig({
  displayName: "LoadingOverlay",
  componentId: "sc-4as3uq-14"
})(["position:absolute;top:0;left:0;right:0;bottom:0;background-color:", ";display:flex;align-items:center;justify-content:center;z-index:1;"], ({
  theme: e
}) => `${e.colors.background}80`), Ts = /* @__PURE__ */ a.div.withConfig({
  displayName: "LoadingSpinner",
  componentId: "sc-4as3uq-15"
})(["width:32px;height:32px;border:3px solid ", ";border-top:3px solid ", ";border-radius:50%;animation:spin 1s linear infinite;@keyframes spin{0%{transform:rotate(0deg);}100%{transform:rotate(360deg);}}"], ({
  theme: e
}) => e.colors.background, ({
  theme: e
}) => e.colors.primary);
function Ja({
  columns: e,
  data: r,
  isLoading: t = !1,
  bordered: n = !0,
  striped: s = !0,
  hoverable: c = !0,
  compact: i = !1,
  stickyHeader: u = !1,
  height: f,
  onRowClick: p,
  isRowSelected: d,
  onSort: h,
  sortColumn: x,
  sortDirection: y,
  pagination: m = !1,
  currentPage: b = 1,
  pageSize: C = 10,
  totalRows: w = 0,
  onPageChange: S,
  onPageSizeChange: L,
  className: A,
  emptyMessage: O = "No data available",
  scrollable: R = !0
}) {
  const T = U(() => e.filter((k) => !k.hidden), [e]), _ = U(() => Math.ceil(w / C), [w, C]), N = U(() => {
    if (!m)
      return r;
    const k = (b - 1) * C, V = k + C;
    return w > 0 && r.length <= C ? r : r.slice(k, V);
  }, [r, m, b, C, w]), Y = (k) => {
    if (!h)
      return;
    h(k, x === k && y === "asc" ? "desc" : "asc");
  }, W = (k) => {
    k < 1 || k > _ || !S || S(k);
  };
  return /* @__PURE__ */ o.jsxs("div", { style: {
    position: "relative"
  }, children: [
    t && /* @__PURE__ */ o.jsx(js, { children: /* @__PURE__ */ o.jsx(Ts, {}) }),
    /* @__PURE__ */ o.jsx(us, { height: f, scrollable: R, children: /* @__PURE__ */ o.jsxs(fs, { bordered: n, striped: s, compact: i, className: A, children: [
      /* @__PURE__ */ o.jsx(gs, { stickyHeader: u, children: /* @__PURE__ */ o.jsx(ms, { children: T.map((k) => /* @__PURE__ */ o.jsxs(hs, { sortable: k.sortable, isSorted: x === k.id, align: k.align, width: k.width, onClick: () => k.sortable && Y(k.id), children: [
        k.header,
        k.sortable && /* @__PURE__ */ o.jsx(xs, { direction: x === k.id ? y : void 0 })
      ] }, k.id)) }) }),
      /* @__PURE__ */ o.jsx(bs, { children: N.length > 0 ? N.map((k, V) => /* @__PURE__ */ o.jsx(ys, { hoverable: c, striped: s, isSelected: d ? d(k, V) : !1, isClickable: !!p, onClick: () => p && p(k, V), children: T.map((oe) => /* @__PURE__ */ o.jsx(vs, { align: oe.align, children: oe.cell(k, V) }, oe.id)) }, V)) : /* @__PURE__ */ o.jsx("tr", { children: /* @__PURE__ */ o.jsx("td", { colSpan: T.length, children: /* @__PURE__ */ o.jsx(ws, { children: O }) }) }) })
    ] }) }),
    m && _ > 0 && /* @__PURE__ */ o.jsxs(Ss, { children: [
      /* @__PURE__ */ o.jsxs(Cs, { children: [
        "Showing ",
        Math.min((b - 1) * C + 1, w),
        " to",
        " ",
        Math.min(b * C, w),
        " of ",
        w,
        " entries"
      ] }),
      /* @__PURE__ */ o.jsxs("div", { style: {
        display: "flex",
        alignItems: "center"
      }, children: [
        L && /* @__PURE__ */ o.jsxs(Es, { children: [
          /* @__PURE__ */ o.jsx("span", { children: "Show" }),
          /* @__PURE__ */ o.jsx("select", { value: C, onChange: (k) => L(Number(k.target.value)), style: {
            padding: "4px 8px",
            borderRadius: "4px",
            border: "1px solid #ccc"
          }, children: [10, 25, 50, 100].map((k) => /* @__PURE__ */ o.jsx("option", { value: k, children: k }, k)) }),
          /* @__PURE__ */ o.jsx("span", { children: "entries" })
        ] }),
        /* @__PURE__ */ o.jsxs(Is, { children: [
          /* @__PURE__ */ o.jsx(le, { size: "small", variant: "outline", onClick: () => W(1), disabled: b === 1, children: "First" }),
          /* @__PURE__ */ o.jsx(le, { size: "small", variant: "outline", onClick: () => W(b - 1), disabled: b === 1, children: "Prev" }),
          /* @__PURE__ */ o.jsx(le, { size: "small", variant: "outline", onClick: () => W(b + 1), disabled: b === _, children: "Next" }),
          /* @__PURE__ */ o.jsx(le, { size: "small", variant: "outline", onClick: () => W(_), disabled: b === _, children: "Last" })
        ] })
      ] })
    ] })
  ] });
}
const pe = {
  [M.MORNING_BREAKOUT]: {
    type: M.MORNING_BREAKOUT,
    name: "9:50-10:10 Macro",
    timeRange: {
      start: "09:50:00",
      end: "10:10:00"
    },
    description: "Morning breakout period - high volatility after market open",
    characteristics: ["High Volume", "Breakout Setups", "Gap Fills", "Opening Range"],
    volatilityLevel: 5,
    volumeLevel: 5,
    isHighProbability: !0
  },
  [M.MID_MORNING_REVERSION]: {
    type: M.MID_MORNING_REVERSION,
    name: "10:50-11:10 Macro",
    timeRange: {
      start: "10:50:00",
      end: "11:10:00"
    },
    description: "Mid-morning reversion period - mean reversion opportunities",
    characteristics: ["Mean Reversion", "Pullback Setups", "Support/Resistance Tests"],
    volatilityLevel: 3,
    volumeLevel: 3,
    isHighProbability: !0
  },
  [M.PRE_LUNCH]: {
    type: M.PRE_LUNCH,
    name: "11:50-12:10 Macro",
    timeRange: {
      start: "11:50:00",
      end: "12:10:00"
    },
    description: "Pre-lunch macro window - specific high-activity period within lunch session",
    characteristics: ["Consolidation", "Range Trading", "Pre-Lunch Activity"],
    volatilityLevel: 2,
    volumeLevel: 2,
    isHighProbability: !1,
    parentMacro: M.LUNCH_MACRO_EXTENDED
  },
  [M.LUNCH_MACRO_EXTENDED]: {
    type: M.LUNCH_MACRO_EXTENDED,
    name: "Lunch Macro (11:30-13:30)",
    timeRange: {
      start: "11:30:00",
      end: "13:30:00"
    },
    description: "Extended lunch period spanning late morning through early afternoon",
    characteristics: ["Multi-Session", "Lunch Trading", "Lower Volume", "Transition Period"],
    volatilityLevel: 2,
    volumeLevel: 2,
    isHighProbability: !1,
    isMultiSession: !0,
    spansSessions: [X.NEW_YORK_AM, X.NEW_YORK_PM],
    subPeriods: []
    // Will be populated with PRE_LUNCH macro
  },
  [M.LUNCH_MACRO]: {
    type: M.LUNCH_MACRO,
    name: "Lunch Macro (12:00-13:30)",
    timeRange: {
      start: "12:00:00",
      end: "13:30:00"
    },
    description: "Traditional lunch time trading - typically lower volume",
    characteristics: ["Low Volume", "Range Bound", "Choppy Price Action"],
    volatilityLevel: 2,
    volumeLevel: 1,
    isHighProbability: !1
  },
  [M.POST_LUNCH]: {
    type: M.POST_LUNCH,
    name: "13:50-14:10 Macro",
    timeRange: {
      start: "13:50:00",
      end: "14:10:00"
    },
    description: "Post-lunch macro window",
    characteristics: ["Volume Pickup", "Trend Resumption"],
    volatilityLevel: 3,
    volumeLevel: 3,
    isHighProbability: !1
  },
  [M.PRE_CLOSE]: {
    type: M.PRE_CLOSE,
    name: "14:50-15:10 Macro",
    timeRange: {
      start: "14:50:00",
      end: "15:10:00"
    },
    description: "Pre-close macro window",
    characteristics: ["Institutional Activity", "Position Adjustments"],
    volatilityLevel: 3,
    volumeLevel: 4,
    isHighProbability: !1
  },
  [M.POWER_HOUR]: {
    type: M.POWER_HOUR,
    name: "15:15-15:45 Macro (Power Hour)",
    timeRange: {
      start: "15:15:00",
      end: "15:45:00"
    },
    description: "Last hour macro - high activity before close",
    characteristics: ["High Volume", "Institutional Flows", "EOD Positioning"],
    volatilityLevel: 4,
    volumeLevel: 5,
    isHighProbability: !0
  },
  [M.MOC]: {
    type: M.MOC,
    name: "MOC (Market on Close)",
    timeRange: {
      start: "15:45:00",
      end: "16:00:00"
    },
    description: "Market on close period",
    characteristics: ["MOC Orders", "Final Positioning", "High Volume"],
    volatilityLevel: 4,
    volumeLevel: 5,
    isHighProbability: !1
  },
  [M.LONDON_OPEN]: {
    type: M.LONDON_OPEN,
    name: "London Open",
    timeRange: {
      start: "08:00:00",
      end: "09:00:00"
    },
    description: "London market opening hour",
    characteristics: ["European Activity", "Currency Moves", "News Reactions"],
    volatilityLevel: 4,
    volumeLevel: 4,
    isHighProbability: !0
  },
  [M.LONDON_NY_OVERLAP]: {
    type: M.LONDON_NY_OVERLAP,
    name: "London/NY Overlap",
    timeRange: {
      start: "14:00:00",
      end: "16:00:00"
    },
    description: "London and New York session overlap",
    characteristics: ["Highest Volume", "Major Moves", "Cross-Market Activity"],
    volatilityLevel: 5,
    volumeLevel: 5,
    isHighProbability: !0
  },
  [M.CUSTOM]: {
    type: M.CUSTOM,
    name: "Custom Period",
    timeRange: {
      start: "00:00:00",
      end: "23:59:59"
    },
    description: "User-defined custom time period",
    characteristics: ["Custom"],
    volatilityLevel: 3,
    volumeLevel: 3,
    isHighProbability: !1
  }
}, Ns = () => {
  const e = Object.values(ks).map((s) => ({
    id: s.type,
    ...s
  })), t = [{
    ...pe[M.LUNCH_MACRO_EXTENDED],
    id: "lunch-macro-extended",
    subPeriods: [{
      ...pe[M.PRE_LUNCH],
      id: "pre-lunch-sub"
    }]
  }], n = {};
  return e.forEach((s) => {
    s.macroPeriods.forEach((c) => {
      n[c.type] = {
        ...c,
        parentSession: s.type
      };
    });
  }), t.forEach((s) => {
    n[s.type] = {
      ...s,
      spansSessions: s.spansSessions
    };
  }), {
    sessions: e,
    sessionsByType: e.reduce((s, c) => (s[c.type] = c, s), {}),
    macrosByType: n,
    multiSessionMacros: t
  };
}, ks = {
  [X.NEW_YORK_AM]: {
    type: X.NEW_YORK_AM,
    name: "New York AM Session",
    timeRange: {
      start: "09:30:00",
      end: "12:00:00"
    },
    description: "New York morning session - high activity and volatility",
    timezone: "America/New_York",
    characteristics: ["High Volume", "Trend Development", "Breakout Opportunities"],
    color: "#dc2626",
    // F1 Red
    macroPeriods: [{
      ...pe[M.MORNING_BREAKOUT],
      id: "morning-breakout"
    }, {
      ...pe[M.MID_MORNING_REVERSION],
      id: "mid-morning-reversion"
    }, {
      ...pe[M.PRE_LUNCH],
      id: "pre-lunch"
    }]
  },
  [X.NEW_YORK_PM]: {
    type: X.NEW_YORK_PM,
    name: "New York PM Session",
    timeRange: {
      start: "12:00:00",
      end: "16:00:00"
    },
    description: "New York afternoon session - institutional activity increases toward close",
    timezone: "America/New_York",
    characteristics: ["Institutional Flows", "EOD Positioning", "Power Hour Activity"],
    color: "#dc2626",
    // F1 Red
    macroPeriods: [{
      ...pe[M.LUNCH_MACRO],
      id: "lunch-macro"
    }, {
      ...pe[M.POST_LUNCH],
      id: "post-lunch"
    }, {
      ...pe[M.PRE_CLOSE],
      id: "pre-close"
    }, {
      ...pe[M.POWER_HOUR],
      id: "power-hour"
    }, {
      ...pe[M.MOC],
      id: "moc"
    }]
  },
  [X.LONDON]: {
    type: X.LONDON,
    name: "London Session",
    timeRange: {
      start: "08:00:00",
      end: "16:00:00"
    },
    description: "London trading session - European market activity",
    timezone: "Europe/London",
    characteristics: ["European Activity", "Currency Focus", "News-Driven"],
    color: "#1f2937",
    // Dark Gray
    macroPeriods: [{
      ...pe[M.LONDON_OPEN],
      id: "london-open"
    }, {
      ...pe[M.LONDON_NY_OVERLAP],
      id: "london-ny-overlap"
    }]
  },
  [X.ASIA]: {
    type: X.ASIA,
    name: "Asia Session",
    timeRange: {
      start: "18:00:00",
      end: "03:00:00"
    },
    description: "Asian trading session - typically lower volatility",
    timezone: "Asia/Tokyo",
    characteristics: ["Lower Volume", "Range Trading", "News Reactions"],
    color: "#4b5563",
    // Gray
    macroPeriods: []
  },
  [X.PRE_MARKET]: {
    type: X.PRE_MARKET,
    name: "Pre-Market",
    timeRange: {
      start: "04:00:00",
      end: "09:30:00"
    },
    description: "Pre-market trading hours",
    timezone: "America/New_York",
    characteristics: ["Low Volume", "News Reactions", "Gap Setups"],
    color: "#6b7280",
    // Light Gray
    macroPeriods: []
  },
  [X.AFTER_HOURS]: {
    type: X.AFTER_HOURS,
    name: "After Hours",
    timeRange: {
      start: "16:00:00",
      end: "20:00:00"
    },
    description: "After-hours trading",
    timezone: "America/New_York",
    characteristics: ["Low Volume", "Earnings Reactions", "News-Driven"],
    color: "#6b7280",
    // Light Gray
    macroPeriods: []
  },
  [X.OVERNIGHT]: {
    type: X.OVERNIGHT,
    name: "Overnight",
    timeRange: {
      start: "20:00:00",
      end: "04:00:00"
    },
    description: "Overnight session",
    timezone: "America/New_York",
    characteristics: ["Very Low Volume", "Futures Activity"],
    color: "#374151",
    // Dark Gray
    macroPeriods: []
  }
};
class ie {
  /**
   * Initialize and get the session hierarchy
   */
  static getSessionHierarchy() {
    return this.hierarchy || (this.hierarchy = this.buildHierarchy()), this.hierarchy;
  }
  /**
   * Build the complete session hierarchy with overlapping macro support
   */
  static buildHierarchy() {
    return Ns();
  }
  /**
   * Parse time string to minutes since midnight
   */
  static timeToMinutes(r) {
    const [t, n, s = 0] = r.split(":").map(Number);
    return t * 60 + n + s / 60;
  }
  /**
   * Convert minutes since midnight to time string
   */
  static minutesToTime(r) {
    const t = Math.floor(r / 60), n = Math.floor(r % 60), s = Math.floor(r % 1 * 60);
    return `${t.toString().padStart(2, "0")}:${n.toString().padStart(2, "0")}:${s.toString().padStart(2, "0")}`;
  }
  /**
   * Check if a time falls within a time range
   */
  static isTimeInRange(r, t) {
    const n = this.timeToMinutes(r), s = this.timeToMinutes(t.start), c = this.timeToMinutes(t.end);
    return c < s ? n >= s || n <= c : n >= s && n <= c;
  }
  /**
   * Validate a time and suggest appropriate session/macro with overlapping support
   */
  static validateTime(r) {
    var c;
    if (!/^([0-1]?[0-9]|2[0-3]):[0-5][0-9](:[0-5][0-9])?$/.test(r))
      return {
        isValid: !1,
        error: "Invalid time format. Use HH:MM or HH:MM:SS format."
      };
    const n = this.getSessionHierarchy(), s = [];
    for (const [i, u] of Object.entries(n.macrosByType))
      this.isTimeInRange(r, u.timeRange) && s.push({
        type: i,
        macro: u,
        isSubPeriod: !!u.parentMacro
      });
    if (s.length > 0) {
      const u = s.sort((p, d) => {
        if (p.isSubPeriod && !d.isSubPeriod)
          return -1;
        if (!p.isSubPeriod && d.isSubPeriod)
          return 1;
        const h = this.timeToMinutes(p.macro.timeRange.end) - this.timeToMinutes(p.macro.timeRange.start), x = this.timeToMinutes(d.macro.timeRange.end) - this.timeToMinutes(d.macro.timeRange.start);
        return h - x;
      })[0], f = s.length > 1;
      return {
        isValid: !0,
        suggestedMacro: u.type,
        suggestedSession: u.macro.parentSession || ((c = u.macro.spansSessions) == null ? void 0 : c[0]),
        warning: f ? `Time falls within ${s.length} overlapping macro periods. Suggesting most specific: ${u.macro.name}` : void 0
      };
    }
    for (const i of n.sessions)
      if (this.isTimeInRange(r, i.timeRange))
        return {
          isValid: !0,
          suggestedSession: i.type,
          warning: "Time falls within session but not in a specific macro period."
        };
    return {
      isValid: !0,
      warning: "Time does not fall within any defined session or macro period."
    };
  }
  /**
   * Get session by type
   */
  static getSession(r) {
    return this.getSessionHierarchy().sessionsByType[r] || null;
  }
  /**
   * Get macro period by type
   */
  static getMacroPeriod(r) {
    return this.getSessionHierarchy().macrosByType[r] || null;
  }
  /**
   * Get all macro periods for a session
   */
  static getMacroPeriodsForSession(r) {
    const t = this.getSession(r);
    return (t == null ? void 0 : t.macroPeriods) || [];
  }
  /**
   * Create a session selection
   */
  static createSessionSelection(r, t, n) {
    if (t) {
      const s = this.getMacroPeriod(t);
      return {
        session: s == null ? void 0 : s.parentSession,
        macroPeriod: t,
        displayLabel: (s == null ? void 0 : s.name) || "Unknown Macro",
        selectionType: "macro"
      };
    }
    if (r) {
      const s = this.getSession(r);
      return {
        session: r,
        displayLabel: (s == null ? void 0 : s.name) || "Unknown Session",
        selectionType: "session"
      };
    }
    return n ? {
      customTimeRange: n,
      displayLabel: `${n.start} - ${n.end}`,
      selectionType: "custom"
    } : {
      displayLabel: "No Selection",
      selectionType: "custom"
    };
  }
  /**
   * Filter sessions and macros based on criteria
   */
  static filterSessions(r = {}) {
    var c, i;
    const t = this.getSessionHierarchy();
    let n = [...t.sessions], s = Object.values(t.macrosByType);
    return r.activeOnly && (n = n.filter((u) => u.isActive)), (c = r.sessionTypes) != null && c.length && (n = n.filter((u) => r.sessionTypes.includes(u.type))), (i = r.macroTypes) != null && i.length && (s = s.filter((u) => r.macroTypes.includes(u.type))), r.highProbabilityOnly && (s = s.filter((u) => u.isHighProbability)), r.minVolatility !== void 0 && (s = s.filter((u) => u.volatilityLevel >= r.minVolatility)), r.maxVolatility !== void 0 && (s = s.filter((u) => u.volatilityLevel <= r.maxVolatility)), {
      sessions: n,
      macros: s
    };
  }
  /**
   * Get current active session based on current time
   */
  static getCurrentSession() {
    const r = /* @__PURE__ */ new Date(), t = `${r.getHours().toString().padStart(2, "0")}:${r.getMinutes().toString().padStart(2, "0")}:00`, n = this.validateTime(t);
    return n.suggestedMacro ? this.createSessionSelection(n.suggestedSession, n.suggestedMacro) : n.suggestedSession ? this.createSessionSelection(n.suggestedSession) : null;
  }
  /**
   * Check if two time ranges overlap
   */
  static timeRangesOverlap(r, t) {
    const n = this.timeToMinutes(r.start), s = this.timeToMinutes(r.end), c = this.timeToMinutes(t.start), i = this.timeToMinutes(t.end);
    return Math.max(n, c) < Math.min(s, i);
  }
  /**
   * Get display options for UI dropdowns
   */
  static getDisplayOptions() {
    const r = this.getSessionHierarchy(), t = r.sessions.map((s) => ({
      value: s.type,
      label: s.name,
      group: "Sessions"
    })), n = Object.values(r.macrosByType).map((s) => {
      var c;
      return {
        value: s.type,
        label: s.name,
        group: ((c = r.sessionsByType[s.parentSession]) == null ? void 0 : c.name) || "Other",
        parentSession: s.parentSession
      };
    });
    return {
      sessionOptions: t,
      macroOptions: n
    };
  }
  /**
   * Get all overlapping macro periods for a given time
   */
  static getOverlappingMacros(r) {
    const t = this.getSessionHierarchy(), n = [];
    for (const [s, c] of Object.entries(t.macrosByType))
      this.isTimeInRange(r, c.timeRange) && n.push({
        type: s,
        macro: c,
        isSubPeriod: !!c.parentMacro,
        isMultiSession: !!c.spansSessions
      });
    return n.sort((s, c) => {
      if (s.isSubPeriod && !c.isSubPeriod)
        return -1;
      if (!s.isSubPeriod && c.isSubPeriod)
        return 1;
      const i = this.timeToMinutes(s.macro.timeRange.end) - this.timeToMinutes(s.macro.timeRange.start), u = this.timeToMinutes(c.macro.timeRange.end) - this.timeToMinutes(c.macro.timeRange.start);
      return i - u;
    });
  }
  /**
   * Get multi-session macros
   */
  static getMultiSessionMacros() {
    return this.getSessionHierarchy().multiSessionMacros || [];
  }
  /**
   * Check if a macro period has sub-periods
   */
  static hasSubPeriods(r) {
    const t = this.getMacroPeriod(r);
    return !!(t != null && t.subPeriods && t.subPeriods.length > 0);
  }
  /**
   * Get sub-periods for a macro
   */
  static getSubPeriods(r) {
    const t = this.getMacroPeriod(r);
    return (t == null ? void 0 : t.subPeriods) || [];
  }
  /**
   * Convert legacy session string to new session selection
   */
  static convertLegacySession(r) {
    const n = {
      "NY Open": {
        session: X.NEW_YORK_AM
      },
      "London Open": {
        session: X.LONDON
      },
      "Lunch Macro": {
        macro: M.LUNCH_MACRO_EXTENDED
      },
      // Updated to use extended lunch macro
      "Lunch Macro (11:30-13:30)": {
        macro: M.LUNCH_MACRO_EXTENDED
      },
      "Lunch Macro (12:00-13:30)": {
        macro: M.LUNCH_MACRO
      },
      MOC: {
        macro: M.MOC
      },
      Overnight: {
        session: X.OVERNIGHT
      },
      "Pre-Market": {
        session: X.PRE_MARKET
      },
      "After Hours": {
        session: X.AFTER_HOURS
      },
      "Power Hour": {
        macro: M.POWER_HOUR
      },
      "10:50-11:10": {
        macro: M.MID_MORNING_REVERSION
      },
      "11:50-12:10": {
        macro: M.PRE_LUNCH
      },
      "15:15-15:45": {
        macro: M.POWER_HOUR
      }
    }[r];
    return n ? this.createSessionSelection(n.session, n.macro) : null;
  }
}
ie.hierarchy = null;
const Rs = (e = {}) => {
  const {
    initialSelection: r,
    autoDetectCurrent: t = !1,
    filterOptions: n = {},
    onSelectionChange: s,
    validateTimes: c = !0
  } = e, [i, u] = Q(r || {
    displayLabel: "No Selection",
    selectionType: "custom"
  }), f = U(() => ie.getCurrentSession(), []), p = U(() => f !== null, [f]), {
    availableSessions: d,
    availableMacros: h
  } = U(() => {
    const {
      sessions: R,
      macros: T
    } = ie.filterSessions(n), {
      sessionOptions: _,
      macroOptions: N
    } = ie.getDisplayOptions(), Y = _.filter((k) => R.some((V) => V.type === k.value)), W = N.filter((k) => T.some((V) => V.type === k.value));
    return {
      availableSessions: Y,
      availableMacros: W
    };
  }, [n]), x = U(() => {
    const R = ie.getSessionHierarchy();
    return d.map((T) => {
      R.sessionsByType[T.value];
      const _ = h.filter((N) => N.parentSession === T.value).map((N) => ({
        value: N.value,
        label: N.label
      }));
      return {
        session: T.value,
        sessionLabel: T.label,
        macros: _
      };
    });
  }, [d, h]);
  ue(() => {
    t && f && !r && u(f);
  }, [t, f, r]), ue(() => {
    s == null || s(i);
  }, [i, s]);
  const y = F((R) => {
    const T = ie.createSessionSelection(R);
    u(T);
  }, []), m = F((R) => {
    const T = ie.createSessionSelection(void 0, R);
    u(T);
  }, []), b = F((R) => {
    const T = ie.createSessionSelection(void 0, void 0, R);
    u(T);
  }, []), C = F(() => {
    u({
      displayLabel: "No Selection",
      selectionType: "custom"
    });
  }, []), w = F((R) => c ? ie.validateTime(R) : {
    isValid: !0
  }, [c]), S = U(() => {
    if (i.selectionType === "session" && i.session)
      return ie.getSession(i.session) !== null;
    if (i.selectionType === "macro" && i.macroPeriod)
      return ie.getMacroPeriod(i.macroPeriod) !== null;
    if (i.selectionType === "custom" && i.customTimeRange) {
      const R = w(i.customTimeRange.start), T = w(i.customTimeRange.end);
      return R.isValid && T.isValid;
    }
    return i.selectionType === "custom" && !i.customTimeRange;
  }, [i, w]), L = F((R) => ie.getSession(R), []), A = F((R) => ie.getMacroPeriod(R), []), O = F((R) => ie.convertLegacySession(R), []);
  return {
    // State
    selection: i,
    // Selection methods
    selectSession: y,
    selectMacro: m,
    selectCustomRange: b,
    clearSelection: C,
    // Validation
    validateTime: w,
    isValidSelection: S,
    // Options
    availableSessions: d,
    availableMacros: h,
    hierarchicalOptions: x,
    // Current session
    currentSession: f,
    isCurrentSessionActive: p,
    // Utilities
    getSessionDetails: L,
    getMacroDetails: A,
    convertLegacySession: O
  };
}, _s = /* @__PURE__ */ a.div.withConfig({
  displayName: "Container",
  componentId: "sc-1reqqnl-0"
})(["display:flex;flex-direction:column;gap:", ";"], ({
  theme: e
}) => {
  var r;
  return ((r = e.spacing) == null ? void 0 : r.sm) || "8px";
}), Ls = /* @__PURE__ */ a.div.withConfig({
  displayName: "SelectorContainer",
  componentId: "sc-1reqqnl-1"
})(["position:relative;border:1px solid ", ";border-radius:", ";background:", ";transition:all 0.2s ease;opacity:", ";pointer-events:", ";&:hover{border-color:", "40;}&:focus-within{border-color:", ";box-shadow:0 0 0 3px ", "20;}"], ({
  theme: e,
  hasError: r
}) => {
  var t, n;
  return r ? ((t = e.colors) == null ? void 0 : t.error) || "#ef4444" : ((n = e.colors) == null ? void 0 : n.border) || "#4b5563";
}, ({
  theme: e
}) => {
  var r;
  return ((r = e.borderRadius) == null ? void 0 : r.md) || "6px";
}, ({
  theme: e
}) => {
  var r;
  return ((r = e.colors) == null ? void 0 : r.surface) || "#1f2937";
}, ({
  disabled: e
}) => e ? 0.6 : 1, ({
  disabled: e
}) => e ? "none" : "auto", ({
  theme: e,
  hasError: r
}) => {
  var t, n;
  return r ? ((t = e.colors) == null ? void 0 : t.error) || "#ef4444" : ((n = e.colors) == null ? void 0 : n.primary) || "#dc2626";
}, ({
  theme: e
}) => {
  var r;
  return ((r = e.colors) == null ? void 0 : r.primary) || "#dc2626";
}, ({
  theme: e
}) => {
  var r;
  return ((r = e.colors) == null ? void 0 : r.primary) || "#dc2626";
}), Ps = /* @__PURE__ */ a.div.withConfig({
  displayName: "SelectedValue",
  componentId: "sc-1reqqnl-2"
})(["padding:", ";color:", ";font-size:", ";cursor:pointer;display:flex;align-items:center;justify-content:space-between;"], ({
  theme: e
}) => {
  var r;
  return ((r = e.spacing) == null ? void 0 : r.md) || "12px";
}, ({
  theme: e
}) => {
  var r;
  return ((r = e.colors) == null ? void 0 : r.textPrimary) || "#ffffff";
}, ({
  theme: e
}) => {
  var r;
  return ((r = e.fontSizes) == null ? void 0 : r.md) || "1rem";
}), Ms = /* @__PURE__ */ a.div.withConfig({
  displayName: "DropdownIcon",
  componentId: "sc-1reqqnl-3"
})(["transition:transform 0.2s ease;transform:", ";color:", ";"], ({
  isOpen: e
}) => e ? "rotate(180deg)" : "rotate(0deg)", ({
  theme: e
}) => {
  var r;
  return ((r = e.colors) == null ? void 0 : r.textSecondary) || "#9ca3af";
}), Ds = /* @__PURE__ */ a.div.withConfig({
  displayName: "DropdownMenu",
  componentId: "sc-1reqqnl-4"
})(["position:absolute;top:100%;left:0;right:0;z-index:1000;background:", ";border:1px solid ", ";border-radius:", ";box-shadow:0 10px 25px -5px rgba(0,0,0,0.3);max-height:400px;overflow-y:auto;display:", ";"], ({
  theme: e
}) => {
  var r;
  return ((r = e.colors) == null ? void 0 : r.surface) || "#1f2937";
}, ({
  theme: e
}) => {
  var r;
  return ((r = e.colors) == null ? void 0 : r.border) || "#4b5563";
}, ({
  theme: e
}) => {
  var r;
  return ((r = e.borderRadius) == null ? void 0 : r.md) || "6px";
}, ({
  isOpen: e
}) => e ? "block" : "none"), Os = /* @__PURE__ */ a.div.withConfig({
  displayName: "MultiSessionGroup",
  componentId: "sc-1reqqnl-5"
})(["border-bottom:1px solid ", ";background:", ";&:last-child{border-bottom:none;}"], ({
  theme: e
}) => {
  var r;
  return ((r = e.colors) == null ? void 0 : r.border) || "#4b5563";
}, ({
  theme: e
}) => {
  var r;
  return ((r = e.colors) == null ? void 0 : r.background) || "#111827";
}), $s = /* @__PURE__ */ a.div.withConfig({
  displayName: "MultiSessionHeader",
  componentId: "sc-1reqqnl-6"
})(["padding:", ";background:", ";color:", ";font-weight:600;cursor:pointer;display:flex;align-items:center;justify-content:space-between;transition:background-color 0.2s ease;border-left:3px solid ", ";&:hover{background:", "40;}"], ({
  theme: e
}) => {
  var r;
  return ((r = e.spacing) == null ? void 0 : r.md) || "12px";
}, ({
  theme: e,
  isSelected: r
}) => {
  var t, n;
  return r ? ((t = e.colors) == null ? void 0 : t.primary) || "#dc2626" : ((n = e.colors) == null ? void 0 : n.surface) || "#1f2937";
}, ({
  theme: e,
  isSelected: r
}) => {
  var t;
  return r ? "#ffffff" : ((t = e.colors) == null ? void 0 : t.textPrimary) || "#ffffff";
}, ({
  theme: e
}) => {
  var r;
  return ((r = e.colors) == null ? void 0 : r.warning) || "#f59e0b";
}, ({
  theme: e,
  isSelected: r
}) => {
  var t, n;
  return r ? ((t = e.colors) == null ? void 0 : t.primary) || "#dc2626" : ((n = e.colors) == null ? void 0 : n.border) || "#4b5563";
}), As = /* @__PURE__ */ a.div.withConfig({
  displayName: "MultiSessionIndicator",
  componentId: "sc-1reqqnl-7"
})(["display:inline-flex;align-items:center;gap:", ";font-size:", ";color:", ";font-weight:500;"], ({
  theme: e
}) => {
  var r;
  return ((r = e.spacing) == null ? void 0 : r.xs) || "4px";
}, ({
  theme: e
}) => {
  var r;
  return ((r = e.fontSizes) == null ? void 0 : r.xs) || "0.75rem";
}, ({
  theme: e
}) => {
  var r;
  return ((r = e.colors) == null ? void 0 : r.warning) || "#f59e0b";
}), zs = /* @__PURE__ */ a.div.withConfig({
  displayName: "SessionGroup",
  componentId: "sc-1reqqnl-8"
})(["border-bottom:1px solid ", ";&:last-child{border-bottom:none;}"], ({
  theme: e
}) => {
  var r;
  return ((r = e.colors) == null ? void 0 : r.border) || "#4b5563";
}), Fs = /* @__PURE__ */ a.div.withConfig({
  displayName: "SessionHeader",
  componentId: "sc-1reqqnl-9"
})(["padding:", ";background:", ";color:", ";font-weight:600;cursor:pointer;display:flex;align-items:center;justify-content:space-between;transition:background-color 0.2s ease;&:hover{background:", "40;}"], ({
  theme: e
}) => {
  var r;
  return ((r = e.spacing) == null ? void 0 : r.md) || "12px";
}, ({
  theme: e,
  isSelected: r
}) => {
  var t;
  return r ? ((t = e.colors) == null ? void 0 : t.primary) || "#dc2626" : "transparent";
}, ({
  theme: e,
  isSelected: r
}) => {
  var t;
  return r ? "#ffffff" : ((t = e.colors) == null ? void 0 : t.textPrimary) || "#ffffff";
}, ({
  theme: e,
  isSelected: r
}) => {
  var t, n;
  return r ? ((t = e.colors) == null ? void 0 : t.primary) || "#dc2626" : ((n = e.colors) == null ? void 0 : n.border) || "#4b5563";
}), Bs = /* @__PURE__ */ a.div.withConfig({
  displayName: "MacroList",
  componentId: "sc-1reqqnl-10"
})(["background:", ";"], ({
  theme: e
}) => {
  var r;
  return ((r = e.colors) == null ? void 0 : r.background) || "#111827";
}), qs = /* @__PURE__ */ a.div.withConfig({
  displayName: "MacroItem",
  componentId: "sc-1reqqnl-11"
})(["padding:", " ", ";color:", ";cursor:pointer;font-size:", ";transition:all 0.2s ease;border-left:3px solid ", ";&:hover{background:", "20;color:", ";}"], ({
  theme: e
}) => {
  var r;
  return ((r = e.spacing) == null ? void 0 : r.sm) || "8px";
}, ({
  theme: e
}) => {
  var r;
  return ((r = e.spacing) == null ? void 0 : r.lg) || "24px";
}, ({
  theme: e,
  isSelected: r
}) => {
  var t, n;
  return r ? ((t = e.colors) == null ? void 0 : t.primary) || "#dc2626" : ((n = e.colors) == null ? void 0 : n.textSecondary) || "#9ca3af";
}, ({
  theme: e
}) => {
  var r;
  return ((r = e.fontSizes) == null ? void 0 : r.sm) || "0.875rem";
}, ({
  theme: e,
  isSelected: r
}) => {
  var t;
  return r ? ((t = e.colors) == null ? void 0 : t.primary) || "#dc2626" : "transparent";
}, ({
  theme: e
}) => {
  var r;
  return ((r = e.colors) == null ? void 0 : r.border) || "#4b5563";
}, ({
  theme: e
}) => {
  var r;
  return ((r = e.colors) == null ? void 0 : r.textPrimary) || "#ffffff";
}), Hs = /* @__PURE__ */ a.div.withConfig({
  displayName: "CurrentSessionIndicator",
  componentId: "sc-1reqqnl-12"
})(["display:inline-flex;align-items:center;gap:", ";font-size:", ";color:", ";font-weight:500;"], ({
  theme: e
}) => {
  var r;
  return ((r = e.spacing) == null ? void 0 : r.xs) || "4px";
}, ({
  theme: e
}) => {
  var r;
  return ((r = e.fontSizes) == null ? void 0 : r.xs) || "0.75rem";
}, ({
  theme: e
}) => {
  var r;
  return ((r = e.colors) == null ? void 0 : r.success) || "#10b981";
}), Vs = /* @__PURE__ */ a.div.withConfig({
  displayName: "ErrorMessage",
  componentId: "sc-1reqqnl-13"
})(["color:", ";font-size:", ";margin-top:", ";"], ({
  theme: e
}) => {
  var r;
  return ((r = e.colors) == null ? void 0 : r.error) || "#ef4444";
}, ({
  theme: e
}) => {
  var r;
  return ((r = e.fontSizes) == null ? void 0 : r.sm) || "0.875rem";
}, ({
  theme: e
}) => {
  var r;
  return ((r = e.spacing) == null ? void 0 : r.xs) || "4px";
}), Za = ({
  value: e,
  onChange: r,
  showMacroPeriods: t = !0,
  showCurrentSession: n = !0,
  allowCustomRange: s = !1,
  placeholder: c = "Select session or macro period",
  disabled: i = !1,
  error: u,
  className: f
}) => {
  const [p, d] = Q(!1), {
    hierarchicalOptions: h,
    currentSession: x,
    isCurrentSessionActive: y,
    selectSession: m,
    selectMacro: b
  } = Rs({
    onSelectionChange: r
  }), C = U(() => ie.getMultiSessionMacros(), []), w = U(() => e != null && e.displayLabel ? e.displayLabel : c, [e, c]), S = (T) => {
    m(T), d(!1);
  }, L = (T) => {
    b(T), d(!1);
  }, A = (T) => (e == null ? void 0 : e.session) === T && (e == null ? void 0 : e.selectionType) === "session", O = (T) => (e == null ? void 0 : e.macroPeriod) === T && (e == null ? void 0 : e.selectionType) === "macro", R = (T) => (x == null ? void 0 : x.session) === T;
  return /* @__PURE__ */ o.jsxs(_s, { className: f, hasError: !!u, children: [
    /* @__PURE__ */ o.jsxs(Ls, { hasError: !!u, disabled: i, onClick: () => !i && d(!p), children: [
      /* @__PURE__ */ o.jsxs(Ps, { children: [
        /* @__PURE__ */ o.jsx("span", { children: w }),
        /* @__PURE__ */ o.jsx(Ms, { isOpen: p, children: "▼" })
      ] }),
      /* @__PURE__ */ o.jsxs(Ds, { isOpen: p, children: [
        t && C.length > 0 && /* @__PURE__ */ o.jsx(Os, { children: C.map((T) => /* @__PURE__ */ o.jsxs($s, { isSelected: O(T.type), onClick: (_) => {
          _.stopPropagation(), L(T.type);
        }, children: [
          /* @__PURE__ */ o.jsx("span", { children: T.name }),
          /* @__PURE__ */ o.jsx(As, { children: "🌐 MULTI-SESSION" })
        ] }, T.type)) }),
        h.map(({
          session: T,
          sessionLabel: _,
          macros: N
        }) => /* @__PURE__ */ o.jsxs(zs, { children: [
          /* @__PURE__ */ o.jsxs(Fs, { isSelected: A(T), onClick: (Y) => {
            Y.stopPropagation(), S(T);
          }, children: [
            /* @__PURE__ */ o.jsx("span", { children: _ }),
            n && R(T) && /* @__PURE__ */ o.jsx(Hs, { children: "🔴 LIVE" })
          ] }),
          t && N.length > 0 && /* @__PURE__ */ o.jsx(Bs, { children: N.map(({
            value: Y,
            label: W
          }) => /* @__PURE__ */ o.jsxs(qs, { isSelected: O(Y), onClick: (k) => {
            k.stopPropagation(), L(Y);
          }, children: [
            W,
            ie.hasSubPeriods(Y) && /* @__PURE__ */ o.jsx("span", { style: {
              marginLeft: "8px",
              fontSize: "0.75rem",
              opacity: 0.7
            }, children: "📋 Has sub-periods" })
          ] }, Y)) })
        ] }, T))
      ] })
    ] }),
    u && /* @__PURE__ */ o.jsx(Vs, { children: u })
  ] });
}, E = {
  DATE: "date",
  SYMBOL: "symbol",
  DIRECTION: "direction",
  MODEL_TYPE: "model_type",
  SESSION: "session",
  ENTRY_PRICE: "entry_price",
  EXIT_PRICE: "exit_price",
  R_MULTIPLE: "r_multiple",
  ACHIEVED_PL: "achieved_pl",
  WIN_LOSS: "win_loss",
  PATTERN_QUALITY: "pattern_quality_rating",
  ENTRY_TIME: "entry_time",
  EXIT_TIME: "exit_time"
}, rr = /* @__PURE__ */ a.span.withConfig({
  displayName: "ProfitLossCell",
  componentId: "sc-14bks31-0"
})(["color:", ";font-weight:", ";"], ({
  isProfit: e,
  theme: r
}) => e ? r.colors.success || "#10b981" : r.colors.error || "#ef4444", ({
  theme: e
}) => {
  var r;
  return ((r = e.fontWeights) == null ? void 0 : r.semibold) || 600;
}), lt = /* @__PURE__ */ a(He).withConfig({
  displayName: "DirectionBadge",
  componentId: "sc-14bks31-1"
})(["background-color:", ";color:white;"], ({
  direction: e,
  theme: r
}) => e === "Long" ? r.colors.success || "#10b981" : r.colors.error || "#ef4444"), dt = /* @__PURE__ */ a.span.withConfig({
  displayName: "QualityRating",
  componentId: "sc-14bks31-2"
})(["color:", ";font-weight:", ";"], ({
  rating: e,
  theme: r
}) => e >= 4 ? r.colors.success || "#10b981" : e >= 3 ? r.colors.warning || "#f59e0b" : r.colors.error || "#ef4444", ({
  theme: e
}) => {
  var r;
  return ((r = e.fontWeights) == null ? void 0 : r.semibold) || 600;
}), tr = /* @__PURE__ */ a.span.withConfig({
  displayName: "RMultipleCell",
  componentId: "sc-14bks31-3"
})(["color:", ";font-weight:", ";"], ({
  rMultiple: e,
  theme: r
}) => e > 0 ? r.colors.success || "#10b981" : r.colors.error || "#ef4444", ({
  theme: e
}) => {
  var r;
  return ((r = e.fontWeights) == null ? void 0 : r.semibold) || 600;
}), Me = (e) => e == null ? "-" : new Intl.NumberFormat("en-US", {
  style: "currency",
  currency: "USD",
  minimumFractionDigits: 2
}).format(e), or = (e) => {
  try {
    return new Date(e).toLocaleDateString("en-US", {
      year: "numeric",
      month: "short",
      day: "numeric"
    });
  } catch {
    return e;
  }
}, Br = (e) => e || "-", Us = () => [{
  id: E.DATE,
  header: "Date",
  sortable: !0,
  width: "100px",
  cell: (e) => or(e.trade[E.DATE])
}, {
  id: E.SYMBOL,
  header: "Symbol",
  sortable: !0,
  width: "80px",
  cell: (e) => e.trade.market || "MNQ"
}, {
  id: E.DIRECTION,
  header: "Direction",
  sortable: !0,
  width: "80px",
  align: "center",
  cell: (e) => /* @__PURE__ */ o.jsx(lt, { direction: e.trade[E.DIRECTION], size: "small", children: e.trade[E.DIRECTION] })
}, {
  id: E.MODEL_TYPE,
  header: "Model",
  sortable: !0,
  width: "120px",
  cell: (e) => e.trade[E.MODEL_TYPE] || "-"
}, {
  id: E.SESSION,
  header: "Session",
  sortable: !0,
  width: "120px",
  cell: (e) => e.trade[E.SESSION] || "-"
}, {
  id: E.ENTRY_PRICE,
  header: "Entry",
  sortable: !0,
  width: "100px",
  align: "right",
  cell: (e) => Me(e.trade[E.ENTRY_PRICE])
}, {
  id: E.EXIT_PRICE,
  header: "Exit",
  sortable: !0,
  width: "100px",
  align: "right",
  cell: (e) => Me(e.trade[E.EXIT_PRICE])
}, {
  id: E.R_MULTIPLE,
  header: "R Multiple",
  sortable: !0,
  width: "100px",
  align: "right",
  cell: (e) => /* @__PURE__ */ o.jsx(tr, { rMultiple: e.trade[E.R_MULTIPLE] || 0, children: e.trade[E.R_MULTIPLE] ? `${e.trade[E.R_MULTIPLE].toFixed(2)}R` : "-" })
}, {
  id: E.ACHIEVED_PL,
  header: "P&L",
  sortable: !0,
  width: "100px",
  align: "right",
  cell: (e) => /* @__PURE__ */ o.jsx(rr, { isProfit: (e.trade[E.ACHIEVED_PL] || 0) > 0, children: Me(e.trade[E.ACHIEVED_PL]) })
}, {
  id: E.WIN_LOSS,
  header: "Result",
  sortable: !0,
  width: "80px",
  align: "center",
  cell: (e) => /* @__PURE__ */ o.jsx(He, { variant: e.trade[E.WIN_LOSS] === "Win" ? "success" : "error", size: "small", children: e.trade[E.WIN_LOSS] || "-" })
}, {
  id: E.PATTERN_QUALITY,
  header: "Quality",
  sortable: !0,
  width: "80px",
  align: "center",
  cell: (e) => /* @__PURE__ */ o.jsx(dt, { rating: e.trade[E.PATTERN_QUALITY] || 0, children: e.trade[E.PATTERN_QUALITY] ? `${e.trade[E.PATTERN_QUALITY]}/5` : "-" })
}, {
  id: E.ENTRY_TIME,
  header: "Entry Time",
  sortable: !0,
  width: "100px",
  align: "center",
  cell: (e) => Br(e.trade[E.ENTRY_TIME])
}, {
  id: E.EXIT_TIME,
  header: "Exit Time",
  sortable: !0,
  width: "100px",
  align: "center",
  cell: (e) => Br(e.trade[E.EXIT_TIME])
}], Ys = () => [{
  id: E.DATE,
  header: "Date",
  sortable: !0,
  width: "90px",
  cell: (e) => or(e.trade[E.DATE])
}, {
  id: E.SYMBOL,
  header: "Symbol",
  sortable: !0,
  width: "60px",
  cell: (e) => e.trade.market || "MNQ"
}, {
  id: E.DIRECTION,
  header: "Dir",
  sortable: !0,
  width: "50px",
  align: "center",
  cell: (e) => /* @__PURE__ */ o.jsx(lt, { direction: e.trade[E.DIRECTION], size: "small", children: e.trade[E.DIRECTION].charAt(0) })
}, {
  id: E.R_MULTIPLE,
  header: "R",
  sortable: !0,
  width: "60px",
  align: "right",
  cell: (e) => /* @__PURE__ */ o.jsx(tr, { rMultiple: e.trade[E.R_MULTIPLE] || 0, children: e.trade[E.R_MULTIPLE] ? `${e.trade[E.R_MULTIPLE].toFixed(1)}R` : "-" })
}, {
  id: E.ACHIEVED_PL,
  header: "P&L",
  sortable: !0,
  width: "80px",
  align: "right",
  cell: (e) => /* @__PURE__ */ o.jsx(rr, { isProfit: (e.trade[E.ACHIEVED_PL] || 0) > 0, children: Me(e.trade[E.ACHIEVED_PL]) })
}, {
  id: E.WIN_LOSS,
  header: "Result",
  sortable: !0,
  width: "60px",
  align: "center",
  cell: (e) => /* @__PURE__ */ o.jsx(He, { variant: e.trade[E.WIN_LOSS] === "Win" ? "success" : "error", size: "small", children: e.trade[E.WIN_LOSS] === "Win" ? "W" : e.trade[E.WIN_LOSS] === "Loss" ? "L" : "-" })
}], Ws = () => [{
  id: E.DATE,
  header: "Date",
  sortable: !0,
  width: "100px",
  cell: (e) => or(e.trade[E.DATE])
}, {
  id: E.MODEL_TYPE,
  header: "Model",
  sortable: !0,
  width: "120px",
  cell: (e) => e.trade[E.MODEL_TYPE] || "-"
}, {
  id: E.SESSION,
  header: "Session",
  sortable: !0,
  width: "120px",
  cell: (e) => e.trade[E.SESSION] || "-"
}, {
  id: E.R_MULTIPLE,
  header: "R Multiple",
  sortable: !0,
  width: "100px",
  align: "right",
  cell: (e) => /* @__PURE__ */ o.jsx(tr, { rMultiple: e.trade[E.R_MULTIPLE] || 0, children: e.trade[E.R_MULTIPLE] ? `${e.trade[E.R_MULTIPLE].toFixed(2)}R` : "-" })
}, {
  id: E.ACHIEVED_PL,
  header: "P&L",
  sortable: !0,
  width: "100px",
  align: "right",
  cell: (e) => /* @__PURE__ */ o.jsx(rr, { isProfit: (e.trade[E.ACHIEVED_PL] || 0) > 0, children: Me(e.trade[E.ACHIEVED_PL]) })
}, {
  id: E.PATTERN_QUALITY,
  header: "Quality",
  sortable: !0,
  width: "80px",
  align: "center",
  cell: (e) => /* @__PURE__ */ o.jsx(dt, { rating: e.trade[E.PATTERN_QUALITY] || 0, children: e.trade[E.PATTERN_QUALITY] ? `${e.trade[E.PATTERN_QUALITY]}/5` : "-" })
}, {
  id: E.WIN_LOSS,
  header: "Result",
  sortable: !0,
  width: "80px",
  align: "center",
  cell: (e) => /* @__PURE__ */ o.jsx(He, { variant: e.trade[E.WIN_LOSS] === "Win" ? "success" : "error", size: "small", children: e.trade[E.WIN_LOSS] || "-" })
}], Gs = /* @__PURE__ */ a.tr.withConfig({
  displayName: "TableRow",
  componentId: "sc-uyrnn-0"
})(["", " ", " ", " ", " ", ""], ({
  striped: e,
  theme: r,
  isSelected: t
}) => {
  var n;
  return e && !t && g(["&:nth-child(even){background-color:", "50;}"], ((n = r.colors) == null ? void 0 : n.background) || "#f8f9fa");
}, ({
  hoverable: e,
  theme: r,
  isSelected: t
}) => {
  var n;
  return e && !t && g(["&:hover{background-color:", "aa;}"], ((n = r.colors) == null ? void 0 : n.background) || "#f8f9fa");
}, ({
  isSelected: e,
  theme: r
}) => {
  var t;
  return e && g(["background-color:", "15;"], ((t = r.colors) == null ? void 0 : t.primary) || "#3b82f6");
}, ({
  isClickable: e
}) => e && g(["cursor:pointer;"]), ({
  isExpanded: e,
  theme: r
}) => {
  var t;
  return e && g(["border-bottom:2px solid ", ";"], ((t = r.colors) == null ? void 0 : t.primary) || "#3b82f6");
}), qr = /* @__PURE__ */ a.td.withConfig({
  displayName: "TableCell",
  componentId: "sc-uyrnn-1"
})(["text-align:", ";border-bottom:1px solid ", ";color:", ";padding:", " ", ";vertical-align:middle;"], ({
  align: e
}) => e || "left", ({
  theme: e
}) => {
  var r;
  return ((r = e.colors) == null ? void 0 : r.border) || "#e5e7eb";
}, ({
  theme: e
}) => {
  var r;
  return ((r = e.colors) == null ? void 0 : r.textPrimary) || "#111827";
}, ({
  theme: e
}) => {
  var r;
  return ((r = e.spacing) == null ? void 0 : r.sm) || "12px";
}, ({
  theme: e
}) => {
  var r;
  return ((r = e.spacing) == null ? void 0 : r.md) || "16px";
}), Ks = /* @__PURE__ */ a.tr.withConfig({
  displayName: "ExpandedRow",
  componentId: "sc-uyrnn-2"
})(["display:", ";"], ({
  isVisible: e
}) => e ? "table-row" : "none"), Qs = /* @__PURE__ */ a.td.withConfig({
  displayName: "ExpandedCell",
  componentId: "sc-uyrnn-3"
})(["padding:0;border-bottom:1px solid ", ";"], ({
  theme: e
}) => {
  var r;
  return ((r = e.colors) == null ? void 0 : r.border) || "#e5e7eb";
}), Xs = /* @__PURE__ */ a.div.withConfig({
  displayName: "ExpandedContent",
  componentId: "sc-uyrnn-4"
})(["padding:", ";background-color:", "30;border-left:3px solid ", ";"], ({
  theme: e
}) => {
  var r;
  return ((r = e.spacing) == null ? void 0 : r.md) || "16px";
}, ({
  theme: e
}) => {
  var r;
  return ((r = e.colors) == null ? void 0 : r.background) || "#f8f9fa";
}, ({
  theme: e
}) => {
  var r;
  return ((r = e.colors) == null ? void 0 : r.primary) || "#3b82f6";
}), Js = /* @__PURE__ */ a.button.withConfig({
  displayName: "ExpandButton",
  componentId: "sc-uyrnn-5"
})(["background:none;border:none;cursor:pointer;padding:", ";color:", ";font-size:", ";display:flex;align-items:center;justify-content:center;border-radius:", ";transition:all 0.2s ease;&:hover{background-color:", ";color:", ";}&:focus{outline:2px solid ", ";outline-offset:2px;}"], ({
  theme: e
}) => {
  var r;
  return ((r = e.spacing) == null ? void 0 : r.xs) || "8px";
}, ({
  theme: e
}) => {
  var r;
  return ((r = e.colors) == null ? void 0 : r.textSecondary) || "#6b7280";
}, ({
  theme: e
}) => {
  var r;
  return ((r = e.fontSizes) == null ? void 0 : r.sm) || "14px";
}, ({
  theme: e
}) => {
  var r;
  return ((r = e.borderRadius) == null ? void 0 : r.sm) || "4px";
}, ({
  theme: e
}) => {
  var r;
  return ((r = e.colors) == null ? void 0 : r.background) || "#f8f9fa";
}, ({
  theme: e
}) => {
  var r;
  return ((r = e.colors) == null ? void 0 : r.primary) || "#3b82f6";
}, ({
  theme: e
}) => {
  var r;
  return ((r = e.colors) == null ? void 0 : r.primary) || "#3b82f6";
}), Zs = /* @__PURE__ */ a.span.withConfig({
  displayName: "ExpandIcon",
  componentId: "sc-uyrnn-6"
})(["display:inline-block;transition:transform 0.2s ease;transform:", ";&::after{content:'▶';}"], ({
  isExpanded: e
}) => e ? "rotate(90deg)" : "rotate(0deg)"), ei = /* @__PURE__ */ a.div.withConfig({
  displayName: "TradeDetails",
  componentId: "sc-uyrnn-7"
})(["display:grid;grid-template-columns:repeat(auto-fit,minmax(200px,1fr));gap:", ";"], ({
  theme: e
}) => {
  var r;
  return ((r = e.spacing) == null ? void 0 : r.md) || "16px";
}), _e = /* @__PURE__ */ a.div.withConfig({
  displayName: "DetailGroup",
  componentId: "sc-uyrnn-8"
})(["display:flex;flex-direction:column;gap:", ";"], ({
  theme: e
}) => {
  var r;
  return ((r = e.spacing) == null ? void 0 : r.xs) || "8px";
}), Le = /* @__PURE__ */ a.span.withConfig({
  displayName: "DetailLabel",
  componentId: "sc-uyrnn-9"
})(["font-size:", ";font-weight:", ";color:", ";"], ({
  theme: e
}) => {
  var r;
  return ((r = e.fontSizes) == null ? void 0 : r.sm) || "14px";
}, ({
  theme: e
}) => {
  var r;
  return ((r = e.fontWeights) == null ? void 0 : r.medium) || 500;
}, ({
  theme: e
}) => {
  var r;
  return ((r = e.colors) == null ? void 0 : r.textSecondary) || "#6b7280";
}), ce = /* @__PURE__ */ a.span.withConfig({
  displayName: "DetailValue",
  componentId: "sc-uyrnn-10"
})(["font-size:", ";color:", ";"], ({
  theme: e
}) => {
  var r;
  return ((r = e.fontSizes) == null ? void 0 : r.sm) || "14px";
}, ({
  theme: e
}) => {
  var r;
  return ((r = e.colors) == null ? void 0 : r.textPrimary) || "#111827";
}), ri = ({
  trade: e
}) => /* @__PURE__ */ o.jsxs(ei, { children: [
  e.fvg_details && /* @__PURE__ */ o.jsxs(_e, { children: [
    /* @__PURE__ */ o.jsx(Le, { children: "FVG Details" }),
    /* @__PURE__ */ o.jsxs(ce, { children: [
      "Type: ",
      e.fvg_details.rd_type || "-"
    ] }),
    /* @__PURE__ */ o.jsxs(ce, { children: [
      "Entry Version: ",
      e.fvg_details.entry_version || "-"
    ] }),
    /* @__PURE__ */ o.jsxs(ce, { children: [
      "Draw on Liquidity: ",
      e.fvg_details.draw_on_liquidity || "-"
    ] })
  ] }),
  e.setup && /* @__PURE__ */ o.jsxs(_e, { children: [
    /* @__PURE__ */ o.jsx(Le, { children: "Setup Classification" }),
    /* @__PURE__ */ o.jsxs(ce, { children: [
      "Primary: ",
      e.setup.primary_setup || "-"
    ] }),
    /* @__PURE__ */ o.jsxs(ce, { children: [
      "Secondary: ",
      e.setup.secondary_setup || "-"
    ] }),
    /* @__PURE__ */ o.jsxs(ce, { children: [
      "Liquidity: ",
      e.setup.liquidity_taken || "-"
    ] })
  ] }),
  e.analysis && /* @__PURE__ */ o.jsxs(_e, { children: [
    /* @__PURE__ */ o.jsx(Le, { children: "Analysis" }),
    /* @__PURE__ */ o.jsxs(ce, { children: [
      "DOL Target: ",
      e.analysis.dol_target_type || "-"
    ] }),
    /* @__PURE__ */ o.jsxs(ce, { children: [
      "Path Quality: ",
      e.analysis.path_quality || "-"
    ] }),
    /* @__PURE__ */ o.jsxs(ce, { children: [
      "Clustering: ",
      e.analysis.clustering || "-"
    ] })
  ] }),
  /* @__PURE__ */ o.jsxs(_e, { children: [
    /* @__PURE__ */ o.jsx(Le, { children: "Timing" }),
    /* @__PURE__ */ o.jsxs(ce, { children: [
      "Entry: ",
      e.trade.entry_time || "-"
    ] }),
    /* @__PURE__ */ o.jsxs(ce, { children: [
      "Exit: ",
      e.trade.exit_time || "-"
    ] }),
    /* @__PURE__ */ o.jsxs(ce, { children: [
      "FVG: ",
      e.trade.fvg_time || "-"
    ] }),
    /* @__PURE__ */ o.jsxs(ce, { children: [
      "RD: ",
      e.trade.rd_time || "-"
    ] })
  ] }),
  e.trade.notes && /* @__PURE__ */ o.jsxs(_e, { style: {
    gridColumn: "1 / -1"
  }, children: [
    /* @__PURE__ */ o.jsx(Le, { children: "Notes" }),
    /* @__PURE__ */ o.jsx(ce, { children: e.trade.notes })
  ] })
] }), ti = ({
  trade: e,
  index: r,
  columns: t,
  isSelected: n = !1,
  hoverable: s = !0,
  striped: c = !0,
  expandable: i = !1,
  isExpanded: u = !1,
  onRowClick: f,
  onToggleExpand: p,
  expandedContent: d
}) => {
  const [h, x] = Q(!1), y = u !== void 0 ? u : h, m = (w) => {
    w.target.closest("button") || f == null || f(e, r);
  }, b = (w) => {
    w.stopPropagation(), p ? p(e, r) : x(!h);
  }, C = t.filter((w) => !w.hidden);
  return /* @__PURE__ */ o.jsxs(o.Fragment, { children: [
    /* @__PURE__ */ o.jsxs(Gs, { hoverable: s, striped: c, isSelected: n, isClickable: !!f, isExpanded: y, onClick: m, children: [
      i && /* @__PURE__ */ o.jsx(qr, { align: "center", style: {
        width: "40px",
        padding: "8px"
      }, children: /* @__PURE__ */ o.jsx(Js, { onClick: b, children: /* @__PURE__ */ o.jsx(Zs, { isExpanded: y }) }) }),
      C.map((w) => /* @__PURE__ */ o.jsx(qr, { align: w.align, children: w.cell(e, r) }, w.id))
    ] }),
    i && /* @__PURE__ */ o.jsx(Ks, { isVisible: y, children: /* @__PURE__ */ o.jsx(Qs, { colSpan: C.length + 1, children: /* @__PURE__ */ o.jsx(Xs, { children: d || /* @__PURE__ */ o.jsx(ri, { trade: e }) }) }) })
  ] });
}, ge = {
  MODEL_TYPE: "model_type",
  WIN_LOSS: "win_loss",
  DATE_FROM: "dateFrom",
  DATE_TO: "dateTo",
  SESSION: "session",
  DIRECTION: "direction",
  MARKET: "market",
  MIN_R_MULTIPLE: "min_r_multiple",
  MAX_R_MULTIPLE: "max_r_multiple",
  MIN_PATTERN_QUALITY: "min_pattern_quality",
  MAX_PATTERN_QUALITY: "max_pattern_quality"
}, oi = /* @__PURE__ */ a.div.withConfig({
  displayName: "FiltersContainer",
  componentId: "sc-32k3gq-0"
})(["display:flex;flex-direction:column;gap:", ";padding:", ";background-color:", ";border-radius:", ";border:1px solid ", ";"], ({
  theme: e
}) => {
  var r;
  return ((r = e.spacing) == null ? void 0 : r.md) || "16px";
}, ({
  theme: e
}) => {
  var r;
  return ((r = e.spacing) == null ? void 0 : r.md) || "16px";
}, ({
  theme: e
}) => {
  var r;
  return ((r = e.colors) == null ? void 0 : r.background) || "#f8f9fa";
}, ({
  theme: e
}) => {
  var r;
  return ((r = e.borderRadius) == null ? void 0 : r.md) || "8px";
}, ({
  theme: e
}) => {
  var r;
  return ((r = e.colors) == null ? void 0 : r.border) || "#e5e7eb";
}), Hr = /* @__PURE__ */ a.div.withConfig({
  displayName: "FilterRow",
  componentId: "sc-32k3gq-1"
})(["display:flex;gap:", ";align-items:end;flex-wrap:wrap;@media (max-width:768px){flex-direction:column;align-items:stretch;}"], ({
  theme: e
}) => {
  var r;
  return ((r = e.spacing) == null ? void 0 : r.sm) || "12px";
}), be = /* @__PURE__ */ a.div.withConfig({
  displayName: "FilterGroup",
  componentId: "sc-32k3gq-2"
})(["display:flex;flex-direction:column;gap:", ";min-width:120px;"], ({
  theme: e
}) => {
  var r;
  return ((r = e.spacing) == null ? void 0 : r.xs) || "8px";
}), ye = /* @__PURE__ */ a.label.withConfig({
  displayName: "FilterLabel",
  componentId: "sc-32k3gq-3"
})(["font-size:", ";font-weight:", ";color:", ";"], ({
  theme: e
}) => {
  var r;
  return ((r = e.fontSizes) == null ? void 0 : r.sm) || "14px";
}, ({
  theme: e
}) => {
  var r;
  return ((r = e.fontWeights) == null ? void 0 : r.medium) || 500;
}, ({
  theme: e
}) => {
  var r;
  return ((r = e.colors) == null ? void 0 : r.textSecondary) || "#6b7280";
}), ni = /* @__PURE__ */ a.div.withConfig({
  displayName: "FilterActions",
  componentId: "sc-32k3gq-4"
})(["display:flex;gap:", ";align-items:center;margin-left:auto;@media (max-width:768px){margin-left:0;justify-content:flex-end;}"], ({
  theme: e
}) => {
  var r;
  return ((r = e.spacing) == null ? void 0 : r.sm) || "12px";
}), si = /* @__PURE__ */ a.div.withConfig({
  displayName: "AdvancedFilters",
  componentId: "sc-32k3gq-5"
})(["display:", ";flex-direction:column;gap:", ";padding-top:", ";border-top:1px solid ", ";"], ({
  isVisible: e
}) => e ? "flex" : "none", ({
  theme: e
}) => {
  var r;
  return ((r = e.spacing) == null ? void 0 : r.md) || "16px";
}, ({
  theme: e
}) => {
  var r;
  return ((r = e.spacing) == null ? void 0 : r.md) || "16px";
}, ({
  theme: e
}) => {
  var r;
  return ((r = e.colors) == null ? void 0 : r.border) || "#e5e7eb";
}), Vr = /* @__PURE__ */ a.div.withConfig({
  displayName: "RangeInputGroup",
  componentId: "sc-32k3gq-6"
})(["display:flex;gap:", ";align-items:center;"], ({
  theme: e
}) => {
  var r;
  return ((r = e.spacing) == null ? void 0 : r.xs) || "8px";
}), Ur = /* @__PURE__ */ a.span.withConfig({
  displayName: "RangeLabel",
  componentId: "sc-32k3gq-7"
})(["font-size:", ";color:", ";"], ({
  theme: e
}) => {
  var r;
  return ((r = e.fontSizes) == null ? void 0 : r.sm) || "14px";
}, ({
  theme: e
}) => {
  var r;
  return ((r = e.colors) == null ? void 0 : r.textSecondary) || "#6b7280";
}), ii = ({
  filters: e,
  onFiltersChange: r,
  onReset: t,
  isLoading: n = !1,
  showAdvanced: s = !1,
  onToggleAdvanced: c
}) => {
  const i = (p, d) => {
    r({
      ...e,
      [p]: d
    });
  }, u = () => {
    r({}), t == null || t();
  }, f = Object.values(e).some((p) => p !== void 0 && p !== "" && p !== null);
  return /* @__PURE__ */ o.jsxs(oi, { children: [
    /* @__PURE__ */ o.jsxs(Hr, { children: [
      /* @__PURE__ */ o.jsxs(be, { children: [
        /* @__PURE__ */ o.jsx(ye, { children: "Date From" }),
        /* @__PURE__ */ o.jsx(Ie, { type: "date", value: e.dateFrom || "", onChange: (p) => i(ge.DATE_FROM, p), disabled: n })
      ] }),
      /* @__PURE__ */ o.jsxs(be, { children: [
        /* @__PURE__ */ o.jsx(ye, { children: "Date To" }),
        /* @__PURE__ */ o.jsx(Ie, { type: "date", value: e.dateTo || "", onChange: (p) => i(ge.DATE_TO, p), disabled: n })
      ] }),
      /* @__PURE__ */ o.jsxs(be, { children: [
        /* @__PURE__ */ o.jsx(ye, { children: "Model Type" }),
        /* @__PURE__ */ o.jsx(Re, { options: [{
          value: "",
          label: "All Models"
        }, {
          value: "RD-Cont",
          label: "RD-Cont"
        }, {
          value: "FVG-RD",
          label: "FVG-RD"
        }, {
          value: "True-RD",
          label: "True-RD"
        }, {
          value: "IMM-RD",
          label: "IMM-RD"
        }, {
          value: "Dispersed-RD",
          label: "Dispersed-RD"
        }, {
          value: "Wide-Gap-RD",
          label: "Wide-Gap-RD"
        }], value: e.model_type || "", onChange: (p) => i(ge.MODEL_TYPE, p), disabled: n })
      ] }),
      /* @__PURE__ */ o.jsxs(be, { children: [
        /* @__PURE__ */ o.jsx(ye, { children: "Session" }),
        /* @__PURE__ */ o.jsx(Re, { options: [{
          value: "",
          label: "All Sessions"
        }, {
          value: "Pre-Market",
          label: "Pre-Market"
        }, {
          value: "NY Open",
          label: "NY Open"
        }, {
          value: "10:50-11:10",
          label: "10:50-11:10"
        }, {
          value: "11:50-12:10",
          label: "11:50-12:10"
        }, {
          value: "Lunch Macro",
          label: "Lunch Macro"
        }, {
          value: "13:50-14:10",
          label: "13:50-14:10"
        }, {
          value: "14:50-15:10",
          label: "14:50-15:10"
        }, {
          value: "15:15-15:45",
          label: "15:15-15:45"
        }, {
          value: "MOC",
          label: "MOC"
        }, {
          value: "Post MOC",
          label: "Post MOC"
        }], value: e.session || "", onChange: (p) => i(ge.SESSION, p), disabled: n })
      ] }),
      /* @__PURE__ */ o.jsxs(be, { children: [
        /* @__PURE__ */ o.jsx(ye, { children: "Direction" }),
        /* @__PURE__ */ o.jsx(Re, { options: [{
          value: "",
          label: "All Directions"
        }, {
          value: "Long",
          label: "Long"
        }, {
          value: "Short",
          label: "Short"
        }], value: e.direction || "", onChange: (p) => i(ge.DIRECTION, p), disabled: n })
      ] }),
      /* @__PURE__ */ o.jsxs(be, { children: [
        /* @__PURE__ */ o.jsx(ye, { children: "Result" }),
        /* @__PURE__ */ o.jsx(Re, { options: [{
          value: "",
          label: "All Results"
        }, {
          value: "Win",
          label: "Win"
        }, {
          value: "Loss",
          label: "Loss"
        }], value: e.win_loss || "", onChange: (p) => i(ge.WIN_LOSS, p), disabled: n })
      ] }),
      /* @__PURE__ */ o.jsxs(ni, { children: [
        c && /* @__PURE__ */ o.jsxs(le, { variant: "outline", size: "small", onClick: c, disabled: n, children: [
          s ? "Hide" : "Show",
          " Advanced"
        ] }),
        /* @__PURE__ */ o.jsx(le, { variant: "outline", size: "small", onClick: u, disabled: n || !f, children: "Reset" })
      ] })
    ] }),
    /* @__PURE__ */ o.jsx(si, { isVisible: s, children: /* @__PURE__ */ o.jsxs(Hr, { children: [
      /* @__PURE__ */ o.jsxs(be, { children: [
        /* @__PURE__ */ o.jsx(ye, { children: "Market" }),
        /* @__PURE__ */ o.jsx(Re, { options: [{
          value: "",
          label: "All Markets"
        }, {
          value: "MNQ",
          label: "MNQ"
        }, {
          value: "NQ",
          label: "NQ"
        }, {
          value: "ES",
          label: "ES"
        }, {
          value: "MES",
          label: "MES"
        }, {
          value: "YM",
          label: "YM"
        }, {
          value: "MYM",
          label: "MYM"
        }], value: e.market || "", onChange: (p) => i(ge.MARKET, p), disabled: n })
      ] }),
      /* @__PURE__ */ o.jsxs(be, { children: [
        /* @__PURE__ */ o.jsx(ye, { children: "R Multiple Range" }),
        /* @__PURE__ */ o.jsxs(Vr, { children: [
          /* @__PURE__ */ o.jsx(Ie, { type: "number", placeholder: "Min", step: "0.1", value: e.min_r_multiple || "", onChange: (p) => i(ge.MIN_R_MULTIPLE, p ? Number(p) : void 0), disabled: n, style: {
            width: "80px"
          } }),
          /* @__PURE__ */ o.jsx(Ur, { children: "to" }),
          /* @__PURE__ */ o.jsx(Ie, { type: "number", placeholder: "Max", step: "0.1", value: e.max_r_multiple || "", onChange: (p) => i(ge.MAX_R_MULTIPLE, p ? Number(p) : void 0), disabled: n, style: {
            width: "80px"
          } })
        ] })
      ] }),
      /* @__PURE__ */ o.jsxs(be, { children: [
        /* @__PURE__ */ o.jsx(ye, { children: "Pattern Quality Range" }),
        /* @__PURE__ */ o.jsxs(Vr, { children: [
          /* @__PURE__ */ o.jsx(Ie, { type: "number", placeholder: "Min", min: "1", max: "5", step: "0.1", value: e.min_pattern_quality || "", onChange: (p) => i(ge.MIN_PATTERN_QUALITY, p ? Number(p) : void 0), disabled: n, style: {
            width: "80px"
          } }),
          /* @__PURE__ */ o.jsx(Ur, { children: "to" }),
          /* @__PURE__ */ o.jsx(Ie, { type: "number", placeholder: "Max", min: "1", max: "5", step: "0.1", value: e.max_pattern_quality || "", onChange: (p) => i(ge.MAX_PATTERN_QUALITY, p ? Number(p) : void 0), disabled: n, style: {
            width: "80px"
          } })
        ] })
      ] })
    ] }) })
  ] });
}, ai = /* @__PURE__ */ a.div.withConfig({
  displayName: "TableContainer",
  componentId: "sc-13oxwmo-0"
})(["width:100%;overflow:auto;", " ", ""], ({
  height: e
}) => e && `height: ${e};`, ({
  scrollable: e
}) => e && "overflow-x: auto;"), ci = /* @__PURE__ */ a.table.withConfig({
  displayName: "StyledTable",
  componentId: "sc-13oxwmo-1"
})(["width:100%;border-collapse:separate;border-spacing:0;font-size:", ";", " ", ""], ({
  theme: e
}) => {
  var r;
  return ((r = e.fontSizes) == null ? void 0 : r.sm) || "14px";
}, ({
  bordered: e,
  theme: r
}) => {
  var t, n;
  return e && g(["border:1px solid ", ";border-radius:", ";"], ((t = r.colors) == null ? void 0 : t.border) || "#e5e7eb", ((n = r.borderRadius) == null ? void 0 : n.sm) || "4px");
}, ({
  compact: e,
  theme: r
}) => {
  var t, n, s, c;
  return e ? g(["th,td{padding:", " ", ";}"], ((t = r.spacing) == null ? void 0 : t.xs) || "8px", ((n = r.spacing) == null ? void 0 : n.sm) || "12px") : g(["th,td{padding:", " ", ";}"], ((s = r.spacing) == null ? void 0 : s.sm) || "12px", ((c = r.spacing) == null ? void 0 : c.md) || "16px");
}), li = /* @__PURE__ */ a.thead.withConfig({
  displayName: "TableHeader",
  componentId: "sc-13oxwmo-2"
})(["", ""], ({
  stickyHeader: e
}) => e && g(["position:sticky;top:0;z-index:1;"])), di = /* @__PURE__ */ a.tr.withConfig({
  displayName: "TableHeaderRow",
  componentId: "sc-13oxwmo-3"
})(["background-color:", ";"], ({
  theme: e
}) => {
  var r;
  return ((r = e.colors) == null ? void 0 : r.background) || "#f8f9fa";
}), Yr = /* @__PURE__ */ a.th.withConfig({
  displayName: "TableHeaderCell",
  componentId: "sc-13oxwmo-4"
})(["text-align:", ";font-weight:", ";color:", ";border-bottom:1px solid ", ";white-space:nowrap;", " ", " ", ""], ({
  align: e
}) => e || "left", ({
  theme: e
}) => {
  var r;
  return ((r = e.fontWeights) == null ? void 0 : r.semibold) || 600;
}, ({
  theme: e
}) => {
  var r;
  return ((r = e.colors) == null ? void 0 : r.textSecondary) || "#6b7280";
}, ({
  theme: e
}) => {
  var r;
  return ((r = e.colors) == null ? void 0 : r.border) || "#e5e7eb";
}, ({
  width: e
}) => e && `width: ${e};`, ({
  sortable: e
}) => e && g(["cursor:pointer;user-select:none;&:hover{background-color:", "aa;}"], ({
  theme: r
}) => {
  var t;
  return ((t = r.colors) == null ? void 0 : t.background) || "#f8f9fa";
}), ({
  isSorted: e,
  theme: r
}) => {
  var t;
  return e && g(["color:", ";"], ((t = r.colors) == null ? void 0 : t.primary) || "#3b82f6");
}), pi = /* @__PURE__ */ a.span.withConfig({
  displayName: "SortIcon",
  componentId: "sc-13oxwmo-5"
})(["display:inline-block;margin-left:", ";&::after{content:'", "';}"], ({
  theme: e
}) => {
  var r;
  return ((r = e.spacing) == null ? void 0 : r.xs) || "8px";
}, ({
  direction: e
}) => e === "asc" ? "↑" : e === "desc" ? "↓" : "↕"), ui = /* @__PURE__ */ a.tbody.withConfig({
  displayName: "TableBody",
  componentId: "sc-13oxwmo-6"
})([""]), fi = /* @__PURE__ */ a.div.withConfig({
  displayName: "EmptyState",
  componentId: "sc-13oxwmo-7"
})(["padding:", ";text-align:center;color:", ";"], ({
  theme: e
}) => {
  var r;
  return ((r = e.spacing) == null ? void 0 : r.xl) || "32px";
}, ({
  theme: e
}) => {
  var r;
  return ((r = e.colors) == null ? void 0 : r.textSecondary) || "#6b7280";
}), gi = /* @__PURE__ */ a.div.withConfig({
  displayName: "LoadingOverlay",
  componentId: "sc-13oxwmo-8"
})(["position:absolute;top:0;left:0;right:0;bottom:0;background-color:", ";display:flex;align-items:center;justify-content:center;z-index:1;"], ({
  theme: e
}) => {
  var r;
  return `${((r = e.colors) == null ? void 0 : r.background) || "#ffffff"}80`;
}), mi = /* @__PURE__ */ a.div.withConfig({
  displayName: "LoadingSpinner",
  componentId: "sc-13oxwmo-9"
})(["width:32px;height:32px;border:3px solid ", ";border-top:3px solid ", ";border-radius:50%;animation:spin 1s linear infinite;@keyframes spin{0%{transform:rotate(0deg);}100%{transform:rotate(360deg);}}"], ({
  theme: e
}) => {
  var r;
  return ((r = e.colors) == null ? void 0 : r.background) || "#f8f9fa";
}, ({
  theme: e
}) => {
  var r;
  return ((r = e.colors) == null ? void 0 : r.primary) || "#3b82f6";
}), hi = /* @__PURE__ */ a.div.withConfig({
  displayName: "PaginationContainer",
  componentId: "sc-13oxwmo-10"
})(["display:flex;justify-content:space-between;align-items:center;padding:", " 0;font-size:", ";"], ({
  theme: e
}) => {
  var r;
  return ((r = e.spacing) == null ? void 0 : r.md) || "16px";
}, ({
  theme: e
}) => {
  var r;
  return ((r = e.fontSizes) == null ? void 0 : r.sm) || "14px";
}), xi = /* @__PURE__ */ a.div.withConfig({
  displayName: "PageInfo",
  componentId: "sc-13oxwmo-11"
})(["color:", ";"], ({
  theme: e
}) => {
  var r;
  return ((r = e.colors) == null ? void 0 : r.textSecondary) || "#6b7280";
}), bi = /* @__PURE__ */ a.div.withConfig({
  displayName: "PaginationControls",
  componentId: "sc-13oxwmo-12"
})(["display:flex;gap:", ";"], ({
  theme: e
}) => {
  var r;
  return ((r = e.spacing) == null ? void 0 : r.xs) || "8px";
}), ec = ({
  data: e,
  isLoading: r = !1,
  bordered: t = !0,
  striped: n = !0,
  hoverable: s = !0,
  compact: c = !1,
  stickyHeader: i = !1,
  height: u = "",
  onRowClick: f,
  isRowSelected: p,
  onSort: d,
  sortColumn: h = "",
  sortDirection: x = "asc",
  pagination: y = !1,
  currentPage: m = 1,
  pageSize: b = 10,
  totalRows: C = 0,
  onPageChange: w,
  onPageSizeChange: S,
  className: L = "",
  emptyMessage: A = "No trades available",
  scrollable: O = !0,
  showFilters: R = !1,
  filters: T = {},
  onFiltersChange: _,
  columnPreset: N = "default",
  customColumns: Y,
  expandableRows: W = !1,
  renderExpandedContent: k
}) => {
  const [V, oe] = Q(!1), ne = U(() => {
    if (Y)
      return Y;
    switch (N) {
      case "compact":
        return Ys();
      case "performance":
        return Ws();
      default:
        return Us();
    }
  }, [Y, N]), fe = U(() => ne.filter((H) => !H.hidden), [ne]), se = U(() => Math.ceil(C / b), [C, b]), D = U(() => {
    if (!y)
      return e;
    const H = (m - 1) * b, he = H + b;
    return C > 0 && e.length <= b ? e : e.slice(H, he);
  }, [e, y, m, b, C]), J = (H) => {
    if (!d)
      return;
    d(H, h === H && x === "asc" ? "desc" : "asc");
  }, ve = (H) => {
    H < 1 || H > se || !w || w(H);
  };
  return /* @__PURE__ */ o.jsxs("div", { children: [
    R && _ && /* @__PURE__ */ o.jsx(ii, { filters: T, onFiltersChange: _, isLoading: r, showAdvanced: V, onToggleAdvanced: () => oe(!V) }),
    /* @__PURE__ */ o.jsxs("div", { style: {
      position: "relative"
    }, children: [
      r && /* @__PURE__ */ o.jsx(gi, { children: /* @__PURE__ */ o.jsx(mi, {}) }),
      /* @__PURE__ */ o.jsx(ai, { height: u, scrollable: O, children: /* @__PURE__ */ o.jsxs(ci, { bordered: t, striped: n, compact: c, className: L, children: [
        /* @__PURE__ */ o.jsx(li, { stickyHeader: i, children: /* @__PURE__ */ o.jsxs(di, { children: [
          W && /* @__PURE__ */ o.jsx(Yr, { width: "40px", align: "center" }),
          fe.map((H) => /* @__PURE__ */ o.jsxs(Yr, { sortable: H.sortable, isSorted: h === H.id, align: H.align, width: H.width, onClick: () => H.sortable && J(H.id), children: [
            H.header,
            H.sortable && /* @__PURE__ */ o.jsx(pi, { direction: h === H.id ? x : void 0 })
          ] }, H.id))
        ] }) }),
        /* @__PURE__ */ o.jsx(ui, { children: D.length > 0 ? D.map((H, he) => /* @__PURE__ */ o.jsx(ti, { trade: H, index: he, columns: fe, isSelected: p ? p(H, he) : !1, hoverable: s, striped: n, expandable: W, onRowClick: f, expandedContent: k == null ? void 0 : k(H) }, H.trade.id || he)) : /* @__PURE__ */ o.jsx("tr", { children: /* @__PURE__ */ o.jsx("td", { colSpan: fe.length + (W ? 1 : 0), children: /* @__PURE__ */ o.jsx(fi, { children: A }) }) }) })
      ] }) }),
      y && se > 0 && /* @__PURE__ */ o.jsxs(hi, { children: [
        /* @__PURE__ */ o.jsxs(xi, { children: [
          "Showing ",
          Math.min((m - 1) * b + 1, C),
          " to",
          " ",
          Math.min(m * b, C),
          " of ",
          C,
          " entries"
        ] }),
        /* @__PURE__ */ o.jsxs(bi, { children: [
          /* @__PURE__ */ o.jsx(le, { size: "small", variant: "outline", onClick: () => ve(1), disabled: m === 1, children: "First" }),
          /* @__PURE__ */ o.jsx(le, { size: "small", variant: "outline", onClick: () => ve(m - 1), disabled: m === 1, children: "Prev" }),
          /* @__PURE__ */ o.jsx(le, { size: "small", variant: "outline", onClick: () => ve(m + 1), disabled: m === se, children: "Next" }),
          /* @__PURE__ */ o.jsx(le, { size: "small", variant: "outline", onClick: () => ve(se), disabled: m === se, children: "Last" })
        ] })
      ] })
    ] })
  ] });
}, yi = /* @__PURE__ */ a.div.withConfig({
  displayName: "HeaderActions",
  componentId: "sc-1l7c7gv-0"
})(["display:flex;gap:", ";"], ({
  theme: e
}) => e.spacing.sm), rc = ({
  title: e,
  children: r,
  isLoading: t = !1,
  hasError: n = !1,
  errorMessage: s = "An error occurred while loading data",
  showRetry: c = !0,
  onRetry: i,
  isEmpty: u = !1,
  emptyMessage: f = "No data available",
  emptyActionText: p,
  onEmptyAction: d,
  actionButton: h,
  className: x,
  ...y
}) => {
  const m = /* @__PURE__ */ o.jsx(yi, { children: h });
  let b;
  return t ? b = /* @__PURE__ */ o.jsx(Co, { variant: "card", text: "Loading data..." }) : n ? b = /* @__PURE__ */ o.jsx(Mr, { title: "Error", description: s, variant: "compact", actionText: c ? "Retry" : void 0, onAction: c ? i : void 0 }) : u ? b = /* @__PURE__ */ o.jsx(Mr, { title: "No Data", description: f, variant: "compact", actionText: p, onAction: d }) : b = r, /* @__PURE__ */ o.jsx(hn, { title: e, actions: m, className: x, ...y, children: b });
}, vi = /* @__PURE__ */ a.div.withConfig({
  displayName: "SectionContainer",
  componentId: "sc-14y246p-0"
})(["background:", ";border:1px solid ", ";border-radius:", ";padding:", ";margin-bottom:", ";"], ({
  theme: e
}) => e.colors.surface, ({
  theme: e
}) => e.colors.border, ({
  theme: e
}) => e.borderRadius.md, ({
  theme: e
}) => e.spacing.lg, ({
  theme: e
}) => e.spacing.lg), wi = /* @__PURE__ */ a.div.withConfig({
  displayName: "SectionHeader",
  componentId: "sc-14y246p-1"
})(["display:flex;justify-content:space-between;align-items:center;margin-bottom:", ";padding-bottom:", ";border-bottom:1px solid ", ";"], ({
  theme: e
}) => e.spacing.md, ({
  theme: e
}) => e.spacing.sm, ({
  theme: e
}) => e.colors.border), Si = /* @__PURE__ */ a.h2.withConfig({
  displayName: "SectionTitle",
  componentId: "sc-14y246p-2"
})(["color:", ";font-size:", ";font-weight:600;margin:0;"], ({
  theme: e
}) => e.colors.textPrimary, ({
  theme: e
}) => e.fontSizes.lg), Ci = /* @__PURE__ */ a.div.withConfig({
  displayName: "SectionActions",
  componentId: "sc-14y246p-3"
})(["display:flex;gap:", ";"], ({
  theme: e
}) => e.spacing.sm), Ii = /* @__PURE__ */ a.div.withConfig({
  displayName: "SectionContent",
  componentId: "sc-14y246p-4"
})(["min-height:200px;"]), Wr = /* @__PURE__ */ a.div.withConfig({
  displayName: "LoadingState",
  componentId: "sc-14y246p-5"
})(["display:flex;align-items:center;justify-content:center;min-height:200px;color:", ";"], ({
  theme: e
}) => e.colors.textSecondary), Ei = /* @__PURE__ */ a.div.withConfig({
  displayName: "ErrorState",
  componentId: "sc-14y246p-6"
})(["display:flex;align-items:center;justify-content:center;min-height:200px;color:", ";text-align:center;"], ({
  theme: e
}) => e.colors.danger), ji = ({
  name: e,
  title: r,
  children: t,
  actions: n,
  isLoading: s = !1,
  error: c = null,
  className: i,
  collapsible: u = !1,
  defaultCollapsed: f = !1
}) => {
  const [p, d] = we.useState(f), h = () => {
    u && d(!p);
  }, x = r || e.charAt(0).toUpperCase() + e.slice(1), y = () => c ? /* @__PURE__ */ o.jsx(Ei, { children: /* @__PURE__ */ o.jsxs("div", { children: [
    /* @__PURE__ */ o.jsxs("div", { children: [
      "Error loading ",
      e
    ] }),
    /* @__PURE__ */ o.jsx("div", { style: {
      fontSize: "0.9em",
      marginTop: "8px"
    }, children: c })
  ] }) }) : s ? /* @__PURE__ */ o.jsxs(Wr, { children: [
    "Loading ",
    e,
    "..."
  ] }) : t || /* @__PURE__ */ o.jsxs(Wr, { children: [
    "No ",
    e,
    " data available"
  ] });
  return /* @__PURE__ */ o.jsxs(vi, { className: i, "data-section": e, children: [
    /* @__PURE__ */ o.jsxs(wi, { children: [
      /* @__PURE__ */ o.jsxs(Si, { onClick: h, style: {
        cursor: u ? "pointer" : "default"
      }, children: [
        x,
        u && /* @__PURE__ */ o.jsx("span", { style: {
          marginLeft: "8px",
          fontSize: "0.8em"
        }, children: p ? "▶" : "▼" })
      ] }),
      n && /* @__PURE__ */ o.jsx(Ci, { children: n })
    ] }),
    !p && /* @__PURE__ */ o.jsx(Ii, { children: y() })
  ] });
}, tc = ji, Ti = /* @__PURE__ */ a.div.withConfig({
  displayName: "Container",
  componentId: "sc-djltr5-0"
})(["display:grid;grid-template-areas:'header header' 'sidebar content';grid-template-columns:", ";grid-template-rows:auto 1fr;height:100vh;width:100%;overflow:hidden;transition:grid-template-columns ", " ease;"], ({
  sidebarCollapsed: e
}) => e ? "auto 1fr" : "240px 1fr", ({
  theme: e
}) => e.transitions.normal), Ni = /* @__PURE__ */ a.header.withConfig({
  displayName: "HeaderContainer",
  componentId: "sc-djltr5-1"
})(["grid-area:header;background-color:", ";border-bottom:1px solid ", ";padding:", ";z-index:", ";"], ({
  theme: e
}) => e.colors.headerBackground, ({
  theme: e
}) => e.colors.border, ({
  theme: e
}) => e.spacing.md, ({
  theme: e
}) => e.zIndex.fixed), ki = /* @__PURE__ */ a.aside.withConfig({
  displayName: "SidebarContainer",
  componentId: "sc-djltr5-2"
})(["grid-area:sidebar;background-color:", ";border-right:1px solid ", ";overflow-y:auto;transition:width ", " ease;width:", ";"], ({
  theme: e
}) => e.colors.sidebarBackground, ({
  theme: e
}) => e.colors.border, ({
  theme: e
}) => e.transitions.normal, ({
  collapsed: e
}) => e ? "60px" : "240px"), Ri = /* @__PURE__ */ a.main.withConfig({
  displayName: "ContentContainer",
  componentId: "sc-djltr5-3"
})(["grid-area:content;overflow-y:auto;padding:", ";background-color:", ";"], ({
  theme: e
}) => e.spacing.md, ({
  theme: e
}) => e.colors.background), oc = ({
  header: e,
  sidebar: r,
  children: t,
  sidebarCollapsed: n = !1,
  // toggleSidebar, // Unused prop
  className: s
}) => /* @__PURE__ */ o.jsxs(Ti, { sidebarCollapsed: n, className: s, children: [
  /* @__PURE__ */ o.jsx(Ni, { children: e }),
  /* @__PURE__ */ o.jsx(ki, { collapsed: n, children: r }),
  /* @__PURE__ */ o.jsx(Ri, { children: t })
] }), _i = /* @__PURE__ */ a.div.withConfig({
  displayName: "BuilderContainer",
  componentId: "sc-5duzr2-0"
})(["background:#1a1a1a;border:1px solid #4b5563;border-radius:8px;padding:24px;margin-bottom:16px;"]), Li = /* @__PURE__ */ a.h3.withConfig({
  displayName: "SectionTitle",
  componentId: "sc-5duzr2-1"
})(["color:#ffffff;font-size:1.1rem;font-weight:600;margin-bottom:16px;border-bottom:2px solid #dc2626;padding-bottom:8px;"]), Pi = /* @__PURE__ */ a.div.withConfig({
  displayName: "MatrixGrid",
  componentId: "sc-5duzr2-2"
})(["display:grid;grid-template-columns:repeat(auto-fit,minmax(250px,1fr));gap:20px;margin-bottom:20px;"]), Be = /* @__PURE__ */ a.div.withConfig({
  displayName: "ElementSection",
  componentId: "sc-5duzr2-3"
})(["background:#262626;border:1px solid #4b5563;border-radius:6px;padding:16px;"]), Pe = /* @__PURE__ */ a.h4.withConfig({
  displayName: "ElementTitle",
  componentId: "sc-5duzr2-4"
})(["color:#ffffff;font-size:0.9rem;font-weight:600;margin-bottom:12px;text-transform:uppercase;letter-spacing:0.5px;"]), qe = /* @__PURE__ */ a.select.withConfig({
  displayName: "Select",
  componentId: "sc-5duzr2-5"
})(["width:100%;padding:8px 12px;background:#0f0f0f;border:1px solid #4b5563;border-radius:4px;color:#ffffff;font-size:0.9rem;&:focus{outline:none;border-color:#dc2626;box-shadow:0 0 0 2px rgba(220,38,38,0.2);}option{background:#0f0f0f;color:#ffffff;}"]), Mi = /* @__PURE__ */ a.div.withConfig({
  displayName: "PreviewContainer",
  componentId: "sc-5duzr2-6"
})(["background:#0f0f0f;border:1px solid #4b5563;border-radius:6px;padding:16px;margin-top:16px;"]), Di = /* @__PURE__ */ a.div.withConfig({
  displayName: "PreviewText",
  componentId: "sc-5duzr2-7"
})(["color:#ffffff;font-family:'Monaco','Menlo','Ubuntu Mono',monospace;font-size:0.9rem;line-height:1.4;min-height:20px;"]), Gr = /* @__PURE__ */ a.span.withConfig({
  displayName: "RequiredIndicator",
  componentId: "sc-5duzr2-8"
})(["color:#dc2626;margin-left:4px;"]), Kr = /* @__PURE__ */ a.span.withConfig({
  displayName: "OptionalIndicator",
  componentId: "sc-5duzr2-9"
})(["color:#9ca3af;font-size:0.8rem;margin-left:4px;"]), Oi = ({
  onSetupChange: e,
  initialComponents: r
}) => {
  const [t, n] = Q({
    constant: (r == null ? void 0 : r.constant) || "",
    action: (r == null ? void 0 : r.action) || "None",
    variable: (r == null ? void 0 : r.variable) || "None",
    entry: (r == null ? void 0 : r.entry) || ""
  });
  ue(() => {
    t.constant && t.entry && e(t);
  }, [t, e]);
  const s = (i, u) => {
    n((f) => ({
      ...f,
      [i]: u
    }));
  }, c = () => {
    const {
      constant: i,
      action: u,
      variable: f,
      entry: p
    } = t;
    if (!i || !p)
      return "Select required elements to see setup preview...";
    let d = i;
    return u && u !== "None" && (d += ` → ${u}`), f && f !== "None" && (d += ` → ${f}`), d += ` [${p}]`, d;
  };
  return /* @__PURE__ */ o.jsxs(_i, { children: [
    /* @__PURE__ */ o.jsx(Li, { children: "Setup Construction Matrix" }),
    /* @__PURE__ */ o.jsxs(Pi, { children: [
      /* @__PURE__ */ o.jsxs(Be, { children: [
        /* @__PURE__ */ o.jsxs(Pe, { children: [
          "Constant Element",
          /* @__PURE__ */ o.jsx(Gr, { children: "*" })
        ] }),
        /* @__PURE__ */ o.jsxs(qe, { value: t.constant, onChange: (i) => s("constant", i.target.value), children: [
          /* @__PURE__ */ o.jsx("option", { value: "", children: "Select Constant" }),
          Te.constant.parentArrays.map((i) => /* @__PURE__ */ o.jsx("option", { value: i, children: i }, i)),
          Te.constant.fvgTypes.map((i) => /* @__PURE__ */ o.jsx("option", { value: i, children: i }, i))
        ] })
      ] }),
      /* @__PURE__ */ o.jsxs(Be, { children: [
        /* @__PURE__ */ o.jsxs(Pe, { children: [
          "Action Element",
          /* @__PURE__ */ o.jsx(Kr, { children: "(optional)" })
        ] }),
        /* @__PURE__ */ o.jsxs(qe, { value: t.action, onChange: (i) => s("action", i.target.value), children: [
          /* @__PURE__ */ o.jsx("option", { value: "None", children: "None" }),
          Te.action.liquidityEvents.map((i) => /* @__PURE__ */ o.jsx("option", { value: i, children: i }, i))
        ] })
      ] }),
      /* @__PURE__ */ o.jsxs(Be, { children: [
        /* @__PURE__ */ o.jsxs(Pe, { children: [
          "Variable Element",
          /* @__PURE__ */ o.jsx(Kr, { children: "(optional)" })
        ] }),
        /* @__PURE__ */ o.jsxs(qe, { value: t.variable, onChange: (i) => s("variable", i.target.value), children: [
          /* @__PURE__ */ o.jsx("option", { value: "None", children: "None" }),
          Te.variable.rdTypes.map((i) => /* @__PURE__ */ o.jsx("option", { value: i, children: i }, i))
        ] })
      ] }),
      /* @__PURE__ */ o.jsxs(Be, { children: [
        /* @__PURE__ */ o.jsxs(Pe, { children: [
          "Entry Method",
          /* @__PURE__ */ o.jsx(Gr, { children: "*" })
        ] }),
        /* @__PURE__ */ o.jsxs(qe, { value: t.entry, onChange: (i) => s("entry", i.target.value), children: [
          /* @__PURE__ */ o.jsx("option", { value: "", children: "Select Entry Method" }),
          Te.entry.methods.map((i) => /* @__PURE__ */ o.jsx("option", { value: i, children: i }, i))
        ] })
      ] })
    ] }),
    /* @__PURE__ */ o.jsxs(Mi, { children: [
      /* @__PURE__ */ o.jsx(Pe, { children: "Setup Preview" }),
      /* @__PURE__ */ o.jsx(Di, { children: c() })
    ] })
  ] });
}, nc = Oi, Qr = /* @__PURE__ */ a.div.withConfig({
  displayName: "MetricsContainer",
  componentId: "sc-opkdti-0"
})(["display:grid;grid-template-columns:repeat(auto-fit,minmax(200px,1fr));gap:", ";"], ({
  theme: e
}) => e.spacing.md), Xr = /* @__PURE__ */ a.div.withConfig({
  displayName: "MetricCard",
  componentId: "sc-opkdti-1"
})(["background:", ";border:1px solid ", ";border-radius:", ";padding:", ";"], ({
  theme: e
}) => e.colors.surface, ({
  theme: e
}) => e.colors.border, ({
  theme: e
}) => e.borderRadius.md, ({
  theme: e
}) => e.spacing.md), Jr = /* @__PURE__ */ a.div.withConfig({
  displayName: "MetricLabel",
  componentId: "sc-opkdti-2"
})(["color:", ";font-size:", ";margin-bottom:", ";"], ({
  theme: e
}) => e.colors.textSecondary, ({
  theme: e
}) => e.fontSizes.sm, ({
  theme: e
}) => e.spacing.xs), Zr = /* @__PURE__ */ a.div.withConfig({
  displayName: "MetricValue",
  componentId: "sc-opkdti-3"
})(["color:", ";font-size:", ";font-weight:600;"], ({
  theme: e,
  positive: r,
  negative: t
}) => r ? e.colors.success : t ? e.colors.danger : e.colors.textPrimary, ({
  theme: e
}) => e.fontSizes.lg), $i = ({
  metrics: e,
  isLoading: r
}) => r ? /* @__PURE__ */ o.jsx(Qr, { children: Array.from({
  length: 4
}).map((t, n) => /* @__PURE__ */ o.jsxs(Xr, { children: [
  /* @__PURE__ */ o.jsx(Jr, { children: "Loading..." }),
  /* @__PURE__ */ o.jsx(Zr, { children: "--" })
] }, n)) }) : /* @__PURE__ */ o.jsx(Qr, { children: e.map((t, n) => /* @__PURE__ */ o.jsxs(Xr, { children: [
  /* @__PURE__ */ o.jsx(Jr, { children: t.label }),
  /* @__PURE__ */ o.jsx(Zr, { positive: t.positive, negative: t.negative, children: t.value })
] }, n)) }), sc = $i, Ai = /* @__PURE__ */ a.div.withConfig({
  displayName: "AnalysisContainer",
  componentId: "sc-tp1ymt-0"
})(["background:", ";border:1px solid ", ";border-radius:", ";padding:", ";"], ({
  theme: e
}) => e.colors.surface, ({
  theme: e
}) => e.colors.border, ({
  theme: e
}) => e.borderRadius.md, ({
  theme: e
}) => e.spacing.lg), zi = /* @__PURE__ */ a.h3.withConfig({
  displayName: "AnalysisTitle",
  componentId: "sc-tp1ymt-1"
})(["color:", ";font-size:", ";font-weight:600;margin-bottom:", ";"], ({
  theme: e
}) => e.colors.textPrimary, ({
  theme: e
}) => e.fontSizes.lg, ({
  theme: e
}) => e.spacing.md), Fi = /* @__PURE__ */ a.div.withConfig({
  displayName: "AnalysisContent",
  componentId: "sc-tp1ymt-2"
})(["color:", ";line-height:1.6;"], ({
  theme: e
}) => e.colors.textSecondary), Bi = ({
  title: e = "Trade Analysis",
  children: r,
  isLoading: t
}) => /* @__PURE__ */ o.jsxs(Ai, { children: [
  /* @__PURE__ */ o.jsx(zi, { children: e }),
  /* @__PURE__ */ o.jsx(Fi, { children: t ? /* @__PURE__ */ o.jsx("div", { children: "Loading analysis..." }) : r || /* @__PURE__ */ o.jsx("div", { children: "No analysis data available" }) })
] }), ic = Bi, j = {
  // F1 colors - Updated to match racing specifications
  f1Red: "#dc2626",
  f1RedDark: "#b91c1c",
  f1RedLight: "#ef4444",
  f1Blue: "#1e5bc6",
  f1BlueDark: "#1a4da8",
  f1BlueLight: "#4a7dd8",
  // Neutrals
  white: "#ffffff",
  black: "#000000",
  gray50: "#f9fafb",
  gray100: "#f3f4f6",
  gray200: "#e5e7eb",
  gray300: "#d1d5db",
  gray400: "#9ca3af",
  gray500: "#6b7280",
  gray600: "#4b5563",
  gray700: "#374151",
  gray800: "#1f2937",
  gray900: "#111827",
  // Status colors
  green: "#4caf50",
  greenDark: "#388e3c",
  greenLight: "#81c784",
  yellow: "#ffeb3b",
  yellowDark: "#fbc02d",
  yellowLight: "#fff59d",
  red: "#f44336",
  redDark: "#d32f2f",
  redLight: "#e57373",
  blue: "#2196f3",
  blueDark: "#1976d2",
  blueLight: "#64b5f6",
  purple: "#9c27b0",
  purpleDark: "#7b1fa2",
  purpleLight: "#ba68c8",
  // Transparent colors
  whiteTransparent10: "rgba(255, 255, 255, 0.1)",
  blackTransparent10: "rgba(0, 0, 0, 0.1)"
}, q = {
  background: "#0f0f0f",
  surface: "#1a1a1a",
  cardBackground: "#1a1a1a",
  border: "#333333",
  divider: "rgba(255, 255, 255, 0.1)",
  textPrimary: "#ffffff",
  textSecondary: "#aaaaaa",
  textDisabled: "#666666",
  textInverse: "#1a1f2c",
  success: j.green,
  warning: j.yellow,
  error: j.red,
  info: j.blue,
  // Chart colors
  chartGrid: "rgba(255, 255, 255, 0.1)",
  chartLine: j.f1Red,
  // Trading specific colors
  profit: j.green,
  loss: j.red,
  neutral: j.gray400,
  // Component specific colors
  tooltipBackground: "rgba(37, 42, 55, 0.9)",
  modalBackground: "rgba(26, 31, 44, 0.8)"
}, ee = {
  background: "#f5f5f5",
  surface: "#ffffff",
  cardBackground: "#ffffff",
  border: "#e0e0e0",
  divider: "rgba(0, 0, 0, 0.1)",
  textPrimary: "#333333",
  textSecondary: "#666666",
  textDisabled: "#999999",
  textInverse: "#ffffff",
  success: j.green,
  warning: j.yellow,
  error: j.red,
  info: j.blue,
  // Chart colors
  chartGrid: "rgba(0, 0, 0, 0.1)",
  chartLine: j.f1Red,
  // Trading specific colors
  profit: j.green,
  loss: j.red,
  neutral: j.gray400,
  // Component specific colors
  tooltipBackground: "rgba(255, 255, 255, 0.9)",
  modalBackground: "rgba(255, 255, 255, 0.8)"
}, re = {
  xxs: "4px",
  xs: "8px",
  sm: "12px",
  md: "16px",
  lg: "24px",
  xl: "32px",
  xxl: "48px"
}, me = {
  xs: "0.75rem",
  sm: "0.875rem",
  md: "1rem",
  lg: "1.125rem",
  xl: "1.25rem",
  xxl: "1.5rem",
  xxxl: "2.5rem",
  // Added missing xxxl size
  h1: "2.5rem",
  h2: "2rem",
  h3: "1.75rem",
  h4: "1.5rem",
  h5: "1.25rem",
  h6: "1rem"
}, nr = {
  light: 300,
  regular: 400,
  medium: 500,
  semibold: 600,
  bold: 700
}, sr = {
  tight: 1.25,
  normal: 1.5,
  relaxed: 1.75
}, ir = {
  body: "'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif",
  heading: "'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif",
  mono: "SFMono-Regular, Menlo, Monaco, Consolas, 'Liberation Mono', 'Courier New', monospace"
}, ar = {
  xs: "480px",
  sm: "640px",
  md: "768px",
  lg: "1024px",
  xl: "1280px"
}, cr = {
  xs: "2px",
  sm: "4px",
  md: "6px",
  lg: "8px",
  xl: "12px",
  pill: "9999px",
  circle: "50%"
}, lr = {
  sm: "0 1px 3px rgba(0, 0, 0, 0.1)",
  md: "0 4px 6px rgba(0, 0, 0, 0.1)",
  lg: "0 10px 15px rgba(0, 0, 0, 0.1)"
}, dr = {
  fast: "0.1s",
  normal: "0.3s",
  slow: "0.5s"
}, pr = {
  base: 1,
  overlay: 10,
  modal: 20,
  popover: 30,
  tooltip: 40,
  fixed: 100
}, qi = /* @__PURE__ */ a.div.withConfig({
  displayName: "HeaderContainer",
  componentId: "sc-e71xhh-0"
})(["display:flex;justify-content:space-between;align-items:center;padding:", " 0;border-bottom:2px solid #4b5563;margin-bottom:", ";", ""], ({
  theme: e
}) => {
  var r;
  return ((r = e.spacing) == null ? void 0 : r.lg) || "16px";
}, ({
  theme: e
}) => {
  var r;
  return ((r = e.spacing) == null ? void 0 : r.lg) || "16px";
}, ({
  $variant: e
}) => {
  switch (e) {
    case "dashboard":
      return g(["padding:24px 0;margin-bottom:24px;"]);
    case "form":
      return g(["padding:16px 0;margin-bottom:16px;"]);
    default:
      return g(["padding:20px 0;margin-bottom:20px;"]);
  }
}), Hi = /* @__PURE__ */ a.div.withConfig({
  displayName: "TitleSection",
  componentId: "sc-e71xhh-1"
})(["display:flex;flex-direction:column;gap:", ";"], ({
  theme: e
}) => {
  var r;
  return ((r = e.spacing) == null ? void 0 : r.xs) || "4px";
}), Vi = /* @__PURE__ */ a.h1.withConfig({
  displayName: "MainTitle",
  componentId: "sc-e71xhh-2"
})(["font-weight:700;color:", ";margin:0;letter-spacing:-0.025em;text-transform:uppercase;font-family:'Inter',-apple-system,BlinkMacSystemFont,sans-serif;", " span{color:", ";}"], ({
  theme: e
}) => {
  var r;
  return ((r = e.colors) == null ? void 0 : r.textPrimary) || "#ffffff";
}, ({
  $variant: e
}) => {
  switch (e) {
    case "dashboard":
      return g(["font-size:", ";"], me.xxxl);
    case "analysis":
      return g(["font-size:", ";"], me.xxl);
    case "form":
      return g(["font-size:", ";"], me.xl);
    default:
      return g(["font-size:", ";"], me.xxl);
  }
}, ({
  theme: e
}) => {
  var r;
  return ((r = e.colors) == null ? void 0 : r.primary) || "#dc2626";
}), Ui = /* @__PURE__ */ a.div.withConfig({
  displayName: "Subtitle",
  componentId: "sc-e71xhh-3"
})(["font-size:", ";color:#9ca3af;font-weight:500;text-transform:uppercase;letter-spacing:0.05em;"], me.sm), Yi = /* @__PURE__ */ a.div.withConfig({
  displayName: "ActionsSection",
  componentId: "sc-e71xhh-4"
})(["display:flex;align-items:center;gap:", ";flex-wrap:wrap;"], ({
  theme: e
}) => {
  var r;
  return ((r = e.spacing) == null ? void 0 : r.md) || "12px";
}), Wi = /* @__PURE__ */ a.div.withConfig({
  displayName: "StatusIndicator",
  componentId: "sc-e71xhh-5"
})(["display:flex;align-items:center;gap:", ";padding:", " ", ";border-radius:", ";font-size:", ";font-weight:600;text-transform:uppercase;letter-spacing:0.05em;", ""], ({
  theme: e
}) => {
  var r;
  return ((r = e.spacing) == null ? void 0 : r.xs) || "4px";
}, ({
  theme: e
}) => {
  var r;
  return ((r = e.spacing) == null ? void 0 : r.xs) || "4px";
}, ({
  theme: e
}) => {
  var r;
  return ((r = e.spacing) == null ? void 0 : r.sm) || "8px";
}, ({
  theme: e
}) => {
  var r;
  return ((r = e.borderRadius) == null ? void 0 : r.sm) || "4px";
}, ({
  theme: e
}) => {
  var r;
  return ((r = e.fontSizes) == null ? void 0 : r.xs) || "0.75rem";
}, ({
  $isLive: e,
  $variant: r
}) => e ? g(["background:rgba(220,38,38,0.1);border:1px solid #dc2626;color:#dc2626;"]) : r === "active" ? g(["background:rgba(34,197,94,0.1);border:1px solid #22c55e;color:#22c55e;"]) : g(["background:rgba(156,163,175,0.1);border:1px solid #9ca3af;color:#9ca3af;"])), Gi = /* @__PURE__ */ a.div.withConfig({
  displayName: "StatusDot",
  componentId: "sc-e71xhh-6"
})(["width:6px;height:6px;border-radius:50%;background:", ";", ""], ({
  $isLive: e
}) => e ? "#dc2626" : "#22c55e", ({
  $isLive: e
}) => e && g(["animation:pulse 2s infinite;@keyframes pulse{0%,100%{opacity:1;}50%{opacity:0.5;}}"])), Ki = /* @__PURE__ */ a.button.withConfig({
  displayName: "RefreshButton",
  componentId: "sc-e71xhh-7"
})(["padding:", " ", ";background:transparent;color:", ";border:1px solid #4b5563;border-radius:", ";cursor:pointer;font-weight:500;font-size:", ";transition:all 0.2s ease;min-width:100px;position:relative;&:hover{background:#4b5563;color:", ";border-color:", ";}&:disabled{opacity:0.6;cursor:not-allowed;}", ""], ({
  theme: e
}) => {
  var r;
  return ((r = e.spacing) == null ? void 0 : r.sm) || "8px";
}, ({
  theme: e
}) => {
  var r;
  return ((r = e.spacing) == null ? void 0 : r.md) || "12px";
}, ({
  theme: e
}) => {
  var r;
  return ((r = e.colors) == null ? void 0 : r.textSecondary) || "#9ca3af";
}, ({
  theme: e
}) => {
  var r;
  return ((r = e.borderRadius) == null ? void 0 : r.sm) || "4px";
}, ({
  theme: e
}) => {
  var r;
  return ((r = e.fontSizes) == null ? void 0 : r.sm) || "0.875rem";
}, ({
  theme: e
}) => {
  var r;
  return ((r = e.colors) == null ? void 0 : r.textPrimary) || "#ffffff";
}, ({
  theme: e
}) => {
  var r;
  return ((r = e.colors) == null ? void 0 : r.primary) || "#dc2626";
}, ({
  $isRefreshing: e
}) => e && g(["&::after{content:'';position:absolute;top:50%;left:50%;width:16px;height:16px;margin:-8px 0 0 -8px;border:2px solid transparent;border-top:2px solid currentColor;border-radius:50%;animation:spin 1s linear infinite;}@keyframes spin{0%{transform:rotate(0deg);}100%{transform:rotate(360deg);}}"])), Qi = /* @__PURE__ */ a.div.withConfig({
  displayName: "CustomActions",
  componentId: "sc-e71xhh-8"
})(["display:flex;align-items:center;gap:", ";"], ({
  theme: e
}) => {
  var r;
  return ((r = e.spacing) == null ? void 0 : r.sm) || "8px";
}), ac = (e) => {
  const {
    title: r,
    subtitle: t,
    isLive: n = !1,
    liveText: s = "LIVE SESSION",
    statusText: c,
    onRefresh: i,
    isRefreshing: u = !1,
    actions: f,
    variant: p = "dashboard",
    className: d
  } = e, h = n ? s : c;
  return /* @__PURE__ */ o.jsxs(qi, { $variant: p, className: d, children: [
    /* @__PURE__ */ o.jsxs(Hi, { children: [
      /* @__PURE__ */ o.jsx(Vi, { $variant: p, children: p === "dashboard" ? /* @__PURE__ */ o.jsxs(o.Fragment, { children: [
        "🏎️ ",
        r.replace("Trading", "TRADING").replace("Dashboard", "DASHBOARD")
      ] }) : r }),
      t && /* @__PURE__ */ o.jsx(Ui, { children: t })
    ] }),
    /* @__PURE__ */ o.jsxs(Yi, { children: [
      h && /* @__PURE__ */ o.jsxs(Wi, { $isLive: n, $variant: !n && c ? "active" : void 0, children: [
        /* @__PURE__ */ o.jsx(Gi, { $isLive: n }),
        h
      ] }),
      i && /* @__PURE__ */ o.jsx(Ki, { onClick: i, disabled: u, $isRefreshing: u, children: u ? "Refreshing..." : "Refresh" }),
      f && /* @__PURE__ */ o.jsx(Qi, { children: f })
    ] })
  ] });
}, Je = /* @__PURE__ */ a.div.withConfig({
  displayName: "Container",
  componentId: "sc-vuv4tf-0"
})(["display:flex;flex-direction:column;width:100%;max-width:", ";margin:0 auto;min-height:", ";", " ", " ", " ", ""], ({
  $maxWidth: e
}) => typeof e == "number" ? `${e}px` : e, ({
  $variant: e
}) => e === "dashboard" ? "100vh" : "auto", ({
  $padding: e
}) => {
  const r = {
    sm: re.sm,
    md: re.md,
    lg: re.lg,
    xl: re.xl
  };
  return g(["padding:", ";"], r[e || "lg"]);
}, ({
  $background: e,
  theme: r
}) => {
  const t = {
    default: r.colors.background,
    surface: r.colors.surface,
    elevated: r.colors.elevated
  };
  return g(["background:", ";"], t[e || "default"]);
}, ({
  $variant: e
}) => {
  switch (e) {
    case "dashboard":
      return g(["gap:24px;padding-top:0;"]);
    case "form":
      return g(["gap:16px;max-width:800px;"]);
    case "analysis":
      return g(["gap:20px;max-width:1400px;"]);
    case "settings":
      return g(["gap:16px;max-width:1000px;"]);
    default:
      return g(["gap:16px;"]);
  }
}, ({
  $animated: e
}) => e && g(["transition:all 0.3s ease-in-out;&.entering{opacity:0;transform:translateY(20px);}&.entered{opacity:1;transform:translateY(0);}"])), Xi = /* @__PURE__ */ a.div.withConfig({
  displayName: "LoadingContainer",
  componentId: "sc-vuv4tf-1"
})(["display:flex;flex-direction:column;align-items:center;justify-content:center;min-height:400px;gap:16px;color:#9ca3af;"]), Ji = /* @__PURE__ */ a.div.withConfig({
  displayName: "LoadingSpinner",
  componentId: "sc-vuv4tf-2"
})(["width:40px;height:40px;border:3px solid #4b5563;border-top:3px solid #dc2626;border-radius:50%;animation:spin 1s linear infinite;@keyframes spin{0%{transform:rotate(0deg);}100%{transform:rotate(360deg);}}"]), Zi = /* @__PURE__ */ a.div.withConfig({
  displayName: "ErrorContainer",
  componentId: "sc-vuv4tf-3"
})(["display:flex;flex-direction:column;align-items:center;justify-content:center;padding:40px;background:rgba(244,67,54,0.1);border:1px solid #f44336;border-radius:8px;color:#f44336;text-align:center;gap:16px;"]), ea = /* @__PURE__ */ a.div.withConfig({
  displayName: "ErrorIcon",
  componentId: "sc-vuv4tf-4"
})(["font-size:48px;opacity:0.8;"]), ra = /* @__PURE__ */ a.div.withConfig({
  displayName: "ErrorMessage",
  componentId: "sc-vuv4tf-5"
})(["font-size:16px;font-weight:500;"]), ta = /* @__PURE__ */ a.button.withConfig({
  displayName: "RetryButton",
  componentId: "sc-vuv4tf-6"
})(["padding:8px 16px;background:#f44336;color:white;border:none;border-radius:4px;cursor:pointer;font-weight:500;transition:background 0.2s ease;&:hover{background:#d32f2f;}"]), et = () => /* @__PURE__ */ o.jsxs(Xi, { children: [
  /* @__PURE__ */ o.jsx(Ji, {}),
  /* @__PURE__ */ o.jsx("div", { children: "Loading..." })
] }), oa = ({
  error: e,
  onRetry: r
}) => /* @__PURE__ */ o.jsxs(Zi, { children: [
  /* @__PURE__ */ o.jsx(ea, { children: "⚠️" }),
  /* @__PURE__ */ o.jsx(ra, { children: e }),
  r && /* @__PURE__ */ o.jsx(ta, { onClick: r, children: "Retry" })
] }), cc = (e) => {
  const {
    children: r,
    variant: t = "dashboard",
    maxWidth: n = "100%",
    padding: s = "lg",
    isLoading: c = !1,
    error: i = null,
    loadingFallback: u,
    errorFallback: f,
    className: p,
    animated: d = !0,
    background: h = "default"
  } = e, x = {
    $variant: t,
    $maxWidth: n,
    $padding: s,
    $animated: d,
    $background: h
  };
  return i ? /* @__PURE__ */ o.jsx(Je, { ...x, className: p, children: f || /* @__PURE__ */ o.jsx(oa, { error: i }) }) : c ? /* @__PURE__ */ o.jsx(Je, { ...x, className: p, children: u || /* @__PURE__ */ o.jsx(et, {}) }) : /* @__PURE__ */ o.jsx(Je, { ...x, className: p, children: /* @__PURE__ */ o.jsx(Ft, { fallback: u || /* @__PURE__ */ o.jsx(et, {}), children: r }) });
}, na = /* @__PURE__ */ a.form.withConfig({
  displayName: "FormContainer",
  componentId: "sc-1gwzj6e-0"
})(["display:flex;flex-direction:column;gap:", ";background:", ";border-radius:", ";border:1px solid ", ";position:relative;", " ", " ", ""], ({
  theme: e
}) => {
  var r;
  return ((r = e.spacing) == null ? void 0 : r.md) || "12px";
}, ({
  theme: e
}) => {
  var r;
  return ((r = e.colors) == null ? void 0 : r.surface) || "#1f2937";
}, ({
  theme: e
}) => {
  var r;
  return ((r = e.borderRadius) == null ? void 0 : r.lg) || "8px";
}, ({
  theme: e
}) => {
  var r;
  return ((r = e.colors) == null ? void 0 : r.border) || "#4b5563";
}, ({
  $variant: e
}) => {
  switch (e) {
    case "quick":
      return g(["padding:", ";max-width:600px;"], re.lg);
    case "detailed":
      return g(["padding:", ";max-width:800px;"], re.xl);
    case "modal":
      return g(["padding:", ";max-width:500px;margin:0 auto;"], re.lg);
    case "inline":
      return g(["padding:", ";background:transparent;border:none;"], re.md);
    default:
      return g(["padding:", ";"], re.lg);
  }
}, ({
  $showAccent: e,
  theme: r
}) => {
  var t, n, s, c, i;
  return e && g(["&::before{content:'';position:absolute;top:0;left:0;right:0;height:3px;background:linear-gradient( 90deg,", ",", ",", " );border-radius:", " ", " 0 0;}"], ((t = r.colors) == null ? void 0 : t.primary) || "#dc2626", ((n = r.colors) == null ? void 0 : n.primaryDark) || "#b91c1c", ((s = r.colors) == null ? void 0 : s.primary) || "#dc2626", ((c = r.borderRadius) == null ? void 0 : c.lg) || "8px", ((i = r.borderRadius) == null ? void 0 : i.lg) || "8px");
}, ({
  $disabled: e
}) => e && g(["opacity:0.6;pointer-events:none;"])), sa = /* @__PURE__ */ a.div.withConfig({
  displayName: "FormHeader",
  componentId: "sc-1gwzj6e-1"
})(["display:flex;flex-direction:column;gap:", ";margin-bottom:", ";"], ({
  theme: e
}) => {
  var r;
  return ((r = e.spacing) == null ? void 0 : r.xs) || "4px";
}, ({
  theme: e
}) => {
  var r;
  return ((r = e.spacing) == null ? void 0 : r.md) || "12px";
}), ia = /* @__PURE__ */ a.h3.withConfig({
  displayName: "FormTitle",
  componentId: "sc-1gwzj6e-2"
})(["font-size:", ";font-weight:700;color:", ";margin:0;text-transform:uppercase;letter-spacing:0.025em;display:flex;align-items:center;gap:", ";"], ({
  theme: e
}) => {
  var r;
  return ((r = e.fontSizes) == null ? void 0 : r.lg) || "1.125rem";
}, ({
  theme: e
}) => {
  var r;
  return ((r = e.colors) == null ? void 0 : r.textPrimary) || "#ffffff";
}, ({
  theme: e
}) => {
  var r;
  return ((r = e.spacing) == null ? void 0 : r.sm) || "8px";
}), aa = /* @__PURE__ */ a.div.withConfig({
  displayName: "FormSubtitle",
  componentId: "sc-1gwzj6e-3"
})(["font-size:", ";color:", ";font-weight:500;"], ({
  theme: e
}) => {
  var r;
  return ((r = e.fontSizes) == null ? void 0 : r.sm) || "0.875rem";
}, ({
  theme: e
}) => {
  var r;
  return ((r = e.colors) == null ? void 0 : r.textSecondary) || "#9ca3af";
}), ca = /* @__PURE__ */ a.div.withConfig({
  displayName: "FormContent",
  componentId: "sc-1gwzj6e-4"
})(["display:flex;flex-direction:column;gap:", ";"], ({
  theme: e
}) => {
  var r;
  return ((r = e.spacing) == null ? void 0 : r.md) || "12px";
}), rt = /* @__PURE__ */ a.div.withConfig({
  displayName: "FormMessage",
  componentId: "sc-1gwzj6e-5"
})(["padding:", " ", ";border-radius:", ";font-size:", ";font-weight:500;display:flex;align-items:center;gap:", ";", ""], ({
  theme: e
}) => {
  var r;
  return ((r = e.spacing) == null ? void 0 : r.sm) || "8px";
}, ({
  theme: e
}) => {
  var r;
  return ((r = e.spacing) == null ? void 0 : r.md) || "12px";
}, ({
  theme: e
}) => {
  var r;
  return ((r = e.borderRadius) == null ? void 0 : r.sm) || "4px";
}, ({
  theme: e
}) => {
  var r;
  return ((r = e.fontSizes) == null ? void 0 : r.sm) || "0.875rem";
}, ({
  theme: e
}) => {
  var r;
  return ((r = e.spacing) == null ? void 0 : r.xs) || "4px";
}, ({
  $type: e
}) => {
  switch (e) {
    case "error":
      return g(["background:rgba(244,67,54,0.1);border:1px solid #f44336;color:#f44336;"]);
    case "success":
      return g(["background:rgba(34,197,94,0.1);border:1px solid #22c55e;color:#22c55e;"]);
    case "info":
      return g(["background:rgba(59,130,246,0.1);border:1px solid #3b82f6;color:#3b82f6;"]);
  }
}), la = /* @__PURE__ */ a.div.withConfig({
  displayName: "LoadingOverlay",
  componentId: "sc-1gwzj6e-6"
})(["position:absolute;top:0;left:0;right:0;bottom:0;background:rgba(0,0,0,0.5);display:flex;align-items:center;justify-content:center;border-radius:", ";z-index:10;"], ({
  theme: e
}) => {
  var r;
  return ((r = e.borderRadius) == null ? void 0 : r.lg) || "8px";
}), da = /* @__PURE__ */ a.div.withConfig({
  displayName: "LoadingSpinner",
  componentId: "sc-1gwzj6e-7"
})(["width:32px;height:32px;border:3px solid #4b5563;border-top:3px solid #dc2626;border-radius:50%;animation:spin 1s linear infinite;@keyframes spin{0%{transform:rotate(0deg);}100%{transform:rotate(360deg);}}"]), pa = /* @__PURE__ */ a.div.withConfig({
  displayName: "AutoSaveIndicator",
  componentId: "sc-1gwzj6e-8"
})(["position:absolute;top:8px;right:8px;font-size:", ";color:", ";opacity:", ";transition:opacity 0.3s ease;"], ({
  theme: e
}) => {
  var r;
  return ((r = e.fontSizes) == null ? void 0 : r.xs) || "0.75rem";
}, ({
  theme: e
}) => {
  var r;
  return ((r = e.colors) == null ? void 0 : r.textSecondary) || "#9ca3af";
}, ({
  $visible: e
}) => e ? 1 : 0), lc = (e) => {
  const {
    children: r,
    onSubmit: t,
    title: n,
    subtitle: s,
    isSubmitting: c = !1,
    error: i = null,
    success: u = null,
    variant: f = "quick",
    showAccent: p = !0,
    className: d,
    disabled: h = !1,
    autoSave: x = !1,
    autoSaveInterval: y = 3e4
  } = e, m = async (b) => {
    b.preventDefault(), t && !c && !h && await t(b);
  };
  return /* @__PURE__ */ o.jsxs(na, { $variant: f, $showAccent: p, $disabled: h, className: d, onSubmit: m, noValidate: !0, children: [
    c && /* @__PURE__ */ o.jsx(la, { children: /* @__PURE__ */ o.jsx(da, {}) }),
    x && /* @__PURE__ */ o.jsx(pa, { $visible: !c, children: "Auto-save enabled" }),
    (n || s) && /* @__PURE__ */ o.jsxs(sa, { children: [
      n && /* @__PURE__ */ o.jsx(ia, { children: n }),
      s && /* @__PURE__ */ o.jsx(aa, { children: s })
    ] }),
    i && /* @__PURE__ */ o.jsxs(rt, { $type: "error", children: [
      "⚠️ ",
      i
    ] }),
    u && /* @__PURE__ */ o.jsxs(rt, { $type: "success", children: [
      "✅ ",
      u
    ] }),
    /* @__PURE__ */ o.jsx(ca, { children: r })
  ] });
}, ua = /* @__PURE__ */ a.div.withConfig({
  displayName: "FieldContainer",
  componentId: "sc-sq94oz-0"
})(["display:flex;flex-direction:column;gap:", ";"], ({
  $size: e
}) => ({
  sm: re.xs,
  md: re.sm,
  lg: re.md
})[e || "md"]), fa = /* @__PURE__ */ a.label.withConfig({
  displayName: "Label",
  componentId: "sc-sq94oz-1"
})(["font-size:", ";font-weight:600;color:", ";display:flex;align-items:center;gap:", ";", " ", ""], ({
  theme: e
}) => {
  var r;
  return ((r = e.fontSizes) == null ? void 0 : r.sm) || "0.875rem";
}, ({
  theme: e
}) => {
  var r;
  return ((r = e.colors) == null ? void 0 : r.textPrimary) || "#ffffff";
}, ({
  theme: e
}) => {
  var r;
  return ((r = e.spacing) == null ? void 0 : r.xs) || "4px";
}, ({
  $variant: e
}) => {
  switch (e) {
    case "trading":
      return g(["text-transform:uppercase;letter-spacing:0.025em;"]);
    case "analysis":
      return g(["font-weight:500;"]);
    default:
      return g([""]);
  }
}, ({
  $required: e,
  theme: r
}) => {
  var t;
  return e && g(["&::after{content:'*';color:", ";margin-left:2px;}"], ((t = r.colors) == null ? void 0 : t.primary) || "#dc2626");
}), ga = /* @__PURE__ */ a.div.withConfig({
  displayName: "InputContainer",
  componentId: "sc-sq94oz-2"
})(["position:relative;display:flex;align-items:center;", ""], ({
  $disabled: e
}) => e && g(["opacity:0.6;pointer-events:none;"])), ur = /* @__PURE__ */ g(["width:100%;border:1px solid ", ";border-radius:", ";background:", ";color:", ";font-family:inherit;transition:all 0.2s ease;", " &:focus{outline:none;border-color:", ";box-shadow:0 0 0 2px rgba(220,38,38,0.2);}&:disabled{background:#374151;color:#9ca3af;cursor:not-allowed;}&::placeholder{color:#6b7280;}"], ({
  $hasError: e,
  theme: r
}) => {
  var t;
  return e ? ((t = r.colors) == null ? void 0 : t.error) || "#f44336" : "#4b5563";
}, ({
  theme: e
}) => {
  var r;
  return ((r = e.borderRadius) == null ? void 0 : r.sm) || "4px";
}, ({
  theme: e
}) => {
  var r;
  return ((r = e.colors) == null ? void 0 : r.background) || "#111827";
}, ({
  theme: e
}) => {
  var r;
  return ((r = e.colors) == null ? void 0 : r.textPrimary) || "#ffffff";
}, ({
  $size: e
}) => ({
  sm: g(["padding:", " ", ";font-size:", ";"], re.xs, re.sm, me.sm),
  md: g(["padding:", " ", ";font-size:", ";"], re.sm, re.md, me.md),
  lg: g(["padding:", " ", ";font-size:", ";"], re.md, re.lg, me.lg)
})[e || "md"], ({
  theme: e
}) => {
  var r;
  return ((r = e.colors) == null ? void 0 : r.primary) || "#dc2626";
}), ma = /* @__PURE__ */ a.input.withConfig({
  displayName: "Input",
  componentId: "sc-sq94oz-3"
})(["", ""], ur), ha = /* @__PURE__ */ a.select.withConfig({
  displayName: "Select",
  componentId: "sc-sq94oz-4"
})(["", " cursor:pointer;option{background:", ";color:", ";}"], ur, ({
  theme: e
}) => {
  var r;
  return ((r = e.colors) == null ? void 0 : r.background) || "#111827";
}, ({
  theme: e
}) => {
  var r;
  return ((r = e.colors) == null ? void 0 : r.textPrimary) || "#ffffff";
}), xa = /* @__PURE__ */ a.textarea.withConfig({
  displayName: "TextArea",
  componentId: "sc-sq94oz-5"
})(["", " resize:vertical;min-height:80px;font-family:inherit;"], ur), ba = /* @__PURE__ */ a.div.withConfig({
  displayName: "PrefixContainer",
  componentId: "sc-sq94oz-6"
})(["position:absolute;left:12px;display:flex;align-items:center;color:", ";pointer-events:none;z-index:1;"], ({
  theme: e
}) => {
  var r;
  return ((r = e.colors) == null ? void 0 : r.textSecondary) || "#9ca3af";
}), ya = /* @__PURE__ */ a.div.withConfig({
  displayName: "SuffixContainer",
  componentId: "sc-sq94oz-7"
})(["position:absolute;right:12px;display:flex;align-items:center;color:", ";"], ({
  theme: e
}) => {
  var r;
  return ((r = e.colors) == null ? void 0 : r.textSecondary) || "#9ca3af";
}), va = /* @__PURE__ */ a.div.withConfig({
  displayName: "ErrorMessage",
  componentId: "sc-sq94oz-8"
})(["font-size:", ";color:", ";font-weight:500;display:flex;align-items:center;gap:", ";"], ({
  theme: e
}) => {
  var r;
  return ((r = e.fontSizes) == null ? void 0 : r.xs) || "0.75rem";
}, ({
  theme: e
}) => {
  var r;
  return ((r = e.colors) == null ? void 0 : r.error) || "#f44336";
}, ({
  theme: e
}) => {
  var r;
  return ((r = e.spacing) == null ? void 0 : r.xs) || "4px";
}), wa = /* @__PURE__ */ a.div.withConfig({
  displayName: "HelpText",
  componentId: "sc-sq94oz-9"
})(["font-size:", ";color:", ";"], ({
  theme: e
}) => {
  var r;
  return ((r = e.fontSizes) == null ? void 0 : r.xs) || "0.75rem";
}, ({
  theme: e
}) => {
  var r;
  return ((r = e.colors) == null ? void 0 : r.textSecondary) || "#9ca3af";
}), Sa = /* @__PURE__ */ a.div.withConfig({
  displayName: "ValidationIndicator",
  componentId: "sc-sq94oz-10"
})(["position:absolute;right:8px;display:flex;align-items:center;", " ", ""], ({
  $validating: e
}) => e && g(["&::after{content:'';width:12px;height:12px;border:2px solid #4b5563;border-top:2px solid #dc2626;border-radius:50%;animation:spin 1s linear infinite;}@keyframes spin{0%{transform:rotate(0deg);}100%{transform:rotate(360deg);}}"]), ({
  $valid: e,
  $validating: r
}) => !r && g(["color:", ";&::after{content:'", "';}"], e ? "#22c55e" : "#f44336", e ? "✓" : "✗")), dc = (e) => {
  const {
    label: r,
    field: t,
    type: n = "text",
    placeholder: s,
    required: c = !1,
    disabled: i = !1,
    helpText: u,
    options: f = [],
    inputProps: p = {},
    className: d,
    size: h = "md",
    variant: x = "default",
    prefix: y,
    suffix: m
  } = e, b = !!(t.error && t.touched), C = t.touched && !t.validating, w = () => {
    const S = {
      id: p.id || r.toLowerCase().replace(/\s+/g, "-"),
      value: t.value,
      onChange: t.setValue,
      onBlur: () => t.setTouched(!0),
      disabled: i,
      placeholder: s,
      $hasError: b,
      $size: h,
      ...p
    };
    switch (n) {
      case "select":
        return /* @__PURE__ */ o.jsxs(ha, { ...S, children: [
          s && /* @__PURE__ */ o.jsx("option", { value: "", disabled: !0, children: s }),
          f.map((L) => /* @__PURE__ */ o.jsx("option", { value: L.value, children: L.label }, L.value))
        ] });
      case "textarea":
        return /* @__PURE__ */ o.jsx(xa, { ...S });
      default:
        return /* @__PURE__ */ o.jsx(ma, { ...S, type: n });
    }
  };
  return /* @__PURE__ */ o.jsxs(ua, { $size: h, className: d, children: [
    /* @__PURE__ */ o.jsx(fa, { $required: c, $variant: x, htmlFor: p.id || r.toLowerCase().replace(/\s+/g, "-"), children: r }),
    /* @__PURE__ */ o.jsxs(ga, { $hasError: b, $disabled: i, children: [
      y && /* @__PURE__ */ o.jsx(ba, { children: y }),
      w(),
      m && /* @__PURE__ */ o.jsx(ya, { children: m }),
      C && /* @__PURE__ */ o.jsx(Sa, { $valid: t.valid, $validating: t.validating })
    ] }),
    b && /* @__PURE__ */ o.jsxs(va, { children: [
      "⚠️ ",
      t.error
    ] }),
    u && !b && /* @__PURE__ */ o.jsx(wa, { children: u })
  ] });
}, Ca = (e = !1) => {
  const [r, t] = Q(e), [n, s] = Q(null), [c, i] = Q(!1), u = F((y) => {
    t(y), y && (s(null), i(!1));
  }, []), f = F((y) => {
    s(y), t(!1), i(!1);
  }, []), p = F(() => {
    s(null);
  }, []), d = F(() => {
    t(!1), s(null), i(!1);
  }, []), h = F(async (y) => {
    u(!0);
    try {
      const m = await y();
      return i(!0), t(!1), m;
    } catch (m) {
      const b = m instanceof Error ? m.message : "An unexpected error occurred";
      throw f(b), m;
    }
  }, [u, f]), x = F((y) => async (...m) => {
    try {
      await h(() => y(...m));
    } catch (b) {
      console.error("Operation failed:", b);
    }
  }, [h]);
  return {
    // State
    isLoading: r,
    error: n,
    isSuccess: c,
    isError: n !== null,
    // Actions
    setLoading: u,
    setError: f,
    clearError: p,
    reset: d,
    withLoading: h,
    withLoadingCallback: x
  };
};
function pc(e, r = {}) {
  const {
    fetchOnMount: t = !0,
    dependencies: n = []
  } = r, [s, c] = Q({
    data: null,
    isLoading: !1,
    error: null,
    isInitialized: !1
  }), i = F(async (...u) => {
    c((f) => ({
      ...f,
      isLoading: !0,
      error: null
    }));
    try {
      const f = await e(...u);
      return c({
        data: f,
        isLoading: !1,
        error: null,
        isInitialized: !0
      }), f;
    } catch (f) {
      const p = f instanceof Error ? f : new Error(String(f));
      throw c((d) => ({
        ...d,
        isLoading: !1,
        error: p,
        isInitialized: !0
      })), p;
    }
  }, [e]);
  return ue(() => {
    t && i();
  }, [t, i, ...n]), {
    ...s,
    fetchData: i,
    refetch: () => i()
  };
}
function uc(e, r) {
  const [t, n] = Q(e);
  return ue(() => {
    const s = setTimeout(() => {
      n(e);
    }, r);
    return () => {
      clearTimeout(s);
    };
  }, [e, r]), t;
}
function fc(e = {}) {
  const {
    componentName: r,
    logToConsole: t = !0,
    reportToMonitoring: n = !0,
    onError: s
  } = e, [c, i] = Q(null), [u, f] = Q(!1), p = F((x) => {
    if (i(x), f(!0), t) {
      const y = r ? `[${r}]` : "";
      console.error(`Error caught by useErrorHandler${y}:`, x);
    }
    s && s(x);
  }, [r, t, n, s]), d = F(() => {
    i(null), f(!1);
  }, []), h = F(async (x) => {
    try {
      return await x();
    } catch (y) {
      p(y);
      return;
    }
  }, [p]);
  return ue(() => () => {
    i(null), f(!1);
  }, []), {
    error: c,
    hasError: u,
    handleError: p,
    resetError: d,
    tryExecute: h
  };
}
function tt(e, r) {
  const t = () => {
    if (typeof window > "u")
      return r;
    try {
      const i = window.localStorage.getItem(e);
      return i ? JSON.parse(i) : r;
    } catch (i) {
      return console.warn(`Error reading localStorage key "${e}":`, i), r;
    }
  }, [n, s] = Q(t), c = (i) => {
    try {
      const u = i instanceof Function ? i(n) : i;
      s(u), typeof window < "u" && window.localStorage.setItem(e, JSON.stringify(u));
    } catch (u) {
      console.warn(`Error setting localStorage key "${e}":`, u);
    }
  };
  return ue(() => {
    const i = (u) => {
      u.key === e && u.newValue && s(JSON.parse(u.newValue));
    };
    return window.addEventListener("storage", i), () => window.removeEventListener("storage", i);
  }, [e]), [n, c];
}
function gc(e) {
  const {
    totalItems: r,
    itemsPerPage: t = 10,
    initialPage: n = 1,
    persistKey: s
  } = e, [c, i] = s ? tt(`${s}_page`, n) : Q(n), [u, f] = s ? tt(`${s}_itemsPerPage`, t) : Q(t), p = U(() => Math.max(1, Math.ceil(r / u)), [r, u]), d = U(() => Math.min(Math.max(1, c), p), [c, p]);
  d !== c && i(d);
  const h = (d - 1) * u, x = Math.min(h + u - 1, r - 1), y = d > 1, m = d < p, b = U(() => {
    const O = [];
    if (p <= 5)
      for (let R = 1; R <= p; R++)
        O.push(R);
    else {
      let R = Math.max(1, d - Math.floor(2.5));
      const T = Math.min(p, R + 5 - 1);
      T === p && (R = Math.max(1, T - 5 + 1));
      for (let _ = R; _ <= T; _++)
        O.push(_);
    }
    return O;
  }, [d, p]), C = F(() => {
    m && i(d + 1);
  }, [m, d, i]), w = F(() => {
    y && i(d - 1);
  }, [y, d, i]), S = F((A) => {
    const O = Math.min(Math.max(1, A), p);
    i(O);
  }, [p, i]), L = F((A) => {
    f(A), i(1);
  }, [f, i]);
  return {
    currentPage: d,
    itemsPerPage: u,
    totalPages: p,
    hasPreviousPage: y,
    hasNextPage: m,
    startIndex: h,
    endIndex: x,
    pageRange: b,
    nextPage: C,
    previousPage: w,
    goToPage: S,
    setItemsPerPage: L
  };
}
const Ia = (e, r = "$", t = !1) => {
  const s = Math.abs(e).toLocaleString("en-US", {
    minimumFractionDigits: 2,
    maximumFractionDigits: 2
  });
  return e > 0 ? t ? `+${r}${s}` : `${r}${s}` : e < 0 ? `-${r}${s}` : `${r}${s}`;
}, mc = (e, r = {}) => {
  const {
    currency: t = "$",
    showPositiveSign: n = !1,
    customAriaLabel: s
  } = r;
  return U(() => {
    if (e == null)
      return {
        formattedAmount: "",
        isProfit: !1,
        isLoss: !1,
        isNeutral: !1,
        isEmpty: !0,
        ariaLabel: s || "No profit/loss data available"
      };
    const c = e > 0, i = e < 0, u = e === 0, f = Ia(e, t, n), p = `${c ? "Profit" : i ? "Loss" : "Breakeven"} of ${f}`;
    return {
      formattedAmount: f,
      isProfit: c,
      isLoss: i,
      isNeutral: u,
      isEmpty: !1,
      ariaLabel: s || p
    };
  }, [e, t, n, s]);
}, Ea = (e) => e == null ? !0 : Array.isArray(e) ? e.length === 0 : typeof e == "object" ? Object.keys(e).length === 0 : typeof e == "string" ? e.trim().length === 0 : !1, hc = (e) => {
  const {
    fetchData: r,
    initialData: t = null,
    fetchOnMount: n = !0,
    refreshInterval: s,
    isEmpty: c = Ea,
    transformError: i,
    dependencies: u = []
  } = e, [f, p] = Q(t), [d, h] = Q(null), x = Ca(), y = U(() => f === null || c(f), [f, c]), m = F(async () => {
    try {
      const S = await x.withLoading(r);
      p(S), h(/* @__PURE__ */ new Date());
    } catch (S) {
      const L = i && S instanceof Error ? i(S) : S instanceof Error ? S.message : "Failed to fetch data";
      x.setError(L), console.error("Data fetch failed:", S);
    }
  }, [r, x, i]), b = F(async () => {
    await m();
  }, [m]), C = F(() => {
    p(t), h(null), x.reset();
  }, [t, x]), w = F((S) => {
    p(S), h(/* @__PURE__ */ new Date()), x.clearError();
  }, [x]);
  return ue(() => {
    n && m();
  }, [n, m]), ue(() => {
    u.length > 0 && d !== null && m();
  }, u), ue(() => {
    if (!s || s <= 0)
      return;
    const S = setInterval(() => {
      !x.isLoading && !x.error && m();
    }, s);
    return () => clearInterval(S);
  }, [s, x.isLoading, x.error, m]), {
    // State
    data: f,
    isLoading: x.isLoading,
    error: x.error,
    isEmpty: y,
    isSuccess: x.isSuccess,
    isError: x.isError,
    lastFetched: d,
    // Actions
    refresh: b,
    clearError: x.clearError,
    reset: C,
    setData: w
  };
}, xc = (e = "en-US") => U(() => ({
  formatCurrency: (f, p = {}) => {
    const {
      currency: d = "USD",
      locale: h = e,
      minimumFractionDigits: x = 2,
      maximumFractionDigits: y = 2,
      showPositiveSign: m = !1
    } = p, C = new Intl.NumberFormat(h, {
      style: "currency",
      currency: d,
      minimumFractionDigits: x,
      maximumFractionDigits: y
    }).format(Math.abs(f));
    return f > 0 && m ? `+${C}` : f < 0 ? `-${C}` : C;
  },
  formatPercent: (f, p = {}) => {
    const {
      locale: d = e,
      minimumFractionDigits: h = 2,
      maximumFractionDigits: x = 2,
      showPositiveSign: y = !1
    } = p, m = new Intl.NumberFormat(d, {
      style: "percent",
      minimumFractionDigits: h,
      maximumFractionDigits: x
    }), b = f > 1 ? f / 100 : f, C = m.format(Math.abs(b));
    return b > 0 && y ? `+${C}` : b < 0 ? `-${C}` : C;
  },
  formatNumber: (f, p = {}) => {
    const {
      locale: d = e,
      minimumFractionDigits: h = 0,
      maximumFractionDigits: x = 2,
      useGrouping: y = !0
    } = p;
    return new Intl.NumberFormat(d, {
      minimumFractionDigits: h,
      maximumFractionDigits: x,
      useGrouping: y
    }).format(f);
  },
  formatDate: (f, p = "medium") => {
    const d = typeof f == "string" ? new Date(f) : f;
    return new Intl.DateTimeFormat(e, {
      dateStyle: p
    }).format(d);
  },
  formatTime: (f, p = "short") => {
    const d = typeof f == "string" ? new Date(f) : f;
    return new Intl.DateTimeFormat(e, {
      timeStyle: p
    }).format(d);
  },
  formatRelativeTime: (f) => {
    const p = typeof f == "string" ? new Date(f) : f, h = Math.floor(((/* @__PURE__ */ new Date()).getTime() - p.getTime()) / 1e3);
    if (typeof Intl.RelativeTimeFormat < "u") {
      const b = new Intl.RelativeTimeFormat(e, {
        numeric: "auto"
      }), C = [{
        unit: "year",
        seconds: 31536e3
      }, {
        unit: "month",
        seconds: 2592e3
      }, {
        unit: "day",
        seconds: 86400
      }, {
        unit: "hour",
        seconds: 3600
      }, {
        unit: "minute",
        seconds: 60
      }, {
        unit: "second",
        seconds: 1
      }];
      for (const w of C) {
        const S = Math.floor(Math.abs(h) / w.seconds);
        if (S >= 1)
          return b.format(h > 0 ? -S : S, w.unit);
      }
      return b.format(0, "second");
    }
    const x = Math.abs(h), y = h < 0;
    if (x < 60)
      return y ? "in a few seconds" : "a few seconds ago";
    if (x < 3600) {
      const b = Math.floor(x / 60);
      return y ? `in ${b} minute${b > 1 ? "s" : ""}` : `${b} minute${b > 1 ? "s" : ""} ago`;
    }
    if (x < 86400) {
      const b = Math.floor(x / 3600);
      return y ? `in ${b} hour${b > 1 ? "s" : ""}` : `${b} hour${b > 1 ? "s" : ""} ago`;
    }
    const m = Math.floor(x / 86400);
    return y ? `in ${m} day${m > 1 ? "s" : ""}` : `${m} day${m > 1 ? "s" : ""} ago`;
  }
}), [e]), ja = {
  small: g(["font-size:", ";padding:", " ", ";"], ({
    theme: e
  }) => {
    var r;
    return ((r = e.fontSizes) == null ? void 0 : r.xs) || "12px";
  }, ({
    theme: e
  }) => {
    var r;
    return ((r = e.spacing) == null ? void 0 : r.xxs) || "2px";
  }, ({
    theme: e
  }) => {
    var r;
    return ((r = e.spacing) == null ? void 0 : r.xs) || "4px";
  }),
  medium: g(["font-size:", ";padding:", " ", ";"], ({
    theme: e
  }) => {
    var r;
    return ((r = e.fontSizes) == null ? void 0 : r.sm) || "14px";
  }, ({
    theme: e
  }) => {
    var r;
    return ((r = e.spacing) == null ? void 0 : r.xs) || "4px";
  }, ({
    theme: e
  }) => {
    var r;
    return ((r = e.spacing) == null ? void 0 : r.sm) || "8px";
  }),
  large: g(["font-size:", ";padding:", " ", ";"], ({
    theme: e
  }) => {
    var r;
    return ((r = e.fontSizes) == null ? void 0 : r.lg) || "18px";
  }, ({
    theme: e
  }) => {
    var r;
    return ((r = e.spacing) == null ? void 0 : r.sm) || "8px";
  }, ({
    theme: e
  }) => {
    var r;
    return ((r = e.spacing) == null ? void 0 : r.md) || "12px";
  })
}, Ta = {
  profit: g(["color:", ";background-color:", ";border:1px solid ", ";"], ({
    theme: e
  }) => {
    var r, t;
    return ((r = e.colors) == null ? void 0 : r.profit) || ((t = e.colors) == null ? void 0 : t.success) || "#4caf50";
  }, ({
    theme: e
  }) => {
    var r;
    return (r = e.colors) != null && r.profit ? `${e.colors.profit}15` : "rgba(76, 175, 80, 0.1)";
  }, ({
    theme: e
  }) => {
    var r;
    return (r = e.colors) != null && r.profit ? `${e.colors.profit}30` : "rgba(76, 175, 80, 0.2)";
  }),
  loss: g(["color:", ";background-color:", ";border:1px solid ", ";"], ({
    theme: e
  }) => {
    var r, t;
    return ((r = e.colors) == null ? void 0 : r.loss) || ((t = e.colors) == null ? void 0 : t.error) || "#f44336";
  }, ({
    theme: e
  }) => {
    var r;
    return (r = e.colors) != null && r.loss ? `${e.colors.loss}15` : "rgba(244, 67, 54, 0.1)";
  }, ({
    theme: e
  }) => {
    var r;
    return (r = e.colors) != null && r.loss ? `${e.colors.loss}30` : "rgba(244, 67, 54, 0.2)";
  }),
  neutral: g(["color:", ";background-color:", ";border:1px solid ", ";"], ({
    theme: e
  }) => {
    var r, t;
    return ((r = e.colors) == null ? void 0 : r.neutral) || ((t = e.colors) == null ? void 0 : t.textSecondary) || "#757575";
  }, ({
    theme: e
  }) => {
    var r;
    return (r = e.colors) != null && r.neutral ? `${e.colors.neutral}15` : "rgba(117, 117, 117, 0.1)";
  }, ({
    theme: e
  }) => {
    var r;
    return (r = e.colors) != null && r.neutral ? `${e.colors.neutral}30` : "rgba(117, 117, 117, 0.2)";
  }),
  default: g(["color:", ";background-color:transparent;border:1px solid transparent;"], ({
    theme: e
  }) => {
    var r;
    return ((r = e.colors) == null ? void 0 : r.textPrimary) || "#ffffff";
  })
}, bc = /* @__PURE__ */ g(["display:inline-flex;align-items:center;justify-content:flex-end;font-weight:", ";font-family:", ";transition:", ";border-radius:", ";&:hover{transform:translateY(-1px);box-shadow:", ";}"], ({
  theme: e
}) => {
  var r;
  return ((r = e.fontWeights) == null ? void 0 : r.semibold) || "600";
}, ({
  theme: e
}) => {
  var r;
  return ((r = e.fontFamilies) == null ? void 0 : r.mono) || "monospace";
}, ({
  theme: e
}) => {
  var r;
  return ((r = e.transitions) == null ? void 0 : r.fast) || "all 0.2s ease";
}, ({
  theme: e
}) => {
  var r;
  return ((r = e.borderRadius) == null ? void 0 : r.sm) || "4px";
}, ({
  theme: e
}) => {
  var r;
  return ((r = e.shadows) == null ? void 0 : r.sm) || "0 2px 4px rgba(0, 0, 0, 0.1)";
}), yc = /* @__PURE__ */ g(["opacity:0.6;position:relative;&::after{content:'';position:absolute;top:0;left:0;right:0;bottom:0;background:linear-gradient(90deg,transparent,rgba(255,255,255,0.2),transparent);animation:shimmer 1.5s infinite;}@keyframes shimmer{0%{transform:translateX(-100%);}100%{transform:translateX(100%);}}"]), vc = (e) => ja[e], wc = (e, r, t) => e ? "profit" : r ? "loss" : t ? "neutral" : "default", Sc = (e) => Ta[e], pt = {
  name: "f1",
  colors: {
    // Primary colors
    primary: j.f1Red,
    primaryDark: j.f1RedDark,
    primaryLight: j.f1RedLight,
    // Secondary colors
    secondary: j.f1Blue,
    secondaryDark: j.f1BlueDark,
    secondaryLight: j.f1BlueLight,
    // Accent colors
    accent: j.purple,
    accentDark: j.purpleDark,
    accentLight: j.purpleLight,
    // Status colors
    success: q.success,
    warning: q.warning,
    error: q.error,
    danger: q.error,
    // Use error color for danger
    info: q.info,
    // Neutral colors
    background: q.background,
    surface: q.surface,
    elevated: j.gray700,
    // Added elevated color for F1 theme
    cardBackground: q.surface,
    border: q.border,
    divider: "rgba(255, 255, 255, 0.1)",
    // Text colors
    textPrimary: q.textPrimary,
    textSecondary: q.textSecondary,
    textDisabled: q.textDisabled,
    textInverse: q.textInverse,
    // Chart colors
    chartGrid: q.chartGrid,
    chartLine: q.chartLine,
    chartAxis: j.gray400,
    chartTooltip: q.tooltipBackground,
    // Trading specific colors
    profit: q.profit,
    loss: q.loss,
    neutral: q.neutral,
    // Tab colors
    tabActive: j.f1Red,
    tabInactive: j.gray600,
    // Component specific colors
    tooltipBackground: q.tooltipBackground,
    modalBackground: q.modalBackground,
    sidebarBackground: j.gray800,
    headerBackground: "rgba(0, 0, 0, 0.2)"
  },
  spacing: re,
  breakpoints: ar,
  fontSizes: me,
  fontWeights: nr,
  lineHeights: sr,
  fontFamilies: ir,
  borderRadius: cr,
  shadows: lr,
  transitions: dr,
  zIndex: pr
}, Na = {
  name: "light",
  colors: {
    // Primary colors
    primary: j.f1Red,
    primaryDark: j.f1RedDark,
    primaryLight: j.f1RedLight,
    // Secondary colors
    secondary: j.f1Blue,
    secondaryDark: j.f1BlueDark,
    secondaryLight: j.f1BlueLight,
    // Accent colors
    accent: j.purple,
    accentDark: j.purpleDark,
    accentLight: j.purpleLight,
    // Status colors
    success: ee.success,
    warning: ee.warning,
    error: ee.error,
    danger: ee.error,
    // Use error color for danger
    info: ee.info,
    // Neutral colors
    background: ee.background,
    surface: ee.surface,
    elevated: j.gray100,
    // Added elevated color for light theme
    cardBackground: ee.surface,
    border: ee.border,
    divider: j.blackTransparent10,
    // Text colors
    textPrimary: ee.textPrimary,
    textSecondary: ee.textSecondary,
    textDisabled: ee.textDisabled,
    textInverse: ee.textInverse,
    // Chart colors
    chartGrid: ee.chartGrid,
    chartLine: ee.chartLine,
    chartAxis: j.gray600,
    chartTooltip: ee.tooltipBackground,
    // Trading specific colors
    profit: ee.profit,
    loss: ee.loss,
    neutral: ee.neutral,
    // Tab colors
    tabActive: j.f1Red,
    tabInactive: j.gray400,
    // Component specific colors
    tooltipBackground: ee.tooltipBackground,
    modalBackground: ee.modalBackground,
    sidebarBackground: j.white,
    headerBackground: "rgba(0, 0, 0, 0.05)"
  },
  spacing: re,
  breakpoints: ar,
  fontSizes: me,
  fontWeights: nr,
  lineHeights: sr,
  fontFamilies: ir,
  borderRadius: cr,
  shadows: lr,
  transitions: dr,
  zIndex: pr
}, ka = {
  name: "dark",
  colors: {
    // Primary colors (using blue as primary instead of red to differentiate from F1 theme)
    primary: j.f1Blue,
    primaryDark: j.f1BlueDark,
    primaryLight: j.f1BlueLight,
    // Secondary colors
    secondary: j.f1Blue,
    secondaryDark: j.f1BlueDark,
    secondaryLight: j.f1BlueLight,
    // Accent colors
    accent: j.purple,
    accentDark: j.purpleDark,
    accentLight: j.purpleLight,
    // Status colors
    success: q.success,
    warning: q.warning,
    error: q.error,
    danger: q.error,
    // Use error color for danger
    info: q.info,
    // Neutral colors
    background: j.gray900,
    // Slightly different from F1 theme
    surface: j.gray800,
    elevated: j.gray700,
    // Added elevated color for dark theme
    cardBackground: j.gray800,
    border: j.gray700,
    divider: "rgba(255, 255, 255, 0.1)",
    // Text colors
    textPrimary: j.white,
    textSecondary: j.gray300,
    textDisabled: j.gray500,
    textInverse: j.gray900,
    // Chart colors
    chartGrid: q.chartGrid,
    chartLine: j.f1Blue,
    // Using blue instead of red
    chartAxis: j.gray400,
    chartTooltip: q.tooltipBackground,
    // Trading specific colors
    profit: q.profit,
    loss: q.loss,
    neutral: q.neutral,
    // Tab colors
    tabActive: j.f1Blue,
    // Using blue instead of red
    tabInactive: j.gray600,
    // Component specific colors
    tooltipBackground: "rgba(26, 32, 44, 0.9)",
    // Slightly different from F1 theme
    modalBackground: "rgba(26, 32, 44, 0.8)",
    sidebarBackground: j.gray900,
    headerBackground: "rgba(0, 0, 0, 0.3)"
  },
  spacing: re,
  breakpoints: ar,
  fontSizes: me,
  fontWeights: nr,
  lineHeights: sr,
  fontFamilies: ir,
  borderRadius: cr,
  shadows: lr,
  transitions: dr,
  zIndex: pr
}, Ra = /* @__PURE__ */ qt(["*,*::before,*::after{box-sizing:border-box;}html,body,div,span,applet,object,iframe,h1,h2,h3,h4,h5,h6,p,blockquote,pre,a,abbr,acronym,address,big,cite,code,del,dfn,em,img,ins,kbd,q,s,samp,small,strike,strong,sub,sup,tt,var,b,u,i,center,dl,dt,dd,ol,ul,li,fieldset,form,label,legend,table,caption,tbody,tfoot,thead,tr,th,td,article,aside,canvas,details,embed,figure,figcaption,footer,header,hgroup,menu,nav,output,ruby,section,summary,time,mark,audio,video{margin:0;padding:0;border:0;font-size:100%;font:inherit;vertical-align:baseline;}article,aside,details,figcaption,figure,footer,header,hgroup,menu,nav,section{display:block;}body{line-height:1.5;font-family:", ";background-color:", ";color:", ";-webkit-font-smoothing:antialiased;-moz-osx-font-smoothing:grayscale;}ol,ul{list-style:none;}blockquote,q{quotes:none;}blockquote:before,blockquote:after,q:before,q:after{content:'';content:none;}table{border-collapse:collapse;border-spacing:0;}a{color:", ";text-decoration:none;&:hover{text-decoration:underline;}}button,input,select,textarea{font-family:inherit;font-size:inherit;line-height:inherit;}::-webkit-scrollbar{width:8px;height:8px;}::-webkit-scrollbar-track{background:", ";}::-webkit-scrollbar-thumb{background:", ";border-radius:4px;}::-webkit-scrollbar-thumb:hover{background:", ";}:focus{outline:2px solid ", ";outline-offset:2px;}::selection{background-color:", ";color:", ";}"], ({
  theme: e
}) => e.fontFamilies.body, ({
  theme: e
}) => e.colors.background, ({
  theme: e
}) => e.colors.textPrimary, ({
  theme: e
}) => e.colors.primary, ({
  theme: e
}) => e.colors.background, ({
  theme: e
}) => e.colors.border, ({
  theme: e
}) => e.colors.primary, ({
  theme: e
}) => e.colors.primary, ({
  theme: e
}) => e.colors.primary, ({
  theme: e
}) => e.colors.textInverse), _a = Ra, La = {
  f1: pt,
  light: Na,
  dark: ka
}, fr = pt, Ze = (e) => La[e] || fr, ut = nt({
  theme: fr,
  setTheme: () => {
  }
}), Cc = () => st(ut), Ic = ({
  initialTheme: e = fr,
  persistTheme: r = !0,
  storageKey: t = "adhd-dashboard-theme",
  children: n
}) => {
  const [s, c] = Q(() => {
    if (r && typeof window < "u") {
      const p = window.localStorage.getItem(t);
      if (p)
        try {
          const d = Ze(p);
          return d || JSON.parse(p);
        } catch (d) {
          console.error("Failed to parse stored theme:", d);
        }
    }
    return typeof e == "string" ? Ze(e) : e;
  }), i = (f) => {
    const p = typeof f == "string" ? Ze(f) : f;
    c(p), r && typeof window < "u" && window.localStorage.setItem(t, p.name || JSON.stringify(p));
  }, u = ({
    children: f
  }) => /* @__PURE__ */ o.jsxs(Ht, { theme: s, children: [
    /* @__PURE__ */ o.jsx(_a, {}),
    f
  ] });
  return /* @__PURE__ */ o.jsx(ut.Provider, { value: {
    theme: s,
    setTheme: i
  }, children: /* @__PURE__ */ o.jsx(u, { children: n }) });
};
function Ec(e, r, t = "StoreContext") {
  const n = nt(void 0);
  n.displayName = t;
  const s = ({
    children: p,
    initialState: d
  }) => {
    const [h, x] = Bt(e, d || r), y = U(() => ({
      state: h,
      dispatch: x
    }), [h]);
    return /* @__PURE__ */ o.jsx(n.Provider, { value: y, children: p });
  };
  function c() {
    const p = st(n);
    if (p === void 0)
      throw new Error(`use${t} must be used within a ${t}Provider`);
    return p;
  }
  function i(p) {
    const {
      state: d
    } = c();
    return p(d);
  }
  function u(p) {
    const {
      dispatch: d
    } = c();
    return U(() => (...h) => {
      d(p(...h));
    }, [d, p]);
  }
  function f(p) {
    const {
      dispatch: d
    } = c();
    return U(() => {
      const h = {};
      for (const x in p)
        h[x] = (...y) => {
          d(p[x](...y));
        };
      return h;
    }, [d, p]);
  }
  return {
    Context: n,
    Provider: s,
    useStore: c,
    useSelector: i,
    useAction: u,
    useActions: f
  };
}
function jc(...e) {
  const r = e.pop(), t = e;
  let n = null, s = null;
  return (c) => {
    const i = t.map((u) => u(c));
    return (n === null || i.length !== n.length || i.some((u, f) => u !== n[f])) && (s = r(...i), n = i), s;
  };
}
function Tc(e, r) {
  const {
    key: t,
    initialState: n,
    version: s = 1,
    migrate: c,
    serialize: i = JSON.stringify,
    deserialize: u = JSON.parse,
    filter: f = (w) => w,
    merge: p = (w, S) => ({
      ...S,
      ...w
    }),
    debug: d = !1
  } = r, h = () => {
    try {
      const w = localStorage.getItem(t);
      if (w === null)
        return null;
      const {
        state: S,
        version: L
      } = u(w);
      return L !== s && c ? (d && console.log(`Migrating state from version ${L} to ${s}`), c(S, L)) : S;
    } catch (w) {
      return d && console.error("Error loading state from local storage:", w), null;
    }
  }, x = (w) => {
    try {
      const S = f(w), L = i({
        state: S,
        version: s
      });
      localStorage.setItem(t, L);
    } catch (S) {
      d && console.error("Error saving state to local storage:", S);
    }
  }, y = () => {
    try {
      localStorage.removeItem(t);
    } catch (w) {
      d && console.error("Error clearing state from local storage:", w);
    }
  }, m = h(), b = m ? p(m, n) : n;
  return d && m && (console.log("Loaded persisted state:", m), console.log("Merged initial state:", b)), {
    reducer: (w, S) => {
      const L = e(w, S);
      return x(L), L;
    },
    initialState: b,
    clear: y
  };
}
function Nc(e, r = "$") {
  return `${r}${e.toFixed(2)}`;
}
function kc(e, r = 1) {
  return `${(e * 100).toFixed(r)}%`;
}
function Rc(e, r = "short") {
  const t = typeof e == "string" ? new Date(e) : e;
  switch (r) {
    case "medium":
      return t.toLocaleDateString("en-US", {
        year: "numeric",
        month: "short",
        day: "numeric"
      });
    case "long":
      return t.toLocaleDateString("en-US", {
        year: "numeric",
        month: "long",
        day: "numeric"
      });
    case "short":
    default:
      return t.toLocaleDateString("en-US", {
        year: "numeric",
        month: "2-digit",
        day: "2-digit"
      });
  }
}
function _c(e, r = 50) {
  return e.length <= r ? e : `${e.substring(0, r - 3)}...`;
}
function Lc() {
  return Math.random().toString(36).substring(2, 9);
}
function Pc(e, r) {
  let t = null;
  return function(...n) {
    const s = () => {
      t = null, e(...n);
    };
    t && clearTimeout(t), t = setTimeout(s, r);
  };
}
function Mc(e, r) {
  let t = !1;
  return function(...n) {
    t || (e(...n), t = !0, setTimeout(() => {
      t = !1;
    }, r));
  };
}
function Dc(e = {}) {
  console.log("Monitoring service initialized", e);
}
function Oc(e, r) {
  console.error("Error captured by monitoring service:", e, r);
}
function $c(e) {
  console.log("User set for monitoring service:", e);
}
function Ac(e, r) {
  const t = performance.now();
  return {
    name: e,
    startTime: t,
    finish: () => {
      const s = performance.now() - t;
      console.log(`Transaction "${e}" finished in ${s.toFixed(2)}ms`, r);
    }
  };
}
const K = {
  MODEL_TYPE: "model_type",
  WIN_LOSS: "win_loss",
  R_MULTIPLE: "r_multiple",
  DATE: "date",
  SESSION: "session",
  DIRECTION: "direction",
  MARKET: "market",
  ACHIEVED_PL: "achieved_pl",
  PATTERN_QUALITY_RATING: "pattern_quality_rating"
};
class Pa {
  constructor() {
    this.dbName = "adhd-trading-dashboard", this.version = 2, this.db = null, this.stores = {
      trades: "trades",
      fvg_details: "trade_fvg_details",
      setups: "trade_setups",
      analysis: "trade_analysis",
      sessions: "trading_sessions"
    };
  }
  /**
   * Initialize the database with new schema
   * @returns A promise that resolves when the database is initialized
   */
  async initDB() {
    return this.db ? this.db : new Promise((r, t) => {
      const n = indexedDB.open(this.dbName, this.version);
      n.onupgradeneeded = (s) => {
        var i;
        const c = s.target.result;
        if (!c.objectStoreNames.contains(this.stores.trades)) {
          const u = c.createObjectStore(this.stores.trades, {
            keyPath: "id",
            autoIncrement: !0
          });
          u.createIndex(K.DATE, K.DATE, {
            unique: !1
          }), u.createIndex(K.MODEL_TYPE, K.MODEL_TYPE, {
            unique: !1
          }), u.createIndex(K.SESSION, K.SESSION, {
            unique: !1
          }), u.createIndex(K.WIN_LOSS, K.WIN_LOSS, {
            unique: !1
          }), u.createIndex(K.R_MULTIPLE, K.R_MULTIPLE, {
            unique: !1
          });
        }
        if (c.objectStoreNames.contains(this.stores.fvg_details) || c.createObjectStore(this.stores.fvg_details, {
          keyPath: "id",
          autoIncrement: !0
        }).createIndex("trade_id", "trade_id", {
          unique: !1
        }), c.objectStoreNames.contains(this.stores.setups) || c.createObjectStore(this.stores.setups, {
          keyPath: "id",
          autoIncrement: !0
        }).createIndex("trade_id", "trade_id", {
          unique: !1
        }), c.objectStoreNames.contains(this.stores.analysis) || c.createObjectStore(this.stores.analysis, {
          keyPath: "id",
          autoIncrement: !0
        }).createIndex("trade_id", "trade_id", {
          unique: !1
        }), !c.objectStoreNames.contains(this.stores.sessions)) {
          c.createObjectStore(this.stores.sessions, {
            keyPath: "id",
            autoIncrement: !0
          }).createIndex("name", "name", {
            unique: !0
          });
          const f = [{
            name: "Pre-Market",
            start_time: "04:00:00",
            end_time: "09:30:00",
            description: "Pre-market trading hours"
          }, {
            name: "NY Open",
            start_time: "09:30:00",
            end_time: "10:30:00",
            description: "New York opening hour"
          }, {
            name: "10:50-11:10",
            start_time: "10:50:00",
            end_time: "11:10:00",
            description: "Mid-morning macro window"
          }, {
            name: "11:50-12:10",
            start_time: "11:50:00",
            end_time: "12:10:00",
            description: "Pre-lunch macro window"
          }, {
            name: "Lunch Macro",
            start_time: "12:00:00",
            end_time: "13:30:00",
            description: "Lunch time trading"
          }, {
            name: "13:50-14:10",
            start_time: "13:50:00",
            end_time: "14:10:00",
            description: "Post-lunch macro window"
          }, {
            name: "14:50-15:10",
            start_time: "14:50:00",
            end_time: "15:10:00",
            description: "Pre-close macro window"
          }, {
            name: "15:15-15:45",
            start_time: "15:15:00",
            end_time: "15:45:00",
            description: "Late afternoon window"
          }, {
            name: "MOC",
            start_time: "15:45:00",
            end_time: "16:00:00",
            description: "Market on close"
          }, {
            name: "Post MOC",
            start_time: "16:00:00",
            end_time: "20:00:00",
            description: "After hours trading"
          }];
          (i = n.transaction) == null || i.addEventListener("complete", () => {
            const d = c.transaction([this.stores.sessions], "readwrite").objectStore(this.stores.sessions);
            f.forEach((h) => d.add(h));
          });
        }
      }, n.onsuccess = (s) => {
        this.db = s.target.result, r(this.db);
      }, n.onerror = (s) => {
        console.error("Error opening IndexedDB:", s), t(new Error("Failed to open IndexedDB"));
      };
    });
  }
  /**
   * Save a complete trade with all related details
   * @param tradeData Complete trade data including all related tables
   * @returns A promise that resolves with the saved trade ID
   */
  async saveTradeWithDetails(r) {
    try {
      const t = await this.initDB();
      return new Promise((n, s) => {
        const c = t.transaction([this.stores.trades, this.stores.fvg_details, this.stores.setups, this.stores.analysis], "readwrite");
        c.onerror = (p) => {
          console.error("Transaction error:", p), s(new Error("Failed to save trade with details"));
        };
        const i = c.objectStore(this.stores.trades), u = {
          ...r.trade,
          created_at: (/* @__PURE__ */ new Date()).toISOString(),
          updated_at: (/* @__PURE__ */ new Date()).toISOString()
        }, f = i.add(u);
        f.onsuccess = () => {
          const p = f.result, d = [];
          if (r.fvg_details) {
            const h = c.objectStore(this.stores.fvg_details), x = {
              ...r.fvg_details,
              trade_id: p
            };
            d.push(new Promise((y, m) => {
              const b = h.add(x);
              b.onsuccess = () => y(), b.onerror = () => m(new Error("Failed to save FVG details"));
            }));
          }
          if (r.setup) {
            const h = c.objectStore(this.stores.setups), x = {
              ...r.setup,
              trade_id: p
            };
            d.push(new Promise((y, m) => {
              const b = h.add(x);
              b.onsuccess = () => y(), b.onerror = () => m(new Error("Failed to save setup data"));
            }));
          }
          if (r.analysis) {
            const h = c.objectStore(this.stores.analysis), x = {
              ...r.analysis,
              trade_id: p
            };
            d.push(new Promise((y, m) => {
              const b = h.add(x);
              b.onsuccess = () => y(), b.onerror = () => m(new Error("Failed to save analysis data"));
            }));
          }
          c.oncomplete = () => {
            n(p);
          };
        }, f.onerror = (p) => {
          console.error("Error saving trade:", p), s(new Error("Failed to save trade"));
        };
      });
    } catch (t) {
      throw console.error("Error in saveTradeWithDetails:", t), new Error("Failed to save trade with details");
    }
  }
  /**
   * Get a complete trade by ID with all related data
   * @param id The ID of the trade to get
   * @returns A promise that resolves with the complete trade data
   */
  async getTradeById(r) {
    try {
      const t = await this.initDB();
      return new Promise((n, s) => {
        const c = t.transaction([this.stores.trades, this.stores.fvg_details, this.stores.setups, this.stores.analysis], "readonly"), u = c.objectStore(this.stores.trades).get(r);
        u.onsuccess = () => {
          const f = u.result;
          if (!f) {
            n(null);
            return;
          }
          const p = {
            trade: f
          }, x = c.objectStore(this.stores.fvg_details).index("trade_id").get(r);
          x.onsuccess = () => {
            x.result && (p.fvg_details = x.result);
            const b = c.objectStore(this.stores.setups).index("trade_id").get(r);
            b.onsuccess = () => {
              b.result && (p.setup = b.result);
              const S = c.objectStore(this.stores.analysis).index("trade_id").get(r);
              S.onsuccess = () => {
                S.result && (p.analysis = S.result), n(p);
              }, S.onerror = (L) => {
                console.error("Error getting analysis data:", L), n(p);
              };
            }, b.onerror = (C) => {
              console.error("Error getting setup data:", C), n(p);
            };
          }, x.onerror = (y) => {
            console.error("Error getting FVG details:", y), n(p);
          };
        }, u.onerror = (f) => {
          console.error("Error getting trade:", f), s(new Error("Failed to get trade"));
        };
      });
    } catch (t) {
      return console.error("Error in getTradeById:", t), null;
    }
  }
  /**
   * Get performance metrics from all trades
   * @returns A promise that resolves with performance metrics
   */
  async getPerformanceMetrics() {
    try {
      const r = await this.initDB();
      return new Promise((t, n) => {
        const i = r.transaction([this.stores.trades], "readonly").objectStore(this.stores.trades).getAll();
        i.onsuccess = () => {
          const u = i.result;
          if (u.length === 0) {
            t({
              totalTrades: 0,
              winningTrades: 0,
              losingTrades: 0,
              winRate: 0,
              profitFactor: 0,
              averageWin: 0,
              averageLoss: 0,
              largestWin: 0,
              largestLoss: 0,
              totalPnl: 0,
              maxDrawdown: 0,
              maxDrawdownPercent: 0,
              sharpeRatio: 0,
              sortinoRatio: 0,
              calmarRatio: 0,
              averageRMultiple: 0,
              expectancy: 0,
              sqn: 0,
              period: "all",
              startDate: "",
              endDate: ""
            });
            return;
          }
          const f = u.length, p = u.filter((D) => D[K.WIN_LOSS] === "Win").length, d = u.filter((D) => D[K.WIN_LOSS] === "Loss").length, h = f > 0 ? p / f * 100 : 0, x = u.filter((D) => D.achieved_pl !== void 0).map((D) => D.achieved_pl), y = x.reduce((D, J) => D + J, 0), m = x.filter((D) => D > 0), b = x.filter((D) => D < 0), C = m.length > 0 ? m.reduce((D, J) => D + J, 0) / m.length : 0, w = b.length > 0 ? Math.abs(b.reduce((D, J) => D + J, 0) / b.length) : 0, S = m.length > 0 ? Math.max(...m) : 0, L = b.length > 0 ? Math.abs(Math.min(...b)) : 0, A = m.reduce((D, J) => D + J, 0), O = Math.abs(b.reduce((D, J) => D + J, 0)), R = O > 0 ? A / O : 0, T = u.filter((D) => D[K.R_MULTIPLE] !== void 0).map((D) => D[K.R_MULTIPLE]), _ = T.length > 0 ? T.reduce((D, J) => D + J, 0) / T.length : 0, N = _ * (h / 100);
          let Y = 0, W = 0, k = 0;
          for (const D of u)
            if (D.achieved_pl !== void 0) {
              Y += D.achieved_pl, Y > W && (W = Y);
              const J = W - Y;
              J > k && (k = J);
            }
          const V = W > 0 ? k / W * 100 : 0, oe = T.length > 0 ? Math.sqrt(T.length) * _ / Math.sqrt(T.reduce((D, J) => D + Math.pow(J - _, 2), 0) / T.length) : 0, ne = u.map((D) => D.date).sort(), fe = ne.length > 0 ? ne[0] : "", se = ne.length > 0 ? ne[ne.length - 1] : "";
          t({
            totalTrades: f,
            winningTrades: p,
            losingTrades: d,
            winRate: h,
            profitFactor: R,
            averageWin: C,
            averageLoss: w,
            largestWin: S,
            largestLoss: L,
            totalPnl: y,
            maxDrawdown: k,
            maxDrawdownPercent: V,
            sharpeRatio: 0,
            // Would need daily returns to calculate
            sortinoRatio: 0,
            // Would need daily returns to calculate
            calmarRatio: 0,
            // Would need daily returns to calculate
            averageRMultiple: _,
            expectancy: N,
            sqn: oe,
            period: "all",
            startDate: fe,
            endDate: se
          });
        }, i.onerror = (u) => {
          console.error("Error getting performance metrics:", u), n(new Error("Failed to get performance metrics"));
        };
      });
    } catch (r) {
      throw console.error("Error in getPerformanceMetrics:", r), new Error("Failed to get performance metrics");
    }
  }
  /**
   * Filter trades based on criteria
   * @param filters The filter criteria
   * @returns A promise that resolves with filtered trades
   */
  async filterTrades(r) {
    try {
      const t = await this.initDB();
      return new Promise((n, s) => {
        const c = t.transaction([this.stores.trades, this.stores.fvg_details, this.stores.setups, this.stores.analysis], "readonly"), u = c.objectStore(this.stores.trades).getAll();
        u.onsuccess = async () => {
          let f = u.result;
          r.dateFrom && (f = f.filter((d) => d.date >= r.dateFrom)), r.dateTo && (f = f.filter((d) => d.date <= r.dateTo)), r.model_type && (f = f.filter((d) => d[K.MODEL_TYPE] === r.model_type)), r.session && (f = f.filter((d) => d[K.SESSION] === r.session)), r.direction && (f = f.filter((d) => d[K.DIRECTION] === r.direction)), r.win_loss && (f = f.filter((d) => d[K.WIN_LOSS] === r.win_loss)), r.market && (f = f.filter((d) => d[K.MARKET] === r.market)), r.min_r_multiple !== void 0 && (f = f.filter((d) => d[K.R_MULTIPLE] !== void 0 && d[K.R_MULTIPLE] >= r.min_r_multiple)), r.max_r_multiple !== void 0 && (f = f.filter((d) => d[K.R_MULTIPLE] !== void 0 && d[K.R_MULTIPLE] <= r.max_r_multiple)), r.min_pattern_quality !== void 0 && (f = f.filter((d) => d[K.PATTERN_QUALITY_RATING] !== void 0 && d[K.PATTERN_QUALITY_RATING] >= r.min_pattern_quality)), r.max_pattern_quality !== void 0 && (f = f.filter((d) => d[K.PATTERN_QUALITY_RATING] !== void 0 && d[K.PATTERN_QUALITY_RATING] <= r.max_pattern_quality));
          const p = [];
          for (const d of f) {
            const h = {
              trade: d
            }, m = c.objectStore(this.stores.fvg_details).index("trade_id").get(d.id);
            await new Promise((O) => {
              m.onsuccess = () => {
                m.result && (h.fvg_details = m.result), O();
              }, m.onerror = () => O();
            });
            const w = c.objectStore(this.stores.setups).index("trade_id").get(d.id);
            await new Promise((O) => {
              w.onsuccess = () => {
                w.result && (h.setup = w.result), O();
              }, w.onerror = () => O();
            });
            const A = c.objectStore(this.stores.analysis).index("trade_id").get(d.id);
            await new Promise((O) => {
              A.onsuccess = () => {
                A.result && (h.analysis = A.result), O();
              }, A.onerror = () => O();
            }), p.push(h);
          }
          n(p);
        }, u.onerror = (f) => {
          console.error("Error filtering trades:", f), s(new Error("Failed to filter trades"));
        };
      });
    } catch (t) {
      throw console.error("Error in filterTrades:", t), new Error("Failed to filter trades");
    }
  }
  /**
   * Get all trades (simplified version for backward compatibility)
   * @returns A promise that resolves with all trades
   */
  async getAllTrades() {
    try {
      return await this.filterTrades({});
    } catch (r) {
      return console.error("Error in getAllTrades:", r), [];
    }
  }
  /**
   * Delete a trade and all related data
   * @param id The ID of the trade to delete
   * @returns A promise that resolves when the trade is deleted
   */
  async deleteTrade(r) {
    try {
      const t = await this.initDB();
      return new Promise((n, s) => {
        const c = t.transaction([this.stores.trades, this.stores.fvg_details, this.stores.setups, this.stores.analysis], "readwrite");
        c.onerror = (w) => {
          console.error("Transaction error:", w), s(new Error("Failed to delete trade"));
        };
        const f = c.objectStore(this.stores.fvg_details).index("trade_id").openCursor(IDBKeyRange.only(r));
        f.onsuccess = (w) => {
          const S = w.target.result;
          S && (S.delete(), S.continue());
        };
        const h = c.objectStore(this.stores.setups).index("trade_id").openCursor(IDBKeyRange.only(r));
        h.onsuccess = (w) => {
          const S = w.target.result;
          S && (S.delete(), S.continue());
        };
        const m = c.objectStore(this.stores.analysis).index("trade_id").openCursor(IDBKeyRange.only(r));
        m.onsuccess = (w) => {
          const S = w.target.result;
          S && (S.delete(), S.continue());
        };
        const C = c.objectStore(this.stores.trades).delete(r);
        c.oncomplete = () => {
          n();
        }, C.onerror = (w) => {
          console.error("Error deleting trade:", w), s(new Error("Failed to delete trade"));
        };
      });
    } catch (t) {
      throw console.error("Error in deleteTrade:", t), new Error("Failed to delete trade");
    }
  }
  /**
   * Update a trade with all related data
   * @param id The trade ID to update
   * @param tradeData Updated trade data
   * @returns A promise that resolves when the trade is updated
   */
  async updateTradeWithDetails(r, t) {
    try {
      const n = await this.initDB();
      return new Promise((s, c) => {
        const i = n.transaction([this.stores.trades, this.stores.fvg_details, this.stores.setups, this.stores.analysis], "readwrite");
        i.onerror = (d) => {
          console.error("Transaction error:", d), c(new Error("Failed to update trade"));
        };
        const u = i.objectStore(this.stores.trades), f = {
          ...t.trade,
          id: r,
          updated_at: (/* @__PURE__ */ new Date()).toISOString()
        }, p = u.put(f);
        p.onsuccess = () => {
          if (t.fvg_details) {
            const d = i.objectStore(this.stores.fvg_details), h = {
              ...t.fvg_details,
              trade_id: r
            };
            d.put(h);
          }
          if (t.setup) {
            const d = i.objectStore(this.stores.setups), h = {
              ...t.setup,
              trade_id: r
            };
            d.put(h);
          }
          if (t.analysis) {
            const d = i.objectStore(this.stores.analysis), h = {
              ...t.analysis,
              trade_id: r
            };
            d.put(h);
          }
        }, i.oncomplete = () => {
          s();
        }, p.onerror = (d) => {
          console.error("Error updating trade:", d), c(new Error("Failed to update trade"));
        };
      });
    } catch (n) {
      throw console.error("Error in updateTradeWithDetails:", n), new Error("Failed to update trade");
    }
  }
}
const ft = new Pa(), zc = ft, Fc = ft;
export {
  Va as AppErrorBoundary,
  He as Badge,
  le as Button,
  hn as Card,
  tc as DashboardSection,
  oc as DashboardTemplate,
  rc as DataCard,
  Mr as EmptyState,
  Wa as EnhancedFormField,
  kn as ErrorBoundary,
  cc as F1Container,
  lc as F1Form,
  dc as F1FormField,
  ac as F1Header,
  Ua as FeatureErrorBoundary,
  Qa as FormField,
  Za as HierarchicalSessionSelector,
  Ie as Input,
  qa as LoadingCell,
  Co as LoadingPlaceholder,
  Ha as LoadingSpinner,
  M as MacroPeriodType,
  Xa as Modal,
  Gt as OrderSide,
  Kt as OrderStatus,
  Wt as OrderType,
  Te as SETUP_ELEMENTS,
  Re as Select,
  Ba as SelectDropdown,
  X as SessionType,
  ie as SessionUtils,
  nc as SetupBuilder,
  Ka as SortableTable,
  Aa as StatusIndicator,
  E as TRADE_COLUMN_IDS,
  Ya as TabPanel,
  Ja as Table,
  za as Tag,
  ut as ThemeContext,
  Ic as ThemeProvider,
  Qt as TimeInForce,
  Fa as TimePicker,
  ic as TradeAnalysis,
  Ut as TradeDirection,
  sc as TradeMetrics,
  Yt as TradeStatus,
  ec as TradeTable,
  ii as TradeTableFilters,
  ti as TradeTableRow,
  ct as UnifiedErrorBoundary,
  $a as VALID_TRADING_MODELS,
  j as baseColors,
  cr as borderRadius,
  ar as breakpoints,
  Oc as captureError,
  jc as createSelector,
  Ec as createStoreContext,
  q as darkModeColors,
  ka as darkTheme,
  Pc as debounce,
  pt as f1Theme,
  ir as fontFamilies,
  me as fontSizes,
  nr as fontWeights,
  Nc as formatCurrency,
  Rc as formatDate,
  kc as formatPercentage,
  Br as formatTime,
  Lc as generateId,
  Ys as getCompactTradeTableColumns,
  Ws as getPerformanceTradeTableColumns,
  Sc as getProfitLossColors,
  vc as getProfitLossSize,
  wc as getProfitLossVariant,
  Us as getTradeTableColumns,
  Dc as initMonitoring,
  ee as lightModeColors,
  Na as lightTheme,
  sr as lineHeights,
  Tc as persistState,
  bc as profitLossBaseStyles,
  Ta as profitLossColors,
  yc as profitLossLoadingStyles,
  ja as profitLossSizes,
  $c as setUser,
  lr as shadows,
  Ga as sortFunctions,
  re as spacing,
  Ac as startTransaction,
  Mc as throttle,
  Fc as tradeStorage,
  zc as tradeStorageService,
  dr as transitions,
  _c as truncateText,
  pc as useAsyncData,
  xc as useDataFormatting,
  hc as useDataSection,
  uc as useDebounce,
  fc as useErrorHandler,
  On as useFormField,
  Ca as useLoadingState,
  tt as useLocalStorage,
  gc as usePagination,
  mc as useProfitLossFormatting,
  Rs as useSessionSelection,
  Un as useSortableTable,
  Cc as useTheme,
  Dn as validationRules,
  pr as zIndex
};
