import { SessionSelection, SessionType, MacroPeriodType, TimeRange, SessionFilterOptions, TimeValidationResult } from '../types/tradingSessions';
export interface UseSessionSelectionOptions {
    /** Initial session selection */
    initialSelection?: SessionSelection;
    /** Whether to auto-detect current session */
    autoDetectCurrent?: boolean;
    /** Filter options for available sessions/macros */
    filterOptions?: SessionFilterOptions;
    /** Callback when selection changes */
    onSelectionChange?: (selection: SessionSelection) => void;
    /** Whether to validate time inputs */
    validateTimes?: boolean;
}
export interface UseSessionSelectionReturn {
    selection: SessionSelection;
    selectSession: (sessionType: SessionType) => void;
    selectMacro: (macroType: MacroPeriodType) => void;
    selectCustomRange: (timeRange: TimeRange) => void;
    clearSelection: () => void;
    validateTime: (time: string) => TimeValidationResult;
    isValidSelection: boolean;
    availableSessions: Array<{
        value: SessionType;
        label: string;
        group: string;
    }>;
    availableMacros: Array<{
        value: MacroPeriodType;
        label: string;
        group: string;
        parentSession: SessionType;
    }>;
    hierarchicalOptions: Array<{
        session: SessionType;
        sessionLabel: string;
        macros: Array<{
            value: MacroPeriodType;
            label: string;
        }>;
    }>;
    currentSession: SessionSelection | null;
    isCurrentSessionActive: boolean;
    getSessionDetails: (sessionType: SessionType) => any;
    getMacroDetails: (macroType: MacroPeriodType) => any;
    convertLegacySession: (legacySession: string) => SessionSelection | null;
}
/**
 * useSessionSelection Hook
 */
export declare const useSessionSelection: (options?: UseSessionSelectionOptions) => UseSessionSelectionReturn;
//# sourceMappingURL=useSessionSelection.d.ts.map