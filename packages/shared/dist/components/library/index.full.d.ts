/**
 * ADHD Trading Dashboard Component Library
 *
 * Comprehensive component library built from proven refactoring patterns.
 * Provides consistent, reusable components following F1 racing theme.
 *
 * ARCHITECTURE:
 * - Atomic Design principles
 * - F1 racing theme consistency
 * - TypeScript-first approach
 * - Storybook documentation ready
 * - Performance optimized
 */
export { F1Container } from './containers/F1Container';
export type { F1ContainerProps } from './containers/F1Container';
export { F1Header } from './headers/F1Header';
export type { F1HeaderProps } from './headers/F1Header';
export { F1Form } from './forms/F1Form';
export type { F1FormProps } from './forms/F1Form';
export { F1FormField } from './forms/F1FormField';
export type { F1FormFieldProps } from './forms/F1FormField';
export { useFormField } from '../../hooks/useFormField';
export type { UseFormFieldReturn, FormFieldConfig, ValidationRule } from '../../hooks/useFormField';
export declare const COMPONENT_LIBRARY_VERSION = "1.0.0";
export declare const COMPONENT_LIBRARY_NAME = "ADHD Trading Dashboard Components";
export declare const COMPONENT_LIBRARY_DESCRIPTION = "F1-themed component library for trading applications";
/**
 * Component Library Statistics
 */
export declare const LIBRARY_STATS: {
    totalComponents: number;
    categories: number;
    hooks: number;
    themes: number;
    patterns: number;
    coverage: string;
    performance: string;
    accessibility: string;
    browser_support: string;
    framework: string;
    typescript: string;
    storybook: string;
    testing: string;
};
//# sourceMappingURL=index.full.d.ts.map