/**
 * Application Routes
 *
 * This file defines the application routes using React Router.
 */

import React, { lazy, Suspense } from 'react';
import { Routes, Route, Navigate } from 'react-router-dom';
import MainLayout from '../layouts/MainLayout';
import LoadingScreen from '../components/molecules/LoadingScreen';

// Lazy-loaded feature components for code splitting
const Dashboard = lazy(() => import('../features/trading-dashboard/TradingDashboard'));
const DailyGuide = lazy(() => import('../features/daily-guide/components/DailyGuide'));
const TradeJournal = lazy(() => import('../features/trade-journal/TradeJournal'));
const TradeAnalysis = lazy(() => import('../features/trade-analysis/TradeAnalysis'));
const TradeForm = lazy(() => import('../features/trade-journal/TradeForm'));
const Settings = lazy(() => import('../features/settings/Settings'));
const NotFound = lazy(() => import('../components/NotFound'));

/**
 * AppRoutes Component
 *
 * Defines the application routes using React Router.
 */
const AppRoutes: React.FC = () => {
  return (
    <Suspense fallback={<LoadingScreen />}>
      <Routes>
        {/* Main layout routes */}
        <Route path="/" element={<MainLayout />}>
          <Route index element={<Dashboard />} />
          <Route path="daily-guide" element={<DailyGuide />} />
          <Route path="journal" element={<TradeJournal />} />
          <Route path="analysis" element={<TradeAnalysis />} />
          <Route path="trade/new" element={<TradeForm />} />
          <Route path="trade/:id" element={<TradeForm />} />
          <Route path="settings" element={<Settings />} />
          <Route path="*" element={<NotFound />} />
        </Route>

        {/* Redirect from legacy paths */}
        <Route path="/dashboard" element={<Navigate to="/" replace />} />
      </Routes>
    </Suspense>
  );
};

export default AppRoutes;
