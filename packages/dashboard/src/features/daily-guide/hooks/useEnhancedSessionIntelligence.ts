/**
 * Enhanced Session Intelligence Hook
 *
 * Advanced ICT session analysis using real trading data from spreadsheet import.
 * Provides sophisticated timing, model selection, and PD Array insights.
 */

import { useState, useEffect, useMemo } from 'react';
import {
  tradeStorageService,
  getCurrentDualTime,
  formatTimeInterval,
  getNextSessionInfo,
  getSessionStatus,
} from '@adhd-trading-dashboard/shared';
import type { CompleteTradeData } from '@adhd-trading-dashboard/shared';

export interface ICTSessionPerformance {
  sessionName: string;
  sessionType: 'Pre-Market' | 'NY Open' | 'Lunch Macro' | 'MOC';
  timeRange: string;
  performance: {
    totalTrades: number;
    winningTrades: number;
    winRate: number;
    avgRMultiple: number;
    totalPnL: number;
    avgRisk: number;
  };
  modelPreference: {
    rdCont: { trades: number; winRate: number; avgR: number };
    fvgRd: { trades: number; winRate: number; avgR: number };
    recommendation: 'RD-Cont' | 'FVG-RD' | 'Either';
  };
  optimalWindows: {
    start: string;
    end: string;
    description: string;
    winRate: number;
    trades: number;
  }[];
  qualityThreshold: number;
  recommendations: string[];
}

export interface CurrentSessionStatus {
  currentTime: string;
  currentTimeFormatted: string;
  activeSession: ICTSessionPerformance | null;
  nextSession: ICTSessionPerformance | null;
  timeToNext: number;
  timeToNextFormatted: string;
  isOptimalWindow: boolean;
  currentRecommendation: string;
  urgency: 'LOW' | 'MEDIUM' | 'HIGH';
}

export interface EnhancedSessionIntelligence {
  sessions: ICTSessionPerformance[];
  currentStatus: CurrentSessionStatus;
  weeklyInsights: {
    bestSession: string;
    bestModel: 'RD-Cont' | 'FVG-RD';
    avgQuality: number;
    qualityThreshold: number;
    recommendations: string[];
  };
}

/**
 * Parse session from trade data
 */
const parseSession = (trade: CompleteTradeData): string | null => {
  // First check the session field
  if (trade.trade.session) {
    return trade.trade.session;
  }

  // Fall back to entry time analysis
  const entryTime = trade.trade.entry_time;
  if (!entryTime) return null;

  const [hours, minutes] = entryTime.split(':').map(Number);
  const timeMinutes = hours * 60 + minutes;

  // Map time to ICT sessions
  if (timeMinutes >= 480 && timeMinutes < 570) return 'Pre-Market'; // 8:00-9:30
  if (timeMinutes >= 570 && timeMinutes < 660) return 'NY Open'; // 9:30-11:00
  if (timeMinutes >= 710 && timeMinutes < 810) return 'Lunch Macro'; // 11:50-13:30
  if (timeMinutes >= 915 && timeMinutes < 960) return 'MOC'; // 15:15-16:00

  return null;
};

/**
 * Calculate optimal windows for a session
 */
const calculateOptimalWindows = (sessionTrades: CompleteTradeData[], sessionType: string) => {
  const windows = {
    'Pre-Market': [
      { start: '08:00', end: '08:30', description: 'Early Pre-Market Setup' },
      { start: '08:30', end: '09:00', description: 'Prime Pre-Market Window' },
      { start: '09:00', end: '09:30', description: 'Market Prep Phase' },
    ],
    'NY Open': [
      { start: '09:30', end: '09:45', description: 'Market Open Volatility' },
      { start: '09:45', end: '10:15', description: 'OPTIMAL WINDOW' },
      { start: '10:15', end: '10:45', description: 'Secondary Opportunity' },
      { start: '10:45', end: '11:00', description: 'Session Wind-down' },
    ],
    'Lunch Macro': [
      { start: '11:50', end: '12:10', description: 'PRIMARY WINDOW' },
      { start: '12:10', end: '12:30', description: 'Continuation Phase' },
      { start: '12:30', end: '13:00', description: 'Midday Consolidation' },
      { start: '13:00', end: '13:30', description: 'Afternoon Transition' },
    ],
    MOC: [
      { start: '15:15', end: '15:45', description: 'Pre-Close Setup' },
      { start: '15:45', end: '16:00', description: 'Final Momentum' },
    ],
  };

  const sessionWindows = windows[sessionType as keyof typeof windows] || [];

  return sessionWindows.map((window) => {
    const windowTrades = sessionTrades.filter((trade) => {
      const entryTime = trade.trade.entry_time;
      if (!entryTime) return false;

      const [hours, minutes] = entryTime.split(':').map(Number);
      const tradeMinutes = hours * 60 + minutes;
      const [startHours, startMins] = window.start.split(':').map(Number);
      const [endHours, endMins] = window.end.split(':').map(Number);
      const startMinutes = startHours * 60 + startMins;
      const endMinutes = endHours * 60 + endMins;

      return tradeMinutes >= startMinutes && tradeMinutes <= endMinutes;
    });

    const wins = windowTrades.filter((t) => t.trade.win_loss === 'Win').length;
    const winRate = windowTrades.length > 0 ? (wins / windowTrades.length) * 100 : 0;

    return {
      ...window,
      winRate,
      trades: windowTrades.length,
    };
  });
};

/**
 * Analyze session performance
 */
const analyzeSessionPerformance = (
  sessionName: string,
  sessionTrades: CompleteTradeData[]
): ICTSessionPerformance => {
  const sessionType = sessionName as ICTSessionPerformance['sessionType'];

  // Basic performance metrics
  const totalTrades = sessionTrades.length;
  const winningTrades = sessionTrades.filter((t) => t.trade.win_loss === 'Win').length;
  const winRate = totalTrades > 0 ? (winningTrades / totalTrades) * 100 : 0;

  const rMultiples = sessionTrades
    .map((t) => t.trade.r_multiple)
    .filter((r): r is number => r !== undefined && r !== null);
  const avgRMultiple =
    rMultiples.length > 0 ? rMultiples.reduce((sum, r) => sum + r, 0) / rMultiples.length : 0;

  const totalPnL = sessionTrades
    .map((t) => t.trade.achieved_pl || 0)
    .reduce((sum, pl) => sum + pl, 0);

  const riskPoints = sessionTrades.map((t) => t.trade.risk_points || 0).filter((r) => r > 0);
  const avgRisk =
    riskPoints.length > 0 ? riskPoints.reduce((sum, r) => sum + r, 0) / riskPoints.length : 0;

  // Model preference analysis
  const rdContTrades = sessionTrades.filter((t) => t.trade.model_type === 'RD-Cont');
  const fvgRdTrades = sessionTrades.filter((t) => t.trade.model_type === 'FVG-RD');

  const rdContWins = rdContTrades.filter((t) => t.trade.win_loss === 'Win').length;
  const fvgRdWins = fvgRdTrades.filter((t) => t.trade.win_loss === 'Win').length;

  const rdContWinRate = rdContTrades.length > 0 ? (rdContWins / rdContTrades.length) * 100 : 0;
  const fvgRdWinRate = fvgRdTrades.length > 0 ? (fvgRdWins / fvgRdTrades.length) * 100 : 0;

  const rdContRs = rdContTrades
    .map((t) => t.trade.r_multiple)
    .filter((r): r is number => r !== undefined);
  const fvgRdRs = fvgRdTrades
    .map((t) => t.trade.r_multiple)
    .filter((r): r is number => r !== undefined);

  const rdContAvgR =
    rdContRs.length > 0 ? rdContRs.reduce((sum, r) => sum + r, 0) / rdContRs.length : 0;
  const fvgRdAvgR =
    fvgRdRs.length > 0 ? fvgRdRs.reduce((sum, r) => sum + r, 0) / fvgRdRs.length : 0;

  // Determine model recommendation
  let modelRecommendation: 'RD-Cont' | 'FVG-RD' | 'Either' = 'Either';
  if (rdContTrades.length >= 2 && fvgRdTrades.length >= 2) {
    if (rdContWinRate > fvgRdWinRate + 10) modelRecommendation = 'RD-Cont';
    else if (fvgRdWinRate > rdContWinRate + 10) modelRecommendation = 'FVG-RD';
  } else if (rdContTrades.length >= 3) {
    modelRecommendation = 'RD-Cont';
  } else if (fvgRdTrades.length >= 3) {
    modelRecommendation = 'FVG-RD';
  }

  // Calculate quality threshold
  const qualityRatings = sessionTrades
    .map((t) => t.trade.pattern_quality_rating)
    .filter((q): q is number => q !== undefined && q !== null);
  const winningQualities = sessionTrades
    .filter((t) => t.trade.win_loss === 'Win')
    .map((t) => t.trade.pattern_quality_rating)
    .filter((q): q is number => q !== undefined && q !== null);

  const avgWinningQuality =
    winningQualities.length > 0
      ? winningQualities.reduce((sum, q) => sum + q, 0) / winningQualities.length
      : 3.5;
  const qualityThreshold = Math.max(3.0, avgWinningQuality - 0.2);

  // Generate recommendations
  const recommendations: string[] = [];
  if (modelRecommendation !== 'Either') {
    recommendations.push(
      `Focus on ${modelRecommendation} setups (${
        modelRecommendation === 'RD-Cont' ? rdContWinRate.toFixed(0) : fvgRdWinRate.toFixed(0)
      }% win rate)`
    );
  }
  if (qualityThreshold > 3.0) {
    recommendations.push(
      `Maintain pattern quality >${qualityThreshold.toFixed(1)} (your success threshold)`
    );
  }
  if (avgRisk > 0) {
    recommendations.push(`Target ${avgRisk.toFixed(0)} point risk (your historical average)`);
  }

  // Time range mapping
  const timeRanges = {
    'Pre-Market': '08:00-09:30',
    'NY Open': '09:30-11:00',
    'Lunch Macro': '11:50-13:30',
    MOC: '15:15-16:00',
  };

  return {
    sessionName,
    sessionType,
    timeRange: timeRanges[sessionType] || '',
    performance: {
      totalTrades,
      winningTrades,
      winRate,
      avgRMultiple,
      totalPnL,
      avgRisk,
    },
    modelPreference: {
      rdCont: { trades: rdContTrades.length, winRate: rdContWinRate, avgR: rdContAvgR },
      fvgRd: { trades: fvgRdTrades.length, winRate: fvgRdWinRate, avgR: fvgRdAvgR },
      recommendation: modelRecommendation,
    },
    optimalWindows: calculateOptimalWindows(sessionTrades, sessionType),
    qualityThreshold,
    recommendations,
  };
};

/**
 * Enhanced Session Intelligence Hook
 */
export const useEnhancedSessionIntelligence = () => {
  const [trades, setTrades] = useState<CompleteTradeData[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Fetch trade data
  useEffect(() => {
    const fetchTrades = async () => {
      try {
        setIsLoading(true);
        setError(null);
        const tradeData = await tradeStorageService.getAllTrades();
        setTrades(tradeData);
      } catch (err) {
        console.error('Error fetching trades for enhanced session intelligence:', err);
        setError('Failed to load trade data');
      } finally {
        setIsLoading(false);
      }
    };

    fetchTrades();
  }, []);

  // Calculate enhanced session intelligence
  const intelligence: EnhancedSessionIntelligence = useMemo(() => {
    if (trades.length === 0) {
      const dualTime = getCurrentDualTime();
      return {
        sessions: [],
        currentStatus: {
          currentTime: dualTime.nyTime,
          currentTimeFormatted: dualTime.formatted,
          activeSession: null,
          nextSession: null,
          timeToNext: 0,
          timeToNextFormatted: 'N/A',
          isOptimalWindow: false,
          currentRecommendation: 'No trading data available',
          urgency: 'LOW',
        },
        weeklyInsights: {
          bestSession: 'N/A',
          bestModel: 'RD-Cont',
          avgQuality: 0,
          qualityThreshold: 3.5,
          recommendations: ['Import trading data to begin analysis'],
        },
      };
    }

    // Group trades by session
    const sessionGroups = trades.reduce((groups, trade) => {
      const session = parseSession(trade);
      if (session) {
        if (!groups[session]) groups[session] = [];
        groups[session].push(trade);
      }
      return groups;
    }, {} as Record<string, CompleteTradeData[]>);

    // Analyze each session
    const sessions = Object.entries(sessionGroups).map(([sessionName, sessionTrades]) =>
      analyzeSessionPerformance(sessionName, sessionTrades)
    );

    // Calculate current status
    const dualTime = getCurrentDualTime();
    const currentTime = dualTime.nyTime;
    const currentTimeFormatted = dualTime.formatted;
    const now = new Date();
    const currentMinutes = now.getHours() * 60 + now.getMinutes();

    // Find active session
    const activeSession =
      sessions.find((session) => {
        if (!session.timeRange) return false;
        const [start, end] = session.timeRange.split('-');
        if (!start || !end) return false;
        const [startHours, startMins] = start.split(':').map(Number);
        const [endHours, endMins] = end.split(':').map(Number);
        const startMinutes = startHours * 60 + startMins;
        const endMinutes = endHours * 60 + endMins;
        return currentMinutes >= startMinutes && currentMinutes <= endMinutes;
      }) || null;

    // Find next session
    const nextSession =
      sessions.find((session) => {
        if (!session.timeRange) return false;
        const [start] = session.timeRange.split('-');
        if (!start) return false;
        const [startHours, startMins] = start.split(':').map(Number);
        const startMinutes = startHours * 60 + startMins;
        return startMinutes > currentMinutes;
      }) || null;

    const timeToNext =
      nextSession && nextSession.timeRange
        ? (() => {
            const [start] = nextSession.timeRange.split('-');
            if (!start) return 0;
            const [startHours, startMins] = start.split(':').map(Number);
            const startMinutes = startHours * 60 + startMins;
            return startMinutes - currentMinutes;
          })()
        : 0;

    const timeToNextFormatted = timeToNext > 0 ? formatTimeInterval(timeToNext).formatted : 'N/A';

    // Check if in optimal window
    const isOptimalWindow = activeSession
      ? activeSession.optimalWindows.some((window) => {
          const [startHours, startMins] = window.start.split(':').map(Number);
          const [endHours, endMins] = window.end.split(':').map(Number);
          const startMinutes = startHours * 60 + startMins;
          const endMinutes = endHours * 60 + endMins;
          return currentMinutes >= startMinutes && currentMinutes <= endMinutes;
        })
      : false;

    // Generate current recommendation
    let currentRecommendation = '';
    let urgency: 'LOW' | 'MEDIUM' | 'HIGH' = 'LOW';

    if (activeSession && isOptimalWindow) {
      currentRecommendation = `OPTIMAL WINDOW ACTIVE - ${
        activeSession.sessionName
      } (${activeSession.performance.winRate.toFixed(0)}% win rate)`;
      urgency = 'HIGH';
    } else if (activeSession) {
      currentRecommendation = `${activeSession.sessionName} active - ${activeSession.modelPreference.recommendation} focus`;
      urgency = 'MEDIUM';
    } else if (nextSession && timeToNext <= 30) {
      currentRecommendation = `${nextSession.sessionName} starting in ${timeToNext} minutes`;
      urgency = 'MEDIUM';
    } else {
      currentRecommendation = 'No active trading session - Monitor for setups';
      urgency = 'LOW';
    }

    // Calculate weekly insights
    const bestSession = sessions.reduce(
      (best, current) => (current.performance.winRate > best.performance.winRate ? current : best),
      sessions[0] || { sessionName: 'N/A', performance: { winRate: 0 } }
    ).sessionName;

    const allRdContTrades = trades.filter((t) => t.trade.model_type === 'RD-Cont');
    const allFvgRdTrades = trades.filter((t) => t.trade.model_type === 'FVG-RD');
    const rdContWinRate =
      allRdContTrades.length > 0
        ? (allRdContTrades.filter((t) => t.trade.win_loss === 'Win').length /
            allRdContTrades.length) *
          100
        : 0;
    const fvgRdWinRate =
      allFvgRdTrades.length > 0
        ? (allFvgRdTrades.filter((t) => t.trade.win_loss === 'Win').length /
            allFvgRdTrades.length) *
          100
        : 0;

    const bestModel: 'RD-Cont' | 'FVG-RD' = rdContWinRate >= fvgRdWinRate ? 'RD-Cont' : 'FVG-RD';

    const allQualities = trades
      .map((t) => t.trade.pattern_quality_rating)
      .filter((q): q is number => q !== undefined && q !== null);
    const avgQuality =
      allQualities.length > 0
        ? allQualities.reduce((sum, q) => sum + q, 0) / allQualities.length
        : 0;

    const winningQualities = trades
      .filter((t) => t.trade.win_loss === 'Win')
      .map((t) => t.trade.pattern_quality_rating)
      .filter((q): q is number => q !== undefined && q !== null);
    const avgWinningQuality =
      winningQualities.length > 0
        ? winningQualities.reduce((sum, q) => sum + q, 0) / winningQualities.length
        : 3.5;
    const qualityThreshold = Math.max(3.0, avgWinningQuality - 0.2);

    const weeklyRecommendations = [
      `Focus on ${bestSession} session (${
        sessions.find((s) => s.sessionName === bestSession)?.performance.winRate.toFixed(0) || 0
      }% win rate)`,
      `Prioritize ${bestModel} setups (${
        bestModel === 'RD-Cont' ? rdContWinRate.toFixed(0) : fvgRdWinRate.toFixed(0)
      }% success rate)`,
      `Maintain pattern quality >${qualityThreshold.toFixed(1)} (your success threshold)`,
    ];

    return {
      sessions,
      currentStatus: {
        currentTime,
        currentTimeFormatted,
        activeSession,
        nextSession,
        timeToNext,
        timeToNextFormatted,
        isOptimalWindow,
        currentRecommendation,
        urgency,
      },
      weeklyInsights: {
        bestSession,
        bestModel,
        avgQuality,
        qualityThreshold,
        recommendations: weeklyRecommendations,
      },
    };
  }, [trades]);

  return {
    intelligence,
    isLoading,
    error,
    refresh: () => {
      setTrades([]);
    },
  };
};
