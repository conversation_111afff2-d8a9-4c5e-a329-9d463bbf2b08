/**
 * Enhanced Setup Intelligence Hook
 * 
 * Analyzes primary_setup, secondary_setup, and liquidity_taken combinations
 * from actual trade data to provide sophisticated setup recommendations.
 */

import { useState, useEffect, useMemo } from 'react';
import { tradeStorageService } from '@adhd-trading-dashboard/shared';
import type { CompleteTradeData } from '@adhd-trading-dashboard/shared';

export interface SetupPerformance {
  setupName: string;
  totalTrades: number;
  winRate: number;
  avgRMultiple: number;
  avgQuality: number;
  bestSession: string;
  successRate: number;
}

export interface SetupCombination {
  primary: string;
  secondary: string;
  liquidity: string;
  performance: {
    totalTrades: number;
    winRate: number;
    avgRMultiple: number;
    avgQuality: number;
    totalPnL: number;
  };
  sessions: {
    sessionName: string;
    winRate: number;
    trades: number;
  }[];
  recommendation: string;
  priority: 'HIGH' | 'MEDIUM' | 'LOW';
}

export interface LiquidityIntelligence {
  liquidityTarget: string;
  performance: {
    totalTrades: number;
    winRate: number;
    avgRMultiple: number;
    successRate: number;
  };
  bestModels: string[];
  bestSessions: string[];
  recommendation: string;
}

export interface SetupIntelligenceData {
  topPrimarySetups: SetupPerformance[];
  topSecondarySetups: SetupPerformance[];
  bestCombinations: SetupCombination[];
  liquidityIntelligence: LiquidityIntelligence[];
  currentRecommendations: {
    primarySetup: string;
    secondarySetup: string;
    liquidityTarget: string;
    reasoning: string;
    expectedWinRate: number;
    expectedRMultiple: number;
  };
}

/**
 * Analyze primary setup performance
 */
const analyzePrimarySetups = (trades: CompleteTradeData[]): SetupPerformance[] => {
  const setupMap = new Map<string, CompleteTradeData[]>();
  
  trades.forEach(trade => {
    const primarySetup = trade.setup?.primary_setup;
    if (primarySetup) {
      if (!setupMap.has(primarySetup)) {
        setupMap.set(primarySetup, []);
      }
      setupMap.get(primarySetup)!.push(trade);
    }
  });
  
  const setupPerformances: SetupPerformance[] = [];
  
  setupMap.forEach((setupTrades, setupName) => {
    const totalTrades = setupTrades.length;
    const wins = setupTrades.filter(t => t.trade.win_loss === 'Win').length;
    const winRate = totalTrades > 0 ? (wins / totalTrades) * 100 : 0;
    
    const rMultiples = setupTrades
      .map(t => t.trade.r_multiple)
      .filter((r): r is number => r !== undefined && r !== null);
    const avgRMultiple = rMultiples.length > 0 ? 
      rMultiples.reduce((sum, r) => sum + r, 0) / rMultiples.length : 0;
    
    const qualities = setupTrades
      .map(t => t.trade.pattern_quality_rating)
      .filter((q): q is number => q !== undefined && q !== null);
    const avgQuality = qualities.length > 0 ? 
      qualities.reduce((sum, q) => sum + q, 0) / qualities.length : 0;
    
    // Find best session for this setup
    const sessionMap = new Map<string, { wins: number; total: number }>();
    setupTrades.forEach(trade => {
      const session = trade.trade.session || 'Unknown';
      if (!sessionMap.has(session)) {
        sessionMap.set(session, { wins: 0, total: 0 });
      }
      const sessionData = sessionMap.get(session)!;
      sessionData.total++;
      if (trade.trade.win_loss === 'Win') {
        sessionData.wins++;
      }
    });
    
    let bestSession = 'Unknown';
    let bestSessionWinRate = 0;
    sessionMap.forEach((data, session) => {
      const sessionWinRate = data.total > 0 ? (data.wins / data.total) * 100 : 0;
      if (sessionWinRate > bestSessionWinRate && data.total >= 2) {
        bestSessionWinRate = sessionWinRate;
        bestSession = session;
      }
    });
    
    setupPerformances.push({
      setupName,
      totalTrades,
      winRate,
      avgRMultiple,
      avgQuality,
      bestSession,
      successRate: winRate
    });
  });
  
  return setupPerformances
    .filter(s => s.totalTrades >= 2)
    .sort((a, b) => b.winRate - a.winRate)
    .slice(0, 5);
};

/**
 * Analyze setup combinations (primary + secondary + liquidity)
 */
const analyzeSetupCombinations = (trades: CompleteTradeData[]): SetupCombination[] => {
  const combinationMap = new Map<string, CompleteTradeData[]>();
  
  trades.forEach(trade => {
    const primary = trade.setup?.primary_setup || '';
    const secondary = trade.setup?.secondary_setup || '';
    const liquidity = trade.setup?.liquidity_taken || '';
    
    if (primary && secondary && liquidity) {
      const key = `${primary}|${secondary}|${liquidity}`;
      if (!combinationMap.has(key)) {
        combinationMap.set(key, []);
      }
      combinationMap.get(key)!.push(trade);
    }
  });
  
  const combinations: SetupCombination[] = [];
  
  combinationMap.forEach((comboTrades, key) => {
    const [primary, secondary, liquidity] = key.split('|');
    const totalTrades = comboTrades.length;
    
    if (totalTrades < 2) return; // Skip combinations with insufficient data
    
    const wins = comboTrades.filter(t => t.trade.win_loss === 'Win').length;
    const winRate = (wins / totalTrades) * 100;
    
    const rMultiples = comboTrades
      .map(t => t.trade.r_multiple)
      .filter((r): r is number => r !== undefined && r !== null);
    const avgRMultiple = rMultiples.length > 0 ? 
      rMultiples.reduce((sum, r) => sum + r, 0) / rMultiples.length : 0;
    
    const qualities = comboTrades
      .map(t => t.trade.pattern_quality_rating)
      .filter((q): q is number => q !== undefined && q !== null);
    const avgQuality = qualities.length > 0 ? 
      qualities.reduce((sum, q) => sum + q, 0) / qualities.length : 0;
    
    const totalPnL = comboTrades
      .map(t => t.trade.achieved_pl || 0)
      .reduce((sum, pl) => sum + pl, 0);
    
    // Analyze session performance for this combination
    const sessionMap = new Map<string, { wins: number; total: number }>();
    comboTrades.forEach(trade => {
      const session = trade.trade.session || 'Unknown';
      if (!sessionMap.has(session)) {
        sessionMap.set(session, { wins: 0, total: 0 });
      }
      const sessionData = sessionMap.get(session)!;
      sessionData.total++;
      if (trade.trade.win_loss === 'Win') {
        sessionData.wins++;
      }
    });
    
    const sessions = Array.from(sessionMap.entries()).map(([sessionName, data]) => ({
      sessionName,
      winRate: data.total > 0 ? (data.wins / data.total) * 100 : 0,
      trades: data.total
    }));
    
    // Generate recommendation
    let recommendation = '';
    let priority: SetupCombination['priority'] = 'LOW';
    
    if (winRate >= 80 && totalTrades >= 3) {
      recommendation = `PRIORITIZE - Exceptional combination (${winRate.toFixed(0)}% win rate)`;
      priority = 'HIGH';
    } else if (winRate >= 70 && avgRMultiple >= 1.5) {
      recommendation = `EXECUTE WITH CONFIDENCE - Strong performance (${winRate.toFixed(0)}% win rate, ${avgRMultiple.toFixed(1)}R avg)`;
      priority = 'HIGH';
    } else if (winRate >= 60) {
      recommendation = `SELECTIVE USE - Good combination (${winRate.toFixed(0)}% win rate)`;
      priority = 'MEDIUM';
    } else {
      recommendation = `AVOID OR IMPROVE - Underperforming combination (${winRate.toFixed(0)}% win rate)`;
      priority = 'LOW';
    }
    
    combinations.push({
      primary,
      secondary,
      liquidity,
      performance: {
        totalTrades,
        winRate,
        avgRMultiple,
        avgQuality,
        totalPnL
      },
      sessions,
      recommendation,
      priority
    });
  });
  
  return combinations
    .sort((a, b) => b.performance.winRate - a.performance.winRate)
    .slice(0, 10);
};

/**
 * Analyze liquidity targeting intelligence
 */
const analyzeLiquidityIntelligence = (trades: CompleteTradeData[]): LiquidityIntelligence[] => {
  const liquidityMap = new Map<string, CompleteTradeData[]>();
  
  trades.forEach(trade => {
    const liquidity = trade.setup?.liquidity_taken;
    if (liquidity) {
      if (!liquidityMap.has(liquidity)) {
        liquidityMap.set(liquidity, []);
      }
      liquidityMap.get(liquidity)!.push(trade);
    }
  });
  
  const liquidityIntelligence: LiquidityIntelligence[] = [];
  
  liquidityMap.forEach((liquidityTrades, liquidityTarget) => {
    const totalTrades = liquidityTrades.length;
    
    if (totalTrades < 2) return;
    
    const wins = liquidityTrades.filter(t => t.trade.win_loss === 'Win').length;
    const winRate = (wins / totalTrades) * 100;
    
    const rMultiples = liquidityTrades
      .map(t => t.trade.r_multiple)
      .filter((r): r is number => r !== undefined && r !== null);
    const avgRMultiple = rMultiples.length > 0 ? 
      rMultiples.reduce((sum, r) => sum + r, 0) / rMultiples.length : 0;
    
    // Find best models for this liquidity target
    const modelMap = new Map<string, { wins: number; total: number }>();
    liquidityTrades.forEach(trade => {
      const model = trade.trade.model_type;
      if (!modelMap.has(model)) {
        modelMap.set(model, { wins: 0, total: 0 });
      }
      const modelData = modelMap.get(model)!;
      modelData.total++;
      if (trade.trade.win_loss === 'Win') {
        modelData.wins++;
      }
    });
    
    const bestModels = Array.from(modelMap.entries())
      .filter(([_, data]) => data.total >= 2)
      .sort((a, b) => (b[1].wins / b[1].total) - (a[1].wins / a[1].total))
      .slice(0, 2)
      .map(([model, _]) => model);
    
    // Find best sessions for this liquidity target
    const sessionMap = new Map<string, { wins: number; total: number }>();
    liquidityTrades.forEach(trade => {
      const session = trade.trade.session || 'Unknown';
      if (!sessionMap.has(session)) {
        sessionMap.set(session, { wins: 0, total: 0 });
      }
      const sessionData = sessionMap.get(session)!;
      sessionData.total++;
      if (trade.trade.win_loss === 'Win') {
        sessionData.wins++;
      }
    });
    
    const bestSessions = Array.from(sessionMap.entries())
      .filter(([_, data]) => data.total >= 2)
      .sort((a, b) => (b[1].wins / b[1].total) - (a[1].wins / a[1].total))
      .slice(0, 2)
      .map(([session, _]) => session);
    
    // Generate recommendation
    let recommendation = '';
    if (winRate >= 75) {
      recommendation = `PRIORITIZE ${liquidityTarget} - Excellent success rate (${winRate.toFixed(0)}%)`;
    } else if (winRate >= 60) {
      recommendation = `TARGET ${liquidityTarget} - Good performance (${winRate.toFixed(0)}%)`;
    } else {
      recommendation = `SELECTIVE USE ${liquidityTarget} - Average performance (${winRate.toFixed(0)}%)`;
    }
    
    liquidityIntelligence.push({
      liquidityTarget,
      performance: {
        totalTrades,
        winRate,
        avgRMultiple,
        successRate: winRate
      },
      bestModels,
      bestSessions,
      recommendation
    });
  });
  
  return liquidityIntelligence
    .sort((a, b) => b.performance.winRate - a.performance.winRate)
    .slice(0, 5);
};

/**
 * Enhanced Setup Intelligence Hook
 */
export const useEnhancedSetupIntelligence = () => {
  const [trades, setTrades] = useState<CompleteTradeData[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Fetch trade data
  useEffect(() => {
    const fetchTrades = async () => {
      try {
        setIsLoading(true);
        setError(null);
        const tradeData = await tradeStorageService.getAllTrades();
        setTrades(tradeData);
      } catch (err) {
        console.error('Error fetching trades for setup intelligence:', err);
        setError('Failed to load trade data');
      } finally {
        setIsLoading(false);
      }
    };

    fetchTrades();
  }, []);

  // Generate setup intelligence
  const setupIntelligence: SetupIntelligenceData = useMemo(() => {
    if (trades.length === 0) {
      return {
        topPrimarySetups: [],
        topSecondarySetups: [],
        bestCombinations: [],
        liquidityIntelligence: [],
        currentRecommendations: {
          primarySetup: 'Insufficient data',
          secondarySetup: 'Insufficient data',
          liquidityTarget: 'Insufficient data',
          reasoning: 'Import more trade data to generate recommendations',
          expectedWinRate: 50,
          expectedRMultiple: 1.0
        }
      };
    }

    const topPrimarySetups = analyzePrimarySetups(trades);
    const topSecondarySetups = analyzePrimarySetups(trades); // Reuse for secondary
    const bestCombinations = analyzeSetupCombinations(trades);
    const liquidityIntelligence = analyzeLiquidityIntelligence(trades);
    
    // Generate current recommendations based on best performing combinations
    const bestCombo = bestCombinations[0];
    const currentRecommendations = bestCombo ? {
      primarySetup: bestCombo.primary,
      secondarySetup: bestCombo.secondary,
      liquidityTarget: bestCombo.liquidity,
      reasoning: `Best performing combination: ${bestCombo.performance.winRate.toFixed(0)}% win rate with ${bestCombo.performance.avgRMultiple.toFixed(1)}R average`,
      expectedWinRate: bestCombo.performance.winRate,
      expectedRMultiple: bestCombo.performance.avgRMultiple
    } : {
      primarySetup: topPrimarySetups[0]?.setupName || 'No data',
      secondarySetup: 'No data',
      liquidityTarget: liquidityIntelligence[0]?.liquidityTarget || 'No data',
      reasoning: 'Based on individual setup performance',
      expectedWinRate: topPrimarySetups[0]?.winRate || 50,
      expectedRMultiple: topPrimarySetups[0]?.avgRMultiple || 1.0
    };

    return {
      topPrimarySetups,
      topSecondarySetups,
      bestCombinations,
      liquidityIntelligence,
      currentRecommendations
    };
  }, [trades]);

  return {
    setupIntelligence,
    isLoading,
    error,
    refresh: () => {
      setTrades([]);
    }
  };
};
