/**
 * Pattern Quality Scoring System Hook
 * 
 * Real-time quality assessment (1-5 scale) replacing subjective pattern ratings with:
 * - PD Array confluence scoring
 * - FVG characteristics assessment
 * - RD strength evaluation
 * - Confirmation signal integration
 */

import { useState, useEffect, useMemo } from 'react';
import { tradeStorageService } from '@adhd-trading-dashboard/shared';
import type { CompleteTradeData } from '@adhd-trading-dashboard/shared';
import { PDArrayElement } from './usePDArrayAnalytics';

export interface PatternQualityScore {
  totalScore: number; // 1-5 scale
  maxScore: number;
  breakdown: {
    pdArrayConfluence: number;
    fvgCharacteristics: number;
    rdStrength: number;
    confirmationSignals: number;
    volumeConfirmation: number;
  };
  rating: 'POOR' | 'FAIR' | 'GOOD' | 'EXCELLENT' | 'EXCEPTIONAL';
  recommendation: string;
  expectedWinProbability: number;
}

export interface LivePatternAnalysis {
  currentScore: PatternQualityScore;
  historicalAccuracy: number; // How accurate our scoring has been
  scoreDistribution: {
    score: number;
    count: number;
    winRate: number;
    avgRMultiple: number;
  }[];
}

/**
 * Extract PD Array elements from trade data
 */
const extractPDArrayElements = (trade: CompleteTradeData): PDArrayElement[] => {
  const elements: PDArrayElement[] = [];
  const setupInfo = trade.trade.model_type?.toLowerCase() || '';
  const notes = trade.trade.notes?.toLowerCase() || '';
  const setup = trade.trade.setup?.toLowerCase() || '';
  const combined = `${setupInfo} ${notes} ${setup}`;
  
  // Check for FVG (Fair Value Gaps)
  if (combined.includes('fvg') || combined.includes('fair value gap') || combined.includes('imbalance')) {
    elements.push('FVG');
  }
  
  // Check for NWOG (New Week Opening Gap)
  if (combined.includes('nwog') || combined.includes('new week opening') || combined.includes('weekly gap')) {
    elements.push('NWOG');
  }
  
  // Check for NDOG (New Day Opening Gap)
  if (combined.includes('ndog') || combined.includes('new day opening') || combined.includes('daily gap')) {
    elements.push('NDOG');
  }
  
  // Check for Liquidity
  if (combined.includes('liquidity') || combined.includes('sweep') || combined.includes('raid') || combined.includes('hunt')) {
    elements.push('Liquidity');
  }
  
  return elements;
};

/**
 * Calculate PD Array confluence score (0-2.0 points)
 */
const calculatePDArrayConfluence = (elements: PDArrayElement[]): number => {
  const elementCount = elements.length;
  
  if (elementCount === 0) return 0;
  if (elementCount === 1) return 0.5;
  if (elementCount === 2) return 1.2;
  if (elementCount === 3) return 1.7;
  return 2.0; // 4+ elements
};

/**
 * Assess FVG characteristics (0-1.5 points)
 */
const assessFVGCharacteristics = (trade: CompleteTradeData): number => {
  const notes = trade.trade.notes?.toLowerCase() || '';
  const setup = trade.trade.setup?.toLowerCase() || '';
  const combined = `${notes} ${setup}`;
  
  if (!combined.includes('fvg') && !combined.includes('fair value gap') && !combined.includes('imbalance')) {
    return 0;
  }
  
  let score = 0.5; // Base score for having FVG
  
  // Size analysis - look for keywords indicating optimal size
  if (combined.includes('clean') || combined.includes('clear') || combined.includes('perfect')) {
    score += 0.3;
  }
  
  // HTF location bonus
  if (combined.includes('htf') || combined.includes('higher timeframe') || combined.includes('daily') || combined.includes('4h')) {
    score += 0.3;
  }
  
  // Freshness factor
  if (combined.includes('fresh') || combined.includes('new') || combined.includes('untested')) {
    score += 0.2;
  }
  
  // Age penalty
  if (combined.includes('old') || combined.includes('tested') || combined.includes('multiple')) {
    score -= 0.2;
  }
  
  return Math.min(Math.max(score, 0), 1.5);
};

/**
 * Evaluate RD strength (0-1.0 points)
 */
const evaluateRDStrength = (trade: CompleteTradeData): number => {
  const notes = trade.trade.notes?.toLowerCase() || '';
  const setup = trade.trade.setup?.toLowerCase() || '';
  const rdType = trade.trade.rd_type?.toLowerCase() || '';
  const combined = `${notes} ${setup} ${rdType}`;
  
  let score = 0;
  
  // Base RD presence
  if (combined.includes('rd') || combined.includes('reaction') || combined.includes('displacement')) {
    score += 0.3;
  }
  
  // Displacement speed indicators
  if (combined.includes('fast') || combined.includes('strong') || combined.includes('aggressive') || combined.includes('sharp')) {
    score += 0.3;
  }
  
  // Volume confirmation
  if (combined.includes('volume') || combined.includes('high vol') || combined.includes('heavy')) {
    score += 0.2;
  }
  
  // Clear structure
  if (combined.includes('clean') || combined.includes('clear') || combined.includes('obvious')) {
    score += 0.2;
  }
  
  return Math.min(score, 1.0);
};

/**
 * Assess confirmation signals (0-1.0 points)
 */
const assessConfirmationSignals = (trade: CompleteTradeData): number => {
  const notes = trade.trade.notes?.toLowerCase() || '';
  const setup = trade.trade.setup?.toLowerCase() || '';
  const combined = `${notes} ${setup}`;
  
  let score = 0;
  
  // Entry validation patterns
  if (combined.includes('doji') || combined.includes('hammer') || combined.includes('engulfing') || combined.includes('pin bar')) {
    score += 0.3;
  }
  
  // Momentum alignment
  if (combined.includes('momentum') || combined.includes('follow through') || combined.includes('continuation')) {
    score += 0.3;
  }
  
  // Multiple timeframe confirmation
  if (combined.includes('confluence') || combined.includes('alignment') || combined.includes('multiple tf')) {
    score += 0.4;
  }
  
  return Math.min(score, 1.0);
};

/**
 * Calculate volume confirmation score (0-0.5 points)
 */
const calculateVolumeConfirmation = (trade: CompleteTradeData): number => {
  const notes = trade.trade.notes?.toLowerCase() || '';
  const setup = trade.trade.setup?.toLowerCase() || '';
  const combined = `${notes} ${setup}`;
  
  if (combined.includes('volume') || combined.includes('vol') || combined.includes('heavy') || combined.includes('spike')) {
    return 0.5;
  }
  
  return 0;
};

/**
 * Calculate pattern quality score for a trade
 */
const calculatePatternQuality = (trade: CompleteTradeData): PatternQualityScore => {
  const elements = extractPDArrayElements(trade);
  
  const breakdown = {
    pdArrayConfluence: calculatePDArrayConfluence(elements),
    fvgCharacteristics: assessFVGCharacteristics(trade),
    rdStrength: evaluateRDStrength(trade),
    confirmationSignals: assessConfirmationSignals(trade),
    volumeConfirmation: calculateVolumeConfirmation(trade)
  };
  
  const totalScore = Object.values(breakdown).reduce((sum, score) => sum + score, 0);
  const maxScore = 6.0; // Maximum possible score
  const normalizedScore = Math.min((totalScore / maxScore) * 5, 5); // Convert to 1-5 scale
  
  // Determine rating
  let rating: PatternQualityScore['rating'];
  if (normalizedScore >= 4.5) rating = 'EXCEPTIONAL';
  else if (normalizedScore >= 3.5) rating = 'EXCELLENT';
  else if (normalizedScore >= 2.5) rating = 'GOOD';
  else if (normalizedScore >= 1.5) rating = 'FAIR';
  else rating = 'POOR';
  
  // Generate recommendation
  let recommendation: string;
  if (normalizedScore >= 4.0) {
    recommendation = 'PRIORITIZE EXECUTION - High probability setup';
  } else if (normalizedScore >= 3.0) {
    recommendation = 'EXECUTE WITH CONFIDENCE - Good setup quality';
  } else if (normalizedScore >= 2.0) {
    recommendation = 'PROCEED WITH CAUTION - Average setup';
  } else {
    recommendation = 'AVOID OR REDUCE SIZE - Low quality setup';
  }
  
  // Calculate expected win probability based on score
  const expectedWinProbability = Math.min(40 + (normalizedScore * 12), 90);
  
  return {
    totalScore: normalizedScore,
    maxScore: 5.0,
    breakdown,
    rating,
    recommendation,
    expectedWinProbability
  };
};

/**
 * Pattern Quality Scoring Hook
 */
export const usePatternQualityScoring = () => {
  const [trades, setTrades] = useState<CompleteTradeData[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Fetch trade data
  useEffect(() => {
    const fetchTrades = async () => {
      try {
        setIsLoading(true);
        setError(null);
        const tradeData = await tradeStorageService.getAllTrades();
        setTrades(tradeData);
      } catch (err) {
        console.error('Error fetching trades for pattern quality scoring:', err);
        setError('Failed to load trade data');
      } finally {
        setIsLoading(false);
      }
    };

    fetchTrades();
  }, []);

  // Calculate live pattern analysis
  const analysis: LivePatternAnalysis = useMemo(() => {
    if (trades.length === 0) {
      return {
        currentScore: {
          totalScore: 0,
          maxScore: 5.0,
          breakdown: {
            pdArrayConfluence: 0,
            fvgCharacteristics: 0,
            rdStrength: 0,
            confirmationSignals: 0,
            volumeConfirmation: 0
          },
          rating: 'POOR',
          recommendation: 'No data available for analysis',
          expectedWinProbability: 50
        },
        historicalAccuracy: 0,
        scoreDistribution: []
      };
    }

    // Calculate scores for all trades
    const scoredTrades = trades.map(trade => ({
      trade,
      score: calculatePatternQuality(trade),
      actualWin: trade.trade.win_loss === 'Win'
    }));

    // Calculate historical accuracy of our scoring system
    const accuracyData = scoredTrades.filter(st => st.score.totalScore > 0);
    let correctPredictions = 0;
    
    accuracyData.forEach(st => {
      const predictedWin = st.score.expectedWinProbability > 60;
      if (predictedWin === st.actualWin) {
        correctPredictions++;
      }
    });
    
    const historicalAccuracy = accuracyData.length > 0 ? 
      (correctPredictions / accuracyData.length) * 100 : 0;

    // Calculate score distribution
    const scoreDistribution = [1, 2, 3, 4, 5].map(score => {
      const tradesInRange = scoredTrades.filter(st => 
        st.score.totalScore >= score - 0.5 && st.score.totalScore < score + 0.5
      );
      
      const wins = tradesInRange.filter(st => st.actualWin).length;
      const winRate = tradesInRange.length > 0 ? (wins / tradesInRange.length) * 100 : 0;
      
      const rMultiples = tradesInRange
        .map(st => st.trade.trade.r_multiple)
        .filter((r): r is number => r !== undefined && r !== null);
      const avgRMultiple = rMultiples.length > 0 ? 
        rMultiples.reduce((sum, r) => sum + r, 0) / rMultiples.length : 0;

      return {
        score,
        count: tradesInRange.length,
        winRate,
        avgRMultiple
      };
    });

    // Use most recent trade for current score (in real implementation, this would be live market data)
    const currentScore = trades.length > 0 ? 
      calculatePatternQuality(trades[trades.length - 1]) : 
      {
        totalScore: 0,
        maxScore: 5.0,
        breakdown: {
          pdArrayConfluence: 0,
          fvgCharacteristics: 0,
          rdStrength: 0,
          confirmationSignals: 0,
          volumeConfirmation: 0
        },
        rating: 'POOR' as const,
        recommendation: 'No current setup to analyze',
        expectedWinProbability: 50
      };

    return {
      currentScore,
      historicalAccuracy,
      scoreDistribution
    };
  }, [trades]);

  return {
    analysis,
    isLoading,
    error,
    calculatePatternQuality, // Export for external use
    refresh: () => {
      setTrades([]);
    }
  };
};
