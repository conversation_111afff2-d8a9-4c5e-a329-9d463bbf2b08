/**
 * Success Probability Calculator Hook
 * 
 * Real-time probability assessment based on current conditions:
 * - Multi-factor analysis combining model, session, quality, and market conditions
 * - Dynamic risk management recommendations
 * - Position sizing based on confluence factors
 */

import { useState, useEffect, useMemo } from 'react';
import { tradeStorageService } from '@adhd-trading-dashboard/shared';
import type { CompleteTradeData } from '@adhd-trading-dashboard/shared';
import { ModelType, ModelRecommendation } from './useModelSelectionEngine';
import { PatternQualityScore } from './usePatternQualityScoring';

export interface SuccessProbability {
  finalProbability: number;
  confidence: 'LOW' | 'MEDIUM' | 'HIGH';
  recommendation: 'AVOID' | 'REDUCE_SIZE' | 'STANDARD' | 'INCREASE_SIZE' | 'PRIORITIZE';
  expectedRMultiple: {
    min: number;
    max: number;
    average: number;
  };
  breakdown: {
    baseModelWinRate: number;
    sessionBonus: number;
    qualityBonus: number;
    newsImpact: number;
    volumeBonus: number;
    confluenceBonus: number;
  };
  riskManagement: {
    positionSizing: 'CONSERVATIVE' | 'STANDARD' | 'AGGRESSIVE';
    maxRiskPercent: number;
    stopLossMultiplier: number;
    takeProfitTargets: number[];
  };
}

export interface MarketContext {
  isNewsDay: boolean;
  newsImpact: 'LOW' | 'MEDIUM' | 'HIGH';
  volumeProfile: 'LOW' | 'NORMAL' | 'HIGH';
  marketHours: 'PRE_MARKET' | 'REGULAR' | 'AFTER_HOURS';
  dayOfWeek: 'MONDAY' | 'TUESDAY' | 'WEDNESDAY' | 'THURSDAY' | 'FRIDAY';
}

/**
 * Determine current market context
 */
const getCurrentMarketContext = (): MarketContext => {
  const now = new Date();
  const hour = now.getHours();
  const dayOfWeek = ['SUNDAY', 'MONDAY', 'TUESDAY', 'WEDNESDAY', 'THURSDAY', 'FRIDAY', 'SATURDAY'][now.getDay()] as MarketContext['dayOfWeek'];
  
  // Determine market hours
  let marketHours: MarketContext['marketHours'];
  if (hour < 9 || (hour === 9 && now.getMinutes() < 30)) {
    marketHours = 'PRE_MARKET';
  } else if (hour >= 16) {
    marketHours = 'AFTER_HOURS';
  } else {
    marketHours = 'REGULAR';
  }

  // Simple news detection (in real implementation, this would integrate with news APIs)
  const isNewsDay = dayOfWeek === 'FRIDAY' || dayOfWeek === 'WEDNESDAY'; // Simplified
  const newsImpact: MarketContext['newsImpact'] = isNewsDay ? 'MEDIUM' : 'LOW';

  // Volume profile estimation (in real implementation, this would use actual volume data)
  const volumeProfile: MarketContext['volumeProfile'] = 
    marketHours === 'REGULAR' && (hour >= 9 && hour <= 11) ? 'HIGH' : 'NORMAL';

  return {
    isNewsDay,
    newsImpact,
    volumeProfile,
    marketHours,
    dayOfWeek
  };
};

/**
 * Calculate session performance bonus
 */
const calculateSessionBonus = (
  currentHour: number,
  sessionPerformance: { hour: number; winRate: number; totalTrades: number }[]
): number => {
  const currentSession = sessionPerformance.find(s => s.hour === currentHour);
  if (!currentSession || currentSession.totalTrades < 2) return 0;

  // Bonus based on historical session performance
  if (currentSession.winRate >= 80) return 15;
  if (currentSession.winRate >= 70) return 10;
  if (currentSession.winRate >= 60) return 5;
  if (currentSession.winRate < 40) return -10;
  return 0;
};

/**
 * Calculate quality bonus based on pattern score
 */
const calculateQualityBonus = (qualityScore: number): number => {
  if (qualityScore >= 4.5) return 20;
  if (qualityScore >= 4.0) return 15;
  if (qualityScore >= 3.5) return 10;
  if (qualityScore >= 3.0) return 5;
  if (qualityScore < 2.0) return -15;
  return 0;
};

/**
 * Calculate news impact on probability
 */
const calculateNewsImpact = (marketContext: MarketContext, model: ModelType): number => {
  if (marketContext.newsImpact === 'LOW') return 0;
  
  // High impact news generally favors RD-Cont over FVG-RD due to volatility
  if (marketContext.newsImpact === 'HIGH') {
    return model === 'RD-Cont' ? 5 : -10;
  }
  
  // Medium impact news
  return model === 'RD-Cont' ? 2 : -5;
};

/**
 * Calculate volume bonus
 */
const calculateVolumeBonus = (marketContext: MarketContext): number => {
  switch (marketContext.volumeProfile) {
    case 'HIGH': return 5;
    case 'LOW': return -5;
    default: return 0;
  }
};

/**
 * Calculate confluence bonus for multiple confirming factors
 */
const calculateConfluenceBonus = (
  modelConfidence: 'LOW' | 'MEDIUM' | 'HIGH',
  qualityRating: string,
  sessionBonus: number
): number => {
  let confluenceFactors = 0;
  
  if (modelConfidence === 'HIGH') confluenceFactors++;
  if (qualityRating === 'EXCELLENT' || qualityRating === 'EXCEPTIONAL') confluenceFactors++;
  if (sessionBonus >= 10) confluenceFactors++;
  
  // Bonus for multiple confirming factors
  if (confluenceFactors >= 3) return 10;
  if (confluenceFactors >= 2) return 5;
  return 0;
};

/**
 * Generate risk management recommendations
 */
const generateRiskManagement = (
  probability: number,
  confidence: 'LOW' | 'MEDIUM' | 'HIGH',
  qualityScore: number
): SuccessProbability['riskManagement'] => {
  let positionSizing: SuccessProbability['riskManagement']['positionSizing'];
  let maxRiskPercent: number;
  let stopLossMultiplier: number;
  let takeProfitTargets: number[];

  if (probability >= 75 && confidence === 'HIGH' && qualityScore >= 4.0) {
    positionSizing = 'AGGRESSIVE';
    maxRiskPercent = 2.5;
    stopLossMultiplier = 1.0;
    takeProfitTargets = [1.5, 2.5, 4.0];
  } else if (probability >= 65 && confidence !== 'LOW') {
    positionSizing = 'STANDARD';
    maxRiskPercent = 2.0;
    stopLossMultiplier = 1.0;
    takeProfitTargets = [1.5, 2.0, 3.0];
  } else {
    positionSizing = 'CONSERVATIVE';
    maxRiskPercent = 1.0;
    stopLossMultiplier = 0.8;
    takeProfitTargets = [1.0, 1.5, 2.0];
  }

  return {
    positionSizing,
    maxRiskPercent,
    stopLossMultiplier,
    takeProfitTargets
  };
};

/**
 * Success Probability Calculator Hook
 */
export const useSuccessProbabilityCalculator = (
  modelRecommendation: ModelRecommendation,
  patternQuality: PatternQualityScore
) => {
  const [trades, setTrades] = useState<CompleteTradeData[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Fetch trade data
  useEffect(() => {
    const fetchTrades = async () => {
      try {
        setIsLoading(true);
        setError(null);
        const tradeData = await tradeStorageService.getAllTrades();
        setTrades(tradeData);
      } catch (err) {
        console.error('Error fetching trades for probability calculation:', err);
        setError('Failed to load trade data');
      } finally {
        setIsLoading(false);
      }
    };

    fetchTrades();
  }, []);

  // Calculate success probability
  const successProbability: SuccessProbability = useMemo(() => {
    if (trades.length < 5) {
      return {
        finalProbability: 50,
        confidence: 'LOW',
        recommendation: 'STANDARD',
        expectedRMultiple: { min: 1.0, max: 2.0, average: 1.5 },
        breakdown: {
          baseModelWinRate: 50,
          sessionBonus: 0,
          qualityBonus: 0,
          newsImpact: 0,
          volumeBonus: 0,
          confluenceBonus: 0
        },
        riskManagement: {
          positionSizing: 'CONSERVATIVE',
          maxRiskPercent: 1.0,
          stopLossMultiplier: 1.0,
          takeProfitTargets: [1.0, 1.5, 2.0]
        }
      };
    }

    const marketContext = getCurrentMarketContext();
    const currentHour = new Date().getHours();

    // Calculate base model win rate
    const modelTrades = trades.filter(t => t.trade.model_type === modelRecommendation.recommendedModel);
    const baseModelWinRate = modelTrades.length > 0 ? 
      (modelTrades.filter(t => t.trade.win_loss === 'Win').length / modelTrades.length) * 100 : 
      modelRecommendation.probability;

    // Calculate session performance
    const sessionPerformance = Array.from({ length: 24 }, (_, hour) => {
      const hourTrades = trades.filter(t => {
        const entryTime = t.trade.entry_time;
        if (!entryTime) return false;
        const tradeHour = parseInt(entryTime.split(':')[0]);
        return tradeHour === hour;
      });
      
      return {
        hour,
        winRate: hourTrades.length > 0 ? 
          (hourTrades.filter(t => t.trade.win_loss === 'Win').length / hourTrades.length) * 100 : 0,
        totalTrades: hourTrades.length
      };
    });

    // Calculate all bonuses and impacts
    const sessionBonus = calculateSessionBonus(currentHour, sessionPerformance);
    const qualityBonus = calculateQualityBonus(patternQuality.totalScore);
    const newsImpact = calculateNewsImpact(marketContext, modelRecommendation.recommendedModel);
    const volumeBonus = calculateVolumeBonus(marketContext);
    const confluenceBonus = calculateConfluenceBonus(
      modelRecommendation.confidence,
      patternQuality.rating,
      sessionBonus
    );

    // Calculate final probability
    const finalProbability = Math.min(Math.max(
      baseModelWinRate + sessionBonus + qualityBonus + newsImpact + volumeBonus + confluenceBonus,
      20
    ), 90);

    // Determine confidence level
    let confidence: 'LOW' | 'MEDIUM' | 'HIGH';
    const totalBonus = Math.abs(sessionBonus + qualityBonus + confluenceBonus);
    if (totalBonus >= 25 && modelRecommendation.confidence === 'HIGH') confidence = 'HIGH';
    else if (totalBonus >= 15) confidence = 'MEDIUM';
    else confidence = 'LOW';

    // Generate recommendation
    let recommendation: SuccessProbability['recommendation'];
    if (finalProbability >= 80 && confidence === 'HIGH') recommendation = 'PRIORITIZE';
    else if (finalProbability >= 70) recommendation = 'INCREASE_SIZE';
    else if (finalProbability >= 60) recommendation = 'STANDARD';
    else if (finalProbability >= 45) recommendation = 'REDUCE_SIZE';
    else recommendation = 'AVOID';

    // Calculate expected R-multiple range
    const modelRMultiples = modelTrades
      .map(t => t.trade.r_multiple)
      .filter((r): r is number => r !== undefined && r !== null);
    
    const avgR = modelRMultiples.length > 0 ? 
      modelRMultiples.reduce((sum, r) => sum + r, 0) / modelRMultiples.length : 1.5;
    
    const expectedRMultiple = {
      min: Math.max(avgR * 0.7, 0.5),
      max: avgR * 1.5,
      average: avgR
    };

    // Generate risk management
    const riskManagement = generateRiskManagement(finalProbability, confidence, patternQuality.totalScore);

    return {
      finalProbability,
      confidence,
      recommendation,
      expectedRMultiple,
      breakdown: {
        baseModelWinRate,
        sessionBonus,
        qualityBonus,
        newsImpact,
        volumeBonus,
        confluenceBonus
      },
      riskManagement
    };
  }, [trades, modelRecommendation, patternQuality]);

  return {
    successProbability,
    isLoading,
    error,
    refresh: () => {
      setTrades([]);
    }
  };
};
