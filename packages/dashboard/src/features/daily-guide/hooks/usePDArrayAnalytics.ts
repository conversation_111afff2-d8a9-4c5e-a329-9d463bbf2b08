/**
 * PD Array Analytics Hook
 * 
 * Analyzes user's actual trading performance with ICT PD Array elements and combinations.
 * Provides dynamic, performance-driven recommendations for the Daily Trading Plan.
 */

import { useState, useEffect, useMemo } from 'react';
import { tradeStorageService } from '@adhd-trading-dashboard/shared';
import type { CompleteTradeData } from '@adhd-trading-dashboard/shared';

// PD Array Element Types
export type PDArrayElement = 'FVG' | 'NWOG' | 'NDOG' | 'Liquidity';
export type PDArrayCombination = 'FVG+NWOG' | 'FVG+Liquidity' | 'NWOG+Liquidity' | 'FVG+NWOG+Liquidity' | 'Single';

export interface ElementPerformance {
  element: PDArrayElement;
  totalTrades: number;
  winningTrades: number;
  winRate: number;
  avgRMultiple: number;
  totalPnL: number;
  bestSession: string;
  trend: 'improving' | 'declining' | 'stable';
}

export interface CombinationPerformance {
  combination: PDArrayCombination;
  totalTrades: number;
  winningTrades: number;
  winRate: number;
  avgRMultiple: number;
  totalPnL: number;
  effectiveness: 'excellent' | 'good' | 'average' | 'poor' | 'avoid';
}

export interface WeeklyComparison {
  currentWeek: {
    startDate: string;
    endDate: string;
    totalTrades: number;
    winRate: number;
    bestElements: PDArrayElement[];
    bestCombinations: PDArrayCombination[];
  };
  previousWeek: {
    startDate: string;
    endDate: string;
    totalTrades: number;
    winRate: number;
    bestElements: PDArrayElement[];
    bestCombinations: PDArrayCombination[];
  } | null;
  improvement: {
    winRateChange: number;
    volumeChange: number;
    elementShifts: string[];
    recommendations: string[];
  };
}

export interface DynamicActionItem {
  id: string;
  description: string;
  priority: 'high' | 'medium' | 'low';
  category: 'element-focus' | 'combination-strategy' | 'risk-management' | 'psychology';
  basedOn: 'current-performance' | 'weekly-comparison' | 'trend-analysis';
  confidence: number; // 0-100
}

export interface PDArrayAnalytics {
  elementPerformance: ElementPerformance[];
  combinationPerformance: CombinationPerformance[];
  weeklyComparison: WeeklyComparison;
  dynamicActionItems: DynamicActionItem[];
  riskGuidelines: {
    hotStreakElements: PDArrayElement[];
    coldStreakElements: PDArrayElement[];
    recommendedPositionSizing: string;
    riskAdjustments: string[];
  };
  performanceNotes: {
    weeklyPatterns: string[];
    timeBasedInsights: string[];
    psychologyInsights: string[];
  };
  totalAnalyzedTrades: number;
  lastUpdated: Date;
}

/**
 * Get current week date range (Monday to Sunday)
 */
const getCurrentWeekRange = (): { start: Date; end: Date } => {
  const now = new Date();
  const dayOfWeek = now.getDay();
  const mondayOffset = dayOfWeek === 0 ? -6 : 1 - dayOfWeek; // Handle Sunday as 0
  
  const monday = new Date(now);
  monday.setDate(now.getDate() + mondayOffset);
  monday.setHours(0, 0, 0, 0);
  
  const sunday = new Date(monday);
  sunday.setDate(monday.getDate() + 6);
  sunday.setHours(23, 59, 59, 999);
  
  return { start: monday, end: sunday };
};

/**
 * Get previous week date range
 */
const getPreviousWeekRange = (): { start: Date; end: Date } => {
  const currentWeek = getCurrentWeekRange();
  const previousMonday = new Date(currentWeek.start);
  previousMonday.setDate(currentWeek.start.getDate() - 7);
  
  const previousSunday = new Date(currentWeek.end);
  previousSunday.setDate(currentWeek.end.getDate() - 7);
  
  return { start: previousMonday, end: previousSunday };
};

/**
 * Extract PD Array elements from trade setup information
 */
const extractPDArrayElements = (trade: CompleteTradeData): PDArrayElement[] => {
  const elements: PDArrayElement[] = [];
  const setupInfo = trade.trade.model_type?.toLowerCase() || '';
  const notes = trade.trade.notes?.toLowerCase() || '';
  const combined = `${setupInfo} ${notes}`;
  
  // Check for FVG (Fair Value Gaps)
  if (combined.includes('fvg') || combined.includes('fair value gap') || combined.includes('imbalance')) {
    elements.push('FVG');
  }
  
  // Check for NWOG (New Week Opening Gap)
  if (combined.includes('nwog') || combined.includes('new week opening') || combined.includes('weekly gap')) {
    elements.push('NWOG');
  }
  
  // Check for NDOG (New Day Opening Gap)
  if (combined.includes('ndog') || combined.includes('new day opening') || combined.includes('daily gap')) {
    elements.push('NDOG');
  }
  
  // Check for Liquidity
  if (combined.includes('liquidity') || combined.includes('sweep') || combined.includes('pool') || combined.includes('hunt')) {
    elements.push('Liquidity');
  }
  
  return elements;
};

/**
 * Determine combination type from elements
 */
const getCombinationType = (elements: PDArrayElement[]): PDArrayCombination => {
  if (elements.length === 1) return 'Single';
  
  const hasNWOG = elements.includes('NWOG');
  const hasFVG = elements.includes('FVG');
  const hasLiquidity = elements.includes('Liquidity');
  
  if (hasFVG && hasNWOG && hasLiquidity) return 'FVG+NWOG+Liquidity';
  if (hasFVG && hasNWOG) return 'FVG+NWOG';
  if (hasFVG && hasLiquidity) return 'FVG+Liquidity';
  if (hasNWOG && hasLiquidity) return 'NWOG+Liquidity';
  
  return 'Single';
};

/**
 * Calculate effectiveness rating
 */
const getEffectivenessRating = (winRate: number, totalTrades: number): CombinationPerformance['effectiveness'] => {
  if (totalTrades < 3) return 'average'; // Insufficient data
  
  if (winRate >= 70) return 'excellent';
  if (winRate >= 60) return 'good';
  if (winRate >= 45) return 'average';
  if (winRate >= 30) return 'poor';
  return 'avoid';
};

/**
 * Generate dynamic action items based on performance analysis
 */
const generateDynamicActionItems = (
  elementPerformance: ElementPerformance[],
  combinationPerformance: CombinationPerformance[],
  weeklyComparison: WeeklyComparison
): DynamicActionItem[] => {
  const items: DynamicActionItem[] = [];
  
  // Find best performing elements
  const bestElements = elementPerformance
    .filter(e => e.totalTrades >= 3)
    .sort((a, b) => b.winRate - a.winRate)
    .slice(0, 2);
  
  if (bestElements.length > 0) {
    items.push({
      id: `focus-${bestElements[0].element.toLowerCase()}`,
      description: `Focus on ${bestElements[0].element} setups - ${bestElements[0].winRate.toFixed(1)}% win rate`,
      priority: 'high',
      category: 'element-focus',
      basedOn: 'current-performance',
      confidence: Math.min(95, bestElements[0].totalTrades * 10)
    });
  }
  
  // Find best combinations
  const bestCombination = combinationPerformance
    .filter(c => c.totalTrades >= 2 && c.effectiveness === 'excellent')
    .sort((a, b) => b.winRate - a.winRate)[0];
  
  if (bestCombination) {
    items.push({
      id: `combination-${bestCombination.combination.toLowerCase().replace(/\+/g, '-')}`,
      description: `Prioritize ${bestCombination.combination} combinations - ${bestCombination.winRate.toFixed(1)}% success rate`,
      priority: 'high',
      category: 'combination-strategy',
      basedOn: 'current-performance',
      confidence: Math.min(90, bestCombination.totalTrades * 15)
    });
  }
  
  // Weekly comparison insights
  if (weeklyComparison.improvement.winRateChange < -10) {
    items.push({
      id: 'risk-reduction',
      description: `Reduce position sizes - win rate declined ${Math.abs(weeklyComparison.improvement.winRateChange).toFixed(1)}% this week`,
      priority: 'high',
      category: 'risk-management',
      basedOn: 'weekly-comparison',
      confidence: 85
    });
  }
  
  return items;
};

/**
 * PD Array Analytics Hook
 */
export const usePDArrayAnalytics = () => {
  const [trades, setTrades] = useState<CompleteTradeData[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Fetch trade data
  useEffect(() => {
    const fetchTrades = async () => {
      try {
        setIsLoading(true);
        setError(null);
        const tradeData = await tradeStorageService.getAllTrades();
        setTrades(tradeData);
      } catch (err) {
        console.error('Error fetching trades for PD Array analytics:', err);
        setError('Failed to load trade data');
      } finally {
        setIsLoading(false);
      }
    };

    fetchTrades();
  }, []);

  // Calculate PD Array analytics
  const analytics: PDArrayAnalytics = useMemo(() => {
    if (trades.length === 0) {
      return {
        elementPerformance: [],
        combinationPerformance: [],
        weeklyComparison: {
          currentWeek: {
            startDate: getCurrentWeekRange().start.toISOString(),
            endDate: getCurrentWeekRange().end.toISOString(),
            totalTrades: 0,
            winRate: 0,
            bestElements: [],
            bestCombinations: []
          },
          previousWeek: null,
          improvement: {
            winRateChange: 0,
            volumeChange: 0,
            elementShifts: [],
            recommendations: ['Insufficient data for analysis']
          }
        },
        dynamicActionItems: [{
          id: 'start-tracking',
          description: 'Start logging PD Array elements in trade notes (FVG, NWOG, NDOG, Liquidity)',
          priority: 'high',
          category: 'element-focus',
          basedOn: 'current-performance',
          confidence: 100
        }],
        riskGuidelines: {
          hotStreakElements: [],
          coldStreakElements: [],
          recommendedPositionSizing: 'Standard risk (1-2% per trade)',
          riskAdjustments: []
        },
        performanceNotes: {
          weeklyPatterns: [],
          timeBasedInsights: [],
          psychologyInsights: []
        },
        totalAnalyzedTrades: 0,
        lastUpdated: new Date()
      };
    }

    // Analyze trades with PD Array elements
    const tradesWithElements = trades.map(trade => ({
      ...trade,
      pdArrayElements: extractPDArrayElements(trade),
      combinationType: getCombinationType(extractPDArrayElements(trade))
    })).filter(trade => trade.pdArrayElements.length > 0);

    // Calculate element performance
    const elementStats: Record<PDArrayElement, { total: number; wins: number; pnl: number; rMultiples: number[] }> = {
      FVG: { total: 0, wins: 0, pnl: 0, rMultiples: [] },
      NWOG: { total: 0, wins: 0, pnl: 0, rMultiples: [] },
      NDOG: { total: 0, wins: 0, pnl: 0, rMultiples: [] },
      Liquidity: { total: 0, wins: 0, pnl: 0, rMultiples: [] }
    };

    tradesWithElements.forEach(trade => {
      trade.pdArrayElements.forEach(element => {
        elementStats[element].total++;
        if (trade.trade.win_loss === 'Win') {
          elementStats[element].wins++;
        }
        elementStats[element].pnl += trade.trade.achieved_pl || 0;
        elementStats[element].rMultiples.push(trade.trade.r_multiple || 0);
      });
    });

    const elementPerformance: ElementPerformance[] = Object.entries(elementStats).map(([element, stats]) => ({
      element: element as PDArrayElement,
      totalTrades: stats.total,
      winningTrades: stats.wins,
      winRate: stats.total > 0 ? (stats.wins / stats.total) * 100 : 0,
      avgRMultiple: stats.rMultiples.length > 0 ? stats.rMultiples.reduce((a, b) => a + b, 0) / stats.rMultiples.length : 0,
      totalPnL: stats.pnl,
      bestSession: 'London', // TODO: Calculate from actual session data
      trend: 'stable' // TODO: Calculate trend from historical data
    }));

    // Calculate combination performance
    const combinationStats: Record<PDArrayCombination, { total: number; wins: number; pnl: number }> = {
      'Single': { total: 0, wins: 0, pnl: 0 },
      'FVG+NWOG': { total: 0, wins: 0, pnl: 0 },
      'FVG+Liquidity': { total: 0, wins: 0, pnl: 0 },
      'NWOG+Liquidity': { total: 0, wins: 0, pnl: 0 },
      'FVG+NWOG+Liquidity': { total: 0, wins: 0, pnl: 0 }
    };

    tradesWithElements.forEach(trade => {
      const combo = trade.combinationType;
      combinationStats[combo].total++;
      if (trade.trade.win_loss === 'Win') {
        combinationStats[combo].wins++;
      }
      combinationStats[combo].pnl += trade.trade.achieved_pl || 0;
    });

    const combinationPerformance: CombinationPerformance[] = Object.entries(combinationStats).map(([combo, stats]) => {
      const winRate = stats.total > 0 ? (stats.wins / stats.total) * 100 : 0;
      return {
        combination: combo as PDArrayCombination,
        totalTrades: stats.total,
        winningTrades: stats.wins,
        winRate,
        avgRMultiple: 0, // TODO: Calculate from R multiples
        totalPnL: stats.pnl,
        effectiveness: getEffectivenessRating(winRate, stats.total)
      };
    });

    // Weekly comparison (simplified for now)
    const currentWeek = getCurrentWeekRange();
    const weeklyComparison: WeeklyComparison = {
      currentWeek: {
        startDate: currentWeek.start.toISOString(),
        endDate: currentWeek.end.toISOString(),
        totalTrades: tradesWithElements.length,
        winRate: tradesWithElements.length > 0 ? (tradesWithElements.filter(t => t.trade.win_loss === 'Win').length / tradesWithElements.length) * 100 : 0,
        bestElements: elementPerformance.filter(e => e.totalTrades >= 2).sort((a, b) => b.winRate - a.winRate).slice(0, 2).map(e => e.element),
        bestCombinations: combinationPerformance.filter(c => c.totalTrades >= 1).sort((a, b) => b.winRate - a.winRate).slice(0, 2).map(c => c.combination)
      },
      previousWeek: null, // TODO: Implement previous week calculation
      improvement: {
        winRateChange: 0,
        volumeChange: 0,
        elementShifts: [],
        recommendations: []
      }
    };

    // Generate dynamic action items
    const dynamicActionItems = generateDynamicActionItems(elementPerformance, combinationPerformance, weeklyComparison);

    return {
      elementPerformance,
      combinationPerformance,
      weeklyComparison,
      dynamicActionItems,
      riskGuidelines: {
        hotStreakElements: elementPerformance.filter(e => e.winRate >= 70 && e.totalTrades >= 3).map(e => e.element),
        coldStreakElements: elementPerformance.filter(e => e.winRate < 40 && e.totalTrades >= 3).map(e => e.element),
        recommendedPositionSizing: 'Standard risk (1-2% per trade)',
        riskAdjustments: []
      },
      performanceNotes: {
        weeklyPatterns: [`${tradesWithElements.length} trades with PD Array elements this analysis period`],
        timeBasedInsights: [],
        psychologyInsights: []
      },
      totalAnalyzedTrades: tradesWithElements.length,
      lastUpdated: new Date()
    };
  }, [trades]);

  return {
    analytics,
    isLoading,
    error,
    refresh: () => {
      setTrades([]);
    }
  };
};
