/**
 * Enhanced Session Focus Component
 *
 * Advanced ICT session intelligence using real trading data from spreadsheet import.
 * Provides sophisticated PD Array and model-specific analysis with ADHD-optimized format.
 */

import React from 'react';
import styled from 'styled-components';
import { Card } from '@adhd-trading-dashboard/shared';
import {
  useSessionAnalytics,
  SessionPerformance,
  CurrentSessionRecommendation,
} from '../hooks/useSessionAnalytics';
import { useEnhancedSessionIntelligence } from '../hooks/useEnhancedSessionIntelligence';

export interface SessionFocusProps {
  /** Whether the component is in a loading state */
  isLoading?: boolean;
  /** The error message, if any */
  error?: string | null;
  /** Function called when the refresh button is clicked */
  onRefresh?: () => void;
  /** Additional class name */
  className?: string;
}

// Styled components with F1 theme
const Container = styled.div`
  display: flex;
  flex-direction: column;
  gap: ${({ theme }) => theme.spacing?.md || '16px'};
`;

const CurrentSessionCard = styled.div`
  background: linear-gradient(135deg, #1a1a1a 0%, #2a2a2a 100%);
  border: 1px solid #dc2626;
  border-radius: 8px;
  padding: 20px;
  position: relative;
  overflow: hidden;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(90deg, #dc2626, #ef4444, #dc2626);
    animation: pulse 2s ease-in-out infinite;
  }

  @keyframes pulse {
    0%,
    100% {
      opacity: 1;
    }
    50% {
      opacity: 0.7;
    }
  }
`;

const SessionHeader = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
`;

const SessionTitle = styled.h3`
  color: #ffffff;
  font-size: 18px;
  font-weight: 600;
  margin: 0;
  display: flex;
  align-items: center;
  gap: 8px;
`;

const LiveIndicator = styled.span`
  background: #dc2626;
  color: #ffffff;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 600;
  animation: blink 1.5s ease-in-out infinite;

  @keyframes blink {
    0%,
    100% {
      opacity: 1;
    }
    50% {
      opacity: 0.6;
    }
  }
`;

const RecommendationBadge = styled.div<{ level: CurrentSessionRecommendation['recommendation'] }>`
  padding: 6px 12px;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 600;
  color: #ffffff;
  background: ${({ level }) => {
    switch (level) {
      case 'high':
        return 'linear-gradient(135deg, #10b981, #059669)';
      case 'medium':
        return 'linear-gradient(135deg, #f59e0b, #d97706)';
      case 'low':
        return 'linear-gradient(135deg, #ef4444, #dc2626)';
      case 'avoid':
        return 'linear-gradient(135deg, #7f1d1d, #991b1b)';
      default:
        return 'linear-gradient(135deg, #6b7280, #4b5563)';
    }
  }};
`;

const MetricsGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
  gap: 16px;
  margin-bottom: 16px;
`;

const MetricCard = styled.div`
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 6px;
  padding: 12px;
  text-align: center;
`;

const MetricValue = styled.div`
  font-size: 20px;
  font-weight: 700;
  color: #ffffff;
  margin-bottom: 4px;
`;

const MetricLabel = styled.div`
  font-size: 12px;
  color: #9ca3af;
  text-transform: uppercase;
  letter-spacing: 0.5px;
`;

const ActionItems = styled.div`
  background: rgba(220, 38, 38, 0.1);
  border: 1px solid rgba(220, 38, 38, 0.3);
  border-radius: 6px;
  padding: 16px;
`;

const ActionTitle = styled.h4`
  color: #dc2626;
  font-size: 14px;
  font-weight: 600;
  margin: 0 0 12px 0;
  text-transform: uppercase;
  letter-spacing: 0.5px;
`;

const ActionList = styled.ul`
  margin: 0;
  padding: 0;
  list-style: none;
`;

const ActionItem = styled.li`
  color: #ffffff;
  font-size: 14px;
  margin-bottom: 8px;
  padding-left: 20px;
  position: relative;

  &::before {
    content: '▶';
    position: absolute;
    left: 0;
    color: #dc2626;
    font-size: 12px;
  }

  &:last-child {
    margin-bottom: 0;
  }
`;

const SessionGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(100px, 1fr));
  gap: 8px;
  margin-top: 16px;
`;

const SessionBlock = styled.div<{
  performance: SessionPerformance['performance'];
  isCurrent: boolean;
}>`
  background: ${({ performance, isCurrent }) => {
    if (isCurrent) return 'linear-gradient(135deg, #dc2626, #ef4444)';
    switch (performance) {
      case 'excellent':
        return 'linear-gradient(135deg, #10b981, #059669)';
      case 'good':
        return 'linear-gradient(135deg, #3b82f6, #2563eb)';
      case 'average':
        return 'linear-gradient(135deg, #6b7280, #4b5563)';
      case 'poor':
        return 'linear-gradient(135deg, #f59e0b, #d97706)';
      case 'avoid':
        return 'linear-gradient(135deg, #ef4444, #dc2626)';
      default:
        return 'linear-gradient(135deg, #374151, #1f2937)';
    }
  }};
  border-radius: 6px;
  padding: 8px;
  text-align: center;
  position: relative;
  ${({ isCurrent }) =>
    isCurrent &&
    `
    border: 2px solid #ffffff;
    box-shadow: 0 0 10px rgba(220, 38, 38, 0.5);
  `}
`;

const SessionHour = styled.div`
  font-size: 12px;
  font-weight: 600;
  color: #ffffff;
  margin-bottom: 4px;
`;

const SessionWinRate = styled.div`
  font-size: 14px;
  font-weight: 700;
  color: #ffffff;
`;

const SessionLabel = styled.div`
  font-size: 10px;
  color: rgba(255, 255, 255, 0.8);
  margin-top: 2px;
`;

// Enhanced ICT Session Components
const ICTSessionCard = styled.div<{ isActive: boolean; isOptimal: boolean }>`
  background: ${({ isActive, isOptimal }) => {
    if (isActive && isOptimal)
      return 'linear-gradient(135deg, rgba(220, 38, 38, 0.2), rgba(239, 68, 68, 0.1))';
    if (isActive) return 'linear-gradient(135deg, rgba(59, 130, 246, 0.2), rgba(37, 99, 235, 0.1))';
    return 'rgba(255, 255, 255, 0.05)';
  }};
  border: 1px solid
    ${({ isActive, isOptimal }) => {
      if (isActive && isOptimal) return 'rgba(220, 38, 38, 0.4)';
      if (isActive) return 'rgba(59, 130, 246, 0.4)';
      return 'rgba(255, 255, 255, 0.1)';
    }};
  border-radius: 12px;
  padding: 16px;
  margin-bottom: 16px;
`;

const ICTSessionHeader = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
`;

const ICTSessionName = styled.div<{ isActive: boolean }>`
  font-size: 16px;
  font-weight: 700;
  color: ${({ isActive }) => (isActive ? '#dc2626' : '#ffffff')};
  display: flex;
  align-items: center;
  gap: 8px;
`;

const ICTTimeRange = styled.div`
  font-size: 12px;
  color: #9ca3af;
`;

const ICTPerformanceGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 12px;
  margin: 12px 0;
`;

const ICTMetric = styled.div`
  text-align: center;
`;

const ICTMetricValue = styled.div`
  font-size: 18px;
  font-weight: 700;
  color: #ffffff;
`;

const ICTMetricLabel = styled.div`
  font-size: 10px;
  color: #9ca3af;
  text-transform: uppercase;
  letter-spacing: 0.5px;
`;

const ModelPreferenceCard = styled.div`
  background: rgba(255, 255, 255, 0.05);
  border-radius: 8px;
  padding: 12px;
  margin: 12px 0;
`;

const ModelPreferenceHeader = styled.div`
  font-size: 12px;
  color: #9ca3af;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  margin-bottom: 8px;
`;

const ModelComparison = styled.div`
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 12px;
`;

const ModelStats = styled.div<{ isRecommended: boolean }>`
  background: ${({ isRecommended }) =>
    isRecommended ? 'rgba(220, 38, 38, 0.1)' : 'rgba(255, 255, 255, 0.03)'};
  border: 1px solid
    ${({ isRecommended }) =>
      isRecommended ? 'rgba(220, 38, 38, 0.3)' : 'rgba(255, 255, 255, 0.1)'};
  border-radius: 6px;
  padding: 8px;
  text-align: center;
`;

const ModelName = styled.div<{ isRecommended: boolean }>`
  font-size: 12px;
  font-weight: 600;
  color: ${({ isRecommended }) => (isRecommended ? '#dc2626' : '#ffffff')};
  margin-bottom: 4px;
`;

const ModelMetrics = styled.div`
  font-size: 10px;
  color: #9ca3af;
`;

const OptimalWindowsGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 8px;
  margin-top: 8px;
`;

const WindowCard = styled.div<{ isOptimal: boolean }>`
  background: ${({ isOptimal }) =>
    isOptimal ? 'rgba(16, 185, 129, 0.1)' : 'rgba(255, 255, 255, 0.03)'};
  border: 1px solid
    ${({ isOptimal }) => (isOptimal ? 'rgba(16, 185, 129, 0.3)' : 'rgba(255, 255, 255, 0.1)')};
  border-radius: 6px;
  padding: 8px;
  text-align: center;
`;

const WindowTime = styled.div`
  font-size: 11px;
  font-weight: 600;
  color: #ffffff;
`;

const WindowDescription = styled.div`
  font-size: 9px;
  color: #9ca3af;
  margin: 2px 0;
`;

const WindowStats = styled.div`
  font-size: 9px;
  color: #d1d5db;
`;

const WeeklyInsightsCard = styled.div`
  background: linear-gradient(135deg, rgba(168, 85, 247, 0.1), rgba(147, 51, 234, 0.05));
  border: 1px solid rgba(168, 85, 247, 0.3);
  border-radius: 12px;
  padding: 16px;
  margin-top: 16px;
`;

const InsightsHeader = styled.div`
  font-size: 14px;
  font-weight: 700;
  color: #a855f7;
  margin-bottom: 12px;
  text-transform: uppercase;
  letter-spacing: 0.5px;
`;

const InsightsList = styled.div`
  display: flex;
  flex-direction: column;
  gap: 8px;
`;

const InsightItem = styled.div`
  font-size: 12px;
  color: #d1d5db;
  padding: 6px 0;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);

  &:last-child {
    border-bottom: none;
  }
`;

/**
 * Session Focus Component
 */
export const SessionFocus: React.FC<SessionFocusProps> = ({
  isLoading = false,
  error = null,
  onRefresh,
  className,
}) => {
  const { analytics, isLoading: analyticsLoading, error: analyticsError } = useSessionAnalytics();
  const {
    intelligence,
    isLoading: enhancedLoading,
    error: enhancedError,
  } = useEnhancedSessionIntelligence();

  const loading = isLoading || analyticsLoading || enhancedLoading;
  const displayError = error || analyticsError || enhancedError;

  // Loading state
  if (loading) {
    return (
      <Card title="🎯 Session Focus">
        <div style={{ padding: '24px', textAlign: 'center' }}>
          Analyzing your trading sessions...
        </div>
      </Card>
    );
  }

  // Error state
  if (displayError) {
    return (
      <Card title="🎯 Session Focus">
        <div style={{ padding: '24px', textAlign: 'center', color: '#f44336' }}>
          Error: {displayError}
          {onRefresh && (
            <button
              onClick={onRefresh}
              style={{
                marginLeft: '16px',
                padding: '8px 16px',
                background: '#f0f0f0',
                border: 'none',
                borderRadius: '4px',
                cursor: 'pointer',
              }}
            >
              Retry
            </button>
          )}
        </div>
      </Card>
    );
  }

  const { currentRecommendation, sessionPerformance, totalAnalyzedTrades } = analytics;

  return (
    <Card
      title="🕘 Enhanced Session Intelligence"
      actions={
        onRefresh ? (
          <button
            onClick={onRefresh}
            style={{ background: 'none', border: 'none', cursor: 'pointer', color: 'inherit' }}
          >
            🔄 Refresh
          </button>
        ) : undefined
      }
    >
      <Container className={className}>
        {/* Current Session Status */}
        <CurrentSessionCard>
          <SessionHeader>
            <SessionTitle>
              {intelligence.currentStatus.currentRecommendation}
              {intelligence.currentStatus.activeSession && <LiveIndicator>LIVE</LiveIndicator>}
            </SessionTitle>
            <RecommendationBadge level={intelligence.currentStatus.urgency.toLowerCase()}>
              {intelligence.currentStatus.urgency}
            </RecommendationBadge>
          </SessionHeader>

          <MetricsGrid>
            <MetricCard>
              <MetricValue>{intelligence.currentStatus.currentTime}</MetricValue>
              <MetricLabel>Current Time</MetricLabel>
            </MetricCard>
            <MetricCard>
              <MetricValue>{intelligence.currentStatus.activeSession?.sessionName || 'None'}</MetricValue>
              <MetricLabel>Active Session</MetricLabel>
            </MetricCard>
            <MetricCard>
              <MetricValue>{intelligence.currentStatus.timeToNext > 0 ? `${intelligence.currentStatus.timeToNext}m` : '-'}</MetricValue>
              <MetricLabel>Next Session</MetricLabel>
            </MetricCard>
          </MetricsGrid>
        </CurrentSessionCard>

        {/* ICT Session Analysis */}
        {intelligence.sessions.map((session) => {
          const isActive = intelligence.currentStatus.activeSession?.sessionName === session.sessionName;
          const hasOptimalWindow = session.optimalWindows.some(w => w.winRate >= 70);

          return (
            <ICTSessionCard key={session.sessionName} isActive={isActive} isOptimal={hasOptimalWindow}>
              <ICTSessionHeader>
                <ICTSessionName isActive={isActive}>
                  {session.sessionName}
                  {isActive && <LiveIndicator>ACTIVE</LiveIndicator>}
                </ICTSessionName>
                <ICTTimeRange>{session.timeRange}</ICTTimeRange>
              </ICTSessionHeader>

              <ICTPerformanceGrid>
                <ICTMetric>
                  <ICTMetricValue>{session.performance.totalTrades}</ICTMetricValue>
                  <ICTMetricLabel>Trades</ICTMetricLabel>
                </ICTMetric>
                <ICTMetric>
                  <ICTMetricValue>{session.performance.winRate.toFixed(0)}%</ICTMetricValue>
                  <ICTMetricLabel>Win Rate</ICTMetricLabel>
                </ICTMetric>
                <ICTMetric>
                  <ICTMetricValue>{session.performance.avgRMultiple.toFixed(1)}R</ICTMetricValue>
                  <ICTMetricLabel>Avg R-Multiple</ICTMetricLabel>
                </ICTMetric>
                <ICTMetric>
                  <ICTMetricValue>{session.performance.avgRisk.toFixed(0)}</ICTMetricValue>
                  <ICTMetricLabel>Avg Risk (pts)</ICTMetricLabel>
                </ICTMetric>
              </ICTPerformanceGrid>

              {/* Model Preference */}
              <ModelPreferenceCard>
                <ModelPreferenceHeader>Model Performance Analysis</ModelPreferenceHeader>
                <ModelComparison>
                  <ModelStats isRecommended={session.modelPreference.recommendation === 'RD-Cont'}>
                    <ModelName isRecommended={session.modelPreference.recommendation === 'RD-Cont'}>
                      RD-Cont
                    </ModelName>
                    <ModelMetrics>
                      {session.modelPreference.rdCont.trades} trades<br/>
                      {session.modelPreference.rdCont.winRate.toFixed(0)}% win<br/>
                      {session.modelPreference.rdCont.avgR.toFixed(1)}R avg
                    </ModelMetrics>
                  </ModelStats>
                  <ModelStats isRecommended={session.modelPreference.recommendation === 'FVG-RD'}>
                    <ModelName isRecommended={session.modelPreference.recommendation === 'FVG-RD'}>
                      FVG-RD
                    </ModelName>
                    <ModelMetrics>
                      {session.modelPreference.fvgRd.trades} trades<br/>
                      {session.modelPreference.fvgRd.winRate.toFixed(0)}% win<br/>
                      {session.modelPreference.fvgRd.avgR.toFixed(1)}R avg
                    </ModelMetrics>
                  </ModelStats>
                </ModelComparison>
                <div style={{ textAlign: 'center', marginTop: '8px', fontSize: '12px', color: '#dc2626', fontWeight: '600' }}>
                  Recommendation: {session.modelPreference.recommendation}
                </div>
              </ModelPreferenceCard>

              {/* Optimal Windows */}
              <div>
                <ModelPreferenceHeader>Optimal Time Windows</ModelPreferenceHeader>
                <OptimalWindowsGrid>
                  {session.optimalWindows.map((window, index) => (
                    <WindowCard key={index} isOptimal={window.winRate >= 70 && window.trades >= 2}>
                      <WindowTime>{window.start}-{window.end}</WindowTime>
                      <WindowDescription>{window.description}</WindowDescription>
                      <WindowStats>
                        {window.trades > 0 ? `${window.winRate.toFixed(0)}% (${window.trades} trades)` : 'No data'}
                      </WindowStats>
                    </WindowCard>
                  ))}
                </OptimalWindowsGrid>
              </div>

              {/* Session Recommendations */}
              {session.recommendations.length > 0 && (
                <ActionItems>
                  <ActionTitle>Session Recommendations</ActionTitle>
                  <ActionList>
                    {session.recommendations.map((rec, index) => (
                      <ActionItem key={index}>{rec}</ActionItem>
                    ))}
                  </ActionList>
                </ActionItems>
              )}
            </ICTSessionCard>
          );
        })}

        {/* Weekly Insights */}
        <WeeklyInsightsCard>
          <InsightsHeader>📊 Weekly Performance Insights</InsightsHeader>
          <InsightsList>
            <InsightItem><strong>Best Session:</strong> {intelligence.weeklyInsights.bestSession}</InsightItem>
            <InsightItem><strong>Best Model:</strong> {intelligence.weeklyInsights.bestModel}</InsightItem>
            <InsightItem><strong>Average Quality:</strong> {intelligence.weeklyInsights.avgQuality.toFixed(1)}/5.0</InsightItem>
            <InsightItem><strong>Quality Threshold:</strong> >{intelligence.weeklyInsights.qualityThreshold.toFixed(1)} for optimal performance</InsightItem>
          </InsightsList>
          <ActionItems style={{ marginTop: '12px' }}>
            <ActionTitle>Key Recommendations</ActionTitle>
            <ActionList>
              {intelligence.weeklyInsights.recommendations.map((rec, index) => (
                <ActionItem key={index}>{rec}</ActionItem>
              ))}
            </ActionList>
          </ActionItems>
        </WeeklyInsightsCard>
      </Container>
    </Card>
  );
};
