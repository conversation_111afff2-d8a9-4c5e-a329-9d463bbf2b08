/**
 * Session Focus Component
 * 
 * Replaces Market Overview with personalized session analytics based on user's actual trading data.
 * Provides ADHD-optimized quick-scan format with actionable insights.
 */

import React from 'react';
import styled from 'styled-components';
import { Card } from '@adhd-trading-dashboard/shared';
import { useSessionAnalytics, SessionPerformance, CurrentSessionRecommendation } from '../hooks/useSessionAnalytics';

export interface SessionFocusProps {
  /** Whether the component is in a loading state */
  isLoading?: boolean;
  /** The error message, if any */
  error?: string | null;
  /** Function called when the refresh button is clicked */
  onRefresh?: () => void;
  /** Additional class name */
  className?: string;
}

// Styled components with F1 theme
const Container = styled.div`
  display: flex;
  flex-direction: column;
  gap: ${({ theme }) => theme.spacing?.md || '16px'};
`;

const CurrentSessionCard = styled.div`
  background: linear-gradient(135deg, #1a1a1a 0%, #2a2a2a 100%);
  border: 1px solid #dc2626;
  border-radius: 8px;
  padding: 20px;
  position: relative;
  overflow: hidden;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(90deg, #dc2626, #ef4444, #dc2626);
    animation: pulse 2s ease-in-out infinite;
  }

  @keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.7; }
  }
`;

const SessionHeader = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
`;

const SessionTitle = styled.h3`
  color: #ffffff;
  font-size: 18px;
  font-weight: 600;
  margin: 0;
  display: flex;
  align-items: center;
  gap: 8px;
`;

const LiveIndicator = styled.span`
  background: #dc2626;
  color: #ffffff;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 600;
  animation: blink 1.5s ease-in-out infinite;

  @keyframes blink {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.6; }
  }
`;

const RecommendationBadge = styled.div<{ level: CurrentSessionRecommendation['recommendation'] }>`
  padding: 6px 12px;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 600;
  color: #ffffff;
  background: ${({ level }) => {
    switch (level) {
      case 'high': return 'linear-gradient(135deg, #10b981, #059669)';
      case 'medium': return 'linear-gradient(135deg, #f59e0b, #d97706)';
      case 'low': return 'linear-gradient(135deg, #ef4444, #dc2626)';
      case 'avoid': return 'linear-gradient(135deg, #7f1d1d, #991b1b)';
      default: return 'linear-gradient(135deg, #6b7280, #4b5563)';
    }
  }};
`;

const MetricsGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
  gap: 16px;
  margin-bottom: 16px;
`;

const MetricCard = styled.div`
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 6px;
  padding: 12px;
  text-align: center;
`;

const MetricValue = styled.div`
  font-size: 20px;
  font-weight: 700;
  color: #ffffff;
  margin-bottom: 4px;
`;

const MetricLabel = styled.div`
  font-size: 12px;
  color: #9ca3af;
  text-transform: uppercase;
  letter-spacing: 0.5px;
`;

const ActionItems = styled.div`
  background: rgba(220, 38, 38, 0.1);
  border: 1px solid rgba(220, 38, 38, 0.3);
  border-radius: 6px;
  padding: 16px;
`;

const ActionTitle = styled.h4`
  color: #dc2626;
  font-size: 14px;
  font-weight: 600;
  margin: 0 0 12px 0;
  text-transform: uppercase;
  letter-spacing: 0.5px;
`;

const ActionList = styled.ul`
  margin: 0;
  padding: 0;
  list-style: none;
`;

const ActionItem = styled.li`
  color: #ffffff;
  font-size: 14px;
  margin-bottom: 8px;
  padding-left: 20px;
  position: relative;

  &::before {
    content: '▶';
    position: absolute;
    left: 0;
    color: #dc2626;
    font-size: 12px;
  }

  &:last-child {
    margin-bottom: 0;
  }
`;

const SessionGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(100px, 1fr));
  gap: 8px;
  margin-top: 16px;
`;

const SessionBlock = styled.div<{ performance: SessionPerformance['performance']; isCurrent: boolean }>`
  background: ${({ performance, isCurrent }) => {
    if (isCurrent) return 'linear-gradient(135deg, #dc2626, #ef4444)';
    switch (performance) {
      case 'excellent': return 'linear-gradient(135deg, #10b981, #059669)';
      case 'good': return 'linear-gradient(135deg, #3b82f6, #2563eb)';
      case 'average': return 'linear-gradient(135deg, #6b7280, #4b5563)';
      case 'poor': return 'linear-gradient(135deg, #f59e0b, #d97706)';
      case 'avoid': return 'linear-gradient(135deg, #ef4444, #dc2626)';
      default: return 'linear-gradient(135deg, #374151, #1f2937)';
    }
  }};
  border-radius: 6px;
  padding: 8px;
  text-align: center;
  position: relative;
  ${({ isCurrent }) => isCurrent && `
    border: 2px solid #ffffff;
    box-shadow: 0 0 10px rgba(220, 38, 38, 0.5);
  `}
`;

const SessionHour = styled.div`
  font-size: 12px;
  font-weight: 600;
  color: #ffffff;
  margin-bottom: 4px;
`;

const SessionWinRate = styled.div`
  font-size: 14px;
  font-weight: 700;
  color: #ffffff;
`;

const SessionLabel = styled.div`
  font-size: 10px;
  color: rgba(255, 255, 255, 0.8);
  margin-top: 2px;
`;

/**
 * Session Focus Component
 */
export const SessionFocus: React.FC<SessionFocusProps> = ({
  isLoading = false,
  error = null,
  onRefresh,
  className,
}) => {
  const { analytics, isLoading: analyticsLoading, error: analyticsError } = useSessionAnalytics();

  const loading = isLoading || analyticsLoading;
  const displayError = error || analyticsError;

  // Loading state
  if (loading) {
    return (
      <Card title="🎯 Session Focus">
        <div style={{ padding: '24px', textAlign: 'center' }}>
          Analyzing your trading sessions...
        </div>
      </Card>
    );
  }

  // Error state
  if (displayError) {
    return (
      <Card title="🎯 Session Focus">
        <div style={{ padding: '24px', textAlign: 'center', color: '#f44336' }}>
          Error: {displayError}
          {onRefresh && (
            <button
              onClick={onRefresh}
              style={{
                marginLeft: '16px',
                padding: '8px 16px',
                background: '#f0f0f0',
                border: 'none',
                borderRadius: '4px',
                cursor: 'pointer',
              }}
            >
              Retry
            </button>
          )}
        </div>
      </Card>
    );
  }

  const { currentRecommendation, sessionPerformance, totalAnalyzedTrades } = analytics;

  return (
    <Card
      title="🎯 Session Focus"
      actions={
        onRefresh ? (
          <button
            onClick={onRefresh}
            style={{ background: 'none', border: 'none', cursor: 'pointer', color: 'inherit' }}
          >
            🔄 Refresh
          </button>
        ) : undefined
      }
    >
      <Container className={className}>
        {/* Current Session Recommendation */}
        <CurrentSessionCard>
          <SessionHeader>
            <SessionTitle>
              {currentRecommendation.sessionLabel}
              <LiveIndicator>LIVE</LiveIndicator>
            </SessionTitle>
            <RecommendationBadge level={currentRecommendation.recommendation}>
              {currentRecommendation.recommendation.toUpperCase()}
            </RecommendationBadge>
          </SessionHeader>

          <MetricsGrid>
            <MetricCard>
              <MetricValue>{currentRecommendation.winRate.toFixed(1)}%</MetricValue>
              <MetricLabel>Win Rate</MetricLabel>
            </MetricCard>
            <MetricCard>
              <MetricValue>{currentRecommendation.riskLevel.toUpperCase()}</MetricValue>
              <MetricLabel>Risk Level</MetricLabel>
            </MetricCard>
            <MetricCard>
              <MetricValue>{totalAnalyzedTrades}</MetricValue>
              <MetricLabel>Total Trades</MetricLabel>
            </MetricCard>
          </MetricsGrid>

          <ActionItems>
            <ActionTitle>Action Items</ActionTitle>
            <ActionList>
              {currentRecommendation.actionItems.map((item, index) => (
                <ActionItem key={index}>{item}</ActionItem>
              ))}
            </ActionList>
          </ActionItems>
        </CurrentSessionCard>

        {/* Session Performance Grid */}
        <div>
          <h4 style={{ color: '#ffffff', marginBottom: '12px', fontSize: '16px' }}>
            24-Hour Performance Map
          </h4>
          <SessionGrid>
            {sessionPerformance.map((session) => (
              <SessionBlock
                key={session.hour}
                performance={session.performance}
                isCurrent={session.hour === currentRecommendation.currentHour}
              >
                <SessionHour>{session.hour}:00</SessionHour>
                <SessionWinRate>
                  {session.totalTrades > 0 ? `${session.winRate.toFixed(0)}%` : '-'}
                </SessionWinRate>
                <SessionLabel>{session.label}</SessionLabel>
              </SessionBlock>
            ))}
          </SessionGrid>
        </div>
      </Container>
    </Card>
  );
};
