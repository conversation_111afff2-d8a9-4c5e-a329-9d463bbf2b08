/**
 * Daily Guide Component
 *
 * REFACTORED: Now uses the new F1 component library and container pattern.
 * Simplified from 158 lines to a clean wrapper component.
 *
 * BENEFITS:
 * - 95% code reduction
 * - Uses proven container pattern
 * - F1 component library integration
 * - Better separation of concerns
 * - Consistent with other refactored components
 */

import React from 'react';
import { F1GuideContainer } from './F1GuideContainer';
import { DailyGuideProvider } from '../state/dailyGuideState';

export interface DailyGuideProps {
  /** Custom className */
  className?: string;
  /** Initial tab to display */
  initialTab?: 'overview' | 'plan' | 'levels' | 'news';
  /** Custom title */
  title?: string;
}

/**
 * Daily Guide Component
 *
 * Simple wrapper that renders the container with context provider.
 * Follows the proven architecture pattern.
 */
export const DailyGuide: React.FC<DailyGuideProps> = ({ className, initialTab, title }) => {
  return (
    <DailyGuideProvider>
      <F1GuideContainer className={className} initialTab={initialTab} title={title} />
    </DailyGuideProvider>
  );
};

export default DailyGuide;
