/**
 * Dynamic Trading Plan Component
 * 
 * Replaces static action items with intelligent, performance-driven recommendations
 * based on user's actual PD Array element performance and combinations.
 */

import React from 'react';
import styled from 'styled-components';
import { Card } from '@adhd-trading-dashboard/shared';
import { usePDArrayAnalytics, type DynamicActionItem, type ElementPerformance, type CombinationPerformance } from '../hooks/usePDArrayAnalytics';

export interface DynamicTradingPlanProps {
  /** Whether the component is in a loading state */
  isLoading?: boolean;
  /** The error message, if any */
  error?: string | null;
  /** Function called when the refresh button is clicked */
  onRefresh?: () => void;
  /** Additional class name */
  className?: string;
}

// Styled components with F1 theme
const Container = styled.div`
  display: flex;
  flex-direction: column;
  gap: ${({ theme }) => theme.spacing?.lg || '24px'};
`;

const Section = styled.div`
  background: rgba(255, 255, 255, 0.02);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 8px;
  padding: 20px;
`;

const SectionTitle = styled.h3`
  color: #dc2626;
  font-size: 16px;
  font-weight: 600;
  margin: 0 0 16px 0;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  display: flex;
  align-items: center;
  gap: 8px;
`;

const WeeklyComparisonHeader = styled.div`
  background: linear-gradient(135deg, #1a1a1a 0%, #2a2a2a 100%);
  border: 1px solid #dc2626;
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 20px;
  position: relative;
  overflow: hidden;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(90deg, #dc2626, #ef4444, #dc2626);
    animation: pulse 2s ease-in-out infinite;
  }

  @keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.7; }
  }
`;

const WeeklyStats = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
  gap: 16px;
  margin-top: 12px;
`;

const StatCard = styled.div`
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 6px;
  padding: 12px;
  text-align: center;
`;

const StatValue = styled.div`
  font-size: 18px;
  font-weight: 700;
  color: #ffffff;
  margin-bottom: 4px;
`;

const StatLabel = styled.div`
  font-size: 12px;
  color: #9ca3af;
  text-transform: uppercase;
  letter-spacing: 0.5px;
`;

const ActionItemsList = styled.div`
  display: flex;
  flex-direction: column;
  gap: 12px;
`;

const ActionItemCard = styled.div<{ priority: 'high' | 'medium' | 'low'; confidence: number }>`
  background: ${({ priority }) => {
    switch (priority) {
      case 'high': return 'linear-gradient(135deg, rgba(220, 38, 38, 0.1), rgba(239, 68, 68, 0.05))';
      case 'medium': return 'linear-gradient(135deg, rgba(245, 158, 11, 0.1), rgba(251, 191, 36, 0.05))';
      case 'low': return 'linear-gradient(135deg, rgba(107, 114, 128, 0.1), rgba(156, 163, 175, 0.05))';
    }
  }};
  border: 1px solid ${({ priority }) => {
    switch (priority) {
      case 'high': return 'rgba(220, 38, 38, 0.3)';
      case 'medium': return 'rgba(245, 158, 11, 0.3)';
      case 'low': return 'rgba(107, 114, 128, 0.3)';
    }
  }};
  border-radius: 8px;
  padding: 16px;
  position: relative;
`;

const ActionItemHeader = styled.div`
  display: flex;
  justify-content: between;
  align-items: flex-start;
  margin-bottom: 8px;
`;

const ActionItemDescription = styled.div`
  color: #ffffff;
  font-size: 14px;
  line-height: 1.5;
  flex: 1;
`;

const PriorityBadge = styled.div<{ priority: 'high' | 'medium' | 'low' }>`
  background: ${({ priority }) => {
    switch (priority) {
      case 'high': return 'linear-gradient(135deg, #dc2626, #ef4444)';
      case 'medium': return 'linear-gradient(135deg, #f59e0b, #fbbf24)';
      case 'low': return 'linear-gradient(135deg, #6b7280, #9ca3af)';
    }
  }};
  color: #ffffff;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 600;
  text-transform: uppercase;
  margin-left: 12px;
`;

const ConfidenceMeter = styled.div<{ confidence: number }>`
  position: absolute;
  bottom: 0;
  left: 0;
  height: 3px;
  width: ${({ confidence }) => confidence}%;
  background: linear-gradient(90deg, #dc2626, #ef4444);
  border-radius: 0 0 8px 8px;
`;

const ElementGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 16px;
`;

const ElementCard = styled.div<{ performance: 'excellent' | 'good' | 'average' | 'poor' }>`
  background: ${({ performance }) => {
    switch (performance) {
      case 'excellent': return 'linear-gradient(135deg, rgba(16, 185, 129, 0.1), rgba(5, 150, 105, 0.05))';
      case 'good': return 'linear-gradient(135deg, rgba(59, 130, 246, 0.1), rgba(37, 99, 235, 0.05))';
      case 'average': return 'linear-gradient(135deg, rgba(107, 114, 128, 0.1), rgba(75, 85, 99, 0.05))';
      case 'poor': return 'linear-gradient(135deg, rgba(239, 68, 68, 0.1), rgba(220, 38, 38, 0.05))';
    }
  }};
  border: 1px solid ${({ performance }) => {
    switch (performance) {
      case 'excellent': return 'rgba(16, 185, 129, 0.3)';
      case 'good': return 'rgba(59, 130, 246, 0.3)';
      case 'average': return 'rgba(107, 114, 128, 0.3)';
      case 'poor': return 'rgba(239, 68, 68, 0.3)';
    }
  }};
  border-radius: 8px;
  padding: 16px;
`;

const ElementName = styled.div`
  color: #ffffff;
  font-size: 16px;
  font-weight: 600;
  margin-bottom: 8px;
`;

const ElementStats = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
`;

const ElementWinRate = styled.div`
  color: #ffffff;
  font-size: 14px;
  font-weight: 500;
`;

const ElementTrades = styled.div`
  color: #9ca3af;
  font-size: 12px;
`;

const getElementPerformanceLevel = (winRate: number, totalTrades: number): 'excellent' | 'good' | 'average' | 'poor' => {
  if (totalTrades < 3) return 'average';
  if (winRate >= 70) return 'excellent';
  if (winRate >= 60) return 'good';
  if (winRate >= 45) return 'average';
  return 'poor';
};

/**
 * Dynamic Trading Plan Component
 */
export const DynamicTradingPlan: React.FC<DynamicTradingPlanProps> = ({
  isLoading = false,
  error = null,
  onRefresh,
  className,
}) => {
  const { analytics, isLoading: analyticsLoading, error: analyticsError } = usePDArrayAnalytics();

  const loading = isLoading || analyticsLoading;
  const displayError = error || analyticsError;

  // Loading state
  if (loading) {
    return (
      <Card title="🎯 Dynamic Trading Plan">
        <div style={{ padding: '24px', textAlign: 'center' }}>
          Analyzing your PD Array performance...
        </div>
      </Card>
    );
  }

  // Error state
  if (displayError) {
    return (
      <Card title="🎯 Dynamic Trading Plan">
        <div style={{ padding: '24px', textAlign: 'center', color: '#f44336' }}>
          Error: {displayError}
          {onRefresh && (
            <button
              onClick={onRefresh}
              style={{
                marginLeft: '16px',
                padding: '8px 16px',
                background: '#f0f0f0',
                border: 'none',
                borderRadius: '4px',
                cursor: 'pointer',
              }}
            >
              Retry
            </button>
          )}
        </div>
      </Card>
    );
  }

  const { weeklyComparison, dynamicActionItems, elementPerformance, combinationPerformance, totalAnalyzedTrades } = analytics;

  return (
    <Card
      title="🎯 Dynamic Trading Plan"
      actions={
        onRefresh ? (
          <button
            onClick={onRefresh}
            style={{ background: 'none', border: 'none', cursor: 'pointer', color: 'inherit' }}
          >
            🔄 Refresh
          </button>
        ) : undefined
      }
    >
      <Container className={className}>
        {/* Weekly Performance Comparison */}
        <WeeklyComparisonHeader>
          <SectionTitle>📊 This Week's Performance</SectionTitle>
          <WeeklyStats>
            <StatCard>
              <StatValue>{weeklyComparison.currentWeek.totalTrades}</StatValue>
              <StatLabel>PD Array Trades</StatLabel>
            </StatCard>
            <StatCard>
              <StatValue>{weeklyComparison.currentWeek.winRate.toFixed(1)}%</StatValue>
              <StatLabel>Win Rate</StatLabel>
            </StatCard>
            <StatCard>
              <StatValue>{totalAnalyzedTrades}</StatValue>
              <StatLabel>Total Analyzed</StatLabel>
            </StatCard>
          </WeeklyStats>
        </WeeklyComparisonHeader>

        {/* Dynamic Action Items */}
        <Section>
          <SectionTitle>🎯 Performance-Driven Action Items</SectionTitle>
          <ActionItemsList>
            {dynamicActionItems.map((item) => (
              <ActionItemCard key={item.id} priority={item.priority} confidence={item.confidence}>
                <ActionItemHeader>
                  <ActionItemDescription>{item.description}</ActionItemDescription>
                  <PriorityBadge priority={item.priority}>{item.priority}</PriorityBadge>
                </ActionItemHeader>
                <ConfidenceMeter confidence={item.confidence} />
              </ActionItemCard>
            ))}
          </ActionItemsList>
        </Section>

        {/* PD Array Element Performance */}
        <Section>
          <SectionTitle>📈 PD Array Element Performance</SectionTitle>
          <ElementGrid>
            {elementPerformance.filter(e => e.totalTrades > 0).map((element) => (
              <ElementCard 
                key={element.element} 
                performance={getElementPerformanceLevel(element.winRate, element.totalTrades)}
              >
                <ElementName>{element.element}</ElementName>
                <ElementStats>
                  <ElementWinRate>{element.winRate.toFixed(1)}% Win Rate</ElementWinRate>
                  <ElementTrades>{element.totalTrades} trades</ElementTrades>
                </ElementStats>
              </ElementCard>
            ))}
          </ElementGrid>
          {elementPerformance.filter(e => e.totalTrades > 0).length === 0 && (
            <div style={{ textAlign: 'center', color: '#9ca3af', padding: '20px' }}>
              Start logging PD Array elements in your trade notes to see performance analysis
            </div>
          )}
        </Section>

        {/* Best Combinations */}
        {combinationPerformance.filter(c => c.totalTrades > 0).length > 0 && (
          <Section>
            <SectionTitle>🔥 Best Element Combinations</SectionTitle>
            <ElementGrid>
              {combinationPerformance
                .filter(c => c.totalTrades > 0)
                .sort((a, b) => b.winRate - a.winRate)
                .slice(0, 4)
                .map((combo) => (
                  <ElementCard 
                    key={combo.combination} 
                    performance={combo.effectiveness === 'excellent' ? 'excellent' : 
                                combo.effectiveness === 'good' ? 'good' : 
                                combo.effectiveness === 'average' ? 'average' : 'poor'}
                  >
                    <ElementName>{combo.combination}</ElementName>
                    <ElementStats>
                      <ElementWinRate>{combo.winRate.toFixed(1)}% Win Rate</ElementWinRate>
                      <ElementTrades>{combo.totalTrades} trades</ElementTrades>
                    </ElementStats>
                  </ElementCard>
                ))}
            </ElementGrid>
          </Section>
        )}
      </Container>
    </Card>
  );
};
