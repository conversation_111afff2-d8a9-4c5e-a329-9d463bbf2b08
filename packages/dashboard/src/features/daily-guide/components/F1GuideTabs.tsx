/**
 * F1GuideTabs Component
 *
 * REFACTORED FROM: DailyGuide.tsx (158 lines → focused components)
 * F1 racing-themed tabs for guide navigation.
 *
 * BENEFITS:
 * - Focused responsibility (tab navigation only)
 * - F1 racing theme with smooth animations
 * - Consistent with other F1Tab components
 * - Better separation of concerns
 * - Reusable tab navigation pattern
 */

import React from 'react';
import styled from 'styled-components';

export type GuideTab = 'overview' | 'plan' | 'levels' | 'news';

export interface F1GuideTabsProps {
  /** Currently active tab */
  activeTab: GuideTab;
  /** Tab change handler */
  onTabChange: (tab: GuideTab) => void;
  /** Whether tabs are disabled */
  disabled?: boolean;
  /** Custom className */
  className?: string;
}

const TabsContainer = styled.div`
  display: flex;
  gap: 0;
  margin: ${({ theme }) => theme.spacing?.lg || '24px'} 0
    ${({ theme }) => theme.spacing?.xl || '32px'} 0;
  border-bottom: 1px solid ${({ theme }) => theme.colors?.border || '#4b5563'};
  position: relative;
`;

const Tab = styled.button<{ $isActive: boolean; $disabled?: boolean }>`
  padding: ${({ theme }) => theme.spacing?.md || '12px'}
    ${({ theme }) => theme.spacing?.lg || '24px'};
  border: none;
  background: transparent;
  color: ${({ $isActive, theme }) =>
    $isActive ? theme.colors?.textPrimary || '#ffffff' : theme.colors?.textSecondary || '#9ca3af'};
  cursor: ${({ $disabled }) => ($disabled ? 'not-allowed' : 'pointer')};
  transition: all 0.2s ease;
  font-weight: ${({ $isActive }) => ($isActive ? '600' : '400')};
  font-size: ${({ theme }) => theme.fontSizes?.md || '1rem'};
  position: relative;
  border-bottom: 2px solid transparent;
  text-transform: uppercase;
  letter-spacing: 0.025em;
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
  display: flex;
  align-items: center;
  gap: ${({ theme }) => theme.spacing?.xs || '4px'};

  /* F1 Racing active indicator */
  &::after {
    content: '';
    position: absolute;
    bottom: -1px;
    left: 0;
    right: 0;
    height: 2px;
    background: ${({ theme }) => theme.colors?.primary || '#dc2626'};
    transform: scaleX(${({ $isActive }) => ($isActive ? 1 : 0)});
    transition: transform 0.2s ease;
    transform-origin: center;
  }

  /* F1 Racing hover effect */
  &:hover:not(:disabled) {
    color: ${({ $isActive, theme }) =>
      $isActive ? theme.colors?.textPrimary || '#ffffff' : theme.colors?.textPrimary || '#ffffff'};
    transform: translateY(-1px);

    &::after {
      transform: scaleX(1);
      background: ${({ $isActive, theme }) =>
        $isActive ? theme.colors?.primary || '#dc2626' : theme.colors?.textSecondary || '#9ca3af'};
    }
  }

  &:active:not(:disabled) {
    transform: translateY(0);
  }

  /* Disabled styling */
  ${({ $disabled }) =>
    $disabled &&
    `
    opacity: 0.5;
    cursor: not-allowed;
  `}

  /* Mobile responsive */
  @media (max-width: 768px) {
    padding: ${({ theme }) => theme.spacing?.sm || '8px'}
      ${({ theme }) => theme.spacing?.md || '12px'};
    font-size: ${({ theme }) => theme.fontSizes?.sm || '0.875rem'};
  }
`;

const TabIcon = styled.span`
  font-size: 16px;

  @media (max-width: 768px) {
    font-size: 14px;
  }
`;

const TabLabel = styled.span`
  @media (max-width: 768px) {
    display: none;
  }
`;

/**
 * Tab configuration with icons and labels
 */
const TAB_CONFIG: Record<GuideTab, { icon: string; label: string; description: string }> = {
  overview: {
    icon: '🎯',
    label: 'Session Focus',
    description: 'Personalized session analytics and recommendations',
  },
  plan: {
    icon: '📋',
    label: 'Trading Plan',
    description: 'Daily trading checklist and strategy',
  },
  levels: {
    icon: '🎯',
    label: 'Key Levels',
    description: 'Important support and resistance levels',
  },
  news: {
    icon: '📰',
    label: 'Market News',
    description: 'Latest market news and events',
  },
};

/**
 * F1GuideTabs Component
 *
 * PATTERN: F1 Tabs Pattern
 * - Racing-inspired styling with red accents
 * - Smooth hover animations and transitions
 * - Clear visual feedback for active state
 * - Accessible keyboard navigation
 * - Responsive design for mobile
 */
export const F1GuideTabs: React.FC<F1GuideTabsProps> = ({
  activeTab,
  onTabChange,
  disabled = false,
  className,
}) => {
  const handleTabClick = (tab: GuideTab) => {
    if (!disabled) {
      onTabChange(tab);
    }
  };

  const handleKeyDown = (event: React.KeyboardEvent, tab: GuideTab) => {
    if ((event.key === 'Enter' || event.key === ' ') && !disabled) {
      event.preventDefault();
      onTabChange(tab);
    }
  };

  return (
    <TabsContainer className={className} role="tablist">
      {(Object.keys(TAB_CONFIG) as GuideTab[]).map((tab) => {
        const config = TAB_CONFIG[tab];
        const isActive = activeTab === tab;

        return (
          <Tab
            key={tab}
            $isActive={isActive}
            $disabled={disabled}
            onClick={() => handleTabClick(tab)}
            onKeyDown={(e) => handleKeyDown(e, tab)}
            disabled={disabled}
            role="tab"
            aria-selected={isActive}
            aria-controls={`guide-panel-${tab}`}
            tabIndex={disabled ? -1 : 0}
            title={config.description}
          >
            <TabIcon>{config.icon}</TabIcon>
            <TabLabel>{config.label}</TabLabel>
          </Tab>
        );
      })}
    </TabsContainer>
  );
};

export default F1GuideTabs;
