/**
 * F1DashboardContainer Component
 *
 * REFACTORED FROM: TradingDashboard.tsx (388 lines → focused components)
 * Main orchestrator for trading dashboard with F1 container pattern.
 *
 * BENEFITS:
 * - Uses F1Container for consistent styling
 * - Separates orchestration from presentation
 * - Better error handling and loading states
 * - Follows proven container pattern
 * - F1 racing theme integration
 */

import React, { Suspense, useState } from 'react';
import styled from 'styled-components';
import { TradeFormData } from '@adhd-trading-dashboard/shared';
import { useTradingDashboard } from '../hooks/useTradingDashboard';
import { F1DashboardHeader } from './F1DashboardHeader';
import { F1DashboardTabs } from './F1DashboardTabs';
import { useDashboardNavigation } from './useDashboardNavigation';
import { DashboardTabContentRenderer } from './dashboardTabConfig';

export interface F1DashboardContainerProps {
  /** Custom className */
  className?: string;
  /** Initial tab to display */
  initialTab?: 'summary' | 'trades' | 'setups' | 'analytics';
}

const Container = styled.div`
  display: flex;
  flex-direction: column;
  gap: ${({ theme }) => theme.spacing?.lg || '24px'};
  background: ${({ theme }) => theme.colors?.background || '#0f0f0f'};
  color: ${({ theme }) => theme.colors?.textPrimary || '#ffffff'};
  min-height: 100vh;
  padding: ${({ theme }) => theme.spacing?.lg || '24px'};
  max-width: 1400px;
  margin: 0 auto;
`;

const ContentArea = styled.div`
  display: flex;
  flex-direction: column;
  gap: ${({ theme }) => theme.spacing?.lg || '24px'};
  flex: 1;
`;

const TabContentContainer = styled.div`
  animation: fadeIn 0.3s ease-in-out;

  @keyframes fadeIn {
    from {
      opacity: 0;
      transform: translateY(10px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }
`;

const LoadingState = styled.div`
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: ${({ theme }) => theme.spacing?.xl || '48px'};
  text-align: center;
  min-height: 400px;
`;

const LoadingIcon = styled.div`
  font-size: 48px;
  margin-bottom: ${({ theme }) => theme.spacing?.md || '12px'};
  opacity: 0.7;
  animation: pulse 2s infinite;

  @keyframes pulse {
    0%,
    100% {
      opacity: 0.7;
    }
    50% {
      opacity: 0.3;
    }
  }
`;

const LoadingText = styled.p`
  font-size: ${({ theme }) => theme.fontSizes?.lg || '1.125rem'};
  color: ${({ theme }) => theme.colors?.textSecondary || '#9ca3af'};
  margin: 0;
`;

const ErrorState = styled.div`
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: ${({ theme }) => theme.spacing?.xl || '48px'};
  text-align: center;
  min-height: 400px;
  background: ${({ theme }) => theme.colors?.error || '#ef4444'}10;
  border: 1px solid ${({ theme }) => theme.colors?.error || '#ef4444'}40;
  border-radius: ${({ theme }) => theme.borderRadius?.lg || '8px'};
  margin: ${({ theme }) => theme.spacing?.lg || '24px'} 0;
`;

const ErrorIcon = styled.div`
  font-size: 48px;
  margin-bottom: ${({ theme }) => theme.spacing?.md || '12px'};
  color: ${({ theme }) => theme.colors?.error || '#ef4444'};
`;

const ErrorTitle = styled.h3`
  font-size: ${({ theme }) => theme.fontSizes?.lg || '1.125rem'};
  font-weight: 600;
  color: ${({ theme }) => theme.colors?.error || '#ef4444'};
  margin: 0 0 ${({ theme }) => theme.spacing?.sm || '8px'} 0;
`;

const ErrorMessage = styled.p`
  font-size: ${({ theme }) => theme.fontSizes?.sm || '0.875rem'};
  color: ${({ theme }) => theme.colors?.textSecondary || '#9ca3af'};
  margin: 0;
  max-width: 400px;
`;

const RetryButton = styled.button`
  margin-top: ${({ theme }) => theme.spacing?.md || '12px'};
  padding: ${({ theme }) => theme.spacing?.sm || '8px'}
    ${({ theme }) => theme.spacing?.md || '12px'};
  background: ${({ theme }) => theme.colors?.primary || '#dc2626'};
  color: white;
  border: none;
  border-radius: ${({ theme }) => theme.borderRadius?.md || '6px'};
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;

  &:hover {
    background: ${({ theme }) => theme.colors?.primaryDark || '#b91c1c'};
    transform: translateY(-1px);
  }
`;

const LoadingOverlay = styled.div`
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: ${({ theme }) => theme.zIndex?.modal || 1000};
`;

const LoadingSpinner = styled.div`
  border: 4px solid rgba(255, 255, 255, 0.3);
  border-top: 4px solid ${({ theme }) => theme.colors?.primary || '#dc2626'};
  border-radius: 50%;
  width: 40px;
  height: 40px;
  animation: spin 1s linear infinite;

  @keyframes spin {
    0% {
      transform: rotate(0deg);
    }
    100% {
      transform: rotate(360deg);
    }
  }
`;

/**
 * LoadingFallback Component
 */
const LoadingFallback: React.FC = () => (
  <LoadingState>
    <LoadingIcon>🏎️</LoadingIcon>
    <LoadingText>Loading Trading Dashboard...</LoadingText>
  </LoadingState>
);

/**
 * ErrorFallback Component
 */
const ErrorFallback: React.FC<{ error: string; onRetry: () => void }> = ({ error, onRetry }) => (
  <ErrorState>
    <ErrorIcon>⚠️</ErrorIcon>
    <ErrorTitle>Dashboard Error</ErrorTitle>
    <ErrorMessage>{error}</ErrorMessage>
    <RetryButton onClick={onRetry}>Try Again</RetryButton>
  </ErrorState>
);

/**
 * DashboardContent Component
 */
const DashboardContent: React.FC<F1DashboardContainerProps> = ({ initialTab }) => {
  const {
    trades,
    performanceMetrics,
    chartData,
    setupPerformance,
    sessionPerformance,
    isLoading,
    error,
    fetchDashboardData,
    completeTradeData, // Get original data for RecentTradesTable
  } = useTradingDashboard();

  const { activeTab, setActiveTab } = useDashboardNavigation({
    defaultTab: initialTab || 'summary',
  });

  // Trade form state for Analytics tab
  const [tradeFormValues, setTradeFormValues] = useState<TradeFormData>({
    date: new Date().toISOString().split('T')[0],
    symbol: 'MNQ',
    direction: 'long',
    quantity: '1',
    entryPrice: '0',
    exitPrice: '0',
    profit: '0',
    model: '',
    session: '',
    setup: '',
    patternQuality: '',
    dolTarget: '',
    rdType: '',
    drawOnLiquidity: '',
    entryVersion: '',
    notes: '',
    tags: [],
    result: 'win',
  });

  const handleTradeFormChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    const { name, value, type } = e.target;
    setTradeFormValues((prev) => ({
      ...prev,
      [name]: type === 'number' ? value : value,
    }));
  };

  // Prepare data and handlers for tab content
  const tabContentProps = {
    activeTab,
    data: {
      trades, // Keep DashboardTrade[] for metrics calculations
      performanceMetrics,
      chartData,
      setupPerformance,
      sessionPerformance,
      completeTradeData, // Add CompleteTradeData[] for RecentTradesTable
    },
    isLoading,
    error,
    tradeFormValues,
    handleTradeFormChange,
  };

  if (error) {
    return <ErrorFallback error={error} onRetry={fetchDashboardData} />;
  }

  return (
    <Container>
      {/* F1 Racing Header */}
      <F1DashboardHeader
        isLoading={isLoading}
        sessionNumber={1}
        isLiveSession={true}
        onRefresh={fetchDashboardData}
      />

      {/* F1 Racing Tabs */}
      <F1DashboardTabs activeTab={activeTab} onTabChange={setActiveTab} disabled={isLoading} />

      {/* Tab Content */}
      <ContentArea>
        <TabContentContainer>
          <Suspense fallback={<LoadingFallback />}>
            <DashboardTabContentRenderer {...tabContentProps} />
          </Suspense>
        </TabContentContainer>
      </ContentArea>

      {/* Loading Overlay */}
      {isLoading && (
        <LoadingOverlay>
          <LoadingSpinner />
        </LoadingOverlay>
      )}
    </Container>
  );
};

/**
 * F1DashboardContainer Component
 *
 * PATTERN: F1 Container Pattern
 * - Error boundaries and loading states
 * - Consistent F1 styling and theme
 * - Proper separation of concerns
 * - Suspense for code splitting
 */
export const F1DashboardContainer: React.FC<F1DashboardContainerProps> = (props) => {
  return (
    <Suspense fallback={<LoadingFallback />}>
      <DashboardContent {...props} />
    </Suspense>
  );
};

export default F1DashboardContainer;
