/**
 * Daily Guide Page
 *
 * FIXED: Now uses the actual Daily Guide feature with Session Focus
 * instead of the placeholder implementation.
 */

import React from 'react';
import { DailyGuide as FeatureDailyGuide } from '../features/daily-guide';

/**
 * DailyGuide Page
 *
 * Routes to the actual Daily Guide feature implementation
 * which includes Session Focus and real trade data analytics.
 */
const DailyGuide: React.FC = () => {
  return <FeatureDailyGuide />;
};

export default DailyGuide;
