{"fileNames": ["../../node_modules/typescript/lib/lib.es5.d.ts", "../../node_modules/typescript/lib/lib.es2015.d.ts", "../../node_modules/typescript/lib/lib.es2016.d.ts", "../../node_modules/typescript/lib/lib.es2017.d.ts", "../../node_modules/typescript/lib/lib.es2018.d.ts", "../../node_modules/typescript/lib/lib.es2019.d.ts", "../../node_modules/typescript/lib/lib.es2020.d.ts", "../../node_modules/typescript/lib/lib.es2021.d.ts", "../../node_modules/typescript/lib/lib.es2022.d.ts", "../../node_modules/typescript/lib/lib.es2023.d.ts", "../../node_modules/typescript/lib/lib.es2024.d.ts", "../../node_modules/typescript/lib/lib.esnext.d.ts", "../../node_modules/typescript/lib/lib.dom.d.ts", "../../node_modules/typescript/lib/lib.dom.iterable.d.ts", "../../node_modules/typescript/lib/lib.es2015.core.d.ts", "../../node_modules/typescript/lib/lib.es2015.collection.d.ts", "../../node_modules/typescript/lib/lib.es2015.generator.d.ts", "../../node_modules/typescript/lib/lib.es2015.iterable.d.ts", "../../node_modules/typescript/lib/lib.es2015.promise.d.ts", "../../node_modules/typescript/lib/lib.es2015.proxy.d.ts", "../../node_modules/typescript/lib/lib.es2015.reflect.d.ts", "../../node_modules/typescript/lib/lib.es2015.symbol.d.ts", "../../node_modules/typescript/lib/lib.es2015.symbol.wellknown.d.ts", "../../node_modules/typescript/lib/lib.es2016.array.include.d.ts", "../../node_modules/typescript/lib/lib.es2016.intl.d.ts", "../../node_modules/typescript/lib/lib.es2017.arraybuffer.d.ts", "../../node_modules/typescript/lib/lib.es2017.date.d.ts", "../../node_modules/typescript/lib/lib.es2017.object.d.ts", "../../node_modules/typescript/lib/lib.es2017.sharedmemory.d.ts", "../../node_modules/typescript/lib/lib.es2017.string.d.ts", "../../node_modules/typescript/lib/lib.es2017.intl.d.ts", "../../node_modules/typescript/lib/lib.es2017.typedarrays.d.ts", "../../node_modules/typescript/lib/lib.es2018.asyncgenerator.d.ts", "../../node_modules/typescript/lib/lib.es2018.asynciterable.d.ts", "../../node_modules/typescript/lib/lib.es2018.intl.d.ts", "../../node_modules/typescript/lib/lib.es2018.promise.d.ts", "../../node_modules/typescript/lib/lib.es2018.regexp.d.ts", "../../node_modules/typescript/lib/lib.es2019.array.d.ts", "../../node_modules/typescript/lib/lib.es2019.object.d.ts", "../../node_modules/typescript/lib/lib.es2019.string.d.ts", "../../node_modules/typescript/lib/lib.es2019.symbol.d.ts", "../../node_modules/typescript/lib/lib.es2019.intl.d.ts", "../../node_modules/typescript/lib/lib.es2020.bigint.d.ts", "../../node_modules/typescript/lib/lib.es2020.date.d.ts", "../../node_modules/typescript/lib/lib.es2020.promise.d.ts", "../../node_modules/typescript/lib/lib.es2020.sharedmemory.d.ts", "../../node_modules/typescript/lib/lib.es2020.string.d.ts", "../../node_modules/typescript/lib/lib.es2020.symbol.wellknown.d.ts", "../../node_modules/typescript/lib/lib.es2020.intl.d.ts", "../../node_modules/typescript/lib/lib.es2020.number.d.ts", "../../node_modules/typescript/lib/lib.es2021.promise.d.ts", "../../node_modules/typescript/lib/lib.es2021.string.d.ts", "../../node_modules/typescript/lib/lib.es2021.weakref.d.ts", "../../node_modules/typescript/lib/lib.es2021.intl.d.ts", "../../node_modules/typescript/lib/lib.es2022.array.d.ts", "../../node_modules/typescript/lib/lib.es2022.error.d.ts", "../../node_modules/typescript/lib/lib.es2022.intl.d.ts", "../../node_modules/typescript/lib/lib.es2022.object.d.ts", "../../node_modules/typescript/lib/lib.es2022.string.d.ts", "../../node_modules/typescript/lib/lib.es2022.regexp.d.ts", "../../node_modules/typescript/lib/lib.es2023.array.d.ts", "../../node_modules/typescript/lib/lib.es2023.collection.d.ts", "../../node_modules/typescript/lib/lib.es2023.intl.d.ts", "../../node_modules/typescript/lib/lib.es2024.arraybuffer.d.ts", "../../node_modules/typescript/lib/lib.es2024.collection.d.ts", "../../node_modules/typescript/lib/lib.es2024.object.d.ts", "../../node_modules/typescript/lib/lib.es2024.promise.d.ts", "../../node_modules/typescript/lib/lib.es2024.regexp.d.ts", "../../node_modules/typescript/lib/lib.es2024.sharedmemory.d.ts", "../../node_modules/typescript/lib/lib.es2024.string.d.ts", "../../node_modules/typescript/lib/lib.esnext.array.d.ts", "../../node_modules/typescript/lib/lib.esnext.collection.d.ts", "../../node_modules/typescript/lib/lib.esnext.intl.d.ts", "../../node_modules/typescript/lib/lib.esnext.disposable.d.ts", "../../node_modules/typescript/lib/lib.esnext.promise.d.ts", "../../node_modules/typescript/lib/lib.esnext.decorators.d.ts", "../../node_modules/typescript/lib/lib.esnext.iterator.d.ts", "../../node_modules/typescript/lib/lib.esnext.float16.d.ts", "../../node_modules/typescript/lib/lib.decorators.d.ts", "../../node_modules/typescript/lib/lib.decorators.legacy.d.ts", "../../node_modules/@types/react/global.d.ts", "../../node_modules/csstype/index.d.ts", "../../node_modules/@types/prop-types/index.d.ts", "../../node_modules/@types/react/index.d.ts", "../../node_modules/@types/react/jsx-runtime.d.ts", "../../node_modules/@remix-run/router/dist/history.d.ts", "../../node_modules/@remix-run/router/dist/utils.d.ts", "../../node_modules/@remix-run/router/dist/router.d.ts", "../../node_modules/@remix-run/router/dist/index.d.ts", "../../node_modules/react-router/dist/lib/context.d.ts", "../../node_modules/react-router/dist/lib/components.d.ts", "../../node_modules/react-router/dist/lib/hooks.d.ts", "../../node_modules/react-router/dist/index.d.ts", "../../node_modules/react-router-dom/dist/dom.d.ts", "../../node_modules/react-router-dom/dist/index.d.ts", "../shared/dist/types/trading.d.ts", "../shared/dist/types/tradingsessions.d.ts", "../shared/dist/types/index.d.ts", "../shared/dist/constants/setupelements.d.ts", "../shared/dist/constants/index.d.ts", "../shared/dist/components/atoms/badge.d.ts", "../shared/dist/components/atoms/button.d.ts", "../shared/dist/components/atoms/input.d.ts", "../shared/dist/components/atoms/loadingplaceholder.d.ts", "../shared/dist/components/atoms/select.d.ts", "../shared/dist/components/atoms/statusindicator.d.ts", "../shared/dist/components/atoms/tag.d.ts", "../shared/dist/components/atoms/timepicker.d.ts", "../shared/dist/components/atoms/selectdropdown.d.ts", "../shared/dist/components/atoms/loadingcell.d.ts", "../shared/dist/components/atoms/loadingspinner.d.ts", "../shared/dist/components/atoms/index.d.ts", "../shared/dist/components/molecules/card.d.ts", "../shared/dist/components/molecules/emptystate.d.ts", "../shared/dist/components/molecules/errorboundary.d.ts", "../shared/dist/components/molecules/unifiederrorboundary.d.ts", "../shared/dist/components/molecules/tabpanel.d.ts", "../shared/dist/hooks/useformfield.d.ts", "../shared/dist/components/molecules/enhancedformfield.d.ts", "../shared/dist/hooks/usesortabletable.d.ts", "../shared/dist/components/molecules/sortabletable.d.ts", "../shared/dist/components/molecules/formfield.d.ts", "../shared/dist/components/molecules/modal.d.ts", "../shared/dist/components/molecules/table.d.ts", "../shared/dist/components/molecules/hierarchicalsessionselector.d.ts", "../shared/dist/components/molecules/tradetablecolumns.d.ts", "../shared/dist/components/molecules/tradetable.d.ts", "../shared/dist/components/molecules/tradetablerow.d.ts", "../shared/dist/components/molecules/tradetablefilters.d.ts", "../shared/dist/components/molecules/index.d.ts", "../shared/dist/components/organisms/datacard.d.ts", "../shared/dist/components/organisms/dashboardsection.d.ts", "../shared/dist/components/organisms/index.d.ts", "../shared/dist/components/templates/dashboardtemplate.d.ts", "../shared/dist/components/templates/index.d.ts", "../shared/dist/components/trade/setupbuilder.d.ts", "../shared/dist/components/trade/trademetrics.d.ts", "../shared/dist/components/trade/tradeanalysis.d.ts", "../shared/dist/components/trade/types.d.ts", "../shared/dist/components/trade/index.d.ts", "../shared/dist/components/library/headers/f1header.d.ts", "../shared/dist/components/library/containers/f1container.d.ts", "../shared/dist/components/library/forms/f1form.d.ts", "../shared/dist/components/library/forms/f1formfield.d.ts", "../shared/dist/hooks/useloadingstate.d.ts", "../shared/dist/components/library/index.d.ts", "../shared/dist/components/index.d.ts", "../shared/dist/hooks/useasyncdata.d.ts", "../shared/dist/hooks/usedebounce.d.ts", "../shared/dist/hooks/useerrorhandler.d.ts", "../shared/dist/hooks/uselocalstorage.d.ts", "../shared/dist/hooks/usepagination.d.ts", "../shared/dist/hooks/useprofitlossformatting.d.ts", "../shared/dist/hooks/usedatasection.d.ts", "../shared/dist/hooks/usedataformatting.d.ts", "../shared/dist/hooks/usesessionselection.d.ts", "../shared/dist/hooks/index.d.ts", "../shared/dist/theme/types.d.ts", "../../node_modules/@types/hoist-non-react-statics/index.d.ts", "../../node_modules/@types/styled-components/index.d.ts", "../shared/dist/theme/profitlosstheme.d.ts", "../shared/dist/theme/tokens.d.ts", "../shared/dist/theme/f1theme.d.ts", "../shared/dist/theme/lighttheme.d.ts", "../shared/dist/theme/darktheme.d.ts", "../shared/dist/theme/themeprovider.d.ts", "../shared/dist/theme/index.d.ts", "../shared/dist/state/createstorecontext.d.ts", "../shared/dist/state/createselector.d.ts", "../shared/dist/services/persiststate.d.ts", "../shared/dist/state/index.d.ts", "../shared/dist/utils/sessionutils.d.ts", "../shared/dist/utils/index.d.ts", "../shared/dist/monitoring/index.d.ts", "../shared/dist/services/tradestorage.d.ts", "../shared/dist/services/tradestorageinterface.d.ts", "../shared/dist/services/index.d.ts", "../shared/dist/contracts/tradejournalcontract.d.ts", "../shared/dist/contracts/tradingdashboardcontract.d.ts", "../shared/dist/contracts/tradeanalysiscontract.d.ts", "../shared/dist/contracts/index.d.ts", "../shared/dist/index.d.ts", "./src/layouts/sidebar.tsx", "./src/layouts/header.tsx", "./src/layouts/mainlayout.tsx", "./src/components/molecules/loadingscreen.tsx", "./src/services/transformers/setuptransformer.ts", "./src/features/trading-dashboard/types/index.ts", "./src/features/trading-dashboard/hooks/usetradingdashboard.ts", "./src/features/trading-dashboard/components/f1dashboardheader.tsx", "./src/features/trading-dashboard/components/f1dashboardtabs.tsx", "./src/features/trading-dashboard/components/usedashboardnavigation.ts", "./src/features/trading-dashboard/components/metricspanel.tsx", "../../node_modules/recharts/types/container/surface.d.ts", "../../node_modules/recharts/types/container/layer.d.ts", "../../node_modules/@types/d3-time/index.d.ts", "../../node_modules/@types/d3-scale/index.d.ts", "../../node_modules/victory-vendor/d3-scale.d.ts", "../../node_modules/recharts/types/util/types.d.ts", "../../node_modules/recharts/types/component/defaultlegendcontent.d.ts", "../../node_modules/recharts/types/util/payload/getuniqpayload.d.ts", "../../node_modules/recharts/types/component/legend.d.ts", "../../node_modules/recharts/types/component/defaulttooltipcontent.d.ts", "../../node_modules/recharts/types/component/tooltip.d.ts", "../../node_modules/recharts/types/component/responsivecontainer.d.ts", "../../node_modules/recharts/types/component/cell.d.ts", "../../node_modules/recharts/types/component/text.d.ts", "../../node_modules/recharts/types/component/label.d.ts", "../../node_modules/recharts/types/component/labellist.d.ts", "../../node_modules/recharts/types/component/customized.d.ts", "../../node_modules/recharts/types/shape/sector.d.ts", "../../node_modules/@types/d3-path/index.d.ts", "../../node_modules/@types/d3-shape/index.d.ts", "../../node_modules/victory-vendor/d3-shape.d.ts", "../../node_modules/recharts/types/shape/curve.d.ts", "../../node_modules/recharts/types/shape/rectangle.d.ts", "../../node_modules/recharts/types/shape/polygon.d.ts", "../../node_modules/recharts/types/shape/dot.d.ts", "../../node_modules/recharts/types/shape/cross.d.ts", "../../node_modules/recharts/types/shape/symbols.d.ts", "../../node_modules/recharts/types/polar/polargrid.d.ts", "../../node_modules/recharts/types/polar/polarradiusaxis.d.ts", "../../node_modules/recharts/types/polar/polarangleaxis.d.ts", "../../node_modules/recharts/types/polar/pie.d.ts", "../../node_modules/recharts/types/polar/radar.d.ts", "../../node_modules/recharts/types/polar/radialbar.d.ts", "../../node_modules/recharts/types/cartesian/brush.d.ts", "../../node_modules/recharts/types/util/ifoverflowmatches.d.ts", "../../node_modules/recharts/types/cartesian/xaxis.d.ts", "../../node_modules/recharts/types/cartesian/yaxis.d.ts", "../../node_modules/recharts/types/cartesian/referenceline.d.ts", "../../node_modules/recharts/types/cartesian/referencedot.d.ts", "../../node_modules/recharts/types/cartesian/referencearea.d.ts", "../../node_modules/recharts/types/cartesian/cartesianaxis.d.ts", "../../node_modules/recharts/types/cartesian/cartesiangrid.d.ts", "../../node_modules/recharts/types/cartesian/line.d.ts", "../../node_modules/recharts/types/cartesian/area.d.ts", "../../node_modules/recharts/types/cartesian/bar.d.ts", "../../node_modules/recharts/types/cartesian/zaxis.d.ts", "../../node_modules/recharts/types/cartesian/errorbar.d.ts", "../../node_modules/recharts/types/cartesian/scatter.d.ts", "../../node_modules/@types/lodash/common/common.d.ts", "../../node_modules/@types/lodash/common/array.d.ts", "../../node_modules/@types/lodash/common/collection.d.ts", "../../node_modules/@types/lodash/common/date.d.ts", "../../node_modules/@types/lodash/common/function.d.ts", "../../node_modules/@types/lodash/common/lang.d.ts", "../../node_modules/@types/lodash/common/math.d.ts", "../../node_modules/@types/lodash/common/number.d.ts", "../../node_modules/@types/lodash/common/object.d.ts", "../../node_modules/@types/lodash/common/seq.d.ts", "../../node_modules/@types/lodash/common/string.d.ts", "../../node_modules/@types/lodash/common/util.d.ts", "../../node_modules/@types/lodash/index.d.ts", "../../node_modules/recharts/types/util/getlegendprops.d.ts", "../../node_modules/recharts/types/util/chartutils.d.ts", "../../node_modules/recharts/types/chart/accessibilitymanager.d.ts", "../../node_modules/recharts/types/util/cursor/getradialcursorpoints.d.ts", "../../node_modules/recharts/types/chart/generatecategoricalchart.d.ts", "../../node_modules/recharts/types/chart/linechart.d.ts", "../../node_modules/recharts/types/chart/barchart.d.ts", "../../node_modules/recharts/types/chart/piechart.d.ts", "../../node_modules/recharts/types/chart/treemap.d.ts", "../../node_modules/recharts/types/chart/sankey.d.ts", "../../node_modules/recharts/types/chart/radarchart.d.ts", "../../node_modules/recharts/types/chart/scatterchart.d.ts", "../../node_modules/recharts/types/chart/areachart.d.ts", "../../node_modules/recharts/types/chart/radialbarchart.d.ts", "../../node_modules/recharts/types/chart/composedchart.d.ts", "../../node_modules/recharts/types/shape/trapezoid.d.ts", "../../node_modules/recharts/types/numberaxis/funnel.d.ts", "../../node_modules/recharts/types/chart/funnelchart.d.ts", "../../node_modules/recharts/types/util/global.d.ts", "../../node_modules/recharts/types/index.d.ts", "./src/features/trading-dashboard/components/performancechart.tsx", "./src/features/trading-dashboard/components/recenttradestable.tsx", "./src/features/trading-dashboard/components/setupanalysis.tsx", "./src/features/trade-journal/components/trade-form/f1tradeformfield.tsx", "./src/features/trade-journal/types/index.ts", "./src/features/trade-journal/hooks/usetradevalidation.ts", "./src/features/trade-journal/components/trade-form/tradeformfieldconfig.ts", "./src/features/trade-journal/components/trade-form/tradeformfieldgroups.tsx", "./src/features/trade-journal/components/trade-form/usetradeformfields.ts", "./src/features/trade-journal/components/trade-form/tradeformbasicfieldscontainer.tsx", "./src/features/trade-journal/components/trade-form/tradeformbasicfields.tsx", "./src/features/trading-dashboard/components/dashboardtabconfig.tsx", "./src/features/trading-dashboard/components/f1dashboardcontainer.tsx", "./src/features/trading-dashboard/tradingdashboard.tsx", "./src/features/daily-guide/types/market.ts", "./src/features/daily-guide/types/trading.ts", "./src/features/daily-guide/types/data.ts", "./src/features/daily-guide/types/preferences.ts", "./src/features/daily-guide/types/index.ts", "./src/features/daily-guide/types.ts", "./src/features/daily-guide/state/dailyguidestate.ts", "./src/features/daily-guide/state/dailyguideselectors.ts", "./src/features/daily-guide/state/index.ts", "./src/features/daily-guide/hooks/usedailyguide.ts", "./src/features/daily-guide/hooks/usesessionanalytics.ts", "./src/features/daily-guide/hooks/usepdarrayanalytics.ts", "./src/features/daily-guide/hooks/index.ts", "./src/features/daily-guide/components/f1guideheader.tsx", "./src/features/daily-guide/components/f1guidetabs.tsx", "./src/features/daily-guide/components/useguidenavigation.ts", "./src/features/daily-guide/hooks/useenhancedsessionintelligence.ts", "./src/features/daily-guide/components/sessionfocus.tsx", "./src/features/daily-guide/hooks/usemodelselectionengine.ts", "./src/features/daily-guide/hooks/usepatternqualityscoring.ts", "./src/features/daily-guide/hooks/usegranularsessionintelligence.ts", "./src/features/daily-guide/hooks/usesuccessprobabilitycalculator.ts", "./src/features/daily-guide/hooks/useenhancedsetupintelligence.ts", "./src/features/daily-guide/components/eliteictintelligence.tsx", "./src/features/daily-guide/hooks/usepdarrayintelligence.ts", "./src/features/daily-guide/components/pdarraylevels.tsx", "./src/features/daily-guide/components/guidetabconfig.tsx", "./src/features/daily-guide/components/f1guidecontainer.tsx", "./src/features/daily-guide/components/dailyguide.tsx", "./src/features/trade-journal/hooks/usetradejournal.ts", "./src/features/trade-journal/hooks/usetradefilters.ts", "./src/features/trade-journal/hooks/usetradelist.ts", "./src/features/trade-journal/components/trade-list/tradelistheader.tsx", "./src/features/trade-journal/components/trade-list/tradelistrow.tsx", "./src/features/trade-journal/components/trade-list/tradelistexpandedrow.tsx", "./src/features/trade-journal/components/trade-list/tradelistempty.tsx", "./src/features/trade-journal/components/trade-list/tradelistloading.tsx", "./src/features/trade-journal/components/trade-list/index.ts", "./src/features/trade-journal/components/tradelist.tsx", "./src/features/trade-journal/components/selectdropdown.tsx", "./src/features/trade-journal/components/tabpanel.tsx", "./src/features/trade-journal/components/timepicker.tsx", "./src/features/trade-journal/components/legacydataimport.jsx", "./src/features/trade-journal/components/trade-form/tradeformheader.tsx", "./src/features/trade-journal/hooks/usetradeformdata.ts", "./src/features/trade-journal/hooks/usetradecalculations.ts", "./src/features/trade-journal/constants/patternquality.ts", "./src/features/trade-journal/hooks/usetradesubmission.ts", "./src/features/trade-journal/hooks/usetradeform.ts", "./src/features/trade-journal/hooks/index.ts", "./src/features/trade-journal/components/trade-form/tradeformtimingfields.tsx", "./src/features/trade-journal/components/trade-form/tradeformriskfields.tsx", "./src/features/trade-journal/components/trade-form/tradeformstrategyfields.tsx", "./src/features/trade-journal/components/trade-form/tradeformactions.tsx", "./src/features/trade-journal/components/trade-form/tradeformmessages.tsx", "./src/features/trade-journal/components/trade-form/tradeformloading.tsx", "./src/features/trade-journal/components/trade-form/index.ts", "./src/features/trade-journal/components/trade-journal/tradejournalheader.tsx", "./src/features/trade-journal/components/f1filterfield.tsx", "./src/features/trade-journal/components/usefilterstate.ts", "./src/features/trade-journal/components/filterfieldconfig.tsx", "./src/features/trade-journal/components/f1filterpanel.tsx", "./src/features/trade-journal/components/trade-journal/tradejournalfilters.tsx", "./src/features/trade-journal/components/trade-journal/tradejournalcontent.tsx", "./src/features/trade-journal/components/trade-journal/index.ts", "./src/features/trade-journal/components/trade-dol-analysis/tradedolanalysis.tsx", "./src/features/trade-journal/constants/dolanalysis.ts", "./src/features/trade-journal/components/trade-dol-analysis/doltypeselector.tsx", "./src/features/trade-journal/components/trade-dol-analysis/dolstrengthselector.tsx", "./src/features/trade-journal/components/trade-dol-analysis/dolreactionselector.tsx", "./src/features/trade-journal/components/trade-dol-analysis/dolcontextselector.tsx", "./src/features/trade-journal/components/trade-dol-analysis/doldetailedanalysis.tsx", "./src/features/trade-journal/components/trade-dol-analysis/doleffectivenessrating.tsx", "./src/features/trade-journal/components/trade-dol-analysis/index.ts", "./src/features/trade-journal/components/trade-pattern-quality/criterionselector.tsx", "./src/features/trade-journal/components/trade-pattern-quality/patternqualityassessment.tsx", "./src/features/trade-journal/components/trade-pattern-quality/index.ts", "./src/features/trade-journal/components/index.ts", "./src/features/trade-journal/components/f1journalheader.tsx", "./src/features/trade-journal/components/f1journaltabs.tsx", "./src/features/trade-journal/components/usejournalnavigation.ts", "./src/features/trade-journal/components/journaltabconfig.tsx", "./src/features/trade-journal/components/f1journalcontainer.tsx", "./src/features/trade-journal/tradejournal.tsx", "./src/features/trade-analysis/types.ts", "./src/features/trade-analysis/services/tradeanalysiscalculations.ts", "./src/features/trade-analysis/services/performancecache.ts", "./src/features/trade-analysis/services/realtradeanalysisapi.ts", "./src/features/trade-analysis/services/tradeanalysisapi.ts", "./src/features/trade-analysis/hooks/tradeanalysiscontext.tsx", "./src/features/trade-analysis/components/analysisheader.tsx", "./src/features/trade-analysis/components/analysistabs.tsx", "./src/features/trade-analysis/components/filterpanel.tsx", "./src/features/trade-analysis/components/performancesummary.tsx", "./src/features/trade-analysis/components/tradestableheader.tsx", "./src/features/trade-analysis/components/tradestablerow.tsx", "./src/features/trade-analysis/components/tradestablebody.tsx", "./src/features/trade-analysis/hooks/usetradestabledata.ts", "./src/features/trade-analysis/components/tradestablecontainer.tsx", "./src/features/trade-analysis/components/tradestable.tsx", "./src/features/trade-analysis/components/categoryperformancechart.tsx", "./src/features/trade-analysis/components/timeperformancechart.tsx", "./src/features/trade-analysis/components/tradedetail.tsx", "./src/features/trade-analysis/components/tabcontentrenderer.tsx", "./src/features/trade-analysis/components/tradeanalysiscontainer.tsx", "./src/features/trade-analysis/tradeanalysis.tsx", "./src/features/trade-journal/components/trade-analysis-section/tradeanalysissection.tsx", "./src/features/trade-journal/components/trade-analysis-section/index.ts", "./src/features/trade-journal/tradeform.tsx", "./src/features/settings/components/settingsheader.tsx", "./src/features/settings/components/settingsformfield.tsx", "./src/features/settings/components/settingsform.tsx", "./src/features/settings/hooks/usesettingsform.ts", "./src/features/settings/components/settingscontainer.tsx", "./src/features/settings/settings.tsx", "./src/components/notfound.tsx", "./src/routes/routes.tsx", "./src/routes/index.ts", "./src/components/apperrorboundary.tsx", "./src/app.tsx", "./src/minimalapp.tsx", "./src/simpleapp.tsx", "./src/testapp.tsx", "./src/devtools-config.js", "../../node_modules/@types/react-dom/client.d.ts", "../../node_modules/web-vitals/dist/modules/types.d.ts", "../../node_modules/web-vitals/dist/modules/getcls.d.ts", "../../node_modules/web-vitals/dist/modules/getfcp.d.ts", "../../node_modules/web-vitals/dist/modules/getfid.d.ts", "../../node_modules/web-vitals/dist/modules/getlcp.d.ts", "../../node_modules/web-vitals/dist/modules/getttfb.d.ts", "../../node_modules/web-vitals/dist/modules/index.d.ts", "./src/reportwebvitals.ts", "./src/index.tsx", "./src/simple-index.tsx", "./src/components/featureerrorboundary.tsx", "../../node_modules/file-system-cache/lib/filesystemcache.d.ts", "../../node_modules/file-system-cache/lib/index.d.ts", "../../node_modules/@babel/types/lib/index.d.ts", "../../node_modules/@types/babel__generator/index.d.ts", "../../node_modules/@babel/parser/typings/babel-parser.d.ts", "../../node_modules/@types/babel__template/index.d.ts", "../../node_modules/@types/babel__traverse/index.d.ts", "../../node_modules/@types/babel__core/index.d.ts", "../../node_modules/@types/node/assert.d.ts", "../../node_modules/@types/node/assert/strict.d.ts", "../../node_modules/@types/node/globals.d.ts", "../../node_modules/@types/node/async_hooks.d.ts", "../../node_modules/@types/node/buffer.d.ts", "../../node_modules/@types/node/child_process.d.ts", "../../node_modules/@types/node/cluster.d.ts", "../../node_modules/@types/node/console.d.ts", "../../node_modules/@types/node/constants.d.ts", "../../node_modules/@types/node/crypto.d.ts", "../../node_modules/@types/node/dgram.d.ts", "../../node_modules/@types/node/diagnostics_channel.d.ts", "../../node_modules/@types/node/dns.d.ts", "../../node_modules/@types/node/dns/promises.d.ts", "../../node_modules/@types/node/domain.d.ts", "../../node_modules/@types/node/events.d.ts", "../../node_modules/@types/node/fs.d.ts", "../../node_modules/@types/node/fs/promises.d.ts", "../../node_modules/@types/node/http.d.ts", "../../node_modules/@types/node/http2.d.ts", "../../node_modules/@types/node/https.d.ts", "../../node_modules/@types/node/inspector.d.ts", "../../node_modules/@types/node/module.d.ts", "../../node_modules/@types/node/net.d.ts", "../../node_modules/@types/node/os.d.ts", "../../node_modules/@types/node/path.d.ts", "../../node_modules/@types/node/perf_hooks.d.ts", "../../node_modules/@types/node/process.d.ts", "../../node_modules/@types/node/punycode.d.ts", "../../node_modules/@types/node/querystring.d.ts", "../../node_modules/@types/node/readline.d.ts", "../../node_modules/@types/node/repl.d.ts", "../../node_modules/@types/node/stream.d.ts", "../../node_modules/@types/node/stream/promises.d.ts", "../../node_modules/@types/node/stream/consumers.d.ts", "../../node_modules/@types/node/stream/web.d.ts", "../../node_modules/@types/node/string_decoder.d.ts", "../../node_modules/@types/node/test.d.ts", "../../node_modules/@types/node/timers.d.ts", "../../node_modules/@types/node/timers/promises.d.ts", "../../node_modules/@types/node/tls.d.ts", "../../node_modules/@types/node/trace_events.d.ts", "../../node_modules/@types/node/tty.d.ts", "../../node_modules/@types/node/url.d.ts", "../../node_modules/@types/node/util.d.ts", "../../node_modules/@types/node/v8.d.ts", "../../node_modules/@types/node/vm.d.ts", "../../node_modules/@types/node/wasi.d.ts", "../../node_modules/@types/node/worker_threads.d.ts", "../../node_modules/@types/node/zlib.d.ts", "../../node_modules/@types/node/globals.global.d.ts", "../../node_modules/@types/node/index.d.ts", "../../node_modules/@types/mime/index.d.ts", "../../node_modules/@types/send/index.d.ts", "../../node_modules/@types/qs/index.d.ts", "../../node_modules/@types/range-parser/index.d.ts", "../../node_modules/@types/express-serve-static-core/index.d.ts", "../../node_modules/@types/http-errors/index.d.ts", "../../node_modules/@types/serve-static/index.d.ts", "../../node_modules/@types/connect/index.d.ts", "../../node_modules/@types/body-parser/index.d.ts", "../../node_modules/@types/express/node_modules/@types/express-serve-static-core/index.d.ts", "../../node_modules/@types/express/index.d.ts", "../../node_modules/@storybook/react/node_modules/@storybook/channels/dist/main-c55d8855.d.ts", "../../node_modules/@storybook/react/node_modules/@storybook/channels/dist/postmessage/index.d.ts", "../../node_modules/@storybook/react/node_modules/@storybook/channels/dist/websocket/index.d.ts", "../../node_modules/@storybook/react/node_modules/@storybook/channels/dist/index.d.ts", "../../node_modules/@storybook/react/node_modules/@storybook/types/dist/index.d.ts", "../../node_modules/@storybook/react/dist/types-0fc72a6d.d.ts", "../../node_modules/type-fest/source/primitive.d.ts", "../../node_modules/type-fest/source/typed-array.d.ts", "../../node_modules/type-fest/source/basic.d.ts", "../../node_modules/type-fest/source/observable-like.d.ts", "../../node_modules/type-fest/source/internal.d.ts", "../../node_modules/type-fest/source/except.d.ts", "../../node_modules/type-fest/source/simplify.d.ts", "../../node_modules/type-fest/source/writable.d.ts", "../../node_modules/type-fest/source/mutable.d.ts", "../../node_modules/type-fest/source/merge.d.ts", "../../node_modules/type-fest/source/merge-exclusive.d.ts", "../../node_modules/type-fest/source/require-at-least-one.d.ts", "../../node_modules/type-fest/source/require-exactly-one.d.ts", "../../node_modules/type-fest/source/require-all-or-none.d.ts", "../../node_modules/type-fest/source/remove-index-signature.d.ts", "../../node_modules/type-fest/source/partial-deep.d.ts", "../../node_modules/type-fest/source/partial-on-undefined-deep.d.ts", "../../node_modules/type-fest/source/readonly-deep.d.ts", "../../node_modules/type-fest/source/literal-union.d.ts", "../../node_modules/type-fest/source/promisable.d.ts", "../../node_modules/type-fest/source/opaque.d.ts", "../../node_modules/type-fest/source/invariant-of.d.ts", "../../node_modules/type-fest/source/set-optional.d.ts", "../../node_modules/type-fest/source/set-required.d.ts", "../../node_modules/type-fest/source/set-non-nullable.d.ts", "../../node_modules/type-fest/source/value-of.d.ts", "../../node_modules/type-fest/source/promise-value.d.ts", "../../node_modules/type-fest/source/async-return-type.d.ts", "../../node_modules/type-fest/source/conditional-keys.d.ts", "../../node_modules/type-fest/source/conditional-except.d.ts", "../../node_modules/type-fest/source/conditional-pick.d.ts", "../../node_modules/type-fest/source/union-to-intersection.d.ts", "../../node_modules/type-fest/source/stringified.d.ts", "../../node_modules/type-fest/source/fixed-length-array.d.ts", "../../node_modules/type-fest/source/multidimensional-array.d.ts", "../../node_modules/type-fest/source/multidimensional-readonly-array.d.ts", "../../node_modules/type-fest/source/iterable-element.d.ts", "../../node_modules/type-fest/source/entry.d.ts", "../../node_modules/type-fest/source/entries.d.ts", "../../node_modules/type-fest/source/set-return-type.d.ts", "../../node_modules/type-fest/source/asyncify.d.ts", "../../node_modules/type-fest/source/numeric.d.ts", "../../node_modules/type-fest/source/jsonify.d.ts", "../../node_modules/type-fest/source/schema.d.ts", "../../node_modules/type-fest/source/literal-to-primitive.d.ts", "../../node_modules/type-fest/source/string-key-of.d.ts", "../../node_modules/type-fest/source/exact.d.ts", "../../node_modules/type-fest/source/readonly-tuple.d.ts", "../../node_modules/type-fest/source/optional-keys-of.d.ts", "../../node_modules/type-fest/source/has-optional-keys.d.ts", "../../node_modules/type-fest/source/required-keys-of.d.ts", "../../node_modules/type-fest/source/has-required-keys.d.ts", "../../node_modules/type-fest/source/spread.d.ts", "../../node_modules/type-fest/source/split.d.ts", "../../node_modules/type-fest/source/camel-case.d.ts", "../../node_modules/type-fest/source/camel-cased-properties.d.ts", "../../node_modules/type-fest/source/camel-cased-properties-deep.d.ts", "../../node_modules/type-fest/source/delimiter-case.d.ts", "../../node_modules/type-fest/source/kebab-case.d.ts", "../../node_modules/type-fest/source/delimiter-cased-properties.d.ts", "../../node_modules/type-fest/source/kebab-cased-properties.d.ts", "../../node_modules/type-fest/source/delimiter-cased-properties-deep.d.ts", "../../node_modules/type-fest/source/kebab-cased-properties-deep.d.ts", "../../node_modules/type-fest/source/pascal-case.d.ts", "../../node_modules/type-fest/source/pascal-cased-properties.d.ts", "../../node_modules/type-fest/source/pascal-cased-properties-deep.d.ts", "../../node_modules/type-fest/source/snake-case.d.ts", "../../node_modules/type-fest/source/snake-cased-properties.d.ts", "../../node_modules/type-fest/source/snake-cased-properties-deep.d.ts", "../../node_modules/type-fest/source/includes.d.ts", "../../node_modules/type-fest/source/screaming-snake-case.d.ts", "../../node_modules/type-fest/source/join.d.ts", "../../node_modules/type-fest/source/trim.d.ts", "../../node_modules/type-fest/source/replace.d.ts", "../../node_modules/type-fest/source/get.d.ts", "../../node_modules/type-fest/source/last-array-element.d.ts", "../../node_modules/type-fest/source/package-json.d.ts", "../../node_modules/type-fest/source/tsconfig-json.d.ts", "../../node_modules/type-fest/index.d.ts", "../../node_modules/@storybook/react/dist/index.d.ts", "./src/components/molecules/profitlosscell.tsx", "./src/components/molecules/profitlosscell.stories.tsx", "./src/features/daily-guide/api/dailyguideapi.ts", "./src/features/daily-guide/context/dailyguidecontext.tsx", "./src/features/daily-guide/components/dailyguideheader.tsx", "./src/features/daily-guide/components/ui/sectioncard.tsx", "./src/features/daily-guide/components/ui/sentimentbadge.tsx", "./src/features/daily-guide/components/ui/prioritytag.tsx", "./src/features/daily-guide/components/ui/index.ts", "./src/features/daily-guide/components/marketsummary.tsx", "./src/features/daily-guide/components/marketindicators.tsx", "./src/features/daily-guide/components/marketnews.tsx", "./src/features/daily-guide/components/marketoverview.tsx", "./src/features/daily-guide/components/tradingplanheader.tsx", "./src/features/daily-guide/components/planitemslist.tsx", "./src/features/daily-guide/components/riskmanagementgrid.tsx", "./src/features/daily-guide/components/additemform.tsx", "./src/features/daily-guide/hooks/usetradingplanform.ts", "./src/features/daily-guide/components/tradingplancontainer.tsx", "./src/features/daily-guide/components/tradingplan.tsx", "./src/features/daily-guide/components/keylevels.tsx", "./src/features/daily-guide/components/dailyguidecontainer.tsx", "./src/features/daily-guide/dailyguide.tsx", "./src/features/daily-guide/components/dynamictradingplan.tsx", "./src/features/daily-guide/components/index.ts", "./src/features/daily-guide/index.ts", "./src/features/daily-guide/hooks/useictactionplan.ts", "./src/features/daily-guide/components/ictactionplan.tsx", "./src/features/daily-guide/components/f1-guide-components.ts", "./src/features/performance-dashboard/components/metricspanel.tsx", "./src/features/performance-dashboard/components/performancechart.tsx", "./src/features/performance-dashboard/components/recenttradespanel.tsx", "./src/features/performance-dashboard/hooks/usedashboarddata.ts", "./src/features/performance-dashboard/dashboard.tsx", "./src/features/performance-dashboard/index.ts", "./src/features/settings/components/settingssection.tsx", "./src/features/settings/components/settingitem.tsx", "./src/features/settings/components/toggleswitch.tsx", "./src/features/settings/hooks/usesettings.ts", "./src/features/settings/index.ts", "./src/features/settings/components/index.ts", "./src/features/trade-analysis/components/tradeanalysistable.tsx", "./src/features/trade-analysis/components/tradeanalysissummary.tsx", "./src/features/trade-analysis/components/tradeanalysischarts.tsx", "./src/features/trade-analysis/hooks/usetradeanalysis.ts", "./src/features/trade-analysis/index.ts", "./src/features/trade-analysis/components/distributionchart.tsx", "./src/features/trade-analysis/components/equitycurve.tsx", "./src/features/trade-analysis/components/f1analysisheader.tsx", "./src/features/trade-analysis/components/metricspanel.tsx", "./src/features/trade-analysis/components/index.ts", "./src/features/trade-analysis/types/index.ts", "./src/features/trade-entry/components/setupbuilder.tsx", "./src/features/trade-journal/tradeform.test.d.ts", "../../node_modules/@types/aria-query/index.d.ts", "../../node_modules/@testing-library/react/node_modules/@testing-library/dom/types/matches.d.ts", "../../node_modules/@testing-library/react/node_modules/@testing-library/dom/types/wait-for.d.ts", "../../node_modules/@testing-library/react/node_modules/@testing-library/dom/types/query-helpers.d.ts", "../../node_modules/@testing-library/react/node_modules/@testing-library/dom/types/queries.d.ts", "../../node_modules/@testing-library/react/node_modules/@testing-library/dom/types/get-queries-for-element.d.ts", "../../node_modules/pretty-format/build/types.d.ts", "../../node_modules/pretty-format/build/index.d.ts", "../../node_modules/@testing-library/react/node_modules/@testing-library/dom/types/screen.d.ts", "../../node_modules/@testing-library/react/node_modules/@testing-library/dom/types/wait-for-element-to-be-removed.d.ts", "../../node_modules/@testing-library/react/node_modules/@testing-library/dom/types/get-node-text.d.ts", "../../node_modules/@testing-library/react/node_modules/@testing-library/dom/types/events.d.ts", "../../node_modules/@testing-library/react/node_modules/@testing-library/dom/types/pretty-dom.d.ts", "../../node_modules/@testing-library/react/node_modules/@testing-library/dom/types/role-helpers.d.ts", "../../node_modules/@testing-library/react/node_modules/@testing-library/dom/types/config.d.ts", "../../node_modules/@testing-library/react/node_modules/@testing-library/dom/types/suggestions.d.ts", "../../node_modules/@testing-library/react/node_modules/@testing-library/dom/types/index.d.ts", "../../node_modules/@testing-library/react/node_modules/@types/react-dom/index.d.ts", "../../node_modules/@testing-library/react/node_modules/@types/react-dom/test-utils/index.d.ts", "../../node_modules/@testing-library/react/types/index.d.ts", "../../node_modules/@vitest/utils/dist/types.d.ts", "../../node_modules/@vitest/utils/dist/helpers.d.ts", "../../node_modules/@vitest/utils/dist/index.d.ts", "../../node_modules/@vitest/runner/dist/tasks-c965d7f6.d.ts", "../../node_modules/@vitest/runner/dist/runner-3b8473ea.d.ts", "../../node_modules/@vitest/runner/dist/index.d.ts", "../../node_modules/@types/chai/index.d.ts", "../../node_modules/@vitest/expect/dist/index.d.ts", "../../node_modules/@vitest/snapshot/dist/environment-38cdead3.d.ts", "../../node_modules/@vitest/snapshot/dist/index-6461367c.d.ts", "../../node_modules/@vitest/snapshot/dist/index.d.ts", "../../node_modules/esbuild/lib/main.d.ts", "../../node_modules/vitest/node_modules/vite/types/metadata.d.ts", "../../node_modules/vitest/node_modules/vite/types/hmrpayload.d.ts", "../../node_modules/vitest/node_modules/vite/types/customevent.d.ts", "../../node_modules/vitest/node_modules/rollup/dist/rollup.d.ts", "../../node_modules/vitest/node_modules/vite/types/importglob.d.ts", "../../node_modules/source-map-js/source-map.d.ts", "../../node_modules/postcss/lib/previous-map.d.ts", "../../node_modules/postcss/lib/input.d.ts", "../../node_modules/postcss/lib/css-syntax-error.d.ts", "../../node_modules/postcss/lib/declaration.d.ts", "../../node_modules/postcss/lib/root.d.ts", "../../node_modules/postcss/lib/warning.d.ts", "../../node_modules/postcss/lib/lazy-result.d.ts", "../../node_modules/postcss/lib/no-work-result.d.ts", "../../node_modules/postcss/lib/processor.d.ts", "../../node_modules/postcss/lib/result.d.ts", "../../node_modules/postcss/lib/document.d.ts", "../../node_modules/postcss/lib/rule.d.ts", "../../node_modules/postcss/lib/node.d.ts", "../../node_modules/postcss/lib/comment.d.ts", "../../node_modules/postcss/lib/container.d.ts", "../../node_modules/postcss/lib/at-rule.d.ts", "../../node_modules/postcss/lib/list.d.ts", "../../node_modules/postcss/lib/postcss.d.ts", "../../node_modules/vitest/node_modules/vite/dist/node/index.d.ts", "../../node_modules/@vitest/runner/dist/types.d.ts", "../../node_modules/@vitest/runner/types.d.ts", "../../node_modules/@vitest/utils/dist/diff.d.ts", "../../node_modules/@vitest/utils/diff.d.ts", "../../node_modules/@vitest/runner/dist/utils.d.ts", "../../node_modules/@vitest/runner/utils.d.ts", "../../node_modules/tinybench/dist/index.d.cts", "../../node_modules/vite-node/dist/types.d-1e7e3fdf.d.ts", "../../node_modules/vite-node/dist/types-c39b64bb.d.ts", "../../node_modules/vite-node/dist/client.d.ts", "../../node_modules/@vitest/snapshot/dist/manager.d.ts", "../../node_modules/@vitest/snapshot/manager.d.ts", "../../node_modules/vite-node/dist/index.d.ts", "../../node_modules/source-map/source-map.d.ts", "../../node_modules/vite-node/node_modules/vite/dist/node/index.d.ts", "../../node_modules/vite-node/dist/server.d.ts", "../../node_modules/vitest/dist/types-e3c9754d.d.ts", "../../node_modules/tinyspy/dist/index.d.ts", "../../node_modules/@vitest/spy/dist/index.d.ts", "../../node_modules/@vitest/snapshot/dist/environment.d.ts", "../../node_modules/@vitest/snapshot/environment.d.ts", "../../node_modules/vitest/dist/config.d.ts", "../../node_modules/vitest/dist/index.d.ts", "./src/features/trade-journal/tradeform.test.js", "./src/features/trade-journal/index.ts", "./src/features/trade-journal/components/tabpanel.test.d.ts", "./src/features/trade-journal/components/tabpanel.test.js", "./src/features/trade-journal/components/f1-filter-components.ts", "./src/features/trade-journal/components/f1-journal-components.ts", "./src/features/trade-journal/components/trade-form/f1-components.ts", "./src/features/trade-journal/constants/setupclassification.ts", "./src/features/trade-journal/tests/usetradesubmission.test.d.ts", "./src/features/trade-journal/tests/usetradesubmission.test.js", "./src/features/trading-dashboard/index.ts", "./src/features/trading-dashboard/components/dashboardtabs.tsx", "./src/features/trading-dashboard/components/f1header.tsx", "./src/features/trading-dashboard/components/quicktradeformfields.tsx", "./src/features/trading-dashboard/components/quicktradeformactions.tsx", "./src/features/trading-dashboard/hooks/usequicktradeform.ts", "./src/features/trading-dashboard/components/quicktradeformcontainer.tsx", "./src/features/trading-dashboard/components/quicktradeform.tsx", "./src/features/trading-dashboard/hooks/usetradingdashboarddata.ts", "./src/features/trading-dashboard/context/tradingdashboardcontext.tsx", "./src/features/trading-dashboard/components/index.ts", "./src/features/trading-dashboard/components/tradingdashboardcontainer.tsx", "./src/features/trading-dashboard/components/f1-dashboard-components.ts", "./src/features/trading-dashboard/utils/datavalidation.ts", "./src/layouts/index.ts", "./src/pages/dailyguide.tsx", "./src/pages/dashboard.tsx", "./src/pages/notfound.tsx", "./src/pages/settings.tsx", "./src/pages/tradeanalysis.tsx", "./src/pages/tradeform.tsx", "./src/pages/tradejournal.tsx", "./src/routes/routes.test.d.ts", "./src/routes/routes.test.js", "./src/routes/components/molecules/loadingscreen.tsx", "./src/routes/layouts/mainlayout.tsx", "./src/services/contracts/tradejournalapiimpl.ts", "../../node_modules/vitest/globals.d.ts"], "fileIdsList": [[427, 477], [477], [86, 87, 88, 477], [86, 87, 477], [86, 477], [84, 477, 500, 501, 580], [84, 477, 500], [477, 496, 497, 498], [477, 496], [84, 426, 432, 451, 477, 495, 499], [477, 640], [477, 637, 638, 639, 640, 641, 644, 645, 646, 647, 648, 649, 650, 651], [477, 636], [477, 643], [477, 637, 638, 639], [477, 637, 638], [477, 640, 641, 643], [477, 638], [84, 477], [84, 477, 654], [477, 652, 653, 654], [427, 428, 429, 430, 431, 477], [427, 429, 477], [451, 477, 484, 492], [451, 477, 484], [196, 477], [212, 477], [448, 451, 477, 484, 486, 487, 488], [477, 487, 489, 491, 493, 494], [242, 244, 245, 246, 247, 248, 249, 250, 251, 252, 253, 254, 477], [242, 243, 245, 246, 247, 248, 249, 250, 251, 252, 253, 254, 477], [243, 244, 245, 246, 247, 248, 249, 250, 251, 252, 253, 254, 477], [242, 243, 244, 246, 247, 248, 249, 250, 251, 252, 253, 254, 477], [242, 243, 244, 245, 247, 248, 249, 250, 251, 252, 253, 254, 477], [242, 243, 244, 245, 246, 248, 249, 250, 251, 252, 253, 254, 477], [242, 243, 244, 245, 246, 247, 249, 250, 251, 252, 253, 254, 477], [242, 243, 244, 245, 246, 247, 248, 250, 251, 252, 253, 254, 477], [242, 243, 244, 245, 246, 247, 248, 249, 251, 252, 253, 254, 477], [242, 243, 244, 245, 246, 247, 248, 249, 250, 252, 253, 254, 477], [242, 243, 244, 245, 246, 247, 248, 249, 250, 251, 253, 254, 477], [242, 243, 244, 245, 246, 247, 248, 249, 250, 251, 252, 254, 477], [242, 243, 244, 245, 246, 247, 248, 249, 250, 251, 252, 253, 477], [433, 477], [436, 477], [437, 442, 468, 477], [438, 448, 449, 456, 465, 476, 477], [438, 439, 448, 456, 477], [440, 477], [441, 442, 449, 457, 477], [442, 465, 473, 477], [443, 445, 448, 456, 477], [444, 477], [445, 446, 477], [447, 448, 477], [448, 477], [448, 449, 450, 465, 476, 477], [448, 449, 450, 465, 477], [451, 456, 465, 476, 477], [448, 449, 451, 452, 456, 465, 473, 476, 477], [451, 453, 465, 473, 476, 477], [433, 434, 435, 436, 437, 438, 439, 440, 441, 442, 443, 444, 445, 446, 447, 448, 449, 450, 451, 452, 453, 454, 455, 456, 457, 458, 459, 460, 461, 462, 463, 464, 465, 466, 467, 468, 469, 470, 471, 472, 473, 474, 475, 476, 477, 478, 479, 480, 481, 482, 483], [448, 454, 477], [455, 476, 477], [445, 448, 456, 465, 477], [457, 477], [458, 477], [436, 459, 477], [460, 475, 477, 481], [461, 477], [462, 477], [448, 463, 477], [463, 464, 477, 479], [437, 448, 465, 466, 467, 477], [437, 465, 467, 477], [465, 466, 477], [468, 477], [469, 477], [448, 471, 472, 477], [471, 472, 477], [442, 456, 473, 477], [474, 477], [456, 475, 477], [437, 451, 462, 476, 477], [442, 477], [465, 477, 478], [477, 479], [477, 480], [437, 442, 448, 450, 459, 465, 476, 477, 479, 481], [465, 477, 482], [81, 82, 83, 477], [449, 465, 477, 484, 485], [451, 477, 484, 486, 490], [82, 84, 159, 477], [477, 658, 662], [477, 658, 659, 660], [477, 659], [477, 658], [477, 658, 659, 696], [477, 693], [477, 697], [477, 664], [477, 643, 664], [477, 643, 664, 665], [477, 712], [477, 703], [477, 710], [477, 695], [477, 656], [477, 643, 656, 657], [425, 477], [477, 688], [477, 686, 688], [477, 677, 685, 686, 687, 689], [477, 675], [477, 678, 683, 688, 691], [477, 674, 691], [477, 678, 679, 682, 683, 684, 691], [477, 678, 679, 680, 682, 683, 691], [477, 675, 676, 677, 678, 679, 683, 684, 685, 687, 688, 689, 691], [477, 673, 675, 676, 677, 678, 679, 680, 682, 683, 684, 685, 686, 687, 688, 689, 690], [477, 673, 691], [477, 678, 680, 681, 683, 684, 691], [477, 682, 691], [477, 683, 684, 688, 691], [477, 676, 686], [477, 642], [89, 93, 477], [84, 89, 93, 94, 477], [89, 90, 91, 92, 477], [84, 89, 90, 477], [84, 89, 477], [84, 199, 215, 218, 229, 230, 477], [84, 199, 208, 216, 229, 230, 477], [84, 198, 199, 477], [84, 199, 477], [84, 199, 229, 230, 477], [84, 199, 229, 230, 236, 238, 241, 477], [84, 199, 208, 215, 218, 229, 230, 477], [84, 199, 208, 216, 228, 229, 230, 477], [84, 199, 208, 218, 228, 229, 230, 477], [84, 199, 208, 228, 229, 230, 477], [84, 199, 203, 209, 215, 220, 229, 230, 239, 240, 477], [199, 477], [84, 199, 254, 257, 258, 259, 477], [84, 199, 254, 256, 257, 258, 477], [84, 199, 216, 477], [84, 199, 208, 477], [84, 199, 200, 201, 477], [84, 199, 201, 203, 477], [194, 195, 199, 200, 202, 203, 204, 205, 206, 207, 208, 209, 210, 211, 215, 216, 217, 218, 219, 220, 221, 222, 223, 224, 225, 226, 227, 229, 230, 231, 232, 233, 234, 235, 236, 237, 238, 239, 240, 241, 260, 261, 262, 263, 264, 265, 266, 267, 268, 269, 270, 271, 272, 273, 477], [84, 199, 270, 477], [84, 199, 211, 477], [84, 199, 218, 222, 223, 477], [84, 199, 209, 211, 477], [84, 199, 214, 477], [84, 199, 214, 255, 477], [84, 202, 256, 477], [84, 198, 477], [477, 502, 503, 504, 505, 507, 508, 509, 510, 511, 512, 513, 514, 515, 516, 517, 518, 519, 520, 521, 522, 523, 524, 525, 526, 527, 528, 529, 530, 531, 532, 533, 534, 535, 536, 537, 538, 539, 540, 541, 542, 543, 544, 545, 546, 547, 548, 549, 550, 551, 552, 553, 554, 555, 556, 557, 558, 559, 560, 561, 562, 563, 564, 565, 566, 567, 568, 569, 570, 571, 572, 573, 574, 575, 576, 577, 578, 579], [477, 528], [477, 528, 541], [477, 506, 555], [477, 556], [477, 507, 530], [477, 530], [477, 506], [477, 559], [477, 539], [477, 506, 547, 555], [477, 550], [477, 552], [477, 502], [477, 522], [477, 503, 504, 543], [477, 563], [477, 561], [477, 507, 508], [477, 509], [477, 520], [477, 506, 511], [477, 565], [477, 507], [477, 559, 568, 571], [477, 507, 508, 552], [197, 477], [213, 477], [477, 700, 701], [477, 692, 700, 701, 709], [477, 700], [448, 449, 451, 453, 456, 465, 473, 476, 477, 482, 667, 668, 669, 670, 671, 672, 691], [449, 477, 481, 658, 661, 662, 663, 666, 692, 694, 698, 699, 702, 704, 705, 706, 708, 709], [449, 477, 481, 658, 661, 662, 663, 666, 692, 694, 698, 699, 702, 704, 705, 706, 708, 709, 711, 713, 714], [477, 715], [448, 449, 451, 453, 456, 465, 473, 476, 477, 482, 484, 667, 668, 669, 670, 671, 672, 691], [477, 669], [477, 671], [414, 477], [414, 415, 416, 417, 418, 419, 477], [85, 95, 182, 406, 407, 477], [84, 85, 182, 477], [84, 85, 160, 477], [85, 160, 477, 581, 582], [84, 85, 95, 160, 477], [85, 477], [85, 294, 477], [84, 85, 160, 182, 294, 477], [84, 85, 295, 316, 477], [84, 85, 146, 160, 477, 585, 586, 590, 593, 594, 601, 602], [84, 85, 160, 182, 477], [84, 85, 160, 182, 300, 477], [84, 85, 160, 182, 307, 308, 309, 310, 311, 477], [85, 302, 303, 304, 315, 316, 477], [84, 85, 160, 301, 302, 303, 304, 315, 477], [84, 85, 160, 303, 306, 312, 314, 477], [84, 85, 160, 182, 477, 608], [85, 306, 317, 477, 586, 595, 596, 597, 598, 600, 601, 602, 605], [84, 85, 160, 294, 477], [84, 85, 160, 182, 294, 477, 591, 592, 593], [84, 85, 160, 182, 313, 477], [84, 85, 160, 182, 299, 305, 477], [84, 85, 294, 477, 600], [84, 85, 160, 182, 294, 477, 595, 596, 597, 598, 599], [85, 477, 587, 588, 589], [84, 85, 303, 477], [84, 85, 294, 477, 584], [84, 85, 477, 585, 603], [85, 298, 299, 300, 477], [84, 85, 294, 297, 477], [84, 85, 182, 307, 477], [84, 85, 182, 300, 477], [84, 85, 182, 307, 308, 477], [84, 85, 294, 477], [85, 294, 297, 301, 477, 585, 590, 593, 604, 606], [85, 182, 294, 477], [85, 295, 296, 477], [85, 293, 477], [85, 289, 290, 477], [85, 289, 290, 291, 292, 477], [85, 290, 477], [84, 85, 160, 477, 611, 612, 613, 614], [85, 477, 615], [85, 398, 399, 400, 401, 402, 477], [84, 85, 160, 398, 400, 401, 477], [84, 85, 160, 399, 477], [85, 403, 477, 617, 618, 619, 620], [84, 85, 402, 477], [84, 85, 160, 373, 378, 477], [84, 85, 160, 182, 373, 378, 477], [85, 379, 380, 381, 382, 388, 389, 390, 391, 392, 393, 477, 623, 624, 625, 628, 629, 631], [84, 85, 182, 378, 382, 388, 389, 390, 391, 477], [84, 85, 182, 373, 477], [84, 85, 160, 182, 378, 379, 380, 381, 392, 477], [84, 85, 387, 477], [84, 85, 160, 373, 384, 477], [84, 85, 160, 378, 383, 385, 386, 477], [84, 85, 160, 182, 373, 477], [84, 85, 182, 373, 377, 477], [84, 85, 373, 477], [84, 85, 373, 383, 477], [85, 182, 373, 378, 381, 382, 388, 389, 390, 391, 394, 477, 623, 624, 625, 626], [85, 182, 373, 477], [85, 182, 373, 374, 375, 477], [85, 182, 373, 376, 477], [84, 85, 378, 393, 477], [85, 182, 477], [85, 347, 348, 349, 350, 477], [85, 367, 368, 369, 370, 371, 477], [84, 85, 160, 347, 348, 349, 477], [84, 85, 160, 318, 319, 367, 368, 369, 370, 477], [84, 85, 95, 160, 366, 477], [85, 347, 477], [85, 326, 327, 328, 329, 330, 331, 345, 353, 362, 365, 477], [84, 85, 160, 182, 327, 353, 368, 477], [84, 85, 177, 477], [85, 160, 182, 329, 477, 655, 715], [85, 395, 477], [84, 85, 160, 182, 279, 280, 354, 364, 477], [84, 85, 160, 355, 477], [85, 354, 356, 357, 358, 359, 360, 361, 477], [84, 85, 160, 362, 477], [85, 278, 281, 282, 283, 284, 477], [85, 285, 332, 339, 340, 341, 342, 343, 344, 477], [84, 85, 279, 280, 284, 477], [84, 85, 160, 279, 280, 282, 283, 477], [85, 278, 477], [84, 85, 160, 278, 279, 280, 281, 477], [84, 85, 160, 279, 477], [84, 85, 160, 279, 280, 477], [84, 85, 160, 182, 279, 280, 338, 477], [84, 85, 279, 280, 281, 477], [85, 346, 351, 352, 477], [84, 85, 160, 182, 327, 351, 477], [84, 85, 279, 350, 477], [85, 321, 322, 323, 324, 325, 477], [84, 85, 95, 160, 182, 477], [84, 85, 160, 327, 477], [84, 85, 160, 182, 327, 477], [84, 85, 160, 279, 335, 477], [85, 363, 364, 477], [84, 85, 160, 279, 335, 363, 477], [84, 85, 95, 160, 187, 279, 320, 326, 477], [84, 85, 477], [84, 85, 368, 477], [85, 279, 477], [85, 280, 333, 334, 336, 337, 477], [84, 85, 279, 477], [85, 95, 280, 333, 334, 336, 477], [84, 85, 177, 279, 477], [84, 85, 95, 182, 279, 335, 477], [85, 372, 397, 477], [85, 177, 335, 336, 477, 655, 715], [85, 95, 160, 182, 337, 397, 477, 655, 715], [84, 85, 95, 160, 337, 345, 396, 477], [84, 85, 371, 477], [84, 85, 160, 191, 193, 275, 276, 277, 285, 477], [85, 190, 191, 192, 286, 287, 477], [84, 85, 160, 182, 189, 190, 191, 192, 286, 477], [85, 477, 727, 728, 733, 734, 735, 737], [84, 85, 160, 188, 477], [84, 85, 160, 188, 274, 477], [84, 85, 182, 477, 732], [84, 85, 182, 477, 729, 730, 731], [84, 85, 160, 182, 193, 275, 276, 277, 477, 733, 735, 736], [84, 85, 191, 477], [84, 85, 182, 188, 477, 727, 734], [84, 85, 182, 187, 188, 477], [85, 188, 189, 193, 275, 276, 277, 288, 477], [84, 85, 287, 477], [84, 85, 408, 412, 413, 421, 477], [85, 183, 184, 185, 477], [84, 85, 95, 160, 183, 184, 477], [84, 85, 477, 607], [84, 85, 477, 726], [84, 85, 95, 424, 477, 627], [84, 85, 95, 397, 477], [85, 420, 477], [85, 405, 477], [85, 95, 160, 182, 406, 477, 655, 715], [84, 85, 95, 185, 186, 288, 317, 372, 394, 397, 403, 404, 477], [84, 85, 410, 412, 413, 477], [101, 102, 103, 104, 105, 106, 107, 108, 109, 110, 111, 477], [112, 130, 133, 135, 140, 146, 477], [84, 118, 477], [118, 141, 142, 143, 144, 145, 477], [84, 97, 477], [113, 114, 115, 116, 117, 119, 121, 122, 123, 124, 125, 126, 127, 128, 129, 477], [84, 120, 477], [84, 96, 126, 477], [84, 96, 477], [84, 115, 477], [84, 113, 477], [131, 132, 477], [134, 477], [136, 137, 138, 139, 477], [84, 98, 477], [99, 477], [178, 179, 180, 477], [98, 477], [118, 120, 145, 148, 149, 150, 151, 152, 153, 154, 155, 156, 477], [97, 477], [98, 100, 147, 157, 167, 171, 173, 174, 177, 181, 477], [170, 175, 176, 477], [168, 477], [96, 477], [96, 98, 477], [168, 169, 170, 477], [158, 477], [158, 161, 162, 163, 164, 165, 166, 477], [160, 477], [84, 158, 477], [96, 97, 477], [172, 477]], "fileInfos": [{"version": "69684132aeb9b5642cbcd9e22dff7818ff0ee1aa831728af0ecf97d3364d5546", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "45b7ab580deca34ae9729e97c13cfd999df04416a79116c3bfb483804f85ded4", "impliedFormat": 1}, {"version": "3facaf05f0c5fc569c5649dd359892c98a85557e3e0c847964caeb67076f4d75", "impliedFormat": 1}, {"version": "e44bb8bbac7f10ecc786703fe0a6a4b952189f908707980ba8f3c8975a760962", "impliedFormat": 1}, {"version": "5e1c4c362065a6b95ff952c0eab010f04dcd2c3494e813b493ecfd4fcb9fc0d8", "impliedFormat": 1}, {"version": "68d73b4a11549f9c0b7d352d10e91e5dca8faa3322bfb77b661839c42b1ddec7", "impliedFormat": 1}, {"version": "5efce4fc3c29ea84e8928f97adec086e3dc876365e0982cc8479a07954a3efd4", "impliedFormat": 1}, {"version": "feecb1be483ed332fad555aff858affd90a48ab19ba7272ee084704eb7167569", "impliedFormat": 1}, {"version": "ee7bad0c15b58988daa84371e0b89d313b762ab83cb5b31b8a2d1162e8eb41c2", "impliedFormat": 1}, {"version": "27bdc30a0e32783366a5abeda841bc22757c1797de8681bbe81fbc735eeb1c10", "impliedFormat": 1}, {"version": "8fd575e12870e9944c7e1d62e1f5a73fcf23dd8d3a321f2a2c74c20d022283fe", "impliedFormat": 1}, {"version": "8bf8b5e44e3c9c36f98e1007e8b7018c0f38d8adc07aecef42f5200114547c70", "impliedFormat": 1}, {"version": "092c2bfe125ce69dbb1223c85d68d4d2397d7d8411867b5cc03cec902c233763", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "07f073f19d67f74d732b1adea08e1dc66b1b58d77cb5b43931dee3d798a2fd53", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "c57796738e7f83dbc4b8e65132f11a377649c00dd3eee333f672b8f0a6bea671", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "dc2df20b1bcdc8c2d34af4926e2c3ab15ffe1160a63e58b7e09833f616efff44", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "515d0b7b9bea2e31ea4ec968e9edd2c39d3eebf4a2d5cbd04e88639819ae3b71", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "0559b1f683ac7505ae451f9a96ce4c3c92bdc71411651ca6ddb0e88baaaad6a3", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "0dc1e7ceda9b8b9b455c3a2d67b0412feab00bd2f66656cd8850e8831b08b537", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "ce691fb9e5c64efb9547083e4a34091bcbe5bdb41027e310ebba8f7d96a98671", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "8d697a2a929a5fcb38b7a65594020fcef05ec1630804a33748829c5ff53640d0", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "4ff2a353abf8a80ee399af572debb8faab2d33ad38c4b4474cff7f26e7653b8d", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "936e80ad36a2ee83fc3caf008e7c4c5afe45b3cf3d5c24408f039c1d47bdc1df", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "d15bea3d62cbbdb9797079416b8ac375ae99162a7fba5de2c6c505446486ac0a", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "68d18b664c9d32a7336a70235958b8997ebc1c3b8505f4f1ae2b7e7753b87618", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "eb3d66c8327153d8fa7dd03f9c58d351107fe824c79e9b56b462935176cdf12a", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "38f0219c9e23c915ef9790ab1d680440d95419ad264816fa15009a8851e79119", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "69ab18c3b76cd9b1be3d188eaf8bba06112ebbe2f47f6c322b5105a6fbc45a2e", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "fef8cfad2e2dc5f5b3d97a6f4f2e92848eb1b88e897bb7318cef0e2820bceaab", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "2f11ff796926e0832f9ae148008138ad583bd181899ab7dd768a2666700b1893", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "4de680d5bb41c17f7f68e0419412ca23c98d5749dcaaea1896172f06435891fc", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "954296b30da6d508a104a3a0b5d96b76495c709785c1d11610908e63481ee667", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "ac9538681b19688c8eae65811b329d3744af679e0bdfa5d842d0e32524c73e1c", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "0a969edff4bd52585473d24995c5ef223f6652d6ef46193309b3921d65dd4376", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "9e9fbd7030c440b33d021da145d3232984c8bb7916f277e8ffd3dc2e3eae2bdb", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "811ec78f7fefcabbda4bfa93b3eb67d9ae166ef95f9bff989d964061cbf81a0c", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "717937616a17072082152a2ef351cb51f98802fb4b2fdabd32399843875974ca", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "d7e7d9b7b50e5f22c915b525acc5a49a7a6584cf8f62d0569e557c5cfc4b2ac2", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "71c37f4c9543f31dfced6c7840e068c5a5aacb7b89111a4364b1d5276b852557", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "576711e016cf4f1804676043e6a0a5414252560eb57de9faceee34d79798c850", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "89c1b1281ba7b8a96efc676b11b264de7a8374c5ea1e6617f11880a13fc56dc6", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "74f7fa2d027d5b33eb0471c8e82a6c87216223181ec31247c357a3e8e2fddc5b", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "d6d7ae4d1f1f3772e2a3cde568ed08991a8ae34a080ff1151af28b7f798e22ca", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "063600664504610fe3e99b717a1223f8b1900087fab0b4cad1496a114744f8df", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "934019d7e3c81950f9a8426d093458b65d5aff2c7c1511233c0fd5b941e608ab", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "52ada8e0b6e0482b728070b7639ee42e83a9b1c22d205992756fe020fd9f4a47", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "3bdefe1bfd4d6dee0e26f928f93ccc128f1b64d5d501ff4a8cf3c6371200e5e6", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "59fb2c069260b4ba00b5643b907ef5d5341b167e7d1dbf58dfd895658bda2867", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "639e512c0dfc3fad96a84caad71b8834d66329a1f28dc95e3946c9b58176c73a", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "368af93f74c9c932edd84c58883e736c9e3d53cec1fe24c0b0ff451f529ceab1", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "af3dd424cf267428f30ccfc376f47a2c0114546b55c44d8c0f1d57d841e28d74", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "995c005ab91a498455ea8dfb63aa9f83fa2ea793c3d8aa344be4a1678d06d399", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "959d36cddf5e7d572a65045b876f2956c973a586da58e5d26cde519184fd9b8a", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "965f36eae237dd74e6cca203a43e9ca801ce38824ead814728a2807b1910117d", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "3925a6c820dcb1a06506c90b1577db1fdbf7705d65b62b99dce4be75c637e26b", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "0a3d63ef2b853447ec4f749d3f368ce642264246e02911fcb1590d8c161b8005", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "b5ce7a470bc3628408429040c4e3a53a27755022a32fd05e2cb694e7015386c7", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "8444af78980e3b20b49324f4a16ba35024fef3ee069a0eb67616ea6ca821c47a", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "3287d9d085fbd618c3971944b65b4be57859f5415f495b33a6adc994edd2f004", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "b4b67b1a91182421f5df999988c690f14d813b9850b40acd06ed44691f6727ad", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "df83c2a6c73228b625b0beb6669c7ee2a09c914637e2d35170723ad49c0f5cd4", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "436aaf437562f276ec2ddbee2f2cdedac7664c1e4c1d2c36839ddd582eeb3d0a", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "8e3c06ea092138bf9fa5e874a1fdbc9d54805d074bee1de31b99a11e2fec239d", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "87dc0f382502f5bbce5129bdc0aea21e19a3abbc19259e0b43ae038a9fc4e326", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "b1cb28af0c891c8c96b2d6b7be76bd394fddcfdb4709a20ba05a7c1605eea0f9", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "2fef54945a13095fdb9b84f705f2b5994597640c46afeb2ce78352fab4cb3279", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "ac77cb3e8c6d3565793eb90a8373ee8033146315a3dbead3bde8db5eaf5e5ec6", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "56e4ed5aab5f5920980066a9409bfaf53e6d21d3f8d020c17e4de584d29600ad", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "4ece9f17b3866cc077099c73f4983bddbcb1dc7ddb943227f1ec070f529dedd1", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "0a6282c8827e4b9a95f4bf4f5c205673ada31b982f50572d27103df8ceb8013c", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "1c9319a09485199c1f7b0498f2988d6d2249793ef67edda49d1e584746be9032", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "e3a2a0cee0f03ffdde24d89660eba2685bfbdeae955a6c67e8c4c9fd28928eeb", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "811c71eee4aa0ac5f7adf713323a5c41b0cf6c4e17367a34fbce379e12bbf0a4", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "51ad4c928303041605b4d7ae32e0c1ee387d43a24cd6f1ebf4a2699e1076d4fa", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "60037901da1a425516449b9a20073aa03386cce92f7a1fd902d7602be3a7c2e9", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "d4b1d2c51d058fc21ec2629fff7a76249dec2e36e12960ea056e3ef89174080f", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "22adec94ef7047a6c9d1af3cb96be87a335908bf9ef386ae9fd50eeb37f44c47", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "4245fee526a7d1754529d19227ecbf3be066ff79ebb6a380d78e41648f2f224d", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "8e7f8264d0fb4c5339605a15daadb037bf238c10b654bb3eee14208f860a32ea", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "782dec38049b92d4e85c1585fbea5474a219c6984a35b004963b00beb1aab538", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "bbdf156fea2fabed31a569445835aeedcc33643d404fcbaa54541f06c109df3f", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "8a8eb4ebffd85e589a1cc7c178e291626c359543403d58c9cd22b81fab5b1fb9", "impliedFormat": 1}, {"version": "65ff5a0aefd7817a03c1ad04fee85c9cdd3ec415cc3c9efec85d8008d4d5e4ee", "impliedFormat": 1}, {"version": "7fb6faf1006c3503d44e4922c8c65772ddc98e92bad7c2ae2d5db123f3e297b3", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "af7fd2870746deed40e130fc0a3966de74e8f52a97ec114d0fbb35876ab05ca9", "impliedFormat": 1}, {"version": "036b2bdb6931917cd8c03e4448d26d14b760035ff0cc636f582eedbac84cdb8c", "impliedFormat": 1}, {"version": "31172fc1c2ff8750789aa6ca42dbb19869ba33acea9df7141e6a65154f49d31b", "impliedFormat": 1}, {"version": "adb0acb5deb823b8a300cbb9453cee23d5acdebfc3569cdb2947cfba8e465711", "impliedFormat": 1}, {"version": "e9f9b0b6a912d089ea1e008db41892a1a7dedfe37ed3603da7aabcd596b62176", "impliedFormat": 1}, {"version": "b620e82a2a4b595315172015f8a7ef55710c05e4dd3ca50835597a4d4196f3ae", "impliedFormat": 1}, {"version": "c86b1dc09268220a591c811f103cebdedeffe99c5394241cc7a9fa96d01e168b", "impliedFormat": 1}, {"version": "b147482273abaf18f285b79d9d8bfad9a20a0c8a4fd46e4c03978299f19ee518", "impliedFormat": 1}, {"version": "adad2f34346602d079ba8572f05bfd67619c1b91497adae041bbb2faaee5b99b", "impliedFormat": 1}, {"version": "202767e06b68e590b65afba715122116259e4dc500592cf05e320d6383c4dffb", "impliedFormat": 1}, {"version": "c217e646d1decd693b2e119c2a7741145704546e78e784ec9d62f4997c3c4abe", "affectsGlobalScope": true, "impliedFormat": 1}, "3693da5cb1b1ebe52a32db59fe7fc8827c54a15b56c926a8ba213824b5391ca7", "3e38ddf48d88329be70ab96c28ec0293ba2cf29fdf59d31ddf9ff56cba3a36bf", "94eb184397a818eb51b16660801fea9e4e3a395a693889c5785cd0a667789e41", "209888584b3953000581cf7e1b69bd96134b22096f0d91d662ca7fc0ff53dd9b", "a0d5f667257c92ec5bbb44656fe56d644044c166c42f20930110fd362a4bc858", "3885cfc35c1a802cdb6fe70b5d89eb52efc18a0415b5fc3f826d919fca60340f", "4f3a42efe81f8dbf8a761368b9152f4bdc691689cea0ca3a11604759c73c2150", "d3e07135a4c155104134cce3fd68f12fdf57c6b912fcca7ad8374b2b374462cb", "ed5852beebbfb5a9fc899a9ec4ccef8bd6f3773f90127d104db77c2cda7d8f61", "c5428e7cd54436721b2ff584b3f0e99bbbf76f4841a8ed5e8b9021fff24ab8f5", "9967bb65f040ff37d7e1e886157a3e2e43a0b0969b6934722089c697991abce6", "021bd4f785730caea92a7d662ff1c6841995cdae5d3e987d74183d8507acf087", "dc26959a1c5356e602771a83c44585c9db0106fea8feed8318bf3664f94653a8", "983ac6b77621a79e3c654a3fa7420279d0cadae8f9b731a9686e17ea4d3e0599", "daa40047311fe5dda7a67d5aeff7e9fe23e955a7d914d36a5ee6f98ecb0f30c0", "cdc0d0aaf1f26c546185c175973d1035634936a3dc281ab6b247491ec4fec0d2", "f12de37b91bd74c1abe94719278bb9deae535dc2f8a3b1df193a7f2e4f52b3c0", "f374877589ca8a02d4e929ff1faf239f0b96da61460bf4dd31830d935088aedc", "f1dd49f14997100c21870e7adc68a7931d7d1d158fd0e8a9d6f1c5b0c81372db", "320bfa83c618d19a6bcfd69ca49bad423af1cf8292068b2bc80e51888f8c67ec", "2d66e6ddaf8e2574aa3b018c84aeeba7e01d0db4b305cf9ff3d7f55b4355b77b", "963b29015b6b83bda61e959bdcb6917fe92c9d638e01fb08f281c069c704e898", "b2461c212d929ccc2ae177ad7b5c1d99d08b32a5d90423ae932eb65a55a582cd", "82e94cc2b5e3905631e0013748cda4977bbd513e18db6149754aa7147354a896", "fbeaa31ec66c6d9f48b5b8df73cf5edd9140bf96ec654b7834b2ac6ba9f14240", "bd35831bd176f34383c503076fc8b566db50116cfd45ad9b9aa64e30bdeb594b", "8ce2bec40313c2852bd609dbbaadfe631c41329bc378e7983cace870fa98168b", "a7a59c1f26857f2f005bb781bc04211f4d45410119cc3a1a765ac908303e72d9", "9547468e782d1f80bf158c339c6b8e268c3e99088dd4a5f3fa578815d3b54531", "bea411a8803514de4a9940cde9d199cbbabe3da12b1996b05a76ac3220963d4a", "6a09a49975817e9296387790383f8c64b877d34e9e0f0d1e3ecea97236156668", "c41c5491c30fa2fc1cd936fcb487a57b678a45304474f8dc844bd92eb4f6e6af", "6854170230b5aff4152d5101f6449e687eb4ca6c684be4f15cc5edbf5be1e8d6", "4cca28fd6fbdbd903359a9e39a134ab8a3b60ea6338cc762a01d4f59c9716ebd", "3408ca1cdec042fc0b31f4a3ad0eb3a49e3508f86a3b83af0019725593b7ad7a", "7902135dac2aa84f4c38c3e00f37cf140b49d9f2fe4d6d8e990cfd433b98e7fe", "7fbbb4c43d650fa4e4ca05a4cba51dfeac400b6d559072c2febecb5aa2c1621c", "af944dff7d5ae541efb5ffc9216c92d7081020b7ac9a7b8fe1b1081855bfe373", "1cd49fdcfac28861920e771608cc0dc142f179841b2857d0b41bbe3e5313e91e", "ea406f2e284dd1807b7e233fa60d2cbdd9a20bb49c58ec3d05031673af4b2f1d", "fda7162044e4ca770658cb4dc79373d59248e0462f427fc34fb889a691ca232b", "9ca1649ebbcda60271c406a056abff1d0a37fa10bc419e34ad4385c497a36068", "8c5af7093cb5cc38d31c9f66b56915dfa40d2c6d6bced7cb829fcd115329d0f8", "7694ca0da0ea533f076ac3ab5b74776cd6f5d189aac85828f704d3cc26304403", "f6fa087efa8d69949452d49f3de62489745d84bd8fe3ece22144b6b41b730eb1", "7115aa067a36e5fb30dda3abb987d05157e4ac7dd6e6798918b4a4d4a762e039", "d0a19780c17013561f886ac44ff6ea68d53f2539d6044c94822995189844bd22", "3e0d3aa8e61e244efbbe62c9171695e49f09d41ce7fe22095101e130b43775ca", "b7cb7c45c6ff6cf090d92f18ed9b28cef47585721e06d40af0bb92eeda04dc88", "0aef2bfe1b89da5a0ba7753b06e5346443ca0cae87816fe27ff20ed4057eae11", "9a619f0c8a424c34bfc013c87ab6efa7fccad7984c1de70c48de4bcb2b4c9cdf", "679dabf1d0745562a88edf66e636ae6674c2a4111fa59dbcd43e3ad9c7c499dc", "68997526f75e81fa461a5310f14f5049466ea81807125548bf57aae52087462a", "49be59afcc0e363501c9030de849f7a84914452e929e7694069f000f2cfbdc54", "ca407aec6865d3e55e61e906568330b2b0ec739e33bda448efa1854d6d174bd7", "e229bfc631039f7bcddac5c23d72aae1fe845cf41c02c6d55497880a92127e66", "96cfc664f755adec47a54a55c38b9301129a63f7839187b63acc7fadbe84c4f9", "04a3ffe4df3a13cc84e38ca1788389c73116492f57bacf2f92ba88d97b3a2919", "94d60f7232d03b53901f2a21450739a19a70e5d1bc91590f603c71945f0ed0a5", "174dc49b4c906ac7f91236ce9e9a793cac7a15e4dc0e35d8c7f91a09dfaead87", "10d8ca6f2e6b8def3e45f6df7b294e39d747b6f8dd7cc8c0154eddf05f51193b", "0f279cf83f197d8bd641b1aaabc84fe1e2ea8ef39951e0fb5d97779594226401", "736807a90a36d3bf96fbbbf29420eb5bd4144d6975a3f5f6807eb1e5ee729d0d", {"version": "b2d0630483bf337ef9dac326c3334a245aa4946e9f60f12baf7da5be44beafbb", "impliedFormat": 1}, {"version": "356701ea5df9eea3bf62b0f29857cb950d95eec9b9063f85c17be705926cdd2a", "affectsGlobalScope": true, "impliedFormat": 1}, "479bf10fefdabdf6115e9ec92a01ab48431d271c566fa82ba0e09085a033021e", "64aecb1ac5b34956f020e61c68d6a6ee495365d4570b6d0159fb99b873e37334", "22a56f0c00974c7047b1afbb379242616cf51e28876677714e5ba7bda49878d5", "2020391dc9615833dfb991ff57fac4592a269f17b0c58abd8ac01e3cdf2e194d", "279a3e69586fccc815b3a8157eac9332ec5e082fab8cf82bd45987eaf8fe7aea", "0347a67d0f9c3d505b0e0f6e6840ec6dfa8b27ae3e6b5deb89adcf00c4c70499", "71136684c2a6a081ad510ac5fbc0b890f1fb6cb0c9c24985e7e8163166366af1", "c1a04fba4d8ad37df24e946836821860aa7654c1b46498068977edc45116f602", "81a23e587fe38c5b62a0958341e0eec1232f5f13813dd7dc9fa5b9ccc16c611c", "d079e1e4c85900e0ec13ec842dfceeb8c02f4ccaa07fd466f7b67b20aa1d029e", "f8d51bee1a73d32e722951eb5710b43c7c6d9526c71726fae041f9ebb905cabc", "039a6d266d8ba49b5bd0d3e89892a0368978aa59bd8a583adb74c0463b1958a7", "ec87a4f512af06e0caab5a7ad41cc376bc2deefbce32474ef87b4aaca797e4ca", "664239d3d85cafaa9ed03ffb1d8ba89e21a63474002ab051674fb0f4dca69492", "4f04848536803871b2d13a3ed9ac5edd04e3ab7a542c13ab9e6a072e3f497808", "16f1aaef19d19098cf0914e74d34ad06e8220c10e5a8084d479f0ba693faeab3", "dae247f7ae11b795edaf49fdc599817fee515c578bc4d52ec7e68ba4fea767cd", "c85eb2ae40dd26a37dcfc0e835b3b75261acd6ecc5a166100cca2a22d7565ac5", "9c4f03af405783d323948c0f09b126747e40d220bac65c599748ac43a55fc43e", "4e022ad16bd8e703602d60c73d8da7a7a2e31418368613b1d54ec397cde1aeec", "ece259ac023717d6311dfc16041afbcf5bf4e4e259e4c53f84459d4012048423", "a551bde32a01b8b66b73fcd3c309a852539a992050ddfdbce4c986db45d8de33", {"version": "45337901c43708013ce67aa6859e393aecafe74d4092f570b4d53c9ddb6e1d98", "signature": "8abbb40ebf04e4c54c903252a4cb34276b888fe466e484bcce13c0e3bef798fd"}, {"version": "734b50615c8700431af4ad447eedc9cc784b3ec11dc6c12f6f56a529b5be1397", "signature": "c9d631f071275484a72947688f51c6dfcf165b0a5597af0e6c9faf3d74933326"}, {"version": "54b51820422d37f23d62bbaad774cf181fdc5a6d91116f403c11562fdf4e9077", "signature": "08fd22931fa2ff638debc6cb64638b45fe901ed5b34c42fc8120cc87a7f3c91f"}, {"version": "2d81c7d3ff6e7c00e9ecd1d58f6109ef579b13b8a956f268ad5f55555c7a0553", "signature": "350d59818bbc7c74258419aac86cb52a5a1e3fe5daf2aa5ddc5609ac29077c37"}, {"version": "5c6217e082ecdc3b08f0d18390a3754fa62814679c44da9a9d53b7ef20c85684", "signature": "04f230d0da57c47a7013d44024fee67a5d923227f981b3b8897f23401bb6c5a8"}, {"version": "f69f9b3a1e8a2ec719f0ab858cffe53b0fa1818feffb6ef4cd920072bd4fc1f0", "signature": "8d71b3fc7fd9e6a89185125fa58d8f72c4506317a235bcc5a6ca1937fa98965a"}, {"version": "e0f43b8a2b4d046bbd2ae88479c987c9909262a4a30ba21ebc831e6ae105a207", "signature": "e78a35bf11b04018f5979596c940b573fcb098b7e3cf7c3fe95a269389a939cf"}, {"version": "468ed48e2cb1c9d3ee95771bac9cb8ebf15be2c86c68ae9e3924934d66a6db45", "signature": "073a91025f4508afc09c5bb401a87128942e4cd4b2e52c623dee03653c1a7da1"}, {"version": "9e1895d3e1bf8321bb44ebda2f47fd3344309cec3f120ac07ccef4087a0bd38c", "signature": "d0a377af27945d306aab6eead1aecb14788c08060aedeb423db93c56b9a2c1b5"}, {"version": "f7d1b77d60c5db36b0d9b05f2492e342aecde23a27f727e7407d20b48455ef8e", "signature": "86a9489ac02af18bf2dcd7e6403370226ce5f1ebc207400a413a1c8243edd409"}, {"version": "65ad943f540bb151f8dea77775be793e70b01e18b301c1bfdf5044e83740644a", "signature": "7a82dcba28c745c7964d35d8cc6bf6726082042ac0c68bdcb74241873e08c2b5"}, {"version": "af1a175d9870af96701ac1a8a295b88a4b78c6e25a0984a0b506530d4e3e7e35", "impliedFormat": 1}, {"version": "928790d1176b143f86a72ce1293fbdd697ac47e99c6cfbb77363b07635d2d073", "impliedFormat": 1}, {"version": "56208c500dcb5f42be7e18e8cb578f257a1a89b94b3280c506818fed06391805", "impliedFormat": 1}, {"version": "0c94c2e497e1b9bcfda66aea239d5d36cd980d12a6d9d59e66f4be1fa3da5d5a", "impliedFormat": 1}, {"version": "eb9271b3c585ea9dc7b19b906a921bf93f30f22330408ffec6df6a22057f3296", "impliedFormat": 1}, {"version": "2247161a50d13e2caa4d361de76740752113498dfe20d1929e8fb12c8eea0220", "impliedFormat": 1}, {"version": "46c4a99e54dffe9077ae0d5b56b2c9f357ed92798b7a3961092508fdd8fd21e8", "impliedFormat": 1}, {"version": "2abbe344595c3eb8680e4b9bed9546621847b18c0b47d3f4c6116e208b73bac3", "impliedFormat": 1}, {"version": "6507e5ca1af587957b174941d6d9b713bfc1fee0610ff7e742c914b02356f0c3", "impliedFormat": 1}, {"version": "dab96b728b3b3714adbbcccefba1a617f1f49f08ca9c7fb2c195257fb947d480", "impliedFormat": 1}, {"version": "aa1e7203ee981d21f0714282c081fbb214ba871676cd5f733ea2e1bf54201a59", "impliedFormat": 1}, {"version": "e2eba33e0756b5fc6abbe0438e40b11d0b919c7e9ef8e195c3fa2aa884ad70f8", "impliedFormat": 1}, {"version": "49a9650201db6de8ebc7361ea0d0984ad602d912676095fcf595769b92e9f90d", "impliedFormat": 1}, {"version": "3355f107464481d7cab90f7acd52c8f291fa833ae9891c5daeb8fb61ac7371fa", "impliedFormat": 1}, {"version": "135517a282bc52665b257d869818487c1d15e8ea68b7de9be026a2399e5155c6", "impliedFormat": 1}, {"version": "f5374459d1faa9d41c6e3cbe528339a3202361d8fb738540473fac610450e032", "impliedFormat": 1}, {"version": "7bf226837398fc2deb479034c1801215e3d7a561e9d85d63aaa5c5b429d03b9d", "impliedFormat": 1}, {"version": "46e43b1802dccd07fb4181c78dbae6d799eea8061b61dbbedf8764619cb9c25e", "impliedFormat": 1}, {"version": "2c378d9368abcd2eba8c29b294d40909845f68557bc0b38117e4f04fc56e5f9c", "impliedFormat": 1}, {"version": "bb220eaac1677e2ad82ac4e7fd3e609a0c7b6f2d6d9c673a35068c97f9fcd5cd", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "c60b14c297cc569c648ddaea70bc1540903b7f4da416edd46687e88a543515a1", "impliedFormat": 1}, {"version": "d2ea2cac8bd2500a515938bfbedaacb30139b552af0fecbbf8b913ee92fd5475", "impliedFormat": 1}, {"version": "3cb68cd4afa5446554f27cd5f268d0dc9719a20c2f9108ba418f1b5f429f1878", "impliedFormat": 1}, {"version": "50b7c14dcfd6005c11cb1ad447a16f5b02b74912a4fb6c5ab74de114d85462f2", "impliedFormat": 1}, {"version": "115445e53b874659c5de88facfef276bd678b6ae1ee21f24c91528fafe61fe96", "impliedFormat": 1}, {"version": "cf746ee75c562746ecae64deb216d07cdc35871e39b0b6268eb14fa92b667916", "impliedFormat": 1}, {"version": "4a086660560bd550b43bcdb6bd861b3ae064559932b638e57cf2aeab14dc0978", "impliedFormat": 1}, {"version": "cbfb038c69b12de0906d93aa0690712a96660d08338255e2bfdcf6feb85487eb", "impliedFormat": 1}, {"version": "5e4a6e61ddd5d9501f7c4bc7d0c1a9c36d7e03eab8340de5fd3e93c9a05a73b5", "impliedFormat": 1}, {"version": "55fd91897e28954fc9731b6e6aba3119f597634b5ea12ac0e65c51ee6933ae16", "impliedFormat": 1}, {"version": "25c3f21fe02fa5eabceee27c5e048f562b4e4995d054355d6883012ef4ae811f", "impliedFormat": 1}, {"version": "0a8b260d4da268f07abec25ff1a8e02f9a62bb30091c714691ead14222dbabc6", "impliedFormat": 1}, {"version": "0a31c0f3ff84ea0817b4a0eaf967a1f494ef36d7bd048e62d331b5921612db87", "impliedFormat": 1}, {"version": "f382f6e720fe9075431bca0190355594cf6987ead66f28708919248bc536a6b7", "impliedFormat": 1}, {"version": "a99ccdff3eda5c028c054812f51feb7a70c516089033d64141bc0c2e545a300e", "impliedFormat": 1}, {"version": "072f2c74fa93ace28d012ed430b842e3429f283731c9098bc61458c7943d6f1d", "impliedFormat": 1}, {"version": "da85c5089034621051c0730bb8b3c0aa6f0ce8399e1661d1b6ec5035e39c488f", "impliedFormat": 1}, {"version": "eef160102ae89c1fab6077588eb777291b2f6e08a191e06d0cfbc29bd50dc9fc", "impliedFormat": 1}, {"version": "538d170976d1fad3a15139a2d9f8d64f0435ab46ff7fd2d8e87645a39f0a0ae6", "impliedFormat": 1}, {"version": "e95638ec2c66709b779b9c5ae8cb1c155aa3dc14177ec265c5b19a9af1da380f", "impliedFormat": 1}, {"version": "ec6d116f270b49fd2c421a28d2b77d3c502f45b7fc2637821ccf20ce6904e532", "impliedFormat": 1}, {"version": "78afcc56b4bd140e7ee9d89052e6a66c372ed0a7798dd34bad5f22a3764433bd", "impliedFormat": 1}, {"version": "f010d3763e209bf051d0d602a22215cd9f90c8af1febf00787c453aee04e530f", "impliedFormat": 1}, {"version": "94eb3ab4761d1385c438b609f323918d07f8a5081b146a51add44c847ba1102e", "impliedFormat": 1}, {"version": "91eb0ce83e91586fba013a93cfc7c4136fe4246516028cefcdb00c15343b4063", "impliedFormat": 1}, {"version": "7b1dab45d6bd9dc6b65466ad1e69e8b05e06bc9b2431ee48be53ba52a9bea600", "impliedFormat": 1}, {"version": "f7ac629b2536e7e6fea08fb7fb529226bee59ddc1c122783d40cc0ebccbe5218", "impliedFormat": 1}, {"version": "0aaaa19de5128497cbc36ce182eeeb3b601aed5543562cedd75fa70d67d718d7", "impliedFormat": 1}, {"version": "7220461ab7f6d600b313ce621346c315c3a0ebc65b5c6f268488c5c55b68d319", "impliedFormat": 1}, {"version": "b14c272987c82d49f0f12184c9d8d07a7f71767be99cb76faa125b777c70e962", "impliedFormat": 1}, {"version": "fcf79300e5257a23ed3bacaa6861d7c645139c6f7ece134d15e6669447e5e6db", "impliedFormat": 1}, {"version": "187119ff4f9553676a884e296089e131e8cc01691c546273b1d0089c3533ce42", "impliedFormat": 1}, {"version": "aa2c18a1b5a086bbcaae10a4efba409cc95ba7287d8cf8f2591b53704fea3dea", "impliedFormat": 1}, {"version": "b88749bdb18fc1398370e33aa72bc4f88274118f4960e61ce26605f9b33c5ba2", "impliedFormat": 1}, {"version": "0aaef8cded245bf5036a7a40b65622dd6c4da71f7a35343112edbe112b348a1e", "impliedFormat": 1}, {"version": "00baffbe8a2f2e4875367479489b5d43b5fc1429ecb4a4cc98cfc3009095f52a", "impliedFormat": 1}, {"version": "a873c50d3e47c21aa09fbe1e2023d9a44efb07cc0cb8c72f418bf301b0771fd3", "impliedFormat": 1}, {"version": "7c14ccd2eaa82619fffc1bfa877eb68a012e9fb723d07ee98db451fadb618906", "impliedFormat": 1}, {"version": "49c36529ee09ea9ce19525af5bb84985ea8e782cb7ee8c493d9e36d027a3d019", "impliedFormat": 1}, {"version": "df996e25faa505f85aeb294d15ebe61b399cf1d1e49959cdfaf2cc0815c203f9", "impliedFormat": 1}, {"version": "4f6a12044ee6f458db11964153830abbc499e73d065c51c329ec97407f4b13dd", "impliedFormat": 1}, {"version": "44c1a26f578277f8ccef3215a4bd642a0a4fbbaf187cf9ae3053591c891fdc9c", "impliedFormat": 1}, {"version": "815f7cbe81eea0dbf8588a4b816508474afe4fb11798ce62fdbfad9883c48442", "impliedFormat": 1}, {"version": "9696e4d7201576a5cbca4ff661919ac4970310d2077d5372d4d8c8946899bde0", "impliedFormat": 1}, {"version": "161ac1fb2e087312cb732b4f84a1bf955e630501fde9898433f747b265680ed2", "impliedFormat": 1}, {"version": "6c246edf923812e030066371c9880d0900f40c605c495598eb4c6a46f5b5e604", "impliedFormat": 1}, {"version": "22a9a62a9a0df6ea92af561dbc26f5080d053921bc1bbbb79efa0de3904a9364", "impliedFormat": 1}, {"version": "78b0065ca3731769efd285dd3600326bad750b6e92583220aa3343fa1daf23ff", "impliedFormat": 1}, {"version": "1f0719c370f347558eb826b423f6bb467575b26b1e3b7eb6de71d53292f0eafe", "impliedFormat": 1}, {"version": "fc7f9c8676a128e870e8f476a97da1651e4793bccef5fb3ab263386daa967549", "impliedFormat": 1}, {"version": "0a65df27e0e29b3a6aac41fca2028ca19ca9259a0a38a3a1f48b990b477b071a", "impliedFormat": 1}, {"version": "6655716b4c860ac51e746ed8ad294c2a205661a5e006dafcb0b73a20b368e6d8", "impliedFormat": 1}, {"version": "99daea5e406b93dcb3f237ca63521a74f6e33a2d6359b2db05c4d95709481317", "impliedFormat": 1}, {"version": "dcd915ec1eb7841ffdd71296e01a577bef2a6330da2c2e163b01107da48dbe1b", "impliedFormat": 1}, {"version": "167e6b4e019d056223783db9a0c60a2dcdfdbe3b0b0c913832d8166ac55af45d", "impliedFormat": 1}, {"version": "b9e654d169b2b06fcc1f7b8f285d7f4c5813abd8b3c939a65e02e3a02df50404", "impliedFormat": 1}, {"version": "671c388abd87ec4dd2a6006708f295a57263c680391162f0c04144e127af8ec7", "impliedFormat": 1}, {"version": "4379ba1e366a31ccbb53f9bfbe12c5d4047d9011d8f241b6b789074b97aaeee3", "impliedFormat": 1}, {"version": "cd8da02aa032154cef7cb1170d5fb234e9fe020bb08984d86c153534fd2d5de0", "impliedFormat": 1}, {"version": "7c1f4b5bf6b970bf77e0597fd8f2bdcd2237879c0b8902d85ff4225376207348", "impliedFormat": 1}, {"version": "456105330b371dd2eaabd40146a9c60da59f23e24f2dc6eb7d921ca26d014165", "impliedFormat": 1}, {"version": "64b99a241a46e573f21a7f753f35ad3bd8af0f31a1354568d01a4f4aedfb886f", "signature": "3b6c787b451f30597e435a3ca84398762f0c88e594eccca365e8e2e99cb7805a"}, {"version": "74b511058eb9fedaf233354e177efb58767100eca2e2f5346f5e9fc33fdbd3f0", "signature": "999d632d0c4c0c29641498d912a6787f8cc30b2cc02fc308bb4a7d78885c10c6"}, {"version": "b6769efe2f7130980dc99c30e8a9ec4bb77f29e768bbf9b8460a8066134bc7e4", "signature": "69ea7afc755c65ee4d1b162ecd5802d23734d16e60ed9b927e9df65785793326"}, {"version": "8f823ded5db65cb22cc999d5b46a68a634994ccb77b866462b5b73b7e860adc4", "signature": "8210a7b67dd0fbf6db8bd74e0974692d812af3dd68b67fa39e7632d6005abb11"}, {"version": "e8e360c875f4b4ec8a2cf94dced1247e0bf786ff92e020e54bc2e6624e724761", "signature": "615465953907f122dbda7d716c81bb7fb229bbaa3507c3594848ff938e85b9d3"}, {"version": "584350be4290e273c3e6802aa7d61e159721daa58acfe8bd7a0ddc8c60076fd6", "signature": "b828d82178c5a3901a2b0bc311252437999647f15a893268279cf56d8f01243e"}, {"version": "ce99a726f341145bd6f506acdfb5d0ec02b07e501c1efb1916986ce1fab96fbb", "signature": "e4baa1e96fcdb883a58e91743166cacdfd1970505e29dce7cdb2a86c9de5e729"}, {"version": "ce59ac81d711dfe8533c77ec5520571db91930aa3b5a997fe7ea5ff922d043b7", "signature": "ab0c10d36efe00d4acfb485b55ba71fe66c043a32eea410d1cd7c801ccfaa4cd"}, {"version": "137c48d974792edba288a498f816bf018b655219b838b1ad3953838b43384f4a", "signature": "a78b4f05aa10df68e45541ad580e23bf02df79db31ef549ae2b253fbd60f7df4"}, {"version": "0cd8fc3faf3cce0af89a01ff0e92e40ccbe43a48ac7c74189cae18a423656420", "signature": "2023d93698d8cca6926f419aa6d43fdbf12e15db60d845c68d9ffce1d60e12c7"}, {"version": "535e737d9a21c14eefd234f6f7277bea703e6ceb5f4b38a00ac39380000f6b5c", "signature": "3e81fef8d4275d728cfd04dda314e1c0394e832c555b8aa8ee003036e82153af"}, {"version": "8dff38a0ea2048011e3dd76cafe46647a7f2eed87f893baeababc3142565c9b7", "signature": "a4d77bafa220dbfb8a980bbe6dc4e6be1cf774aa873ddd032dd42059874e0663"}, {"version": "b67b6c69f0899b9212385aca1ee5609bc9859dad8f4a86a292ba2ca9b0c044e2", "signature": "006ffe65370a40caef2533e9081d0b06678e0f9305fc3ff3fa112995fbefe683"}, {"version": "2e01509053f2237f181cc11b3525cb050a043947e0cfa2ece4d98898a0d526ad", "signature": "58a2beec407305fdb65d4f89de5865279eebcd60e2eb4e38d0ee27fb757fb0c4"}, {"version": "3e3425a7c61b4d383d6331d6c05f9e4fef72036d05e7d7767ffe0dad7ebd8cfe", "signature": "ba5a0729ffe5208395d07412309c784756225184cdf3f84fc239d1853416c458"}, {"version": "6c3704d7ccdf69e437791e6e71cfb563fb76e4e789ca13480ecc0527d579ac53", "signature": "ddb0d5cfab3053c623652019df779b8e128c1cce8ee8114920f9b6950b3d077c"}, {"version": "2aa838a58eb44d67687aae76cc821ed66d161e0aeac685ee054f04bcc076909e", "signature": "bb77574fc5d73d2fac71a68a057f8e3c147b0ccaa07904cf358750633a48f2f0"}, {"version": "d35ac244bd3687060ad1091be3fff74018f85fbad195ea44ac08e00ea5a35ccd", "signature": "0770b835f0ad720cc9d72ee46b52e86efcfeaf01aabb1ddf79ef5324327d74d1"}, {"version": "17acbdb30499de4f3afdba1651c1c73c9612c324cd3571f0bd330b4a4dcb72de", "signature": "5def5d0882a47fb8a236b5ed0cf22bcf0d5327bfd4274ef21b5ab6dbae58003c"}, {"version": "f42d44b641997f9dd5b927fa759a67c9fc03bed5628c7d62e76f3491ad117cbb", "signature": "7e0d10d3561359fab09d49df9695d0cc798ff26ccff25a26d0b7e23f35cc9415"}, {"version": "8b32e44312f0e8c0da9f415c2be8cb513d954e308e0e46bc6bd40b24508fb3d5", "signature": "735736cf0d85af1615dceb0ed6b1dfbb4424b3afdd29dfab7cf2a06f8425c3b4"}, {"version": "6e2506a65839fe525922f094b67c7a7d5afb2d94e70a5659e82bd184bc4eac47", "signature": "5f372fe4e9f87f1140d165842cc5b08c18074cc3c8ef93815821cc498c516df1"}, {"version": "cc5110831fe610aece076220cfdc163817fc295ff4f898a52464c17461f20eae", "signature": "a7601b4dcb10a9be5babb7a5ba56c48e1dc6d9a53f61e472ec3f12ae016c5191"}, {"version": "030a8c3e0a9fd99d5e299df2f7392ae1d367071fcd6fad398a24804edede9f19", "signature": "865685d162727e367ea7ea9e3eaa34c5df6d79c0a2f838b4567a28cb72174736"}, {"version": "a61b919551f92606dd1bb1561f7bd81d4cbfafb0f635dc170b6bfcf330ff811d", "signature": "6e973e54eebc40544b3779fe2f9dabcade4d907bf8225b1233c9e8fd0bf40a63"}, {"version": "9d8ae88145168babe687dc5c605f19eb68d44078b2ee1ed736f159e398f015d5", "signature": "624996516b5f34cf971e479a1df6f975bd17e3a1fdc2fcfc7589331a80f49771"}, {"version": "38513c880827b3a13d85680aa56978d4ab921812c2c6532303d1081512a09591", "signature": "68d3796e467f0eb60db5272970fa293cc06aee2a99c9b8be5cded7f2cce6002b"}, {"version": "05e83b418664e137dc08173d1eb93dfdf719e17e60c9b3aba8ad8a847c29b461", "signature": "6ce7ba024f8f0d5fc392aba0bbbf2c4e89ce26054902e60e1ab179daaaa5e5aa"}, {"version": "578aa2a3f3d96d5a33e09413fe437eeb15c024f9005f9d7e4edecaa5acfd8338", "signature": "0d72feafd403e3c078ac1de58ea15ea20d28fe44f99bc9b5ba10aebbd10912f6"}, {"version": "04e13be2b63088a6a630dc63ddbd6b15f8346c5b13c21f8a686ed6796625396a", "signature": "96123ec8f16b0fa0179b7e40b816e522b693130100b6c61e665de707bf2ce0a0"}, {"version": "97a780c44449887c6a2f4c774fbd598e3e947634cff4a19c736825a6ff8d934c", "signature": "ffa1de0e72f0247955dc4c2d24b881171cceb018a82dc5d14461ef71a3929954"}, {"version": "f82a869e4811523444d60e625e612cc41ffbb96b3162f3e8f424962a6f60c18a", "signature": "aee300f7ba6b7b3626ba701ff8847a8b70f18167c7879bdb526441589fc0b09e"}, {"version": "1773ba4ddcbdeda414393d71d6385a2cbdfb278ba93f28dcb569d42ff6d5bfd4", "signature": "d92158167905f0bd298ca9907cf17547a02f8965caffa0adc1152d94cea22092"}, {"version": "f8e23e8841ceb616c6dcdbf75ff9decac8e5ac093997f0ad241dac481681e136", "signature": "06980552a658cea9a6b1cfd535ab81491f08f72f7d38fda0cfbcd76e9dc428d8"}, {"version": "0283a5cd7a5da497e58b23f226b3ed6c7761b5a54d5526353bf441c267dd4e0b", "signature": "b66f01e1c04e653b5528a14dc24b8641d2e7547f797685dad8e16015b613d17b"}, {"version": "fc80535c3ad36e45c2d385b583bf566472d32d30c25da6472aa8766373cf79a4", "signature": "ed76c32b74a3371504b01c570e46d0a4cbf1c89574aaed5f67435b1f1087c7c8"}, {"version": "d90cc9c53112600370fbaddb772d91744828a8076148ba98e9bbef5f04f71e9f", "signature": "8cab251a515f454e2d34dd10475dff5bed671796683c408f0226dfbac202241a"}, {"version": "20a39d6e42a36c45a23b8fe7e63c16164b1a6a143b58400d280dc1575b27b36e", "signature": "5168b8f98da7209708e2c2d7e8ba1d34c4832ca186a9a2ec036ef3b54fd78631"}, {"version": "9f309dcf38162c405ae6f3b44f1db840b0b2957a57243e65f9561060d67b1eb4", "signature": "8017ea3c7d95d677c73d8a524e26491df00ca26714b2aac0961eff563bba10e1"}, {"version": "32c0afed21bf39b710da6502aa4b168e06655eb0eb402555d1fef456fb4c2178", "signature": "612c8e136e9ca287e109f723747d1e6db9017300ba666e08c8f4cd11f5a53120"}, {"version": "a66a8a8e6f1c72d7b343cc8b43b7ef8ea9bc36a2ecaf7f64a783d77c01639c61", "signature": "010d3c429654712bde9d27c710939ae59102d57642a8db7f96a9adbeb8879abb"}, {"version": "12779793d5e31b323e00b6091a00faa9fb544139dec2f68456f7126849cfb24f", "signature": "bd33a421994c9a9a14066493aee8f4153f0c85a8de21c70578efda9536d271df"}, {"version": "a426450477e82f47601e5f6ec5e569f3192b3e093028be8d41b27c36d668b9fd", "signature": "053622a9231dbe421b1f2b0a74e2a71cfa201dca2dc1ac0e35410215059f9c15"}, {"version": "6c61accf6dd1f01a6bf68ba5365066a472ae958b4810ba0e794240bed8adcbad", "signature": "cce5a5feae2b66542043b6a3bda2a120981cf366280bc9cadb153ee3a836de4c"}, {"version": "63369b99557b7b961b474f073919ab39bb48125dd625ba50b28acc400572db3d", "signature": "6abbce020d13ec33e276f6383d9c55e6c11e20c58e4be64fac355f6bf31dc36e"}, {"version": "763815d34bad5640cef8fc6b487d0b4c6bc82b2e265e9f8d16a36c42b7a9c74a", "signature": "12f8651679c2e624237b4de23e57d447be9d37720a97bcc011d88b909caba986"}, {"version": "2a2a81c14be5d9ec68f7498c2f710b48a7140232a27ee4d8a989fb15d243b831", "signature": "aff4cdabd4d02429afe37d51af56443ef9b5d2567537629e663912c8b79e3477"}, {"version": "e561aa45b4dc68d05d7f2bdbff87c51b637ee644c3001efeac07bbd9144d017f", "signature": "479db6912b111c096a2ba328ddcbed32d17437261ed4ad8950f52bda9a8bb791"}, {"version": "604ef0f7c055862f41aa81aa60fc6d2be0687aa231f68697a548233aeb5b1f49", "signature": "1aba384721af58b05b673243284d5091a77a4d9d651680fc218853d983a25623"}, {"version": "cb41f1e30e006a250e23a0b7011944c85a3519a47c85ae73e385556c11aa5a90", "signature": "9fe3fa46cd94bff442485c781f11bf0006095d646f9ecac02afd08482f38f506"}, {"version": "f092a14e0d1c95a4888ce37e60c6c187cecb29a572420d616bd9f50afa5c94d9", "signature": "049d6908b545a31667703326f9ed6194353caae61635ddf324cea526875104ad"}, {"version": "994d6ef22bd233205989995a9ce06470f2f0d5add223816579381bd61d599a42", "signature": "f2c77620dc1afbfe85198d6075c3961def047b4da4322dd0b4a7a9876ce7d571"}, {"version": "82bf86f1138fa7ac43bb8acfef5d904d37bd5812483097e5080c8de6a5bed7ea", "signature": "9b956a2a18d4b3fb21aebef2381762d9ac0070a4d6ea8bcdfb7a296a6358bd40"}, {"version": "d0b17a9119a82817973e7bc148233cd47fdd93170134a3f82e877707cd9eff5c", "signature": "ff10ea19e886fdea6e821f8bae3a3b2f9efde13cfce6411fcea2189f77dc90dc"}, {"version": "f17de8f86995601f21db443f9da339cfd0ce9cc0b2f7d8515c262503fadc7580", "signature": "99d786ab622c9b56adcb58f9f0538172bcded1e8b068423d496eeaec27d90220"}, {"version": "4a568319899914004ee04d5a406baf1b0cc7758d1fa8861d5c44e335b7487820", "signature": "5783c7bc7e2f935270dbcb674c47b947a06d02aa08cee2c43defb52975b0b5cf"}, {"version": "57c5a15546375ce5e60e0388ef854f8a35540c0565a6b79e55b2f3246851bdcb", "signature": "56cde92be182159500f43dad83ff8681d9142674b69c98bdc1bc93207162afb1"}, {"version": "c328c68e8e05891e83e856549aca9c4d4931503f444fc059b43064f0fe6946b3", "signature": "67246f72bfabdb9ec95695e7cd266bdce51829aa50451f43c834931a5c345c13"}, {"version": "02d7cace3a648ac0f68ea7762bf87005df6035a79677f8f13accb93c561802f6", "signature": "52815a92f08930e43334f2556b27583c71bcaabd21c10c60595d2b1bba0d94d5"}, {"version": "f695ada160c3904e2974a657dd18fe11fd45d838ae6819f6a3c0398a3ab7819c", "signature": "8e72774646b24a5c834b0391db4933e24bce7df6228d6ae9298e342b39d128c6"}, {"version": "f76c67b94ca83fca6f444c598e4c8fd96d636ebd6236704c2298c0633ed74037", "signature": "cb19e63846f7ac0645d8978ff18d51f695432072d3fa224cd1eb01cd57dd7611"}, {"version": "5903e86c83966b13edab31a8c9e43970b9b7dcd375f40ff59d09fddb3b0a44e3", "signature": "478cec6255a5787f01ab13a361f6b93e42bac34eab71a385652fb785b14d11a4"}, {"version": "55453b842481ae2ff8be351bc4d1af581db8a00ab14e06e4cca885bf470720b4", "signature": "f6fd01b768ae440b7e6a2ad144b9b1a5ecaf662998cc994733ae8c2d74c94cf4"}, {"version": "9b8772681c803856e99eec08ad2b5083bf22164dffac53db543fc9acd801f73f", "signature": "28de0dae3400ca22e319cd470382e1345f33be8ba76c37943a615f0eae327120"}, {"version": "b1dd1757979b99273c73137469b8ac1584d5ce2eeb4b0b0f9ffe2b11ae033e37", "signature": "5c2387ece1da0a323f4e739d76ee96419c4b1fbf6d661224161128155b7721a9"}, {"version": "a6f3f73a0b84203fb184ab452846ac8b88374e833c92c7e2321e697ee3e19e67", "signature": "acdd4bbd9bc6307c4069e07678fdb554a81b126471fcf422d0235c59c8c7843a"}, {"version": "5de930b5d5454051ca56591f1faf2356d89c77866a2686fde4597068f06723ca", "signature": "e4bcab6f60434e3ea6ac50d79729a96c7b404b57f299306e54043519d391ff5f"}, {"version": "174e259c983778c5a965b9f906f28319d3df65466ab5f8e4b832fbce22e76edd", "signature": "3d8a668aac5b5a165f2fc02896aa8e6ea80c7f0dc865f1e0b4c414590ff7d774"}, {"version": "2d7eb8a479694938863960d66a4e8fa6e846d85f447dd93cb6cb9811de4188e7", "signature": "27361f53ea94cbe3c98b6c8fdb619a9ae40ea7729516fe5354dfa7fecec93a1e"}, {"version": "e8dc4df5c1f7ca7b393034d29e162c201eb3c4072e08bb3074d148f8c47a9e62", "signature": "4b151bd99f67b3841de035aec954fa5b4f471f9b453b3833fce9a7a07a86da80"}, {"version": "4c5a587434a1d989dc4e1d43496399eabfa06e226685e138bc0c4e293a986f0a", "signature": "572908b1c86c56b216cf041d2edd654f0966b296561ae0b5e3a3b09ae7bd4598"}, {"version": "7fe2b576b84067a5a9fbabf87ef91c97fc3ed0cdfbbb42393da3064c6411f992", "signature": "da8802fb7d49aad66e3fa4766b487bf1665b82c9e61faafba96787d2f8cde535"}, {"version": "afed7fbc03be5836806605b16ef01607cb19c4f34fb5a5169c3600d7af84fc5e", "signature": "10cb8fea028c1559fb60d271eecb049b6cca3bc18c367c718ef04b991f0d300d"}, {"version": "ce3b3be4ace2f1bbeb6ee809335d81ccaae37bca87d44150568e03001dc9c225", "signature": "23aae7a292c765c4836d272b45091c4d9f7b55fc07f2f40b38a94f0c19ad1af9"}, {"version": "e89900eda980524b5957c0c71689dadb1b16bc9fa6fff08671ff906c8feb16a6", "signature": "50e39678a70a462dc5a8b153730e300f00d45569cc02307c6a4127539cddfe46"}, {"version": "4eca6429f427dcc008295475b260f635d69e032d6a4d0cb76d14156c3add9e15", "signature": "9c5951b324c73f4ca2107ed73aab0085e2e8b0702c30082ad4533cba47d99264"}, {"version": "c801f9ac139846a7052bea4c63a27d3c8191bdc81f83c1973333f4c95099652e", "signature": "7c8cef89d7e657b99007720e435ee4bb3976b60d8e16c22b5f2fb0a83255f698"}, {"version": "ddf984c0f708761d1a5559063a012fa8926b6917fa7459a9999b575f0c2cd0bc", "signature": "9709693ede26e0152dea3808e781be0f4f5342005c9f263ea55fa65b9b6d7b2b"}, {"version": "3380b51dd18c55ef4129fefd6915180ed4d339abfd9d259f51d669e9035d22a5", "signature": "a4d34cb65f0e882dcafcda4dbe78856a5e6de2c25faaece0f854b8a1e1a46e80"}, {"version": "653f796e57f57e8289868a7f95ca6a9dce6631ca4c2338ae031ca3be61135195", "signature": "ba41642922250cc4bc3d4357abf263cabe583f2a6623170c2e32ea41b614d093"}, {"version": "ca7216d84ebbb2779303ad921214ecc570ce2912e0dfe79618f3815d3aaa45cc", "signature": "1cacf783e1851ec49e29a26d18ac819790bae3323752df7e3b1199f9c8d7ad78"}, {"version": "2c34533555f022bf0367657ef78a7ae5d13d3a7aedb6d6044966b536f5560861", "signature": "16386df1fbaeccbcf0337d3e482661761b35e1e7a1fc2439d225afedd1bde445"}, {"version": "c7c7f2cf80fc23b2d9b81a599e66d4ff5eb195bb9ea39c8c4493fee409bea524", "signature": "6665138a546417854e2e4052c4ad623491c10d2db87e93b50a6c6e56d88493e0"}, {"version": "4bc0af1811177d089e8c5ac42c9ba4deb830abdf4ad73448dc613fecb3a7623f", "signature": "93e1b6020078a55235ca74a8fc69cb23708ce4782d4e92c6feb9bfc7657f568c"}, {"version": "8edbbe77ed76d1fd34473e6eee32842ad924125a0e1f4d830168db2fa0e4586a", "signature": "84ceab1f99c513c5c5a9fba45e4c81ed0db4ee4ba39d044bc618d4c63d3cc493"}, {"version": "1ad5cd08861e9baba6bc79f3366490bac1262b7e8bf14f5e85a480a61ead56b0", "signature": "2e1de782466c173dd37d52346d138d6e977f3357ccd2859e6b0fa1e2c48d95bd"}, {"version": "84f21c3c5fd5a18eda20a686b75a50d2d9ceaa403f4e231fb0600b4e3eff35cc", "signature": "8246da6759ef4efe159d91009fc7c53d6dc819dba8d27428ba8d50d420b7679c"}, {"version": "5ed6a44e97740a3943889d8bc30af1729f684c6f29bca2dddef291ad2f68ddd0", "signature": "2b33052a09f6e3990c9782ea2cf32226023f441a82827286e02e8fbd6f2066ae"}, {"version": "34d05aeeb72fce9b19f3b0e357fabf7fcdd964b01cdc75841a2170ae6ff5014a", "signature": "bc97e287525a89f8383d31b293a5864fe407a5c90a4d04102c3b9c9c1c8323cd"}, {"version": "37d47df5cb8a71d19f280cd1376b0cf275c8ef5b703c1edee2bff0698d68aa59", "signature": "90fd983ea7aba602945fb86159a96d671818a9340a09e36edeb069b187ce8a0d"}, {"version": "8609c36448e9fcca92b08f352ea9ca3cec4f24c58688c786790a7e27d3e71655", "signature": "083f71b521c5925869102d3141a0c65886a42e3fcca54402f9b607fd94cfc7bb"}, {"version": "b0b7117ed49ea15caaf39015cb6c2f5c4bdb9829a1b30e58c7cbefb4962001fe", "signature": "7ef3fdda450e6257e03f2181a00ccc8a99b2988e64e5479a6d17b6f9a059b84a"}, {"version": "1476d7ee7bfbee9b8e1e2ceed9c3d319fa051498197de26587b1e2f09d54e473", "signature": "286e0d4b3b9e49681270f8aff6e23bf24aaaf3f0391471f4c10264ace146b285"}, {"version": "923c9982e421d84cdb1efe1fb8ad93e13aab123a8722310f9b99ad817782c27a", "signature": "87bd57fc273bc3f7dc0626ce2fd1c4962635765f98e162b9673e6aac37751a1a"}, {"version": "bbda86942162f9173910bac99eac3fd4fd8593750432cce3be2b07b7bf2a080c", "signature": "308a90d0c0dd8f4b3c275a42424be49b65ff84d97061efa1d01b536eb385a1bf"}, {"version": "d49e0e9ecdc4ab1ce846be58fe70ee54d29c79d940a049a58ee83ae989de4001", "signature": "4d6dc7c1108d6662c73413d040a42a69af9cdf36c2f5d9ef00cad8b5682102fe"}, {"version": "f84eb9ad6dcc3609f22466eb270150c8e5cb8d62532b641ef92d080e4c5ada6e", "signature": "1088ad14fe849d1855eed25724ed1d64e0426be4c9e5eeb6da792cb39f1a3636"}, {"version": "94d8b621c00f42c7ea168ff3b30376d787743541d8c58263555a79c3a249501a", "signature": "2313d5734e455ce618c54a67173357f869a0ccd34b8363d5b60e6a2e577734ad"}, {"version": "01f628f2eb585e8ce27f79b5664c73672788c3e74cd2cc3d2831772c35a8b06b", "signature": "d3d9fb32ab09468c26d65d7ab22ceea85507f0ab9d1291a90c06278b959b15eb"}, {"version": "0c00b1737bb3c5e2f2c2c821c6668d34ce991e42c8264b19d4a720c918bc231a", "signature": "8a0155344b2cf56d81d85fd307ceefd91fcd06556f0b4715f67ddd2a94fd0e0b"}, {"version": "ce6571655a1ba8fe95a10524da1643a06953b6dab17c5c20feda2e930c744146", "signature": "16121f133bdc5386d1792da7394b5228696de882ca50960e560f9e9e9393c3f7"}, {"version": "f0820528b7f980d49c42507854b12b7b962db0e4f77eeb973495fbc0b8a4db35", "signature": "8f25f56415288b1115af24b43c1b1c10919d66c878b99bbfc7b718c665eaa6dd"}, {"version": "b58c77fcd53ed1346e396190670c7337e01f186e6a36bd99a87e31396ff178e5", "signature": "c3edce8760234d0ae39a1df7b5ae92d5c18c860cbb45662525af3b7c0627e33e"}, {"version": "d0f0af361bfc7e4ea926e3c8e21561e71cc7667cd82f3a2363eb957963b28c22", "signature": "d9a2165aac2dc2a9d72c991133a9b52762a47ce919b82609506eceb42cfcec88"}, {"version": "3247feeee712080d1b4b95368d6f975f8fd2ce5115543b96d80830d21c428dd6", "signature": "a3c26ed1ee8f7735d02f892d0cd7beeb3dddab6135daf682cfe31f94dc5e7991"}, {"version": "6c772bc1859ec29a78858506bad80a3f59235487e2966f26a7da1ed752c76238", "signature": "56b4193b7b1abf1d58eb562131e472cbe9887f7ad2fb8ef5ae8acb6ae2285efc"}, {"version": "87dd4a17731de2ce38f900f969a70a746a0dfa9817305287febdacc18da8be87", "signature": "4df43c6a4ce69caed11942ca4cff33ab4f2cc155c25be288848d7e67bedee857"}, {"version": "ca0542d74bd4e7e0879425a042685142fd3ec3431b6467b407e477527963fccc", "signature": "08f107bdb5a87c19c7f97038f9a48c176c4d57388964a85655a12988bab7883a"}, {"version": "397956e33b80a08d7a7994db1eb509e3bf3ef680e80a449ad42f68965cddce75", "signature": "15486387ff98efc340b22c91abe7a9ff2f07aa9fd455b25aa64077ae2c4d9356"}, {"version": "a931a1907f164f4e95145f9c197cee1b77c219c307d43ae43a2512419ee29f5b", "signature": "485439c12ab5a6a0175ae4a809cad2e4b980816e73414089827c1a63f7e3fcb6"}, {"version": "3ddd865b8b535b3c81072762c032745b2a3ac5a6c9dba42ddd52f4d548edb480", "signature": "7e173a0c2c34a5ada5fa3ca0d55fe4f847b1b9b20478fe039f8d2ca8ae69fe51"}, {"version": "e006a06a48381e30ef6ed4fae3eef0d7aa2f265f774db320e2099d856b60e5f5", "signature": "1ccedf74edc259f3d69306b7493c9792a85bf57002570d8e58ce8dd1dda7eb53"}, {"version": "0bd147e36fd63db4bb9be1c4a725f368b588562e141b66f1ac98be5b5eac0bd0", "signature": "630ed98216531acf0fc975a79c54c4681c65e6def867554aab9d010db00e5a77"}, {"version": "99f4f7bd7df896aeca7ea3e014c5be2e5c802a92cea13f536a1918f5abbdf29b", "signature": "0abd556642a89d06f297228eac920cc9c8d6b901f909b10e19ab312e59edfa4a"}, {"version": "90d91117ea66fb9a6acd1d3156693d4698ba747d111479402c061fdb60e15cf7", "signature": "80770d683d60893ad2cc24ea6d584fce7a1f834b573eca2d6d4e62686b50c4d5"}, {"version": "1a13cf9a69f9bc84a9c4821ccbe701ec59eaf7592383caf8be6732df7ac8a9a4", "signature": "007859fee5a297cf8c832bc4172ae3801343a1c20f9c9c2e6461b4192d7a34d5"}, {"version": "3e6238cd93b55813a0546f23b997feea6af223ea3e03abedf65e25255ff10d09", "signature": "6467bb2c0551ba97674a89cd2e17dd3102119badd75e45730d6ab18dbc0209b7"}, {"version": "1e1416dec1b470fb8752c3866f12f5255dcf89b7be2f7de9e29fd77eaecb4e4e", "signature": "960d686d1ac34aff444ba37374721537ea3ca6b3623b0a9762fdba80df903843"}, {"version": "abc8c45c3e46da8d2136140f8446664b29b43b52c60439202dccef906de08717", "signature": "b7a4fc1e70c9f6754df9f29d5a7b35b32af55c338576109bde92ebb93397ce63"}, {"version": "4cab2fede2300fc7ce073016c3360491e29a557e13cf0d9f4eab9c4532267e3a", "signature": "92b865bd32b79c959421f6b4038abed31addf71188441b2b3699d3171bf3bc17"}, {"version": "faa01179f3bcf1f36e6958853e3d7b529085e6bd8fba4eb55b52f3e5538b4358", "signature": "dee8f9c0c17007dd01b999678d39c4aafb2db135782852dbdb9402c4847a7b83"}, {"version": "38e271a4b01877834b343c1c25087d0c5533603297200363a79100c478ee70f3", "signature": "60bab4008469b72f17becf51bfa430b1d09c5b2b633a5c9169c20655a5c057cb"}, {"version": "8169aa17e72b755fa5e9fcc882f9c81120cf2223050a61f9b9a5995884d55646", "signature": "511aae7794c49a93f3c28bdeaf25b10b02f43ffdd6a291416d944f33fb06276b"}, {"version": "ef04c210d8810de1f8ce53881cc97bceec3067b5cd0c13868a1f8097da9fcb0f", "signature": "63e8ef508393e43d3c16528f20ff91e6b3392194ffbb90797ca059a395451f99"}, {"version": "2007ede54e14f8780cf39eb39e43fa60fd8fa2738f48959f6328570e863819ac", "signature": "656a8c6a07bec28e8995f3fa2f99e9719b2c3a7bd4debf33a04d8439e4fe4452"}, {"version": "5903d39bf3ece07e62537173dddcf63d05e60cde4fd2113891bf0a21c3ed9d5f", "signature": "3d5ecc770ca1f1067072c097f5d7fc9f76acf1f8abd68f07f4cf2022afce47ff"}, {"version": "1829fe7f4e94d24842790ee0fb6db3bf55ea04bd95c9dd16adb83a6ac2f31a33", "signature": "4af595df686684b1a9d9b86ff976498cbb8917379006ca69822d860340978fdf"}, {"version": "720981d41f410a6ea3dc4fdbc267c474feef7b098d017975591c1bfdee45780d", "signature": "9fa6156a0a17243100ec0e2aac888de7547686ae982cc620b64e901ea2847ba3"}, {"version": "d81c8e9053d3aae634d7252d98ce2a788c1bd0d4388f16c02c921c47bb409d34", "signature": "b159df87e0f1e057ac63fa3b225b9527c9f9c835acd5472b4a43105b906f1c28"}, {"version": "5f98e58589cfb3b0296ff4989c7364405abf9b5d2c6d74aae7aa10c07abfa5ca", "signature": "e955b0f4edddc076b686fd478d96bd62df91ed88b774d4d7972912f532891958"}, {"version": "34d363fac9ad7171f20e8ee8fbeb8a534bef3aa4108c76c75c21bfbd3650de6c", "signature": "c2970147fdbc5d276935accaa6e2a6e2844e303bc2a6dd92f7623fd3a94eee2f"}, {"version": "e23540c598fd9e4548cee450ff93e73eb823dd86c35cf860792460a5c2e8f49a", "signature": "b7c6cc6ebc4191a0739f76c97d51890e5798c07b889b921db304ac02bd0b8f09"}, {"version": "33fe8d1005cf9f915feecc660ec8bc79f50b109727bd91c427f4b389e816f952", "signature": "633e661b422e08d7f74e13bf7fa8848ff045230aeaafa2025aa71641e965f694"}, {"version": "892730dff1794b5f34aa6b79d0984f79523ace9b431d4b712c74d50259c43ecd", "signature": "646ffdd61f99892108b3c026b02e0b3f2b3fd55150f552bb8b57ae9764f796d7"}, {"version": "6feba102d6c6bdf21b014061133ae44541b48b2a22b6d1c764e9cd805f862caa", "signature": "716bce3d042d4c46cf3fa979c4577b6a23d5859a79a907ef55139f17e8195ad2"}, {"version": "0aa6cc8333ed1e5d0f067f6e296c92fc5c2d4bd4769bea663f573129456c05cc", "signature": "9917b371f53b7838810d6c170c5ba590f43b60b2925a82acb55b9c8ef2221f96"}, {"version": "5aa882be52b887f656af7a6f7e6d5ca971f07d8e277ab272b65bec34cbb6fa56", "signature": "ec89de6c4914ecab4385756a26409a8526df923dd0a9767c818cf42824c7b545"}, {"version": "4d7a0c8e2da53fd5b0d98c85039f0ade6354a7ed4662e919c3730c34820fdae6", "signature": "f8e5e23c57153a10f1b33bcf66b3b6402482996c528344dfd54c4114b115789d"}, {"version": "83e27bbd7304ea67f9afa1535f1d4fdb15866089f0d893c784cbb5b1c6fb3386", "impliedFormat": 1}, {"version": "6231095d0b356894ceec8856ce4e354a38a773a62067978f3af9c478acbcf2e9", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "66754e21a2ac3ffc0eb3fc55cf5c33deb2c115b726fb2c4368b870c5e85106ce", "impliedFormat": 1}, {"version": "ccd9f8f7c8760d8eb0c0c112c772a9756d59365a61fd5cc102a889fed79d0076", "impliedFormat": 1}, {"version": "d7ccbcf0e92d0209f499729768c791fbc278ecad02c3f596db5b519eb4f5d496", "impliedFormat": 1}, {"version": "85542ea0f889f8993e6b4631c541640c17c230bf5cf18d483184de9105536b4d", "impliedFormat": 1}, {"version": "8cc2eed7caa0e187fae67a1e7fdb4da79079489744c6e5be9e0839d258fd5995", "impliedFormat": 1}, {"version": "aaf2cc4016c33c836fcca0dbe20d7a97bf17cafeb581a57a62267b1c38930bd4", "impliedFormat": 1}, {"version": "b56f87a14cc2ca254c01b08da27fb4d33d1d6a84148575af34a51185cb206b8a", "signature": "22e2ce0dbe0b43a105afc528395f86a8c758a4efa0a1ab2aa3956bcc9624b8d9"}, {"version": "80f4b99d577887214270884a40cdb328c1954a18ae2de1f1db0822d98d3d7278", "signature": "85e1cdcaa35e580205336a696020943072a7285f894d3d546927f3c95cfaa3e3"}, {"version": "3f6eae5e1255ed7145a5d3b86fdd946aa49e595d8c661f0c8b7aedce450c4841", "signature": "9c0d91c8dd07033d2bdc78c408507d4cd5c5f96be51b89fbf7931bc2df7752a0"}, {"version": "e51c4e755a9002e36179512ac648dbd11af6a244f1143f1339a8a73e590012ce", "signature": "a8ce1f67cf1af5cf481828bc9eff67efb4c1475b14f52c5f0c3bd0f7589a664d"}, {"version": "76473bcf4c38aeed6cde4eaaf8dcc808741dbe5280b957b982603a85421163a6", "impliedFormat": 1}, {"version": "40c4f089842382be316ea12fd4304184b83181ab0a6aa1050d3014d3a34c5f8f", "impliedFormat": 1}, {"version": "d88b3dc8b7055665059ea06ffafce9467fc4bdfa7cb2d7a6f4262556bb482b0d", "impliedFormat": 1}, {"version": "b6d03c9cfe2cf0ba4c673c209fcd7c46c815b2619fd2aad59fc4229aaef2ed43", "impliedFormat": 1}, {"version": "32ddc6ad753ae79571bbf28cebff7a383bf7f562ac5ef5d25c94ef7f71609d49", "impliedFormat": 1}, {"version": "670a76db379b27c8ff42f1ba927828a22862e2ab0b0908e38b671f0e912cc5ed", "impliedFormat": 1}, {"version": "81df92841a7a12d551fcbc7e4e83dbb7d54e0c73f33a82162d13e9ae89700079", "impliedFormat": 1}, {"version": "069bebfee29864e3955378107e243508b163e77ab10de6a5ee03ae06939f0bb9", "impliedFormat": 1}, {"version": "4911d4c3a7f7c11bad0e2cec329a19a385d10ea83b0b69c76e032359e388f624", "impliedFormat": 1}, {"version": "a69c09dbea52352f479d3e7ac949fde3d17b195abe90b045d619f747b38d6d1a", "impliedFormat": 1}, {"version": "4f6463a60e5754bbc4a864b2aaf8fecb7706b96a21b88f27b534589b801978b6", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "56d13f223ab40f71840795f5bef2552a397a70666ee60878222407f3893fb8d0", "impliedFormat": 1}, {"version": "4ffef5c4698e94e49dcf150e3270bad2b24a2aeab48b24acbe7c1366edff377d", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "2534e46a52653b55dfb5a41ce427ec430c4afbaaf3bfcb1ae09b185c5d6bf169", "impliedFormat": 1}, {"version": "afc6e96061af46bcff47246158caee7e056f5288783f2d83d6858cd25be1c565", "impliedFormat": 1}, {"version": "34f5bcac12b36d70304b73de5f5aab3bb91bd9919f984be80579ebcad03a624e", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "82408ed3e959ddc60d3e9904481b5a8dc16469928257af22a3f7d1a3bc7fd8c4", "impliedFormat": 1}, {"version": "3f2478baf49cf27aa1335ba5299e2394131284e9d50a3845e3f95e52664ff518", "impliedFormat": 1}, {"version": "f50c975ab7b50e25a69e3d8a3773894125b44e9698924105f23b812bf7488baf", "impliedFormat": 1}, {"version": "8bd106053ee0345dde7f626ed1f6100a89fb85f13ea65352627cf78c5f30c553", "impliedFormat": 1}, {"version": "76650408392bf49a8fbf3e2b6b302712a92d76af77b06e2da1cc8077359c4409", "impliedFormat": 1}, {"version": "0af3121e68297b2247dd331c0d24dba599e50736a7517a5622d5591aae4a3122", "impliedFormat": 1}, {"version": "06ccebc2c2db57d6bdbca63b71c4ae5e6ddc42d972fd8f122d4c1a28aa111b25", "impliedFormat": 1}, {"version": "81e8508d1e82278f5d3fee936f267e00c308af36219bfcee2631f9513c9c4017", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "413a4be7f94f631235bbc83dad36c4d15e5a2ff02bca1efdbd03636d6454631b", "impliedFormat": 1}, {"version": "20c468256fd68d3ef1fa53526e76d51d6aa91711e84d72c0343589b99238287e", "impliedFormat": 1}, {"version": "4198acced75d48a039c078734c4efca7788ff8c78609c270a2b63ec20e3e1676", "impliedFormat": 1}, {"version": "8d4c16a26d59e3ce49741a7d4a6e8206b884e226cf308667c7778a0b2c0fee7f", "impliedFormat": 1}, {"version": "288dd0c774a5c6e3964084c7a2bc8cc6b746d70f44a9892d028d04f915cf7ebc", "impliedFormat": 1}, {"version": "d61c7c41eb1960b1285e242fd102c162b65c0522985b839fadda59874308a170", "impliedFormat": 1}, {"version": "e8b18c6385ff784228a6f369694fcf1a6b475355ba89090a88de13587a9391d5", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "1805e0e4d1ed00f6361db25dff6887c7fa9b5b39f32599a34e8551da7daaa9c2", "impliedFormat": 1}, {"version": "abc1c425b2ad6720433f40f1877abfa4223f0f3dd486c9c28c492179ca183cb6", "impliedFormat": 1}, {"version": "fb0989383c6109f20281b3d31265293daefdd76d0d30551782c1654e93704f48", "impliedFormat": 1}, {"version": "a4210a84a82b3e7a8cec5b2f3616e46d523f4f10cc1576d8f2fb89d0987b341e", "impliedFormat": 1}, {"version": "8207e7e6db9aa5fc7e61c8f17ba74cf9c115d26f51f91ee93f790815a7ea9dfb", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "9f1069b9e2c051737b1f9b4f1baf50e4a63385a6a89c32235549ae87fc3d5492", "impliedFormat": 1}, {"version": "22d48bfb37261136423ac687f1fa7bd4dda3083f767416d409a8260cf92bc8fc", "impliedFormat": 1}, {"version": "29c2706fa0cc49a2bd90c83234da33d08bb9554ecec675e91c1f85087f5a5324", "impliedFormat": 1}, {"version": "0acbf26bf958f9e80c1ffa587b74749d2697b75b484062d36e103c137c562bc3", "impliedFormat": 1}, {"version": "95518ff86843e226b62a800f679f6968ad8dac8ccbe30fbfe63de3afb13761a2", "impliedFormat": 1}, {"version": "1b952304137851e45bc009785de89ada562d9376177c97e37702e39e60c2f1ff", "impliedFormat": 1}, {"version": "698ab660b477b9c2cd5ccbd99e7e7df8b4a6134c1f5711fa615ed7aab51cb7f7", "impliedFormat": 1}, {"version": "33eee034727baf564056b4ea719075c23d3b4767d0b5f9c6933b81f3d77774d2", "impliedFormat": 1}, {"version": "c33a6ea7147af60d8e98f1ac127047f4b0d4e2ce28b8f08ff3de07ca7cc00637", "impliedFormat": 1}, {"version": "a4471d2bdba495b2a6a30b8765d5e0282fa7009d88345a9528f73c37869d3b93", "impliedFormat": 1}, {"version": "aee7013623e7632fba449d4df1da92925b27d9b816cb05546044dbfe54c88ef4", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "e10177274a35a9d07c825615340b2fcde2f610f53f3fb40269fd196b4288dda6", "impliedFormat": 1}, {"version": "c9d70d3d7191a66a81cb554557f8ed1cf736ea8397c44a864fe52689de18865a", "impliedFormat": 1}, {"version": "998a3de5237518c0b3ac00a11b3b4417affb008aa20aedee52f3fdae3cb86151", "impliedFormat": 1}, {"version": "ad41008ffe077206e1811fc873f4d9005b5fd7f6ab52bb6118fef600815a5cb4", "impliedFormat": 1}, {"version": "1aad825534c73852a1f3275e527d729a2c0640f539198fdfdfeb83b839851910", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "badae0df9a8016ac36994b0a0e7b82ba6aaa3528e175a8c3cb161e4683eec03e", "impliedFormat": 1}, {"version": "c3db860bcaaaeb3bbc23f353bbda1f8ab82756c8d5e973bebb3953cb09ea68f2", "impliedFormat": 1}, {"version": "235a53595bd20b0b0eeb1a29cb2887c67c48375e92f03749b2488fbd46d0b1a0", "impliedFormat": 1}, {"version": "bc09393cd4cd13f69cf1366d4236fbae5359bb550f0de4e15767e9a91d63dfb1", "impliedFormat": 1}, {"version": "9c266243b01545e11d2733a55ad02b4c00ecdbda99c561cd1674f96e89cdc958", "impliedFormat": 1}, {"version": "c71155c05fc76ff948a4759abc1cb9feec036509f500174bc18dad4c7827a60c", "impliedFormat": 1}, {"version": "ab9b9a36e5284fd8d3bf2f7d5fcbc60052f25f27e4d20954782099282c60d23e", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "88003d9ab15507806f41b120be6d407c1afe566c2f6689ebe3a034dd5ec0c8dc", "impliedFormat": 1}, {"version": "d3f2d715f57df3f04bf7b16dde01dec10366f64fce44503c92b8f78f614c1769", "impliedFormat": 1}, {"version": "b78cd10245a90e27e62d0558564f5d9a16576294eee724a59ae21b91f9269e4a", "impliedFormat": 1}, {"version": "baac9896d29bcc55391d769e408ff400d61273d832dd500f21de766205255acb", "impliedFormat": 1}, {"version": "2f5747b1508ccf83fad0c251ba1e5da2f5a30b78b09ffa1cfaf633045160afed", "impliedFormat": 1}, {"version": "86ea91bfa7fef1eeb958056f30f1db4e0680bc9b5132e5e9d6e9cfd773c0c4fd", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "b71c603a539078a5e3a039b20f2b0a0d1708967530cf97dec8850a9ca45baa2b", "impliedFormat": 1}, {"version": "0e13570a7e86c6d83dd92e81758a930f63747483e2cd34ef36fcdb47d1f9726a", "impliedFormat": 1}, {"version": "104c67f0da1bdf0d94865419247e20eded83ce7f9911a1aa75fc675c077ca66e", "impliedFormat": 1}, {"version": "cc0d0b339f31ce0ab3b7a5b714d8e578ce698f1e13d7f8c60bfb766baeb1d35c", "impliedFormat": 1}, {"version": "a45c25e77c911c1f2a04cade78f6f42b4d7d896a3882d4e226efd3a3fcd5f2c4", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "5c45abf1e13e4463eacfd5dedda06855da8748a6a6cb3334f582b52e219acc04", "impliedFormat": 1}, {"version": "62f2d6b25ff2f5a3e73023781800892ff84ea55e9f97c1b23e1a32890a0d1487", "impliedFormat": 1}, {"version": "858c5fcc43edd48a3795281bd611d0ccc0ff9d7fdce15007be9e30dad87feb8e", "impliedFormat": 1}, {"version": "7d1466792b53ca98aa82a54dbed78b778a3996d4cbda4c1f3ba3e2ed7ba5683a", "impliedFormat": 1}, {"version": "1828d8c12af983359ea7d8b87ec847bbaf0f7e6f3c62fb306098467720072354", "impliedFormat": 1}, {"version": "25c28649e44aeead69a588b73b91ba27b0a08a7cdb7782f52ee1d8122305912c", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "a7a9834ecced9288b5b6f5859443307adedaf02a59b1412c8a9af2055081823f", "impliedFormat": 1}, {"version": "cd51ceafea7762ad639afb3ca5b68e1e4ffeaacaa402d7ef2cae17016e29e098", "impliedFormat": 1}, {"version": "1b8357b3fef5be61b5de6d6a4805a534d68fe3e040c11f1944e27d4aec85936a", "impliedFormat": 1}, {"version": "4a15fc59b27b65b9894952048be2afc561865ec37606cd0f5e929ee4a102233b", "impliedFormat": 1}, {"version": "744e7c636288493667d553c8f8ebd666ccbc0e715df445a4a7c4a48812f20544", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "c05dcfbd5bd0abcefa3ad7d2931424d4d8090bc55bbe4f5c8acb8d2ca5886b2e", "impliedFormat": 1}, {"version": "326da4aebf555d54b995854ff8f3432f63ba067be354fa16c6e1f50daa0667de", "impliedFormat": 1}, {"version": "90748076a143bbeb455f8d5e8ad1cc451424c4856d41410e491268a496165256", "impliedFormat": 1}, {"version": "76e3f3a30c533bf20840d4185ce2d143dc18ca955b64400ac09670a89d388198", "impliedFormat": 1}, {"version": "144dfcee38ebc38aae93a85bc47211c9268d529b099127b74d61242ec5c17f35", "impliedFormat": 1}, {"version": "2cf38989b23031694f04308b6797877534a49818b2f5257f4a5d824e7ea82a5a", "impliedFormat": 1}, {"version": "f981ffdbd651f67db134479a5352dac96648ca195f981284e79dc0a1dbc53fd5", "impliedFormat": 1}, {"version": "e4ace1cf5316aa7720e58c8dd511ba86bab1c981336996fb694fa64b8231d5f0", "impliedFormat": 1}, {"version": "a1c85a61ff2b66291676ab84ae03c1b1ff7139ffde1942173f6aee8dc4ee357b", "impliedFormat": 1}, {"version": "f35a727758da36dd885a70dd13a74d9167691aaff662d50eaaf66ed591957702", "impliedFormat": 1}, {"version": "116205156fb819f2afe33f9c6378ea11b6123fa3090f858211c23f667fff75da", "impliedFormat": 1}, {"version": "8fe68442c15f8952b8816fa4e7e6bd8d5c45542832206bd7bcf3ebdc77d1c3f3", "impliedFormat": 1}, {"version": "3add9402f56a60e9b379593f69729831ac0fc9eae604b6fafde5fa86d2f8a4b9", "impliedFormat": 1}, {"version": "cc28c8b188905e790de427f3cd00b96734c9c662fb849d68ff9d5f0327165c0d", "impliedFormat": 1}, {"version": "da2aa652d2bf03cc042e2ff31e4194f4f18f042b8344dcb2568f761daaf7869f", "impliedFormat": 1}, {"version": "03ed68319c97cd4ce8f1c4ded110d9b40b8a283c3242b9fe934ccfa834e45572", "impliedFormat": 1}, {"version": "de2b56099545de410af72a7e430ead88894e43e4f959de29663d4d0ba464944d", "impliedFormat": 1}, {"version": "eec9e706eef30b4f1c6ff674738d3fca572829b7fa1715f37742863dabb3d2f2", "impliedFormat": 1}, {"version": "cec67731fce8577b0a90aa67ef0522ddb9f1fd681bece50cdcb80a833b4ed06f", "impliedFormat": 1}, {"version": "a14679c24962a81ef24b6f4e95bbc31601551f150d91af2dc0bce51f7961f223", "impliedFormat": 1}, {"version": "3f4d43bb3f61d173a4646c19557e090a06e9a2ec9415313a6d84af388df64923", "impliedFormat": 1}, {"version": "18b86125c67d99150f54225df07349ddd07acde086b55f3eeac1c34c81e424d8", "impliedFormat": 1}, {"version": "d5a5025f04e7a3264ecfa3030ca9a3cb0353450f1915a26d5b84f596240a11cd", "impliedFormat": 1}, {"version": "03f4449c691dd9c51e42efd51155b63c8b89a5f56b5cf3015062e2f818be8959", "impliedFormat": 1}, {"version": "23b213ec3af677b3d33ec17d9526a88d5f226506e1b50e28ce4090fb7e4050a8", "impliedFormat": 1}, {"version": "f0abf96437a6e57b9751a792ba2ebb765729a40d0d573f7f6800b305691b1afb", "impliedFormat": 1}, {"version": "7d30aee3d35e64b4f49c235d17a09e7a7ce2961bebb3996ee1db5aa192f3feba", "impliedFormat": 1}, {"version": "eb1625bab70cfed00931a1e09ecb7834b61a666b0011913b0ec24a8e219023ef", "impliedFormat": 1}, {"version": "1a923815c127b27f7f375c143bb0d9313ccf3c66478d5d2965375eeb7da72a4c", "impliedFormat": 1}, {"version": "4f92df9d64e5413d4b34020ae6b382edda84347daec97099e7c008a9d5c0910b", "impliedFormat": 1}, {"version": "fcc438e50c00c9e865d9c1777627d3fdc1e13a4078c996fb4b04e67e462648c8", "impliedFormat": 1}, {"version": "d0f07efa072420758194c452edb3f04f8eabc01cd4b3884a23e7274d4e2a7b69", "impliedFormat": 1}, {"version": "7086cca41a87b3bf52c6abfc37cda0a0ec86bb7e8e5ef166b07976abec73fa5e", "impliedFormat": 1}, {"version": "4571a6886b4414403eacdd1b4cdbd854453626900ece196a173e15fb2b795155", "impliedFormat": 1}, {"version": "c122227064c2ebf6a5bd2800383181395b56bb71fd6683d5e92add550302e45f", "impliedFormat": 1}, {"version": "60f476f1c4de44a08d6a566c6f1e1b7de6cbe53d9153c9cc2284ca0022e21fba", "impliedFormat": 1}, {"version": "84315d5153613eeb4b34990fb3bc3a1261879a06812ee7ae481141e30876d8dc", "impliedFormat": 1}, {"version": "4f0781ec008bb24dc1923285d25d648ea48fb5a3c36d0786e2ee82eb00eff426", "impliedFormat": 1}, {"version": "8fefaef4be2d484cdfc35a1b514ee7e7bb51680ef998fb9f651f532c0b169e6b", "impliedFormat": 1}, {"version": "8be5c5be3dbf0003a628f99ad870e31bebc2364c28ea3b96231089a94e09f7a6", "impliedFormat": 1}, {"version": "6626bbc69c25a92f6d32e6d2f25038f156b4c2380cbf29a420f7084fb1d2f7d7", "impliedFormat": 1}, {"version": "f351eaa598ba2046e3078e5480a7533be7051e4db9212bb40f4eeb84279aa24d", "impliedFormat": 1}, {"version": "5126032fe6e999f333827ee8e67f7ca1d5f3d6418025878aa5ebf13b499c2024", "impliedFormat": 1}, {"version": "4ce53edb8fb1d2f8b2f6814084b773cdf5846f49bf5a426fbe4029327bda95bf", "impliedFormat": 1}, {"version": "1edc9192dfc277c60b92525cdfa1980e1bfd161ae77286c96777d10db36be73c", "impliedFormat": 1}, {"version": "1573cae51ae8a5b889ec55ecb58e88978fe251fd3962efa5c4fdb69ce00b23ba", "impliedFormat": 1}, {"version": "75a7db3b7ddf0ca49651629bb665e0294fda8d19ba04fddc8a14d32bb35eb248", "impliedFormat": 1}, {"version": "f2d1ac34b05bb6ce326ea1702befb0216363f1d5eccdd1b4b0b2f5a7e953ed8a", "impliedFormat": 1}, {"version": "789665f0cd78bc675a31140d8f133ec6a482d753a514012fe1bb7f86d0a21040", "impliedFormat": 1}, {"version": "bb30fb0534dceb2e41a884c1e4e2bb7a0c668dadd148092bba9ff15aafb94790", "impliedFormat": 1}, {"version": "6ef829366514e4a8f75ce55fa390ebe080810b347e6f4a87bbeecb41e612c079", "impliedFormat": 1}, {"version": "8f313aa8055158f08bd75e3a57161fa473a50884c20142f3318f89f19bfc0373", "impliedFormat": 1}, {"version": "e789eb929b46299187312a01ff71905222f67907e546e491952c384b6f956a63", "impliedFormat": 1}, {"version": "a0147b607f8c88a5433a5313cdc10443c6a45ed430e1b0a335a413dc2b099fd5", "impliedFormat": 1}, {"version": "a86492d82baf906c071536e8de073e601eaa5deed138c2d9c42d471d72395d7e", "impliedFormat": 1}, {"version": "6b1071c06abcbe1c9f60638d570fdbfe944b6768f95d9f28ebc06c7eec9b4087", "impliedFormat": 1}, {"version": "92eb8a98444729aa61be5e6e489602363d763da27d1bcfdf89356c1d360484da", "impliedFormat": 1}, {"version": "1285ddb279c6d0bc5fe46162a893855078ae5b708d804cd93bfc4a23d1e903d9", "impliedFormat": 1}, {"version": "d729b8b400507b9b51ff40d11e012379dbf0acd6e2f66bf596a3bc59444d9bf1", "impliedFormat": 1}, {"version": "fc3ee92b81a6188a545cba5c15dc7c5d38ee0aaca3d8adc29af419d9bdb1fdb9", "impliedFormat": 1}, {"version": "a14371dc39f95c27264f8eb02ce2f80fd84ac693a2750983ac422877f0ae586d", "impliedFormat": 1}, {"version": "755bcc456b4dd032244b51a8b4fe68ee3b2d2e463cf795f3fde970bb3f269fb1", "impliedFormat": 1}, {"version": "c00b402135ef36fb09d59519e34d03445fd6541c09e68b189abb64151f211b12", "impliedFormat": 1}, {"version": "e08e58ac493a27b29ceee80da90bb31ec64341b520907d480df6244cdbec01f8", "impliedFormat": 1}, {"version": "c0fe2b1135ca803efa203408c953e1e12645b8065e1a4c1336ad8bb11ea1101b", "impliedFormat": 1}, {"version": "f3dedc92d06e0fdc43e76c2e1acca21759dd63d2572c9ec78a5188249965d944", "impliedFormat": 1}, {"version": "25b1108faedaf2043a97a76218240b1b537459bbca5ae9e2207c236c40dcfdef", "impliedFormat": 1}, {"version": "a1d1e49ccd2ac07ed8a49a3f98dfd2f7357cf03649b9e348b58b97bb75116f18", "impliedFormat": 1}, {"version": "7ad042f7d744ccfbcf6398216203c7712f01359d6fd4348c8bd8df8164e98096", "impliedFormat": 1}, {"version": "0e0b8353d6d7f7cc3344adbabf3866e64f2f2813b23477254ba51f69e8fdf0eb", "impliedFormat": 1}, {"version": "8e7653c13989dca094412bc4de20d5c449457fc92735546331d5e9cdd79ac16e", "impliedFormat": 1}, {"version": "189dedb255e41c8556d0d61d7f1c18506501896354d0925cbd47060bcddccab1", "impliedFormat": 1}, {"version": "48f0819c2e14214770232f1ab0058125bafdde1d04c4be84339d5533098bf60a", "impliedFormat": 1}, {"version": "2641aff32336e35a5b702aa2d870a0891da29dc1c19ae48602678e2050614041", "impliedFormat": 1}, {"version": "e133066d15e9e860ca96220a548dee28640039a8ac33a9130d0f83c814a78605", "impliedFormat": 1}, {"version": "574346a298daa75f0ae64ff8a9f5d2c90e1e3596364434ba70d5a7ed32ec41c9", "impliedFormat": 1}, {"version": "b5f0a98e00b8f8ccb6e9c4181057cf9b02f2350e426cbf42643f1033a13b2852", "signature": "7cd3884f21f26d2aca97e468591dedd2488ae5cf8b95f47a39e33b05c3cf2a5b"}, {"version": "06f48d704bc6d015466d9fdd7ccad4a241c3f7ce05fe91cca25daf756852d1f7", "signature": "41e8cc43189ec8d9f04159c636f7d5b0a9c002c0dc4c3c98d14b70df261125e1"}, {"version": "d3c02d8e84f3cfa397d6719b84442fd2ddeed6a0343267c2128bb607cf27b224", "signature": "dae7514854d2624d6b793b4f9d19d274376a79ee57f2a171cfd3b608b35fcbe8"}, {"version": "febf32871fbf3e673d5157bfb8abd324f9f2463f881f31c4f3d218d41ad4ae18", "signature": "990d6d364a17014799b16380bcfdd49f8c321b82f5bc8da1f8c386ad6958dd32"}, {"version": "e1a6e494fad645523571f7cabc17e2f60ab2239707a0279acbadfdde1769f065", "signature": "6571b4e221b8b10f30654ac52bc623971ac8e35152e7d7652272ffeb0539f394"}, {"version": "74568c3cf35cbdafe9702d220e6014615595cd028e5fb722c44c3b535fc47d58", "signature": "11ac11ee16c56a6e1333d17d6bb5c334bcc5b2a45d183f147c60273fc36ad4bc"}, {"version": "4fd559bab2bc5907ba8977c87c4a09028147193fd7b2baff3ebda8e19791819a", "signature": "8a07a1cbdb060e1103966afee15126750e6ae8011f0fc5fe5631d37bbd181177"}, {"version": "548dd9a5f21738f6ef3709823fe6540fbb9f163bb7046214235929ad06b69175", "signature": "039df669133bfc1f6190409183de1720d1b21970e072e9b807554e8db89a9059"}, {"version": "a8cafaafb985f82e89293a24140f3ba32dcf80ace105f8e6d1fd72df46e58751", "signature": "70ebc0b228ac83b2b859b4db4d49918138ad035c90d9c8b2b2e336e4c16c9beb"}, {"version": "58f4fd808e5990f14a97603dd5e9782c9d51f3d0858399e4af08cefa3f7ed898", "signature": "c526ab4ab651c404a60b9e16ea7ba4060563bf911a1a56c9773fb17c351e2b5f"}, {"version": "0c8816f09a7deee9873dcaca099b8ba5d55b93d0bbcd57cbd5033d908652a625", "signature": "8ef77dde4c08e42bcb4a0df172aa874a5eebc91d754511c87f0064e9c8cf61fa"}, {"version": "3704b79cbe8c884756900060abcd289abceb58627bff640ee1b4b518382a0aa8", "signature": "aa762a1db4389c3315cf6f3ce11a1d4b03317cb7823889062e59a25476e1e4cb"}, {"version": "2b121978089961fb7ac97ba5a643deab6dcce87ff50ea08fac4cad9b1cd78a37", "signature": "672ab733129d33d3928f5376ed70ad5eebbd722db2aefd4e9fbe2dc7728e1c49"}, {"version": "b492a2e52f4f0230d4a3b3e4e4b31172fb7d4397bc5a21975784acbf4e13bb5a", "signature": "7fb88d3a2bbdfc23c92264f991dc8c48f7c04fe6044cf2d2fc6cabbc2920d72d"}, {"version": "efbc6b31f2605ca1e9ae7a57ee6f10b27ff72f25d7216bab1f0f7d33f5a89b03", "signature": "c771815ad8f010b19fedfd6be167b5b582f0447ec9df4a01f01ae6eea8fb7f8e"}, {"version": "f016bda11834c346e0edb1493163bbda31750c3369979232eab110cad885503c", "signature": "85a626796a501e660b7be446f83ac21c5823a1ce710e493427b283700478d2e9"}, {"version": "b272fe8fc631a45e34483495b32054e793c8f2173dbe792884f5056a412ba973", "signature": "c8f09162beb5befdd3352282f5023d3fe24984a293da1604097a4a70de7c4d23"}, {"version": "ef94b860744aef4109fe6485b0f638f41524d66b7ef22476790ab5ebbfcae9cd", "signature": "4201450b7552f4c7c5b1892c16333a5505e7e45709d4f0a6396536635dcc8a84"}, {"version": "02d02dae6136c3eff65f8f3e607685693590a12f605415dca9def0c7c983c1db", "signature": "79131c03e7a7b841f3f686e96aca69ce3911573c535e57d62226e4def13e565c"}, {"version": "efd488013933767612184fc328c7b889247a78958562329c4331745b6b0947fd", "signature": "7beea214c986257a99c93ec8be1ece312770bae90b0157e860c4deac492ae93f"}, {"version": "8be868999e86a899ac2ee4cbdd5f63f64303729b7788895fc1c9598f48b9c13c", "signature": "d3bf0b7e522962b891fb165f781e4c78964fdf10360c04a7ac29cde8a5c1ed9b"}, {"version": "f622f167f1ed1fb25174d1a4172ee9a53027447848fa6536d4d889dd5e708765", "signature": "666d811978831a9842ae6530359b38bcb7d5bb482eaf2f7f58aacba9740666b4"}, {"version": "0444587a90c694cbf7c67984577d0102a555a0a35ec7885a766daa86147b62eb", "signature": "3dd9239be17be3927490a6c49187a763589f3367559d4a0b6652935c83bfee3f"}, {"version": "f52b6006b72dd6f86bfb966b5d5b7b056c3010543d34548c465fddb5bf90ccfb", "signature": "e2ef9a3ccaf738048d8fb0cf154bcd7f84f910b67a8fb5fd1bcef6f071687f23"}, {"version": "3e949f0ccb460ad16bc36324ce1dc61d53aba10993e2a4a715b2bf3f2562d0e1", "signature": "c5c7506ef2b1507ac4e43e61414626b967890794286a7fe2fd2ed0f8601e0408"}, {"version": "ed8f18925a664eb9943a0f4c8d9e73a24890dd0d0d9f63e7f0250062edc4f9e8", "signature": "e697418da584fc270971cc340be7a5239094a90eb32c0da3503db05cc0cdd220"}, {"version": "c04d8a9a5af479c7ab3a473e260f1a18dd9656a649b335c62489928e82e7080a", "signature": "9a6e716f78db16996275fdb567e3c9bda58354960fce783d7e3d311d6fdd67c5"}, {"version": "60c85e13a10c2dce1a33bdb1e82d7e8c5af9ffb6d47225f47d86b2261cd23f1a", "signature": "bea0c7b2195f6682d33542117033557f39cb8fc892534e46117ca2d3a4a42b08"}, {"version": "123544584f18652555c158f573732d48d97f68c1eba66b417be68765bcd9d285", "signature": "d186e6241303481fa2707f7b0765a5c668622deaeb4f6a8ec0bc2ae04588cf38"}, {"version": "7289b0f801fa6546eb60b16a9e57c6e0742ccc32724e32563b1b153b564941b4", "signature": "af898fa56c457099a6dd1d8b3a304ddc40f004ffd56140c08fb2190d6cff1cd1"}, {"version": "a463e539fb7637f43bfc6f2a23182a9c9c929ad6e17514928eeb544863f0a90a", "signature": "a43b57a8bec795499823bb1412f6390fa94eb4582f1da89021fa402b39782159"}, {"version": "363e5e5b17b65d11ee3cce15581635e39166cb7e7c5590c4da5174285027b506", "signature": "0489d190849e39ef2e37ed37eaaa997656e25acfe280b7d8472435e898066219"}, {"version": "24067b0d4c6ca9975b33dc68e48d457208cf66100c97cda1ec99df1722042cf9", "signature": "3fdf938c520663236af53b01a076b25e90bf8432c6fa8425bf5329726a782115"}, {"version": "486f0d226b3387baba7e970e8a967f6af79c83aafcce8b35334d17878030c77c", "signature": "3789b482cdd1e3b95965c9eeb116da588b2b8e9073e2e077b0b20eafdce9ccd4"}, {"version": "425c8e9f4bf4c5e7d611bbc99464e723a4490a97dfa568dd9a885e33f9dddac9", "signature": "96f7d35cf46a4caedd1f7e9f326bfe3b3a7507d28cc63d09df1555992402bce0"}, {"version": "24ba46e305519c126156b89b597c67f3e5d50a1695bf07b04b99ba8ddbb5f12b", "signature": "bf8bb3155417be61d573eee2e0ba945e8884e8590ddb4e081e16ba7d949a0b27"}, {"version": "812b8c063db0007ab1020258d1fce6846d272a09fcd85bc7de100e2e5c85870f", "signature": "ecd12ffa345eec9cccefeb1b7113bd42b362440965adadfc648c523d6f8f3bd1"}, {"version": "653a4fe259b55c32cd5a67cb7fb467d99a0994c02a6e271715b1c0390762efb7", "signature": "68d07ee170f6a3b98e66a967cda122255171bb477c5819fe27c9759298e5c3fc"}, {"version": "ab0328b70167ba40d364246343b088622f32efd6a818119ab59b799ece96f4f7", "signature": "e94a372e7350675877f9547072eaa91d44b23ccaf74fafba2b379696b25dd967"}, {"version": "a8b729499c700fc6ae6187eda9a4de77f8810f803ed1005410c6ac5177845c51", "signature": "59f44b5f3bb7e5d28a57aed81f63a3a76c2170ed1d702c4ee3a6920b44bb7e8d"}, {"version": "25b8864180ca105d30718efd9d469e5875a0c1ab8077bd3ca91ed97d354e3a25", "signature": "97f4d46a16582b49be39b3dfd871a0b86be0f2dd3a48d92e1d7b5f3ab3a2a98b"}, {"version": "196e4a7243ecb0f7bebc135cf1a7f0e352f2cf32e172b1932cfa61155023e4ca", "signature": "30a3122607edfbfc8c99ff5cdda55b5a1faed243f4ae3ed41d3228c7f7b4f7dc"}, {"version": "3acdbe54b18a522546fc6e622d0232d1d2ea082db3b838001c5cbb4218ea8e14", "signature": "419416682191008fd4eee29bf9d8fb71b8f3982e299b97314db32a41969f264a"}, {"version": "f21422e95538bb44411cf01a1140e7e4a2f094694e37ead8967eed1347dc29a7", "signature": "708d31baa2361105516ec2b499dd2e41000368b50149379412c13d284ffb9872"}, {"version": "1c1476b9aa1db3cadc84596ab4381ffe43e964472395d0a20d47711740c69736", "signature": "8d57e2c45509340129dc5d04e63ac98f7be2b99775b0eb68eb6d8e516fe52de7"}, {"version": "8bf27005cd71dc73212484abc9c4defce9f2b71a553a6adcc3bab4afcd3e627e", "signature": "fad1a595fc60494cd1e15271217ede2cd88b509dc2d8f2bd1338ac94d752601c"}, {"version": "7bbb574ea4cbee75fce49cf42d3afbc6ec03d7be559a4a6412c67653ea071706", "signature": "8faa4e48e246bb267970a386e080abd9d9c33a4fd2dd0f8e963773cae438161c"}, {"version": "37b7d59a726505e0b5f4ae38b3fe9153cd2d82d41dbd0a71de3d3e3ec9b90ccd", "signature": "ef0622fe1d82ea6e4b98a7e455d17d2906206d2948ec90c7e54e944f79b981c7"}, {"version": "750fca479061d15bb5f283151b46c797f064998c54534a1159149a77988db249", "signature": "6d6baccca419a940a4dbfa89a40432f11f562b2795513adcc0bd4c7ce0b9caef"}, {"version": "23563b94e82a7e6a35bb73c6103965389fad6c640d535625646dc990975f5ac6", "signature": "76baa980a07125d16f9fe6072ea78d515fdc18559fcd1383caedbb1985e5f20f"}, {"version": "e6ff2b49c83948ee70f6717268cbab5176824fa3819ef2055ee67de103fc828d", "signature": "96fb6f469cc7166547b74e9ec3d873fb7d786b34c9c9a54da7d9f2a659371ddb"}, {"version": "e3acf79fa8502256a404571a4da2cd876a04b809674d1f1439ea118c8f0f0bc9", "signature": "56eebbbac657fb184885e904b2925c1f934a763421743ab080bbeeca5159081c"}, {"version": "61c4665fa0560115fba39343d08b7c34b69c6e39367791ec602bb91f2cd70570", "signature": "1324919c76063cb07cd0f470799b6f38619661acb0d06fe97d3995e39b1251eb"}, "8e609bb71c20b858c77f0e9f90bb1319db8477b13f9f965f1a1e18524bf50881", {"version": "ae77d81a5541a8abb938a0efedf9ac4bea36fb3a24cc28cfa11c598863aba571", "impliedFormat": 1}, {"version": "f70bc756d933cc38dc603331a4b5c8dee89e1e1fb956cfb7a6e04ebb4c008091", "impliedFormat": 1}, {"version": "8387ec1601cf6b8948672537cf8d430431ba0d87b1f9537b4597c1ab8d3ade5b", "impliedFormat": 1}, {"version": "d16f1c460b1ca9158e030fdf3641e1de11135e0c7169d3e8cf17cc4cc35d5e64", "impliedFormat": 1}, {"version": "fbc350d1cb7543cb75fdd5f3895ab9ac0322268e1bd6a43417565786044424f3", "impliedFormat": 1}, {"version": "e3c5ad476eb2fca8505aee5bdfdf9bf11760df5d0f9545db23f12a5c4d72a718", "impliedFormat": 1}, {"version": "462bccdf75fcafc1ae8c30400c9425e1a4681db5d605d1a0edb4f990a54d8094", "impliedFormat": 1}, {"version": "5923d8facbac6ecf7c84739a5c701a57af94a6f6648d6229a6c768cf28f0f8cb", "impliedFormat": 1}, {"version": "d0570ce419fb38287e7b39c910b468becb5b2278cf33b1000a3d3e82a46ecae2", "impliedFormat": 1}, {"version": "3aca7f4260dad9dcc0a0333654cb3cde6664d34a553ec06c953bce11151764d7", "impliedFormat": 1}, {"version": "a0a6f0095f25f08a7129bc4d7cb8438039ec422dc341218d274e1e5131115988", "impliedFormat": 1}, {"version": "1d2699a343a347a830be26eb17ab340d7875c6f549c8d7477efb1773060cc7e5", "impliedFormat": 1}, {"version": "45785e608b3d380c79e21957a6d1467e1206ac0281644e43e8ed6498808ace72", "impliedFormat": 1}, {"version": "a3ce619711ff1bcdaaf4b5187d1e3f84e76064909a7c7ecb2e2f404f145b7b5c", "impliedFormat": 1}, {"version": "2a90177ebaef25de89351de964c2c601ab54d6e3a157cba60d9cd3eaf5a5ee1a", "impliedFormat": 1}, {"version": "82200e963d3c767976a5a9f41ecf8c65eca14a6b33dcbe00214fcbe959698c46", "impliedFormat": 1}, {"version": "b4966c503c08bbd9e834037a8ab60e5f53c5fd1092e8873c4a1c344806acdab2", "impliedFormat": 1}, {"version": "17ed71200119e86ccef2d96b73b02ce8854b76ad6bd21b5021d4269bec527b5f", "impliedFormat": 1}, {"version": "b598deb1da203a2b58c76cf8d91cfc2ca172d785dacd8466c0a11e400ff6ab2d", "impliedFormat": 1}, {"version": "79410b2e5ccc5aef9710303a24d4101159e7b89a6b77dcb694b376b07a6b3b06", "impliedFormat": 1}, {"version": "19ce9ec982b542ef6d04d29ce678aad2fa52a67d8087e9c6cd95a4d6d98784c8", "impliedFormat": 99}, {"version": "4af47b2f19621ce8f638167a32f141a3a2c0e71ce8ebf51384393ca1c2654e60", "impliedFormat": 99}, {"version": "0beeeb5964025d8564c03cb0bf1a4b60dc40c01f065ad1a4e9030415f5bc7bc2", "impliedFormat": 99}, {"version": "370d29706526cf66ee767a4a3ee4218c9544a252ce22f231648414348704cb4c", "impliedFormat": 99}, {"version": "6bf53608a27a76ef8580f9577618174f0dd5325142cafb8b3a19aa4850319afb", "impliedFormat": 99}, {"version": "821fe27bd167988c3cc518af8d9591ac1bd8d9e9d231ee9eac7b82786dd9f3a6", "impliedFormat": 99}, {"version": "eef204f061321360559bd19235ea32a9d55b3ec22a362cc78d14ef50d4db4490", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "2ea3c07887a34c982f1d902139569a86bfa4fbf5ab79c3397aec80b2ceb20b05", "impliedFormat": 99}, {"version": "73b67d2e87ac7d7baaca64ca33fd1523c0b3c850cb7db5b9c014f1be7996bed1", "impliedFormat": 99}, {"version": "5d5ae61fce1581fd6424269790a9071e3f8e69b029f5d0fcb46ce618c5dbc565", "impliedFormat": 99}, {"version": "38a0ccc7106312a9f60e034e7cd8ac218774d8aa65f832cee3363a7e65f99325", "impliedFormat": 99}, {"version": "850040826cfa77593d44f44487133af21917f4f21507258bd4269501b80d32f0", "impliedFormat": 1}, {"version": "8f07f2b6514744ac96e51d7cb8518c0f4de319471237ea10cf688b8d0e9d0225", "impliedFormat": 1}, {"version": "bcb6ea18f23dae2c48459d7b86d3adccd6898f824fcbf9da08b935f559896580", "impliedFormat": 1}, {"version": "1363ba7d52f2353d0c4306d0ecdaf171bf4509c0148842f9fd8d3986c098a2eb", "impliedFormat": 1}, {"version": "3a24f4a428f24cad90b83fab329a620c4adbace083a8eda62c63365065b79e73", "impliedFormat": 1}, {"version": "739c2c46edc112421fc023c24b4898b1f413f792bb6a02b40ba182c648e56c2f", "impliedFormat": 1}, {"version": "402e5c534fb2b85fa771170595db3ac0dd532112c8fa44fc23f233bc6967488b", "impliedFormat": 1}, {"version": "8885cf05f3e2abf117590bbb951dcf6359e3e5ac462af1c901cfd24c6a6472e2", "impliedFormat": 1}, {"version": "33f3718dababfc26dfd9832c150149ea4e934f255130f8c118a59ae69e5ed441", "impliedFormat": 1}, {"version": "e61df3640a38d535fd4bc9f4a53aef17c296b58dc4b6394fd576b808dd2fe5e6", "impliedFormat": 1}, {"version": "459920181700cec8cbdf2a5faca127f3f17fd8dd9d9e577ed3f5f3af5d12a2e4", "impliedFormat": 1}, {"version": "4719c209b9c00b579553859407a7e5dcfaa1c472994bd62aa5dd3cc0757eb077", "impliedFormat": 1}, {"version": "7ec359bbc29b69d4063fe7dad0baaf35f1856f914db16b3f4f6e3e1bca4099fa", "impliedFormat": 1}, {"version": "70790a7f0040993ca66ab8a07a059a0f8256e7bb57d968ae945f696cbff4ac7a", "impliedFormat": 1}, {"version": "d1b9a81e99a0050ca7f2d98d7eedc6cda768f0eb9fa90b602e7107433e64c04c", "impliedFormat": 1}, {"version": "a022503e75d6953d0e82c2c564508a5c7f8556fad5d7f971372d2d40479e4034", "impliedFormat": 1}, {"version": "b215c4f0096f108020f666ffcc1f072c81e9f2f95464e894a5d5f34c5ea2a8b1", "impliedFormat": 1}, {"version": "644491cde678bd462bb922c1d0cfab8f17d626b195ccb7f008612dc31f445d2d", "impliedFormat": 1}, {"version": "dfe54dab1fa4961a6bcfba68c4ca955f8b5bbeb5f2ab3c915aa7adaa2eabc03a", "impliedFormat": 1}, {"version": "1bb61aa2f08ab4506d41dbe16c5f3f5010f014bbf46fa3d715c0cbe3b00f4e1c", "impliedFormat": 1}, {"version": "47865c5e695a382a916b1eedda1b6523145426e48a2eae4647e96b3b5e52024f", "impliedFormat": 1}, {"version": "e42820cd611b15910c204cd133f692dcd602532b39317d4f2a19389b27e6f03d", "impliedFormat": 1}, {"version": "331b8f71bfae1df25d564f5ea9ee65a0d847c4a94baa45925b6f38c55c7039bf", "impliedFormat": 1}, {"version": "2a771d907aebf9391ac1f50e4ad37952943515eeea0dcc7e78aa08f508294668", "impliedFormat": 1}, {"version": "0146fd6262c3fd3da51cb0254bb6b9a4e42931eb2f56329edd4c199cb9aaf804", "impliedFormat": 1}, {"version": "37a80d6010f34ea173ed76803856d56a64af6a89b755ae9999689421f2542a82", "impliedFormat": 99}, {"version": "f234315aeb08f02d74769341155afa47ef6ec6873607f55c6a9104d50fc27383", "impliedFormat": 99}, {"version": "1c53e1884dc6550ce179c68e9e3086f54af258fff39eb70274ea9294eb7ce6df", "impliedFormat": 99}, {"version": "2d57bdaafc7cd0ebc006f0e77c869d6fe6148809c08e8b5677aef4150cf4a7c7", "impliedFormat": 99}, {"version": "05c7aef6a4e496b93c2e682cced8903c0dfe6340d04f3fe616176e2782193435", "impliedFormat": 99}, {"version": "67856637713bace00feca7f0d4a9907e6a85bcceeb507e07df852cb5f6467834", "impliedFormat": 99}, {"version": "e666e31d323fef5642f87db0da48a83e58f0aaf9e3823e87eabd8ec7e0441a36", "impliedFormat": 99}, {"version": "69bf2422313487956e4dacf049f30cb91b34968912058d244cb19e4baa24da97", "impliedFormat": 1}, {"version": "d12ab69ace581804d4f264eabc71094ca8dadfa70ae2bf5ccd54a8d6105ab84b", "impliedFormat": 1}, {"version": "973af20e33ebed2f6c3af36062c214b03daf2a0a3193554f6538ea928228b671", "impliedFormat": 1}, {"version": "ca179564af22b92d588ce07d527042767d37bacce79fb78cd6fc7d8ff8c1f329", "impliedFormat": 1}, {"version": "e72396ce544667ab49df38ffb91cb3f80ff17d2ad3df903ec30b1d2ca8ea68de", "impliedFormat": 99}, {"version": "b7e28e06011460436d5c2ec2996846ac0c451e135357fc5a7269e5665a32fbd7", "impliedFormat": 99}, {"version": "d422e50d00562af6bb419fca3a81c8437391acc13f52672dcffdfc3da2a93125", "impliedFormat": 1}, {"version": "2887592574fcdfd087647c539dcb0fbe5af2521270dad4a37f9d17c16190d579", "impliedFormat": 1}, {"version": "37a80d6010f34ea173ed76803856d56a64af6a89b755ae9999689421f2542a82", "impliedFormat": 99}, {"version": "632d6fe34a09b481d3007779ad03e4202e4ed4f73bee02fdfb6b23e51ca4aebd", "impliedFormat": 1}, {"version": "7c4d59b36618af873cc489404906c46e2a2f0629a8416ee08b711f5f02096f3f", "affectsGlobalScope": true, "impliedFormat": 99}, {"version": "3fa571018b674c0cdc74584b04f32c421829c409236e1332af4a87ad904b504d", "impliedFormat": 99}, {"version": "2d37a18dbc84466a72a4b00d0293ecfe3170fc664ca32126db0b7eace05824d5", "impliedFormat": 99}, {"version": "f63e23230f3960b712450cf08f0f31e56acdae3ce65e0bf31bfdd6442b29d115", "impliedFormat": 99}, {"version": "f2d1a59a658165341b0e2b7879aa2e19ea6a709146b2d3f70ee8a07159d3d08e", "impliedFormat": 99}, {"version": "5309c17206a98ed2bdd130eb25a213e864697f5b017f774141c12030e82db573", "impliedFormat": 99}, {"version": "8a3ff3da0cc09f4c5523f6e336635cd0e2cd5cc7e5297186b44b6a6b06e3ef96", "impliedFormat": 99}, {"version": "d5e36726b3ce6d6f3698c4983945bae856c4670ef40ed0bbbf1e855767c7ece3", "signature": "8e609bb71c20b858c77f0e9f90bb1319db8477b13f9f965f1a1e18524bf50881"}, {"version": "c17d8b0dae761cdaf89487e7bee6da59ad1677a337d564e0de28d161db70f0dd", "signature": "96f1e93b3fdddd105339be35afe132292e47d1dc201da985cd952b325ba0f8fc"}, "8e609bb71c20b858c77f0e9f90bb1319db8477b13f9f965f1a1e18524bf50881", {"version": "2fd84fde2f9417dd1ed1b98debd2d17dc0d840515cebf734a1b1d58f3b283b7f", "signature": "8e609bb71c20b858c77f0e9f90bb1319db8477b13f9f965f1a1e18524bf50881"}, {"version": "396f1a7531d18fb6a4ff2cb6d98ced138b9adc75f8b34d8dc1a97fdd4569fd86", "signature": "2bbaa26acd8cce907007d760ef01e7db22db2b9fda7973a60601f9164942e235"}, {"version": "02de0bbf66182329f8acf8595f8dbd6d0036feb9fc64407df7853f53f791c9c9", "signature": "fa3bff21b0b047ead047a9890ae4e42493c25238c6bc8af9d4a8b19bea20f2fd"}, {"version": "ac712e92db94d8ee6d8045f1c2bad0fbdf298ec7c170d1d114a4dbc1aff12212", "signature": "c9af3b627fd22734a9a836b0691d963d8e370c94ca53602ab402db7d804f85d7"}, {"version": "70d6322f426d8692b186f9bad7fd714f19f644957a97ea896b3d972cc31d8693", "signature": "083f81f27279cf3bc71c7dc3f2490e0e2f98a167db3a7349a34a9ac5c6a1f32f"}, "8e609bb71c20b858c77f0e9f90bb1319db8477b13f9f965f1a1e18524bf50881", {"version": "42cbc56328fe9116174d6501c4a7ddbe45ddce3e14ef0b0c98f318a5905a0021", "signature": "8e609bb71c20b858c77f0e9f90bb1319db8477b13f9f965f1a1e18524bf50881"}, {"version": "76159ac859d7acb99aeb86d2ebd99f1f38a6013dfb4e68b881c278388d58f901", "signature": "582299407aed7241327f72cff215a91a914a8a8f2965decd6f5a9e315c42ff99"}, {"version": "c90152ec9ebf7f4598ddc0db88156f5d0dc9722b6b4d2ba38d7647a352c0d798", "signature": "73cd088ba940f039c076b77f4623e30ca9ee03a7e2e0fbc4c8bd73d894bda7bd"}, {"version": "3b5ba57b597593988cacce6fda8d2842d0cc20bb48e5e64d1062b797d4ec7d14", "signature": "e1acbd7cc0050649f7b6ca12fc14b3a6e26b078656634399a0f8512337771cab"}, {"version": "d5025c91adcf2ce013653348326f1a1deb6e2f406b1972c8848bde1c368a67a3", "signature": "89e8192d17ba5e99a3c316181495549372061a7d772473a25eac920f6adce133"}, {"version": "d28c3abc3eb83cd8de8155e373447706daa7ff2669015115333725889ffb00ae", "signature": "0dba2988b0cc5e1cda0c61d83e4060aef581d24c1b443c9789f39733ab2befb5"}, {"version": "0b47413d5e9723bbcd950cc148fdf63f03a7aed9b1dfcf4bef1cdee90d3256e7", "signature": "36936961fa17fa25a73819b7f9d3e3b2d08eb34a4ae6022fea83fffed7a25e7e"}, {"version": "00c6d607b62972ab7580f68412a3480542528ec8fbaff7aade2edaec85131930", "signature": "2e1a57abd26a3b7f2db0534757d1ce4169de2ac700a7a7ac1fb3e833f563eee9"}, {"version": "403ceb221e8410b087600f082aaed518a3e9db5a3da6ec375e32c104bafb0ea6", "signature": "e6fa0dd44aaed64efebb45e1c7f46edae39eaf7c6a47afbd3d330b556287d414"}, {"version": "15138dabdc43617dcb3317ed81c598b06d405f1d14613604e474cb6f0cb10056", "signature": "63a395ca876486393670affbfa65b80ada442cbe5f45b7686bdca6981d187ef1"}, {"version": "da0ecee4373a5a22f814b7e272f39f037153c4809fa9300e317985bd2b507eee", "signature": "7c99e464acdd7cfb1de6c3d5b2bcd33b4ca3fb21d9c4f20ab1e026a65be7334a"}, {"version": "da0efbd96903fccf6bb39e7ed4a9d0f89c5ce6662da28b2901e1d825a818ec8a", "signature": "60c072c1b5c2f0def62232c3ab5873c4149262a88b1aad2256e1eab6b0a0d3a7"}, {"version": "d73b4b4fa1d121c061917094b73b737af6e64e91d045b987c3be4876e7897791", "signature": "2a9c4da9b2d18ab316279b8b0b0a92a3b8d69271921046f863b13515acec0b19"}, {"version": "e7b7c1500c3a48f69a0683818febe0c906950ee185ea73f95a6436ea8132791e", "signature": "e517ed79384cc9dada21612937b152e04159baa9f34947df75c5b82a7d3d0815"}, {"version": "72b88900d97283b79f391027876114c08ffef89a261081201eca0d3ce6d06164", "signature": "cd0b6e526affc08e3ee613422d358d6fb22e6200dbc008f1b441bccf85d64cb6"}, {"version": "1ea76f9ca11a86a1a2d4adfa7753139d70f5bb3be390b3297751bcf22aa21fb7", "signature": "6fe9fd42d86fd49c85e717dafaea3e8ac2a5aee992a3a422cd27c606bb743d40"}, {"version": "58ecb1094043962394412656b58a89376d83d67159d8eda6bee19d0705dbdb1c", "signature": "da127439f775bef5b03d2b177424d4cd2c77fe5a7bd1fa595cb2428aca374ab2"}, {"version": "4eb49f78e0f38b229f141fd50fe6d7b66037bc57292eb5904bfbda363f8264ba", "signature": "93eeef004631133d6ccd289df70099fe33f4eaf4c5923e0cdbe1aad789fc8486"}, {"version": "901f8cbe7714844625c3a8dd5ea80f369a7f6217056e271514a7dc9ac4abd6c3", "signature": "44ed844582052d9d07f6c0d36c46b9a9bb4e1fbaa6b9df91d28ff0fd3f6d3b8f"}, {"version": "1e4f1d67be003a7fcd7ff5233b8db813903b2a3bd7c0d64eb1743fe4eca2cd33", "signature": "c61c1f4c5b971501c028e1398a7749e0bf6a1cc4eda9459f033a69b06517176e"}, {"version": "04eb0d46c106586193550266be2153fb8d5a988fdd439c9a3cfd8a8a19a2e5b8", "signature": "975330316fb64cc62d7a7e98732337ab5ea57c06c45f9809b6ed23433507bd85"}, {"version": "7fd6a6bc5906feabfdce83b81269e3d9e6bd44a78e852af19f2c0b3ccbd44e69", "signature": "7861c472817e23e8e8569ef8a49d2891d39c7b1c277f620fba58ca36332de135"}, {"version": "5b04f43f3c465681fde1f787dec39f4ff56dea30519942e345160842ebfb4c8a", "signature": "66ad5d5919ff36b51d9650b2a3c39a16fc63697f3f8fb595fe22dfda2a998f9d"}, "8e609bb71c20b858c77f0e9f90bb1319db8477b13f9f965f1a1e18524bf50881", {"version": "67ab4b433f6faf2a475a10e39ad6a556ae8ee80e1fe4681491bf7575f9d58eaf", "signature": "8e609bb71c20b858c77f0e9f90bb1319db8477b13f9f965f1a1e18524bf50881"}, {"version": "d01d28b18673372f59b1d54b5eb04d7dc8882d853268b457b3751c340346c0bf", "signature": "90e5fcd8a17d123144e6cf65a93bc2f0c12306572512c4a5e62e8b21af94b2f2"}, {"version": "8e64938da757c1cda6a517989bf8b9e237f66cbe78984750bf4628ca8627e708", "signature": "b03122126494020f4e0194825a105ccdab306cf050eefe0e38d13e89c208defe"}, {"version": "8cd81522a043d99b2c042597c08c27443d8bb8e694a20dfb4f83a71fba9748ce", "signature": "2492b49a7d5a67a785c25fa3013a4ac8253d670bdb4c357374354b2713b8dd1c"}, {"version": "829325a03054bf6c70506fa5cfcd997944faf73c54c9285d1a2d043d239f4565", "affectsGlobalScope": true, "impliedFormat": 99}], "root": [[183, 193], [275, 412], [421, 424], [582, 635], [716, 752]], "options": {"allowJs": true, "allowSyntheticDefaultImports": true, "composite": true, "declaration": true, "declarationMap": true, "esModuleInterop": true, "jsx": 4, "module": 99, "noEmitOnError": false, "noFallthroughCasesInSwitch": false, "noImplicitAny": false, "noImplicitReturns": true, "noImplicitThis": false, "noUnusedLocals": true, "noUnusedParameters": true, "outDir": "./dist", "rootDir": "./src", "skipLibCheck": true, "sourceMap": true, "strict": false, "strictNullChecks": false, "target": 7}, "referencedMap": [[429, 1], [427, 2], [86, 2], [89, 3], [88, 4], [87, 5], [581, 6], [501, 7], [499, 8], [496, 2], [497, 9], [498, 9], [500, 10], [650, 2], [647, 2], [646, 2], [641, 11], [652, 12], [637, 13], [648, 14], [640, 15], [639, 16], [649, 2], [644, 17], [651, 2], [645, 18], [638, 2], [653, 19], [654, 20], [655, 21], [636, 2], [432, 22], [428, 1], [430, 23], [431, 1], [493, 24], [662, 2], [492, 25], [212, 2], [197, 26], [213, 27], [196, 2], [489, 28], [495, 29], [494, 28], [159, 19], [490, 2], [243, 30], [244, 31], [242, 32], [245, 33], [246, 34], [247, 35], [248, 36], [249, 37], [250, 38], [251, 39], [252, 40], [253, 41], [254, 42], [485, 2], [433, 43], [434, 43], [436, 44], [437, 45], [438, 46], [439, 47], [440, 48], [441, 49], [442, 50], [443, 51], [444, 52], [445, 53], [446, 53], [447, 54], [448, 55], [449, 56], [450, 57], [435, 2], [483, 2], [451, 58], [452, 59], [453, 60], [484, 61], [454, 62], [455, 63], [456, 64], [457, 65], [458, 66], [459, 67], [460, 68], [461, 69], [462, 70], [463, 71], [464, 72], [465, 73], [467, 74], [466, 75], [468, 76], [469, 77], [470, 2], [471, 78], [472, 79], [473, 80], [474, 81], [475, 82], [476, 83], [477, 84], [478, 85], [479, 86], [480, 87], [481, 88], [482, 89], [83, 2], [487, 2], [488, 2], [413, 19], [81, 2], [84, 90], [85, 19], [486, 91], [491, 92], [160, 93], [663, 94], [661, 95], [660, 96], [659, 97], [693, 95], [697, 98], [694, 99], [698, 100], [664, 2], [712, 101], [665, 102], [666, 103], [703, 103], [713, 104], [704, 105], [711, 106], [696, 107], [695, 2], [657, 108], [658, 109], [656, 2], [82, 2], [667, 2], [425, 2], [426, 110], [689, 111], [687, 112], [688, 113], [676, 114], [677, 112], [684, 115], [675, 116], [680, 117], [690, 2], [681, 118], [686, 119], [691, 120], [674, 121], [682, 122], [683, 123], [678, 124], [685, 111], [679, 125], [643, 126], [642, 2], [94, 127], [95, 128], [93, 129], [91, 130], [90, 131], [92, 130], [237, 132], [238, 133], [227, 134], [234, 135], [235, 136], [240, 137], [236, 138], [233, 139], [232, 140], [231, 141], [241, 142], [229, 135], [230, 135], [239, 135], [257, 143], [267, 144], [261, 144], [269, 144], [272, 144], [259, 145], [260, 144], [262, 144], [265, 144], [268, 144], [264, 146], [266, 144], [263, 135], [206, 19], [210, 19], [200, 135], [203, 19], [208, 135], [209, 147], [202, 148], [205, 19], [207, 19], [204, 149], [195, 19], [194, 19], [274, 150], [271, 151], [224, 152], [223, 135], [221, 19], [222, 135], [225, 153], [226, 154], [219, 19], [215, 155], [218, 135], [217, 135], [216, 135], [211, 135], [220, 155], [270, 135], [256, 156], [258, 143], [255, 157], [273, 2], [228, 2], [201, 2], [199, 158], [673, 2], [706, 2], [699, 2], [710, 2], [580, 159], [529, 160], [542, 161], [504, 2], [556, 162], [558, 163], [557, 163], [531, 164], [530, 2], [532, 165], [559, 166], [563, 167], [561, 167], [540, 168], [539, 2], [548, 166], [507, 166], [535, 2], [576, 169], [551, 170], [553, 171], [571, 166], [506, 172], [523, 173], [538, 2], [573, 2], [544, 174], [560, 167], [564, 175], [562, 176], [577, 2], [546, 2], [520, 172], [512, 2], [511, 177], [536, 166], [537, 166], [510, 178], [543, 2], [505, 2], [522, 2], [550, 2], [578, 179], [517, 166], [518, 180], [565, 163], [567, 181], [566, 181], [502, 2], [521, 2], [528, 2], [519, 166], [549, 2], [516, 2], [575, 2], [515, 2], [513, 182], [514, 2], [552, 2], [545, 2], [572, 183], [526, 177], [524, 177], [525, 177], [541, 2], [508, 2], [568, 167], [570, 175], [569, 176], [555, 2], [554, 184], [547, 2], [534, 2], [574, 2], [579, 2], [503, 2], [533, 2], [527, 2], [509, 177], [79, 2], [80, 2], [13, 2], [14, 2], [16, 2], [15, 2], [2, 2], [17, 2], [18, 2], [19, 2], [20, 2], [21, 2], [22, 2], [23, 2], [24, 2], [3, 2], [25, 2], [26, 2], [4, 2], [27, 2], [31, 2], [28, 2], [29, 2], [30, 2], [32, 2], [33, 2], [34, 2], [5, 2], [35, 2], [36, 2], [37, 2], [38, 2], [6, 2], [42, 2], [39, 2], [40, 2], [41, 2], [43, 2], [7, 2], [44, 2], [49, 2], [50, 2], [45, 2], [46, 2], [47, 2], [48, 2], [8, 2], [54, 2], [51, 2], [52, 2], [53, 2], [55, 2], [9, 2], [56, 2], [57, 2], [58, 2], [60, 2], [59, 2], [61, 2], [62, 2], [10, 2], [63, 2], [64, 2], [65, 2], [11, 2], [66, 2], [67, 2], [68, 2], [69, 2], [70, 2], [1, 2], [71, 2], [72, 2], [12, 2], [76, 2], [74, 2], [78, 2], [73, 2], [77, 2], [75, 2], [198, 185], [214, 186], [702, 187], [705, 187], [708, 188], [701, 189], [700, 2], [707, 190], [714, 191], [715, 192], [709, 191], [753, 193], [671, 2], [692, 194], [670, 195], [669, 2], [672, 2], [668, 196], [415, 197], [416, 197], [417, 197], [418, 197], [419, 197], [420, 198], [414, 2], [408, 199], [407, 200], [424, 200], [186, 201], [583, 202], [582, 201], [404, 203], [412, 204], [584, 205], [598, 206], [317, 207], [603, 208], [586, 209], [605, 210], [312, 211], [610, 212], [316, 213], [302, 201], [303, 201], [315, 214], [609, 215], [606, 216], [602, 206], [592, 217], [593, 206], [594, 218], [591, 206], [314, 219], [596, 206], [597, 217], [306, 220], [601, 221], [600, 222], [595, 209], [590, 223], [589, 217], [587, 201], [588, 217], [304, 224], [585, 225], [604, 226], [301, 227], [298, 228], [305, 200], [311, 200], [309, 229], [608, 200], [307, 200], [308, 230], [300, 200], [313, 200], [299, 200], [310, 231], [599, 232], [607, 233], [296, 234], [295, 234], [297, 235], [294, 236], [291, 237], [293, 238], [289, 204], [292, 239], [290, 204], [611, 201], [612, 201], [613, 209], [615, 240], [614, 200], [616, 241], [622, 242], [618, 201], [402, 243], [400, 244], [399, 201], [398, 201], [617, 201], [619, 201], [620, 200], [401, 200], [621, 245], [403, 246], [379, 209], [380, 201], [389, 247], [628, 201], [629, 201], [630, 209], [381, 248], [632, 249], [631, 209], [382, 247], [392, 250], [390, 247], [625, 251], [393, 252], [624, 251], [623, 200], [391, 248], [388, 253], [385, 254], [387, 255], [383, 201], [384, 256], [378, 257], [626, 258], [386, 259], [627, 260], [375, 261], [376, 262], [377, 263], [374, 261], [394, 264], [373, 265], [633, 265], [634, 209], [720, 266], [721, 267], [347, 201], [350, 268], [371, 269], [367, 270], [368, 201], [349, 271], [366, 272], [370, 273], [331, 274], [328, 201], [718, 2], [719, 275], [329, 201], [330, 201], [396, 276], [395, 277], [359, 278], [360, 278], [361, 278], [358, 278], [357, 278], [356, 278], [362, 279], [354, 280], [722, 281], [278, 201], [345, 282], [342, 203], [285, 283], [284, 284], [281, 285], [282, 286], [332, 287], [344, 201], [343, 201], [340, 288], [341, 289], [339, 289], [283, 290], [353, 291], [352, 292], [351, 293], [346, 203], [326, 294], [324, 203], [323, 295], [321, 296], [325, 201], [322, 297], [363, 298], [365, 299], [364, 300], [327, 301], [348, 302], [369, 303], [355, 204], [335, 304], [723, 304], [338, 305], [334, 306], [319, 306], [337, 307], [333, 308], [318, 308], [320, 306], [336, 309], [280, 306], [717, 310], [724, 2], [725, 311], [635, 2], [716, 312], [397, 313], [372, 314], [279, 265], [286, 315], [727, 201], [738, 316], [287, 317], [190, 201], [191, 201], [728, 209], [736, 318], [193, 319], [275, 320], [733, 321], [730, 201], [732, 322], [729, 209], [276, 209], [277, 319], [737, 323], [192, 324], [735, 325], [731, 200], [189, 326], [734, 326], [726, 327], [288, 328], [188, 265], [739, 265], [422, 329], [184, 201], [740, 330], [185, 331], [183, 203], [409, 302], [741, 332], [742, 333], [743, 203], [744, 201], [745, 334], [746, 335], [747, 201], [421, 336], [750, 201], [406, 337], [751, 203], [748, 2], [749, 338], [405, 339], [752, 265], [187, 265], [423, 340], [410, 302], [411, 302], [101, 19], [102, 19], [112, 341], [103, 19], [110, 19], [104, 19], [111, 19], [105, 19], [109, 19], [106, 19], [107, 19], [108, 19], [147, 342], [142, 19], [143, 19], [144, 343], [141, 19], [146, 344], [113, 19], [114, 19], [119, 343], [115, 19], [122, 19], [125, 345], [130, 346], [123, 19], [121, 347], [124, 19], [117, 19], [127, 348], [126, 349], [129, 349], [128, 348], [116, 350], [132, 19], [131, 351], [133, 352], [134, 19], [135, 353], [140, 354], [136, 355], [138, 19], [137, 19], [139, 2], [100, 356], [99, 2], [181, 357], [180, 358], [178, 358], [179, 2], [157, 359], [148, 2], [155, 2], [154, 2], [149, 2], [150, 2], [118, 2], [145, 2], [151, 2], [152, 2], [153, 2], [156, 360], [120, 2], [182, 361], [174, 2], [177, 362], [170, 363], [175, 364], [176, 365], [169, 2], [168, 19], [171, 366], [165, 367], [163, 367], [167, 368], [164, 367], [161, 369], [166, 370], [162, 2], [158, 2], [98, 371], [96, 2], [97, 2], [173, 372], [172, 360]], "semanticDiagnosticsPerFile": [[283, [{"start": 457, "length": 7, "messageText": "'useMemo' is declared but its value is never read.", "category": 1, "code": 6133, "reportsUnnecessary": true}, {"start": 2102, "length": 16, "messageText": "'validationErrors' is declared but its value is never read.", "category": 1, "code": 6133, "reportsUnnecessary": true}, {"start": 5923, "length": 5, "messageText": "Cannot find name 'field'.", "category": 1, "code": 2304}, {"start": 6021, "length": 5, "messageText": "Cannot find name 'field'.", "category": 1, "code": 2304}, {"start": 6116, "length": 5, "messageText": "Cannot find name 'field'.", "category": 1, "code": 2304}, {"start": 6199, "length": 5, "messageText": "Cannot find name 'field'.", "category": 1, "code": 2304}]], [284, [{"start": 1817, "length": 19, "messageText": "'SetupBuilderSection' is declared but its value is never read.", "category": 1, "code": 6133, "reportsUnnecessary": true}, {"start": 2262, "length": 18, "messageText": "'SetupBuilderHeader' is declared but its value is never read.", "category": 1, "code": 6133, "reportsUnnecessary": true}, {"start": 2750, "length": 20, "messageText": "'SetupBuilderTitleRow' is declared but its value is never read.", "category": 1, "code": 6133, "reportsUnnecessary": true}, {"start": 2889, "length": 16, "messageText": "'SetupBuilderIcon' is declared but its value is never read.", "category": 1, "code": 6133, "reportsUnnecessary": true}, {"start": 3261, "length": 17, "messageText": "'SetupBuilderTitle' is declared but its value is never read.", "category": 1, "code": 6133, "reportsUnnecessary": true}, {"start": 3524, "length": 23, "messageText": "'SetupBuilderDescription' is declared but its value is never read.", "category": 1, "code": 6133, "reportsUnnecessary": true}, {"start": 3787, "length": 19, "messageText": "'SetupBuilderContent' is declared but its value is never read.", "category": 1, "code": 6133, "reportsUnnecessary": true}]], [293, [{"start": 3222, "length": 21, "messageText": "Module '\"./data\"' has no exported member 'DailyGuidePreferences'.", "category": 1, "code": 2305}]], [298, [{"start": 140, "length": 18, "messageText": "'useDailyGuideStore' is declared but its value is never read.", "category": 1, "code": 6133, "reportsUnnecessary": true}]], [300, [{"start": 3168, "length": 20, "messageText": "'getPreviousWeekRange' is declared but its value is never read.", "category": 1, "code": 6133, "reportsUnnecessary": true}]], [305, [{"start": 7294, "length": 14, "messageText": "'qualityRatings' is declared but its value is never read.", "category": 1, "code": 6133, "reportsUnnecessary": true}]], [306, [{"start": 4148, "length": 11, "messageText": "'SessionGrid' is declared but its value is never read.", "category": 1, "code": 6133, "reportsUnnecessary": true}, {"start": 4297, "length": 12, "messageText": "'SessionBlock' is declared but its value is never read.", "category": 1, "code": 6133, "reportsUnnecessary": true}, {"start": 5263, "length": 11, "messageText": "'SessionHour' is declared but its value is never read.", "category": 1, "code": 6133, "reportsUnnecessary": true}, {"start": 5378, "length": 14, "messageText": "'SessionWinRate' is declared but its value is never read.", "category": 1, "code": 6133, "reportsUnnecessary": true}, {"start": 5474, "length": 12, "messageText": "'SessionLabel' is declared but its value is never read.", "category": 1, "code": 6133, "reportsUnnecessary": true}, {"start": 11034, "length": 66, "messageText": "All destructured elements are unused.", "category": 1, "code": 6198, "reportsUnnecessary": true}, {"start": 11841, "length": 5, "code": 2769, "category": 1, "messageText": {"messageText": "No overload matches this call.", "category": 1, "code": 2769, "next": [{"messageText": "Overload 1 of 2, '(props: { slot?: string; style?: CSSProperties; title?: string; ref?: Ref<HTMLDivElement>; className?: string; color?: string; id?: string; lang?: string; role?: AriaR<PERSON>; ... 250 more ...; level: \"high\" | ... 2 more ... | \"avoid\"; } & { ...; } & { ...; }): ReactElement<...>', gave the following error.", "category": 1, "code": 2772, "next": [{"messageText": "Type 'string' is not assignable to type '\"high\" | \"low\" | \"medium\" | \"avoid\"'.", "category": 1, "code": 2322}]}, {"messageText": "Overload 2 of 2, '(props: StyledComponentPropsWithAs<\"div\", any, { level: \"high\" | \"low\" | \"medium\" | \"avoid\"; }, never, \"div\", \"div\">): ReactElement<StyledComponentPropsWithAs<\"div\", any, { level: \"high\" | ... 2 more ... | \"avoid\"; }, never, \"div\", \"div\">, string | JSXElementConstructor<...>>', gave the following error.", "category": 1, "code": 2772, "next": [{"messageText": "Type 'string' is not assignable to type '\"high\" | \"low\" | \"medium\" | \"avoid\"'.", "category": 1, "code": 2322}]}]}, "relatedInformation": [{"start": 2242, "length": 5, "messageText": "The expected type comes from property 'level' which is declared here on type 'IntrinsicAttributes & { slot?: string; style?: CSSProperties; title?: string; ref?: Ref<HTMLDivElement>; className?: string; ... 254 more ...; level: \"high\" | ... 2 more ... | \"avoid\"; } & { ...; } & { ...; }'", "category": 3, "code": 6500}, {"start": 2242, "length": 5, "messageText": "The expected type comes from property 'level' which is declared here on type 'IntrinsicAttributes & { slot?: string; style?: CSSProperties; title?: string; ref?: Ref<HTMLDivElement>; className?: string; ... 254 more ...; level: \"high\" | ... 2 more ... | \"avoid\"; } & { ...; } & { ...; }'", "category": 3, "code": 6500}]}]], [312, [{"start": 10315, "length": 10, "messageText": "'modelStats' is declared but its value is never read.", "category": 1, "code": 6133, "reportsUnnecessary": true}, {"start": 10557, "length": 15, "messageText": "'sessionAnalyses' is declared but its value is never read.", "category": 1, "code": 6133, "reportsUnnecessary": true}, {"start": 10768, "length": 16, "messageText": "'probabilityError' is declared but its value is never read.", "category": 1, "code": 6133, "reportsUnnecessary": true}]], [316, [{"start": 6205, "length": 23, "code": 2322, "category": 1, "messageText": {"messageText": "Type '{ activeTab: GuideTab; data: { marketOverview: MarketOverview; tradingPlan: TradingPlan; keyPriceLevels: KeyPriceLevel[]; selectedDate: string; currentDate: string; }; isLoading: boolean; error: string; handlers: { ...; }; }' is not assignable to type 'GuideTabContentProps'.", "category": 1, "code": 2322, "next": [{"messageText": "The types of 'handlers.onTradingPlanItemToggle' are incompatible between these types.", "category": 1, "code": 2200, "next": [{"messageText": "Type '(id: string, completed: boolean) => void' is not assignable to type '(id: string) => void'.", "category": 1, "code": 2322, "next": [{"messageText": "Target signature provides too few arguments. Expected 2 or more, but got 1.", "category": 1, "code": 2849}], "canonicalHead": {"code": 2322, "messageText": "Type '{ activeTab: GuideTab; data: { marketOverview: MarketOverview; tradingPlan: TradingPlan; keyPriceLevels: KeyPriceLevel[]; selectedDate: string; currentDate: string; }; isLoading: boolean; error: string; handlers: { ...; }; }' is not assignable to type 'GuideTabContentProps'."}}]}]}}]], [318, [{"start": 153, "length": 17, "messageText": "'CompleteTradeData' is declared but its value is never read.", "category": 1, "code": 6133, "reportsUnnecessary": true}, {"start": 1107, "length": 6, "code": 2322, "category": 1, "messageText": {"messageText": "Type 'CompleteTradeData[]' is not assignable to type 'Trade[]'.", "category": 1, "code": 2322, "next": [{"messageText": "Type 'CompleteTradeData' is missing the following properties from type 'Trade': id, symbol, date, direction, and 9 more.", "category": 1, "code": 2740, "canonicalHead": {"code": 2322, "messageText": "Type 'CompleteTradeData' is not assignable to type 'Trade'."}}]}}, {"start": 1930, "length": 6, "code": 2322, "category": 1, "messageText": {"messageText": "Type 'CompleteTradeData[]' is not assignable to type 'Trade[]'.", "category": 1, "code": 2322, "next": [{"messageText": "Type 'CompleteTradeData' is missing the following properties from type 'Trade': id, symbol, date, direction, and 9 more.", "category": 1, "code": 2740, "canonicalHead": {"code": 2322, "messageText": "Type 'CompleteTradeData' is not assignable to type 'Trade'."}}]}}]], [323, [{"start": 5646, "length": 17, "code": 2339, "category": 1, "messageText": "Property 'execution_quality' does not exist on type 'TradeAnalysisRecord'."}, {"start": 5839, "length": 15, "code": 2339, "category": 1, "messageText": "Property 'lessons_learned' does not exist on type 'TradeAnalysisRecord'."}, {"start": 6030, "length": 15, "code": 2339, "category": 1, "messageText": "Property 'emotional_state' does not exist on type 'TradeAnalysisRecord'."}, {"start": 6223, "length": 17, "code": 2339, "category": 1, "messageText": "Property 'market_conditions' does not exist on type 'TradeAnalysisRecord'."}]], [333, [{"start": 165, "length": 5, "messageText": "'Trade' is declared but its value is never read.", "category": 1, "code": 6133, "reportsUnnecessary": true}, {"start": 635, "length": 10, "messageText": "'isNewTrade' is declared but its value is never read.", "category": 1, "code": 6133, "reportsUnnecessary": true}]], [336, [{"start": 1137, "length": 10, "messageText": "'isNewTrade' is declared but its value is never read.", "category": 1, "code": 6133, "reportsUnnecessary": true}, {"start": 3661, "length": 18, "code": 2345, "category": 1, "messageText": {"messageText": "Argument of type '{ clarity: string; confluence: string; context: string; risk: string; reward: string; timeframe: string; volume: string; }' is not assignable to parameter of type 'Record<string, ScoreRange>'.", "category": 1, "code": 2345, "next": [{"messageText": "Property 'clarity' is incompatible with index signature.", "category": 1, "code": 2530, "next": [{"messageText": "Type 'string' is not assignable to type 'ScoreRange'.", "category": 1, "code": 2322}]}]}}, {"start": 3834, "length": 19, "code": 2551, "category": 1, "messageText": "Property 'patternQualityScore' does not exist on type '{ date: string; symbol: string; direction: \"long\" | \"short\"; quantity: string; entryPrice: string; exitPrice: string; stopLoss?: string; takeProfit?: string; profit: string; modelType?: string; session?: string; ... 43 more ...; result: \"win\" | ... 1 more ... | \"breakeven\"; }'. Did you mean 'patternQuality'?", "relatedInformation": [{"file": "../shared/dist/types/trading.d.ts", "start": 9880, "length": 14, "messageText": "'patternQuality' is declared here.", "category": 3, "code": 2728}]}, {"start": 4202, "length": 11, "code": 2339, "category": 1, "messageText": "Property 'dolAnalysis' does not exist on type '{ date: string; symbol: string; direction: \"long\" | \"short\"; quantity: string; entryPrice: string; exitPrice: string; stopLoss?: string; takeProfit?: string; profit: string; modelType?: string; session?: string; ... 43 more ...; result: \"win\" | ... 1 more ... | \"breakeven\"; }'."}]], [337, [{"start": 2048, "length": 8, "messageText": "'navigate' is declared but its value is never read.", "category": 1, "code": 6133, "reportsUnnecessary": true}, {"start": 4111, "length": 9, "code": 2345, "category": 1, "messageText": {"messageText": "Argument of type 'CompleteTradeData' is not assignable to parameter of type 'Trade'.", "category": 1, "code": 2345, "next": [{"messageText": "Type 'CompleteTradeData' is missing the following properties from type 'Trade': id, symbol, date, direction, and 9 more.", "category": 1, "code": 2740}]}}]], [339, [{"start": 656, "length": 16, "messageText": "'SessionSelection' is declared but its value is never read.", "category": 1, "code": 6133, "reportsUnnecessary": true}, {"start": 676, "length": 12, "messageText": "'SessionUtils' is declared but its value is never read.", "category": 1, "code": 6133, "reportsUnnecessary": true}, {"start": 5513, "length": 9, "code": 2339, "category": 1, "messageText": "Property 'entryDate' does not exist on type 'TradeFormData'."}, {"start": 5546, "length": 8, "code": 2322, "category": 1, "messageText": {"messageText": "Type '(e: ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>) => void' is not assignable to type '(value: string) => void'.", "category": 1, "code": 2322, "next": [{"messageText": "Types of parameters 'e' and 'value' are incompatible.", "category": 1, "code": 2328, "next": [{"messageText": "Type 'string' is not assignable to type 'ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>'.", "category": 1, "code": 2322}]}]}, "relatedInformation": [{"file": "../shared/dist/components/atoms/input.d.ts", "start": 406, "length": 8, "messageText": "The expected type comes from property 'onChange' which is declared here on type 'IntrinsicAttributes & InputProps'", "category": 3, "code": 6500}]}, {"start": 8092, "length": 5, "code": 2322, "category": 1, "messageText": "Type 'string' is not assignable to type 'SessionSelection'.", "relatedInformation": [{"file": "../shared/dist/components/molecules/hierarchicalsessionselector.d.ts", "start": 191, "length": 5, "messageText": "The expected type comes from property 'value' which is declared here on type 'IntrinsicAttributes & HierarchicalSessionSelectorProps'", "category": 3, "code": 6500}]}, {"start": 8196, "length": 117, "code": 2352, "category": 1, "messageText": {"messageText": "Conversion of type '{ target: { name: string; value: SessionSelection; }; }' to type 'ChangeEvent<HTMLSelectElement>' may be a mistake because neither type sufficiently overlaps with the other. If this was intentional, convert the expression to 'unknown' first.", "category": 1, "code": 2352, "next": [{"messageText": "Type '{ target: { name: string; value: SessionSelection; }; }' is missing the following properties from type 'ChangeEvent<HTMLSelectElement>': nativeEvent, currentTarget, bubbles, cancelable, and 10 more.", "category": 1, "code": 2740}]}}]], [340, [{"start": 1690, "length": 16, "messageText": "'validationErrors' is declared but its value is never read.", "category": 1, "code": 6133, "reportsUnnecessary": true}]], [341, [{"start": 335, "length": 5, "messageText": "'Trade' is declared but its value is never read.", "category": 1, "code": 6133, "reportsUnnecessary": true}, {"start": 344, "length": 13, "messageText": "'TradeFormData' is declared but its value is never read.", "category": 1, "code": 6133, "reportsUnnecessary": true}, {"start": 2390, "length": 16, "messageText": "'validationErrors' is declared but its value is never read.", "category": 1, "code": 6133, "reportsUnnecessary": true}]], [351, [{"start": 2304, "length": 14, "code": 2322, "category": 1, "messageText": {"messageText": "Type 'import(\"/Users/<USER>/adhd-trading-dashboard-lib/packages/dashboard/src/features/trade-journal/types/index\").FilterState' is not assignable to type 'import(\"/Users/<USER>/adhd-trading-dashboard-lib/packages/dashboard/src/features/trade-journal/components/useFilterState\").FilterState'.", "category": 1, "code": 2322, "next": [{"messageText": "Index signature for type 'string' is missing in type 'FilterState'.", "category": 1, "code": 2329}]}, "relatedInformation": [{"file": "./src/features/trade-journal/components/f1filterpanel.tsx", "start": 823, "length": 14, "messageText": "The expected type comes from property 'initialFilters' which is declared here on type 'IntrinsicAttributes & F1FilterPanelProps'", "category": 3, "code": 6500}]}]], [352, [{"start": 183, "length": 13, "messageText": "'TradeFormData' is declared but its value is never read.", "category": 1, "code": 6133, "reportsUnnecessary": true}, {"start": 2616, "length": 6, "code": 2322, "category": 1, "messageText": {"messageText": "Type 'Trade[]' is not assignable to type 'CompleteTradeData[]'.", "category": 1, "code": 2322, "next": [{"messageText": "Property 'trade' is missing in type 'Trade' but required in type 'CompleteTradeData'.", "category": 1, "code": 2741, "canonicalHead": {"code": 2322, "messageText": "Type 'Trade' is not assignable to type 'CompleteTradeData'."}}]}, "relatedInformation": [{"file": "../shared/dist/types/trading.d.ts", "start": 8500, "length": 5, "messageText": "'trade' is declared here.", "category": 3, "code": 2728}, {"file": "./src/features/trade-journal/components/tradelist.tsx", "start": 2025, "length": 6, "messageText": "The expected type comes from property 'trades' which is declared here on type 'IntrinsicAttributes & TradeListProps'", "category": 3, "code": 6500}]}]], [361, [{"start": 192, "length": 25, "messageText": "'DOL_EFFECTIVENESS_OPTIONS' is declared but its value is never read.", "category": 1, "code": 6133, "reportsUnnecessary": true}]], [364, [{"start": 4814, "length": 12, "messageText": "This comparison appears to be unintentional because the types 'ScoreRange' and '\"\"' have no overlap.", "category": 1, "code": 2367}, {"start": 6138, "length": 5, "code": 2322, "category": 1, "messageText": "Type 'string' is not assignable to type '\"\" | ScoreRange'.", "relatedInformation": [{"file": "./src/features/trade-journal/components/trade-pattern-quality/criterionselector.tsx", "start": 2003, "length": 5, "messageText": "The expected type comes from property 'value' which is declared here on type 'IntrinsicAttributes & CriterionSelectorProps'", "category": 3, "code": 6500}]}, {"start": 6329, "length": 5, "code": 2322, "category": 1, "messageText": "Type 'string' is not assignable to type '\"\" | ScoreRange'.", "relatedInformation": [{"file": "./src/features/trade-journal/components/trade-pattern-quality/criterionselector.tsx", "start": 2003, "length": 5, "messageText": "The expected type comes from property 'value' which is declared here on type 'IntrinsicAttributes & CriterionSelectorProps'", "category": 3, "code": 6500}]}, {"start": 6523, "length": 5, "code": 2322, "category": 1, "messageText": "Type 'string' is not assignable to type '\"\" | ScoreRange'.", "relatedInformation": [{"file": "./src/features/trade-journal/components/trade-pattern-quality/criterionselector.tsx", "start": 2003, "length": 5, "messageText": "The expected type comes from property 'value' which is declared here on type 'IntrinsicAttributes & CriterionSelectorProps'", "category": 3, "code": 6500}]}, {"start": 6708, "length": 5, "code": 2322, "category": 1, "messageText": "Type 'string' is not assignable to type '\"\" | ScoreRange'.", "relatedInformation": [{"file": "./src/features/trade-journal/components/trade-pattern-quality/criterionselector.tsx", "start": 2003, "length": 5, "messageText": "The expected type comes from property 'value' which is declared here on type 'IntrinsicAttributes & CriterionSelectorProps'", "category": 3, "code": 6500}]}, {"start": 6889, "length": 5, "code": 2322, "category": 1, "messageText": "Type 'string' is not assignable to type '\"\" | ScoreRange'.", "relatedInformation": [{"file": "./src/features/trade-journal/components/trade-pattern-quality/criterionselector.tsx", "start": 2003, "length": 5, "messageText": "The expected type comes from property 'value' which is declared here on type 'IntrinsicAttributes & CriterionSelectorProps'", "category": 3, "code": 6500}]}, {"start": 7077, "length": 5, "code": 2322, "category": 1, "messageText": "Type 'string' is not assignable to type '\"\" | ScoreRange'.", "relatedInformation": [{"file": "./src/features/trade-journal/components/trade-pattern-quality/criterionselector.tsx", "start": 2003, "length": 5, "messageText": "The expected type comes from property 'value' which is declared here on type 'IntrinsicAttributes & CriterionSelectorProps'", "category": 3, "code": 6500}]}, {"start": 7268, "length": 5, "code": 2322, "category": 1, "messageText": "Type 'string' is not assignable to type '\"\" | ScoreRange'.", "relatedInformation": [{"file": "./src/features/trade-journal/components/trade-pattern-quality/criterionselector.tsx", "start": 2003, "length": 5, "messageText": "The expected type comes from property 'value' which is declared here on type 'IntrinsicAttributes & CriterionSelectorProps'", "category": 3, "code": 6500}]}]], [370, [{"start": 4437, "length": 14, "code": 2322, "category": 1, "messageText": {"messageText": "Type 'CompleteTradeData[]' is not assignable to type 'Trade[]'.", "category": 1, "code": 2322, "next": [{"messageText": "Type 'CompleteTradeData' is missing the following properties from type 'Trade': id, symbol, date, direction, and 9 more.", "category": 1, "code": 2740, "canonicalHead": {"code": 2322, "messageText": "Type 'CompleteTradeData' is not assignable to type 'Trade'."}}]}, "relatedInformation": [{"file": "./src/features/trade-journal/components/trade-journal/tradejournalcontent.tsx", "start": 1193, "length": 14, "messageText": "The expected type comes from property 'filteredTrades' which is declared here on type 'IntrinsicAttributes & TradeJournalContentProps'", "category": 3, "code": 6500}]}, {"start": 5468, "length": 5, "code": 2322, "category": 1, "messageText": {"messageText": "Type '{ trades: CompleteTradeData[]; isLoading: boolean; title: string; }' is not assignable to type 'IntrinsicAttributes & TradeListProps'.", "category": 1, "code": 2322, "next": [{"messageText": "Property 'title' does not exist on type 'IntrinsicAttributes & TradeListProps'.", "category": 1, "code": 2339}]}}, {"start": 5734, "length": 14, "code": 2322, "category": 1, "messageText": {"messageText": "Type 'CompleteTradeData[]' is not assignable to type 'Trade[]'.", "category": 1, "code": 2322, "next": [{"messageText": "Type 'CompleteTradeData' is missing the following properties from type 'Trade': id, symbol, date, direction, and 9 more.", "category": 1, "code": 2740, "canonicalHead": {"code": 2322, "messageText": "Type 'CompleteTradeData' is not assignable to type 'Trade'."}}]}, "relatedInformation": [{"file": "./src/features/trade-journal/components/trade-journal/tradejournalcontent.tsx", "start": 1193, "length": 14, "messageText": "The expected type comes from property 'filteredTrades' which is declared here on type 'IntrinsicAttributes & TradeJournalContentProps'", "category": 3, "code": 6500}]}, {"start": 7761, "length": 5, "code": 2322, "category": 1, "messageText": {"messageText": "Type '{ trades: CompleteTradeData[]; isLoading: boolean; title: string; }' is not assignable to type 'IntrinsicAttributes & TradeListProps'.", "category": 1, "code": 2322, "next": [{"messageText": "Property 'title' does not exist on type 'IntrinsicAttributes & TradeListProps'.", "category": 1, "code": 2339}]}}]], [371, [{"start": 5162, "length": 6, "code": 2345, "category": 1, "messageText": {"messageText": "Argument of type 'Trade[]' is not assignable to parameter of type 'CompleteTradeData[]'.", "category": 1, "code": 2345, "next": [{"messageText": "Property 'trade' is missing in type 'Trade' but required in type 'CompleteTradeData'.", "category": 1, "code": 2741, "canonicalHead": {"code": 2322, "messageText": "Type 'Trade' is not assignable to type 'CompleteTradeData'."}}]}, "relatedInformation": [{"file": "../shared/dist/types/trading.d.ts", "start": 8500, "length": 5, "messageText": "'trade' is declared here.", "category": 3, "code": 2728}]}, {"start": 5242, "length": 14, "messageText": "'setShowFilters' is declared but its value is never read.", "category": 1, "code": 6133, "reportsUnnecessary": true}, {"start": 5586, "length": 5, "code": 2339, "category": 1, "messageText": "Property 'trade' does not exist on type 'Trade'."}, {"start": 7531, "length": 25, "code": 2322, "category": 1, "messageText": {"messageText": "Type '{ activeTab: JournalTab; data: { trades: Trade[]; filteredTrades: CompleteTradeData[]; recentTrades: Trade[]; filters: FilterState; ... 5 more ...; uniqueDOLTypes: string[]; }; isLoading: boolean; error: string; showFilters: boolean; handlers: { ...; }; }' is not assignable to type 'JournalTabContentProps'.", "category": 1, "code": 2322, "next": [{"messageText": "The types of 'data.trades' are incompatible between these types.", "category": 1, "code": 2200, "next": [{"messageText": "Type 'Trade[]' is not assignable to type 'CompleteTradeData[]'.", "category": 1, "code": 2322, "next": [{"messageText": "Property 'trade' is missing in type 'Trade' but required in type 'CompleteTradeData'.", "category": 1, "code": 2741, "canonicalHead": {"code": 2322, "messageText": "Type 'Trade' is not assignable to type 'CompleteTradeData'."}}], "canonicalHead": {"code": 2322, "messageText": "Type '{ activeTab: JournalTab; data: { trades: Trade[]; filteredTrades: CompleteTradeData[]; recentTrades: Trade[]; filters: FilterState; ... 5 more ...; uniqueDOLTypes: string[]; }; isLoading: boolean; error: string; showFilters: boolean; handlers: { ...; }; }' is not assignable to type 'JournalTabContentProps'."}}]}]}, "relatedInformation": [{"file": "../shared/dist/types/trading.d.ts", "start": 8500, "length": 5, "messageText": "'trade' is declared here.", "category": 3, "code": 2728}]}]], [373, [{"start": 514, "length": 5, "messageText": "Re-exporting a type when 'isolatedModules' is enabled requires using 'export type'.", "category": 1, "code": 1205}]], [374, [{"start": 3519, "length": 16, "code": 2353, "category": 1, "messageText": "Object literal may only specify known properties, and 'averageRMultiple' does not exist in type 'PerformanceMetrics'."}, {"start": 9438, "length": 158, "code": 2345, "category": 1, "messageText": {"messageText": "Argument of type '{ date: string; balance: number; tradeNumber: number; profitLoss: number; }' is not assignable to parameter of type 'EquityPoint'.", "category": 1, "code": 2345, "next": [{"messageText": "Type '{ date: string; balance: number; tradeNumber: number; profitLoss: number; }' is missing the following properties from type 'EquityPoint': equity, baseline", "category": 1, "code": 2739}]}}, {"start": 10291, "length": 12, "code": 2322, "category": 1, "messageText": {"messageText": "Type '{ range: string; count: number; percentage: number; totalPnL: number; }[]' is not assignable to type 'DistributionBar[]'.", "category": 1, "code": 2322, "next": [{"messageText": "Property 'isWin' is missing in type '{ range: string; count: number; percentage: number; totalPnL: number; }' but required in type 'DistributionBar'.", "category": 1, "code": 2741, "canonicalHead": {"code": 2322, "messageText": "Type '{ range: string; count: number; percentage: number; totalPnL: number; }' is not assignable to type 'DistributionBar'."}}]}, "relatedInformation": [{"file": "./src/features/trade-analysis/types.ts", "start": 1438, "length": 5, "messageText": "'isWin' is declared here.", "category": 3, "code": 2728}]}]], [376, [{"start": 373, "length": 18, "messageText": "'PerformanceMetrics' is declared but its value is never read.", "category": 1, "code": 6133, "reportsUnnecessary": true}, {"start": 395, "length": 19, "messageText": "'CategoryPerformance' is declared but its value is never read.", "category": 1, "code": 6133, "reportsUnnecessary": true}, {"start": 418, "length": 15, "messageText": "'TimePerformance' is declared but its value is never read.", "category": 1, "code": 6133, "reportsUnnecessary": true}, {"start": 437, "length": 11, "messageText": "'EquityPoint' is declared but its value is never read.", "category": 1, "code": 6133, "reportsUnnecessary": true}, {"start": 452, "length": 15, "messageText": "'DistributionBar' is declared but its value is never read.", "category": 1, "code": 6133, "reportsUnnecessary": true}, {"start": 1252, "length": 14, "messageText": "Expected 0 arguments, but got 1.", "category": 1, "code": 2554}, {"start": 4432, "length": 10, "code": 2339, "category": 1, "messageText": "Property 'start_date' does not exist on type 'TradeFilters'."}, {"start": 4493, "length": 8, "code": 2339, "category": 1, "messageText": "Property 'end_date' does not exist on type 'TradeFilters'."}, {"start": 5454, "length": 15, "code": 2339, "category": 1, "messageText": "Property 'min_achieved_pl' does not exist on type 'TradeFilters'."}, {"start": 5563, "length": 15, "code": 2339, "category": 1, "messageText": "Property 'max_achieved_pl' does not exist on type 'TradeFilters'."}, {"start": 6665, "length": 19, "code": 2345, "category": 1, "messageText": "Argument of type 'string' is not assignable to parameter of type 'TradingSession'."}]], [377, [{"start": 149, "length": 13, "messageText": "'TradeFormData' is declared but its value is never read.", "category": 1, "code": 6133, "reportsUnnecessary": true}, {"start": 7990, "length": 9, "code": 2322, "category": 1, "messageText": {"messageText": "Type 'TradeDirection' is not assignable to type '\"Long\" | \"Short\"'.", "category": 1, "code": 2322, "next": [{"messageText": "Type '\"long\"' is not assignable to type '\"Long\" | \"Short\"'. Did you mean '\"Long\"'?", "category": 1, "code": 2820}]}, "relatedInformation": [{"file": "../shared/dist/types/trading.d.ts", "start": 9125, "length": 9, "messageText": "The expected type comes from property 'direction' which is declared here on type 'Trade'", "category": 3, "code": 6500}]}, {"start": 8476, "length": 9, "code": 2339, "category": 1, "messageText": "Property 'entryTime' does not exist on type 'Trade'."}, {"start": 8510, "length": 9, "code": 2339, "category": 1, "messageText": "Property 'entryTime' does not exist on type 'Trade'."}, {"start": 8713, "length": 6, "code": 2339, "category": 1, "messageText": "Property 'status' does not exist on type 'Trade'."}, {"start": 8786, "length": 6, "code": 2339, "category": 1, "messageText": "Property 'status' does not exist on type 'Trade'."}, {"start": 8857, "length": 6, "code": 2339, "category": 1, "messageText": "Property 'status' does not exist on type 'Trade'."}, {"start": 9701, "length": 9, "code": 2339, "category": 1, "messageText": "Property 'entryTime' does not exist on type 'Trade'."}, {"start": 9759, "length": 8, "code": 2339, "category": 1, "messageText": "Property 'exitTime' does not exist on type 'Trade'."}, {"start": 11744, "length": 6, "code": 2339, "category": 1, "messageText": "Property 'status' does not exist on type 'Trade'."}, {"start": 13267, "length": 9, "code": 2339, "category": 1, "messageText": "Property 'entryTime' does not exist on type 'Trade'."}, {"start": 14406, "length": 9, "code": 2339, "category": 1, "messageText": "Property 'entryTime' does not exist on type 'Trade'."}, {"start": 15301, "length": 6, "code": 2339, "category": 1, "messageText": "Property 'status' does not exist on type 'Trade'."}]], [378, [{"start": 323, "length": 17, "messageText": "'TradeAnalysisData' is declared but its value is never read.", "category": 1, "code": 6133, "reportsUnnecessary": true}]], [381, [{"start": 434, "length": 6, "messageText": "'Button' is declared but its value is never read.", "category": 1, "code": 6133, "reportsUnnecessary": true}, {"start": 442, "length": 4, "messageText": "'Card' is declared but its value is never read.", "category": 1, "code": 6133, "reportsUnnecessary": true}]], [382, [{"start": 250, "length": 46, "messageText": "'PerformanceMetrics' is declared but its value is never read.", "category": 1, "code": 6133, "reportsUnnecessary": true}]], [384, [{"start": 4765, "length": 9, "code": 2339, "category": 1, "messageText": "Property 'entryTime' does not exist on type 'Trade'."}, {"start": 4956, "length": 10, "code": 2769, "category": 1, "messageText": {"messageText": "No overload matches this call.", "category": 1, "code": 2769, "next": [{"messageText": "Overload 1 of 2, '(props: { style?: CSSProperties; className?: string; max?: number; children: ReactNode; onClick?: () => void; size?: BadgeSize; solid?: boolean; variant?: BadgeVariant; ... 7 more ...; $direction: TradeDirection; } & { ...; } & { ...; }): ReactElement<...>', gave the following error.", "category": 1, "code": 2772, "next": [{"messageText": "Type '\"Long\" | \"Short\"' is not assignable to type 'TradeDirection'.", "category": 1, "code": 2322, "next": [{"messageText": "Type '\"Long\"' is not assignable to type 'TradeDirection'. Did you mean '\"long\"'?", "category": 1, "code": 2820}]}]}, {"messageText": "Overload 2 of 2, '(props: StyledComponentPropsWithAs<FC<BadgeProps>, any, { $direction: TradeDirection; }, never, FC<BadgeProps>, FC<BadgeProps>>): ReactElement<...>', gave the following error.", "category": 1, "code": 2772, "next": [{"messageText": "Type '\"Long\" | \"Short\"' is not assignable to type 'TradeDirection'.", "category": 1, "code": 2322, "next": [{"messageText": "Type '\"Long\"' is not assignable to type 'TradeDirection'. Did you mean '\"long\"'?", "category": 1, "code": 2820}]}]}]}, "relatedInformation": [{"start": 2271, "length": 10, "messageText": "The expected type comes from property '$direction' which is declared here on type 'IntrinsicAttributes & { style?: CSSProperties; className?: string; max?: number; children: ReactNode; onClick?: () => void; size?: BadgeSize; ... 9 more ...; $direction: TradeDirection; } & { ...; } & { ...; }'", "category": 3, "code": 6500}, {"start": 2271, "length": 10, "messageText": "The expected type comes from property '$direction' which is declared here on type 'IntrinsicAttributes & { style?: CSSProperties; className?: string; max?: number; children: ReactNode; onClick?: () => void; size?: BadgeSize; ... 9 more ...; $direction: TradeDirection; } & { ...; } & { ...; }'", "category": 3, "code": 6500}]}, {"start": 5244, "length": 10, "code": 2339, "category": 1, "messageText": "Property 'entryPrice' does not exist on type 'Trade'."}, {"start": 5276, "length": 9, "code": 2339, "category": 1, "messageText": "Property 'exitPrice' does not exist on type 'Trade'."}, {"start": 5607, "length": 17, "code": 2339, "category": 1, "messageText": "Property 'profitLossPercent' does not exist on type 'Trade'."}, {"start": 5658, "length": 17, "code": 2339, "category": 1, "messageText": "Property 'profitLossPercent' does not exist on type 'Trade'."}, {"start": 5805, "length": 6, "code": 2339, "category": 1, "messageText": "Property 'status' does not exist on type 'Trade'."}, {"start": 5855, "length": 6, "code": 2339, "category": 1, "messageText": "Property 'status' does not exist on type 'Trade'."}, {"start": 5921, "length": 6, "code": 2339, "category": 1, "messageText": "Property 'status' does not exist on type 'Trade'."}, {"start": 6302, "length": 5, "code": 2322, "category": 1, "messageText": {"messageText": "Type '{ children: string; key: number; size: \"small\"; variant: \"default\"; title: string; }' is not assignable to type 'IntrinsicAttributes & TagProps'.", "category": 1, "code": 2322, "next": [{"messageText": "Property 'title' does not exist on type 'IntrinsicAttributes & TagProps'.", "category": 1, "code": 2339}]}}]], [386, [{"start": 489, "length": 14, "messageText": "'TradeDirection' is declared but its value is never read.", "category": 1, "code": 6133, "reportsUnnecessary": true}, {"start": 505, "length": 11, "messageText": "'TradeStatus' is declared but its value is never read.", "category": 1, "code": 6133, "reportsUnnecessary": true}, {"start": 2319, "length": 9, "code": 2339, "category": 1, "messageText": "Property 'entryTime' does not exist on type 'Trade'."}, {"start": 2353, "length": 9, "code": 2339, "category": 1, "messageText": "Property 'entryTime' does not exist on type 'Trade'."}, {"start": 2750, "length": 17, "code": 2339, "category": 1, "messageText": "Property 'profitLossPercent' does not exist on type 'Trade'."}, {"start": 2772, "length": 17, "code": 2339, "category": 1, "messageText": "Property 'profitLossPercent' does not exist on type 'Trade'."}, {"start": 2856, "length": 6, "code": 2339, "category": 1, "messageText": "Property 'status' does not exist on type 'Trade'."}, {"start": 2879, "length": 6, "code": 2339, "category": 1, "messageText": "Property 'status' does not exist on type 'Trade'."}]], [387, [{"start": 2582, "length": 13, "messageText": "'ErrorFallback' is declared but its value is never read.", "category": 1, "code": 6133, "reportsUnnecessary": true}]], [390, [{"start": 267, "length": 43, "messageText": "'TimePerformance' is declared but its value is never read.", "category": 1, "code": 6133, "reportsUnnecessary": true}, {"start": 2795, "length": 5, "messageText": "'title' is declared but its value is never read.", "category": 1, "code": 6133, "reportsUnnecessary": true}]], [391, [{"start": 161, "length": 33, "messageText": "'Trade' is declared but its value is never read.", "category": 1, "code": 6133, "reportsUnnecessary": true}, {"start": 4133, "length": 17, "code": 2339, "category": 1, "messageText": "Property 'profitLossPercent' does not exist on type 'Trade'."}, {"start": 4656, "length": 6, "code": 2339, "category": 1, "messageText": "Property 'status' does not exist on type 'Trade'."}, {"start": 4715, "length": 6, "code": 2339, "category": 1, "messageText": "Property 'status' does not exist on type 'Trade'."}, {"start": 4916, "length": 9, "code": 2339, "category": 1, "messageText": "Property 'entryTime' does not exist on type 'Trade'."}, {"start": 5088, "length": 8, "code": 2339, "category": 1, "messageText": "Property 'exitTime' does not exist on type 'Trade'."}, {"start": 5278, "length": 9, "code": 2339, "category": 1, "messageText": "Property 'entryTime' does not exist on type 'Trade'."}, {"start": 5303, "length": 8, "code": 2339, "category": 1, "messageText": "Property 'exitTime' does not exist on type 'Trade'."}, {"start": 5476, "length": 10, "code": 2339, "category": 1, "messageText": "Property 'entryPrice' does not exist on type 'Trade'."}, {"start": 5649, "length": 9, "code": 2339, "category": 1, "messageText": "Property 'exitPrice' does not exist on type 'Trade'."}, {"start": 5819, "length": 8, "code": 2339, "category": 1, "messageText": "Property 'quantity' does not exist on type 'Trade'."}, {"start": 5978, "length": 9, "code": 2339, "category": 1, "messageText": "Property 'timeframe' does not exist on type 'Trade'."}, {"start": 6136, "length": 7, "code": 2339, "category": 1, "messageText": "Property 'session' does not exist on type 'Trade'."}]], [393, [{"start": 3370, "length": 11, "code": 2322, "category": 1, "messageText": "Type 'string' is not assignable to type '\"trades\" | \"summary\" | \"charts\"'.", "relatedInformation": [{"file": "./src/features/trade-analysis/types.ts", "start": 3309, "length": 11, "messageText": "The expected type comes from property 'defaultView' which is declared here on type 'Partial<UserPreferences>'", "category": 3, "code": 6500}]}]], [395, [{"start": 3758, "length": 8, "messageText": "'disabled' is declared but its value is never read.", "category": 1, "code": 6133, "reportsUnnecessary": true}]], [584, [{"start": 4747, "length": 10, "code": 2353, "category": 1, "messageText": "Object literal may only specify known properties, and 'marketData' does not exist in type 'DailyGuideData'."}]], [605, [{"start": 362, "length": 17, "messageText": "'DynamicActionItem' is declared but its value is never read.", "category": 1, "code": 6133, "reportsUnnecessary": true}, {"start": 386, "length": 18, "messageText": "'ElementPerformance' is declared but its value is never read.", "category": 1, "code": 6133, "reportsUnnecessary": true}, {"start": 411, "length": 22, "messageText": "'CombinationPerformance' is declared but its value is never read.", "category": 1, "code": 6133, "reportsUnnecessary": true}]], [607, [{"start": 235, "length": 24, "messageText": "Module './components' has already exported a member named 'TradingPlan'. Consider explicitly re-exporting to resolve the ambiguity.", "category": 1, "code": 2308}, {"start": 235, "length": 24, "messageText": "Module './state' has already exported a member named 'DailyGuideContext'. Consider explicitly re-exporting to resolve the ambiguity.", "category": 1, "code": 2308}]], [613, [{"start": 176, "length": 18, "messageText": "'PerformanceMetrics' is declared but its value is never read.", "category": 1, "code": 6133, "reportsUnnecessary": true}]], [614, [{"start": 170, "length": 18, "messageText": "'PerformanceMetrics' is declared but its value is never read.", "category": 1, "code": 6133, "reportsUnnecessary": true}, {"start": 432, "length": 10, "messageText": "'setMetrics' is declared but its value is never read.", "category": 1, "code": 6133, "reportsUnnecessary": true}]], [623, [{"start": 248, "length": 29, "messageText": "Cannot find module '../hooks/tradeAnalysisState' or its corresponding type declarations.", "category": 1, "code": 2307}]], [626, [{"start": 621, "length": 9, "messageText": "'setTrades' is declared but its value is never read.", "category": 1, "code": 6133, "reportsUnnecessary": true}, {"start": 724, "length": 8, "messageText": "'setError' is declared but its value is never read.", "category": 1, "code": 6133, "reportsUnnecessary": true}, {"start": 1353, "length": 18, "messageText": "'setEquityCurveData' is declared but its value is never read.", "category": 1, "code": 6133, "reportsUnnecessary": true}, {"start": 1431, "length": 19, "messageText": "'setDistributionData' is declared but its value is never read.", "category": 1, "code": 6133, "reportsUnnecessary": true}, {"start": 1505, "length": 10, "messageText": "'setMetrics' is declared but its value is never read.", "category": 1, "code": 6133, "reportsUnnecessary": true}]], [627, [{"start": 114, "length": 70, "messageText": "All imports in import declaration are unused.", "category": 1, "code": 6192, "reportsUnnecessary": true}]], [631, [{"start": 176, "length": 17, "messageText": "'CompleteTradeData' is declared but its value is never read.", "category": 1, "code": 6133, "reportsUnnecessary": true}]], [632, [{"start": 735, "length": 38, "messageText": "Cannot find module './CategoryPerformanceChartRefactored' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 939, "length": 17, "messageText": "'\"./DistributionChart\"' has no exported member named 'DistributionChart'. Did you mean 'DistributionBar'?", "category": 1, "code": 2724}, {"start": 996, "length": 11, "messageText": "Module '\"./EquityCurve\"' has no exported member 'EquityCurve'. Did you mean to use 'import EquityCurve from \"./EquityCurve\"' instead?", "category": 1, "code": 2614}, {"start": 1082, "length": 7, "messageText": "Module '\"./TradeAnalysisCharts\"' has no exported member 'default'.", "category": 1, "code": 2305}, {"start": 1154, "length": 7, "messageText": "Module '\"./TradeAnalysisSummary\"' has no exported member 'default'.", "category": 1, "code": 2305}, {"start": 1228, "length": 7, "messageText": "Module '\"./TradeAnalysisTable\"' has no exported member 'default'.", "category": 1, "code": 2305}]], [633, [{"start": 238, "length": 5, "messageText": "Re-exporting a type when 'isolatedModules' is enabled requires using 'export type'.", "category": 1, "code": 1205}, {"start": 245, "length": 13, "messageText": "Re-exporting a type when 'isolatedModules' is enabled requires using 'export type'.", "category": 1, "code": 1205}, {"start": 260, "length": 18, "messageText": "Re-exporting a type when 'isolatedModules' is enabled requires using 'export type'.", "category": 1, "code": 1205}]], [723, [{"start": 97, "length": 160, "messageText": "All imports in import declaration are unused.", "category": 1, "code": 6192, "reportsUnnecessary": true}]], [731, [{"start": 7496, "length": 6, "messageText": "Not all code paths return a value.", "category": 1, "code": 7030}]], [735, [{"start": 526, "length": 5, "messageText": "'Trade' is declared but its value is never read.", "category": 1, "code": 6133, "reportsUnnecessary": true}, {"start": 2379, "length": 11, "messageText": "'dataFetcher' is declared but its value is never read.", "category": 1, "code": 6133, "reportsUnnecessary": true}]], [737, [{"start": 9647, "length": 18, "messageText": "'enableFeatureFlags' is declared but its value is never read.", "category": 1, "code": 6133, "reportsUnnecessary": true}]], [739, [{"start": 357, "length": 17, "messageText": "'CompleteTradeData' is declared but its value is never read.", "category": 1, "code": 6133, "reportsUnnecessary": true}, {"start": 6410, "length": 5, "code": 2353, "category": 1, "messageText": "Object literal may only specify known properties, and 'model' does not exist in type 'Trade'."}]]], "latestChangedDtsFile": "./dist/features/daily-guide/hooks/useEnhancedSetupIntelligence.d.ts", "version": "5.8.3"}