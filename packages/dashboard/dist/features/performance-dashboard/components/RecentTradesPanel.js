import { jsx as _jsx, jsxs as _jsxs } from "react/jsx-runtime";
import styled from 'styled-components';
const Container = styled.div `
  width: 100%;
`;
const Table = styled.table `
  width: 100%;
  border-collapse: collapse;
`;
const TableHead = styled.thead `
  border-bottom: 1px solid ${({ theme }) => theme.colors.border};
`;
const TableRow = styled.tr `
  &:nth-child(even) {
    background-color: ${({ theme }) => theme.colors.background};
  }

  &:hover {
    background-color: ${({ theme }) => theme.colors.hover};
  }
`;
const TableHeader = styled.th `
  text-align: left;
  padding: ${({ theme }) => theme.spacing.sm};
  font-size: ${({ theme }) => theme.fontSizes.sm};
  font-weight: 500;
  color: ${({ theme }) => theme.colors.textSecondary};
`;
const TableCell = styled.td `
  padding: ${({ theme }) => theme.spacing.sm};
  font-size: ${({ theme }) => theme.fontSizes.md};
`;
const DirectionCell = styled(TableCell) `
  color: ${({ theme, direction }) => direction === 'Long' ? theme.colors.success : theme.colors.danger};
`;
const ResultCell = styled(TableCell) `
  color: ${({ theme, result }) => {
    switch (result) {
        case 'Win':
            return theme.colors.success;
        case 'Loss':
            return theme.colors.danger;
        default:
            return theme.colors.textSecondary;
    }
}};
`;
const ProfitCell = styled(TableCell) `
  color: ${({ theme, value }) => value > 0
    ? theme.colors.success
    : value < 0
        ? theme.colors.danger
        : theme.colors.textSecondary};
`;
const LoadingIndicator = styled.div `
  padding: ${({ theme }) => theme.spacing.lg};
  text-align: center;
  color: ${({ theme }) => theme.colors.textSecondary};
  font-style: italic;
`;
export const RecentTradesPanel = ({ trades, isLoading = false, }) => {
    // DEBUG: Log the trades data received by the component
    console.log('🎯 RecentTradesPanel received trades:', trades?.map((t) => ({
        id: t.trade.id,
        date: t.trade.date,
        market: t.trade.market,
        direction: t.trade.direction,
    })));
    if (isLoading) {
        console.log('⏳ RecentTradesPanel: Loading state');
        return _jsx(LoadingIndicator, { children: "Loading recent trades..." });
    }
    if (!trades || trades.length === 0) {
        console.log('❌ RecentTradesPanel: No trades data');
        return _jsx(LoadingIndicator, { children: "No recent trades found" });
    }
    return (_jsx(Container, { children: _jsxs(Table, { children: [_jsx(TableHead, { children: _jsxs(TableRow, { children: [_jsx(TableHeader, { children: "Date" }), _jsx(TableHeader, { children: "Symbol" }), _jsx(TableHeader, { children: "Direction" }), _jsx(TableHeader, { children: "Result" }), _jsx(TableHeader, { children: "Profit/Loss" })] }) }), _jsx("tbody", { children: trades.map((tradeData) => {
                        const { trade } = tradeData;
                        const result = trade.win_loss === 'Win' ? 'Win' : trade.win_loss === 'Loss' ? 'Loss' : 'breakeven';
                        return (_jsxs(TableRow, { children: [_jsx(TableCell, { children: trade.date }), _jsx(TableCell, { children: trade.market || 'MNQ' }), _jsx(DirectionCell, { direction: trade.direction, children: trade.direction === 'Long' ? '▲ Long' : '▼ Short' }), _jsx(ResultCell, { result: result, children: result.charAt(0).toUpperCase() + result.slice(1) }), _jsxs(ProfitCell, { value: trade.achieved_pl || 0, children: ["$", (trade.achieved_pl || 0).toFixed(2)] })] }, trade.id));
                    }) })] }) }));
};
//# sourceMappingURL=RecentTradesPanel.js.map