/**
 * Real Trade Analysis API Service
 *
 * This service replaces the mock data generation with real trade data integration
 * from the trade journal system using the trade storage service.
 */
import { TradeAnalysisData, TradeFilters as AnalysisFilters } from '../types';
/**
 * Fetch real trade analysis data from the trade storage service
 */
export declare const fetchRealTradeAnalysisData: (filters: AnalysisFilters) => Promise<TradeAnalysisData>;
/**
 * Get available filter options from real trade data
 */
export declare const getFilterOptions: () => Promise<{
    symbols: string[];
    strategies: string[];
    sessions: string[];
    setups: string[];
}>;
/**
 * Get trade statistics for dashboard summary
 */
export declare const getTradeStatistics: () => Promise<{
    totalTrades: number;
    todayTrades: number;
    weekTrades: number;
    monthTrades: number;
    lastTradeDate: string | null;
}>;
//# sourceMappingURL=realTradeAnalysisApi.d.ts.map