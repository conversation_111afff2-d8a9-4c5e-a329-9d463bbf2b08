/**
 * Trade Analysis API
 *
 * API functions for the trade analysis feature
 * Updated to support both real data and mock data
 */
import { TradeAnalysisData, TradeFilters } from '../types';
/**
 * Fetch trade analysis data
 *
 * Uses real trade data from IndexedDB when USE_REAL_DATA is true,
 * otherwise falls back to mock data generation.
 */
export declare const fetchTradeAnalysisData: (filters: TradeFilters) => Promise<TradeAnalysisData>;
/**
 * Get available filter options
 */
export declare const fetchFilterOptions: () => Promise<{
    symbols: string[];
    strategies: string[];
    sessions: string[];
    setups: string[];
}>;
/**
 * Get trade statistics for dashboard
 */
export declare const fetchTradeStatistics: () => Promise<{
    totalTrades: number;
    todayTrades: number;
    weekTrades: number;
    monthTrades: number;
    lastTradeDate: string | null;
}>;
//# sourceMappingURL=tradeAnalysisApi.d.ts.map