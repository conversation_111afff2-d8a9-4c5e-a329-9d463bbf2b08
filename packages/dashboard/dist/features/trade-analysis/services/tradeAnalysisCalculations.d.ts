/**
 * Trade Analysis Calculations Service
 *
 * This service provides real-time calculation of trading metrics from CompleteTradeData.
 * Replaces mock data generation with actual trade analysis calculations.
 */
import { CompleteTradeData } from '@adhd-trading-dashboard/shared';
import { PerformanceMetrics, CategoryPerformance, TimePerformance, EquityPoint, DistributionBar } from '../types';
/**
 * Calculate comprehensive performance metrics from real trade data
 */
export declare const calculateRealPerformanceMetrics: (trades: CompleteTradeData[]) => PerformanceMetrics;
/**
 * Calculate performance by category (symbol, strategy, session, etc.)
 */
export declare const calculateCategoryPerformance: (trades: CompleteTradeData[], category: "market" | "model_type" | "session" | "setup" | "direction") => CategoryPerformance[];
/**
 * Calculate performance by time periods
 */
export declare const calculateTimePerformance: (trades: CompleteTradeData[], timeType: "timeOfDay" | "dayOfWeek" | "monthly") => TimePerformance[];
/**
 * Generate equity curve data from trades
 */
export declare const generateEquityCurve: (trades: CompleteTradeData[]) => EquityPoint[];
/**
 * Generate profit/loss distribution data
 */
export declare const generateDistributionData: (trades: CompleteTradeData[]) => DistributionBar[];
//# sourceMappingURL=tradeAnalysisCalculations.d.ts.map