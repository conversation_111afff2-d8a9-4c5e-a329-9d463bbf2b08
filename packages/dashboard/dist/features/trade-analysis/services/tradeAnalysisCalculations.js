/**
 * Trade Analysis Calculations Service
 *
 * This service provides real-time calculation of trading metrics from CompleteTradeData.
 * Replaces mock data generation with actual trade analysis calculations.
 */
/**
 * Calculate comprehensive performance metrics from real trade data
 */
export const calculateRealPerformanceMetrics = (trades) => {
    if (trades.length === 0) {
        return getEmptyMetrics();
    }
    // Extract trade records for easier processing
    const tradeRecords = trades.map(t => t.trade);
    // Basic trade categorization
    const winningTrades = tradeRecords.filter(trade => (trade.achieved_pl || 0) > 0);
    const losingTrades = tradeRecords.filter(trade => (trade.achieved_pl || 0) < 0);
    const breakEvenTrades = tradeRecords.filter(trade => (trade.achieved_pl || 0) === 0);
    // Profit/Loss calculations
    const totalProfitLoss = tradeRecords.reduce((sum, trade) => sum + (trade.achieved_pl || 0), 0);
    const totalWinAmount = winningTrades.reduce((sum, trade) => sum + (trade.achieved_pl || 0), 0);
    const totalLossAmount = Math.abs(losingTrades.reduce((sum, trade) => sum + (trade.achieved_pl || 0), 0));
    // Average calculations
    const averageWin = winningTrades.length > 0 ? totalWinAmount / winningTrades.length : 0;
    const averageLoss = losingTrades.length > 0 ? totalLossAmount / losingTrades.length : 0;
    // Extreme values
    const largestWin = winningTrades.length > 0
        ? Math.max(...winningTrades.map(trade => trade.achieved_pl || 0))
        : 0;
    const largestLoss = losingTrades.length > 0
        ? Math.min(...losingTrades.map(trade => trade.achieved_pl || 0))
        : 0;
    // Duration calculations (if timing data available)
    const durations = calculateTradeDurations(tradeRecords);
    const averageDuration = durations.length > 0
        ? durations.reduce((sum, duration) => sum + duration, 0) / durations.length
        : 0;
    // Key performance ratios
    const winRate = trades.length > 0 ? winningTrades.length / trades.length : 0;
    const profitFactor = totalLossAmount > 0 ? totalWinAmount / totalLossAmount : (totalWinAmount > 0 ? Infinity : 0);
    const expectancy = winRate * averageWin - (1 - winRate) * averageLoss;
    // R-Multiple analysis
    const averageRMultiple = tradeRecords.length > 0
        ? tradeRecords.reduce((sum, trade) => sum + (trade.r_multiple || 0), 0) / tradeRecords.length
        : 0;
    // Advanced metrics
    const { maxDrawdown, maxDrawdownPercent } = calculateDrawdown(trades);
    const sharpeRatio = calculateSharpeRatio(tradeRecords);
    const calmarRatio = calculateCalmarRatio(totalProfitLoss, maxDrawdownPercent);
    return {
        totalTrades: trades.length,
        winningTrades: winningTrades.length,
        losingTrades: losingTrades.length,
        breakeven: breakEvenTrades.length,
        winRate: Math.round(winRate * 10000) / 100, // Percentage with 2 decimals
        averageWin: Math.round(averageWin * 100) / 100,
        averageLoss: Math.round(averageLoss * 100) / 100,
        profitFactor: Math.round(profitFactor * 100) / 100,
        totalProfitLoss: Math.round(totalProfitLoss * 100) / 100,
        largestWin: Math.round(largestWin * 100) / 100,
        largestLoss: Math.round(largestLoss * 100) / 100,
        averageDuration: Math.round(averageDuration * 100) / 100,
        expectancy: Math.round(expectancy * 100) / 100,
        averageRMultiple: Math.round(averageRMultiple * 100) / 100,
        maxDrawdown: Math.round(maxDrawdown * 100) / 100,
        maxDrawdownPercent: Math.round(maxDrawdownPercent * 100) / 100,
        sharpeRatio: Math.round(sharpeRatio * 100) / 100,
        calmarRatio: Math.round(calmarRatio * 100) / 100,
    };
};
/**
 * Calculate performance by category (symbol, strategy, session, etc.)
 */
export const calculateCategoryPerformance = (trades, category) => {
    if (trades.length === 0)
        return [];
    // Group trades by category
    const categories = new Map();
    trades.forEach(tradeData => {
        const trade = tradeData.trade;
        let categoryValue;
        switch (category) {
            case 'market':
                categoryValue = trade.market || 'Unknown';
                break;
            case 'model_type':
                categoryValue = trade.model_type || 'Unknown';
                break;
            case 'session':
                categoryValue = trade.session || 'Unknown';
                break;
            case 'setup':
                categoryValue = trade.setup || 'Unknown';
                break;
            case 'direction':
                categoryValue = trade.direction || 'Unknown';
                break;
            default:
                categoryValue = 'Unknown';
        }
        if (!categories.has(categoryValue)) {
            categories.set(categoryValue, []);
        }
        categories.get(categoryValue).push(tradeData);
    });
    // Calculate performance for each category
    const performance = [];
    categories.forEach((categoryTrades, categoryValue) => {
        const tradeRecords = categoryTrades.map(t => t.trade);
        const winningTrades = tradeRecords.filter(trade => (trade.achieved_pl || 0) > 0);
        const totalProfitLoss = tradeRecords.reduce((sum, trade) => sum + (trade.achieved_pl || 0), 0);
        const winRate = categoryTrades.length > 0 ? winningTrades.length / categoryTrades.length : 0;
        const averageProfitLoss = categoryTrades.length > 0 ? totalProfitLoss / categoryTrades.length : 0;
        performance.push({
            category,
            value: categoryValue,
            trades: categoryTrades.length,
            winRate: Math.round(winRate * 10000) / 100,
            profitLoss: Math.round(totalProfitLoss * 100) / 100,
            averageProfitLoss: Math.round(averageProfitLoss * 100) / 100,
        });
    });
    // Sort by profit/loss (descending)
    return performance.sort((a, b) => b.profitLoss - a.profitLoss);
};
/**
 * Calculate performance by time periods
 */
export const calculateTimePerformance = (trades, timeType) => {
    if (trades.length === 0)
        return [];
    let timeSlots;
    switch (timeType) {
        case 'timeOfDay':
            timeSlots = [
                '9:30-10:30', '10:30-11:30', '11:30-12:30',
                '12:30-13:30', '13:30-14:30', '14:30-15:30', '15:30-16:00'
            ];
            break;
        case 'dayOfWeek':
            timeSlots = ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday'];
            break;
        case 'monthly':
            timeSlots = [
                'January', 'February', 'March', 'April', 'May', 'June',
                'July', 'August', 'September', 'October', 'November', 'December'
            ];
            break;
        default:
            return [];
    }
    // Initialize time performance data
    const timePerformance = timeSlots.map(timeSlot => ({
        timeSlot,
        trades: 0,
        winRate: 0,
        profitLoss: 0,
    }));
    // Process each trade
    trades.forEach(tradeData => {
        const trade = tradeData.trade;
        const tradeDate = new Date(trade.date);
        let slotIndex = -1;
        if (timeType === 'timeOfDay' && trade.entry_time) {
            slotIndex = getTimeOfDaySlot(trade.entry_time);
        }
        else if (timeType === 'dayOfWeek') {
            const dayOfWeek = tradeDate.getDay();
            if (dayOfWeek >= 1 && dayOfWeek <= 5) { // Monday to Friday
                slotIndex = dayOfWeek - 1;
            }
        }
        else if (timeType === 'monthly') {
            slotIndex = tradeDate.getMonth();
        }
        if (slotIndex >= 0 && slotIndex < timePerformance.length) {
            const slot = timePerformance[slotIndex];
            slot.trades++;
            slot.profitLoss += trade.achieved_pl || 0;
        }
    });
    // Calculate win rates for each slot
    timePerformance.forEach((slot, index) => {
        if (slot.trades > 0) {
            const slotTrades = trades.filter(tradeData => {
                const trade = tradeData.trade;
                const tradeDate = new Date(trade.date);
                if (timeType === 'timeOfDay' && trade.entry_time) {
                    return getTimeOfDaySlot(trade.entry_time) === index;
                }
                else if (timeType === 'dayOfWeek') {
                    const dayOfWeek = tradeDate.getDay();
                    return dayOfWeek === index + 1;
                }
                else if (timeType === 'monthly') {
                    return tradeDate.getMonth() === index;
                }
                return false;
            });
            const winningSlotTrades = slotTrades.filter(tradeData => (tradeData.trade.achieved_pl || 0) > 0);
            slot.winRate = slotTrades.length > 0 ? (winningSlotTrades.length / slotTrades.length) * 100 : 0;
        }
    });
    // Filter out empty slots and round numbers
    return timePerformance
        .filter(slot => slot.trades > 0)
        .map(slot => ({
        ...slot,
        winRate: Math.round(slot.winRate * 100) / 100,
        profitLoss: Math.round(slot.profitLoss * 100) / 100,
    }));
};
/**
 * Generate equity curve data from trades
 */
export const generateEquityCurve = (trades) => {
    if (trades.length === 0)
        return [];
    // Sort trades by date
    const sortedTrades = [...trades].sort((a, b) => new Date(a.trade.date).getTime() - new Date(b.trade.date).getTime());
    const equityPoints = [];
    let runningBalance = 0;
    sortedTrades.forEach((tradeData, index) => {
        const trade = tradeData.trade;
        runningBalance += trade.achieved_pl || 0;
        equityPoints.push({
            date: trade.date,
            balance: Math.round(runningBalance * 100) / 100,
            tradeNumber: index + 1,
            profitLoss: trade.achieved_pl || 0,
        });
    });
    return equityPoints;
};
/**
 * Generate profit/loss distribution data
 */
export const generateDistributionData = (trades) => {
    if (trades.length === 0)
        return [];
    // Define P&L ranges
    const ranges = [
        { min: -Infinity, max: -1000, label: '< -$1000' },
        { min: -1000, max: -500, label: '-$1000 to -$500' },
        { min: -500, max: -100, label: '-$500 to -$100' },
        { min: -100, max: 0, label: '-$100 to $0' },
        { min: 0, max: 100, label: '$0 to $100' },
        { min: 100, max: 500, label: '$100 to $500' },
        { min: 500, max: 1000, label: '$500 to $1000' },
        { min: 1000, max: Infinity, label: '> $1000' },
    ];
    const distribution = ranges.map(range => ({
        range: range.label,
        count: 0,
        percentage: 0,
        totalPnL: 0,
    }));
    // Categorize trades into ranges
    trades.forEach(tradeData => {
        const pnl = tradeData.trade.achieved_pl || 0;
        const rangeIndex = ranges.findIndex(range => pnl > range.min && pnl <= range.max);
        if (rangeIndex >= 0) {
            distribution[rangeIndex].count++;
            distribution[rangeIndex].totalPnL += pnl;
        }
    });
    // Calculate percentages
    distribution.forEach(bar => {
        bar.percentage = trades.length > 0 ? (bar.count / trades.length) * 100 : 0;
        bar.percentage = Math.round(bar.percentage * 100) / 100;
        bar.totalPnL = Math.round(bar.totalPnL * 100) / 100;
    });
    return distribution.filter(bar => bar.count > 0);
};
// Helper functions
const getEmptyMetrics = () => ({
    totalTrades: 0,
    winningTrades: 0,
    losingTrades: 0,
    breakeven: 0,
    winRate: 0,
    averageWin: 0,
    averageLoss: 0,
    profitFactor: 0,
    totalProfitLoss: 0,
    largestWin: 0,
    largestLoss: 0,
    averageDuration: 0,
    expectancy: 0,
});
const calculateTradeDurations = (trades) => {
    return trades
        .filter(trade => trade.entry_time && trade.exit_time)
        .map(trade => {
        const entryTime = new Date(trade.entry_time).getTime();
        const exitTime = new Date(trade.exit_time).getTime();
        return (exitTime - entryTime) / (1000 * 60); // Duration in minutes
    });
};
const getTimeOfDaySlot = (timeString) => {
    const time = new Date(timeString);
    const hour = time.getHours();
    const minute = time.getMinutes();
    const timeValue = hour + minute / 60;
    if (timeValue < 9.5 || timeValue >= 16)
        return -1; // Outside market hours
    if (timeValue < 10.5)
        return 0;
    if (timeValue < 11.5)
        return 1;
    if (timeValue < 12.5)
        return 2;
    if (timeValue < 13.5)
        return 3;
    if (timeValue < 14.5)
        return 4;
    if (timeValue < 15.5)
        return 5;
    return 6;
};
const calculateDrawdown = (trades) => {
    if (trades.length === 0)
        return { maxDrawdown: 0, maxDrawdownPercent: 0 };
    const sortedTrades = [...trades].sort((a, b) => new Date(a.trade.date).getTime() - new Date(b.trade.date).getTime());
    let runningBalance = 0;
    let peak = 0;
    let maxDrawdown = 0;
    let maxDrawdownPercent = 0;
    sortedTrades.forEach(tradeData => {
        runningBalance += tradeData.trade.achieved_pl || 0;
        if (runningBalance > peak) {
            peak = runningBalance;
        }
        const drawdown = peak - runningBalance;
        const drawdownPercent = peak > 0 ? (drawdown / peak) * 100 : 0;
        if (drawdown > maxDrawdown) {
            maxDrawdown = drawdown;
            maxDrawdownPercent = drawdownPercent;
        }
    });
    return { maxDrawdown, maxDrawdownPercent };
};
const calculateSharpeRatio = (trades) => {
    if (trades.length < 2)
        return 0;
    const returns = trades.map(trade => trade.achieved_pl || 0);
    const avgReturn = returns.reduce((sum, ret) => sum + ret, 0) / returns.length;
    const variance = returns.reduce((sum, ret) => sum + Math.pow(ret - avgReturn, 2), 0) / (returns.length - 1);
    const stdDev = Math.sqrt(variance);
    return stdDev > 0 ? avgReturn / stdDev : 0;
};
const calculateCalmarRatio = (totalReturn, maxDrawdownPercent) => {
    return maxDrawdownPercent > 0 ? (totalReturn / maxDrawdownPercent) * 100 : 0;
};
//# sourceMappingURL=tradeAnalysisCalculations.js.map