/**
 * Performance Cache Service
 *
 * This service provides caching and optimization for trade analysis calculations
 * to improve performance when dealing with large datasets.
 */
import { CompleteTradeData } from '@adhd-trading-dashboard/shared';
import { PerformanceMetrics, CategoryPerformance, TimePerformance, EquityPoint, DistributionBar } from '../types';
/**
 * Performance Cache Manager
 */
declare class PerformanceCacheManager {
    private cache;
    private config;
    /**
     * Generate a hash key for cache lookup
     */
    private generateCacheKey;
    /**
     * Get cached result if available and not expired
     */
    private getCached;
    /**
     * Store result in cache
     */
    private setCached;
    /**
     * Clear all cache entries
     */
    clearCache(): void;
    /**
     * Clear expired cache entries
     */
    clearExpiredCache(): void;
    /**
     * Get cache statistics
     */
    getCacheStats(): {
        size: number;
        maxSize: number;
        hitRate: number;
        oldestEntry: number | null;
    };
    /**
     * Cached performance metrics calculation
     */
    getCachedPerformanceMetrics(trades: CompleteTradeData[], calculator: (trades: CompleteTradeData[]) => PerformanceMetrics): Promise<PerformanceMetrics>;
    /**
     * Cached category performance calculation
     */
    getCachedCategoryPerformance(trades: CompleteTradeData[], category: string, calculator: (trades: CompleteTradeData[], category: any) => CategoryPerformance[]): Promise<CategoryPerformance[]>;
    /**
     * Cached time performance calculation
     */
    getCachedTimePerformance(trades: CompleteTradeData[], timeType: string, calculator: (trades: CompleteTradeData[], timeType: any) => TimePerformance[]): Promise<TimePerformance[]>;
    /**
     * Cached equity curve calculation
     */
    getCachedEquityCurve(trades: CompleteTradeData[], calculator: (trades: CompleteTradeData[]) => EquityPoint[]): Promise<EquityPoint[]>;
    /**
     * Cached distribution data calculation
     */
    getCachedDistributionData(trades: CompleteTradeData[], calculator: (trades: CompleteTradeData[]) => DistributionBar[]): Promise<DistributionBar[]>;
}
export declare const performanceCache: PerformanceCacheManager;
/**
 * Batch calculation optimization
 *
 * This function optimizes multiple calculations by batching them together
 * and using cached results where possible.
 */
export declare const batchCalculateAnalytics: (trades: CompleteTradeData[], calculators: {
    performanceMetrics: (trades: CompleteTradeData[]) => PerformanceMetrics;
    categoryPerformance: (trades: CompleteTradeData[], category: any) => CategoryPerformance[];
    timePerformance: (trades: CompleteTradeData[], timeType: any) => TimePerformance[];
    equityCurve: (trades: CompleteTradeData[]) => EquityPoint[];
    distributionData: (trades: CompleteTradeData[]) => DistributionBar[];
}) => Promise<{
    metrics: PerformanceMetrics;
    symbolPerformance: CategoryPerformance[];
    strategyPerformance: CategoryPerformance[];
    sessionPerformance: CategoryPerformance[];
    setupPerformance: CategoryPerformance[];
    directionPerformance: CategoryPerformance[];
    timeOfDayPerformance: TimePerformance[];
    dayOfWeekPerformance: TimePerformance[];
    monthlyPerformance: TimePerformance[];
    equityCurve: EquityPoint[];
    distributionData: DistributionBar[];
}>;
/**
 * Performance monitoring utilities
 */
export declare const performanceMonitor: {
    /**
     * Measure execution time of a function
     */
    measureTime<T>(name: string, fn: () => Promise<T> | T): Promise<T>;
    /**
     * Log memory usage (if available)
     */
    logMemoryUsage(context: string): void;
    /**
     * Get cache statistics
     */
    getCacheStats(): {
        size: number;
        maxSize: number;
        hitRate: number;
        oldestEntry: number | null;
    };
};
export {};
//# sourceMappingURL=performanceCache.d.ts.map