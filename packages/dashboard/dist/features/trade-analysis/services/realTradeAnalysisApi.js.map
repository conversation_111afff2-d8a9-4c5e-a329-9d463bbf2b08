{"version": 3, "file": "realTradeAnalysisApi.js", "sourceRoot": "", "sources": ["../../../../src/features/trade-analysis/services/realTradeAnalysisApi.ts"], "names": [], "mappings": "AAAA;;;;;GAKG;AAEH,OAAO,EACL,mBAAmB,GAGpB,MAAM,gCAAgC,CAAC;AAUxC,OAAO,EACL,+BAA+B,EAC/B,4BAA4B,EAC5B,wBAAwB,EACxB,mBAAmB,EACnB,wBAAwB,GACzB,MAAM,6BAA6B,CAAC;AACrC,OAAO,EAAE,uBAAuB,EAAE,kBAAkB,EAAE,MAAM,oBAAoB,CAAC;AAEjF;;GAEG;AACH,MAAM,CAAC,MAAM,0BAA0B,GAAG,KAAK,EAC7C,OAAwB,EACI,EAAE;IAC9B,IAAI,CAAC;QACH,OAAO,CAAC,GAAG,CAAC,oDAAoD,EAAE,OAAO,CAAC,CAAC;QAE3E,sDAAsD;QACtD,MAAM,cAAc,GAAG,uBAAuB,CAAC,OAAO,CAAC,CAAC;QAExD,8BAA8B;QAC9B,MAAM,MAAM,GAAG,MAAM,mBAAmB,CAAC,YAAY,CAAC,cAAc,CAAC,CAAC;QAEtE,OAAO,CAAC,GAAG,CAAC,gBAAgB,MAAM,CAAC,MAAM,sBAAsB,CAAC,CAAC;QAEjE,mDAAmD;QACnD,MAAM,cAAc,GAAG,sBAAsB,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC;QAE/D,OAAO,CAAC,GAAG,CAAC,mCAAmC,cAAc,CAAC,MAAM,SAAS,CAAC,CAAC;QAE/E,6CAA6C;QAC7C,MAAM,YAAY,GAAG,MAAM,oBAAoB,CAAC,cAAc,CAAC,CAAC;QAEhE,OAAO,CAAC,GAAG,CAAC,8CAA8C,CAAC,CAAC;QAC5D,OAAO,YAAY,CAAC;IACtB,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,OAAO,CAAC,KAAK,CAAC,4CAA4C,EAAE,KAAK,CAAC,CAAC;QACnE,MAAM,IAAI,KAAK,CACb,wCACE,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,eAC3C,EAAE,CACH,CAAC;IACJ,CAAC;AACH,CAAC,CAAC;AAEF;;GAEG;AACH,MAAM,oBAAoB,GAAG,KAAK,EAAE,MAA2B,EAA8B,EAAE;IAC7F,OAAO,CAAC,GAAG,CAAC,kDAAkD,CAAC,CAAC;IAEhE,sCAAsC;IACtC,kBAAkB,CAAC,cAAc,CAAC,iBAAiB,CAAC,CAAC;IAErD,6DAA6D;IAC7D,MAAM,gBAAgB,GAAG,MAAM,kBAAkB,CAAC,WAAW,CAAC,6BAA6B,EAAE,GAAG,EAAE,CAChG,uBAAuB,CAAC,MAAM,EAAE;QAC9B,kBAAkB,EAAE,+BAA+B;QACnD,mBAAmB,EAAE,4BAA4B;QACjD,eAAe,EAAE,wBAAwB;QACzC,WAAW,EAAE,mBAAmB;QAChC,gBAAgB,EAAE,wBAAwB;KAC3C,CAAC,CACH,CAAC;IAEF,yCAAyC;IACzC,MAAM,EACJ,OAAO,EACP,iBAAiB,EACjB,mBAAmB,EACnB,kBAAkB,EAClB,gBAAgB,EAChB,oBAAoB,EACpB,oBAAoB,EACpB,oBAAoB,EACpB,kBAAkB,EAClB,WAAW,EACX,gBAAgB,GACjB,GAAG,gBAAgB,CAAC;IAErB,qCAAqC;IACrC,kBAAkB,CAAC,cAAc,CAAC,gBAAgB,CAAC,CAAC;IAEpD,uBAAuB;IACvB,MAAM,UAAU,GAAG,kBAAkB,CAAC,aAAa,EAAE,CAAC;IACtD,OAAO,CAAC,GAAG,CAAC,sBAAsB,EAAE,UAAU,CAAC,CAAC;IAEhD,OAAO;QACL,MAAM,EAAE,MAAM,CAAC,GAAG,CAAC,4BAA4B,CAAC;QAChD,OAAO;QACP,iBAAiB;QACjB,mBAAmB;QACnB,oBAAoB,EAAE,EAAE,EAAE,qCAAqC;QAC/D,kBAAkB;QAClB,gBAAgB;QAChB,oBAAoB;QACpB,oBAAoB;QACpB,oBAAoB;QACpB,kBAAkB;QAClB,WAAW;QACX,gBAAgB;QAChB,2BAA2B;QAC3B,WAAW,EAAE,MAAM,CAAC,MAAM;QAC1B,SAAS,EAAE;YACT,KAAK,EACH,MAAM,CAAC,MAAM,GAAG,CAAC;gBACf,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,IAAI,IAAI,CAAC,CAAC,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,OAAO,EAAE,CAAC,CAAC;gBAClE,CAAC,CAAC,IAAI,CAAC,GAAG,EAAE;YAChB,GAAG,EACD,MAAM,CAAC,MAAM,GAAG,CAAC;gBACf,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,IAAI,IAAI,CAAC,CAAC,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,OAAO,EAAE,CAAC,CAAC;gBAClE,CAAC,CAAC,IAAI,CAAC,GAAG,EAAE;SACjB;QACD,WAAW,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;KACtC,CAAC;AACJ,CAAC,CAAC;AAEF;;GAEG;AACH,MAAM,uBAAuB,GAAG,CAAC,OAAwB,EAAgB,EAAE;IACzE,MAAM,cAAc,GAAiB,EAAE,CAAC;IAExC,qBAAqB;IACrB,IAAI,OAAO,CAAC,SAAS,EAAE,CAAC;QACtB,cAAc,CAAC,UAAU,GAAG,OAAO,CAAC,SAAS,CAAC,SAAS,CAAC;QACxD,cAAc,CAAC,QAAQ,GAAG,OAAO,CAAC,SAAS,CAAC,OAAO,CAAC;IACtD,CAAC;IAED,+CAA+C;IAC/C,IAAI,OAAO,CAAC,OAAO,IAAI,OAAO,CAAC,OAAO,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;QAClD,2EAA2E;QAC3E,cAAc,CAAC,MAAM,GAAG,OAAO,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,4BAA4B;IAC1E,CAAC;IAED,mBAAmB;IACnB,IAAI,OAAO,CAAC,UAAU,IAAI,OAAO,CAAC,UAAU,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;QACxD,2CAA2C;QAC3C,MAAM,SAAS,GAAG,OAAO,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC;QACxC,cAAc,CAAC,SAAS,GAAG,SAAS,KAAK,MAAM,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,OAAO,CAAC;IACrE,CAAC;IAED,iBAAiB;IACjB,IAAI,OAAO,CAAC,QAAQ,IAAI,OAAO,CAAC,QAAQ,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;QACpD,cAAc,CAAC,OAAO,GAAG,OAAO,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;IAC/C,CAAC;IAED,qDAAqD;IACrD,IAAI,OAAO,CAAC,UAAU,IAAI,OAAO,CAAC,UAAU,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;QACxD,cAAc,CAAC,UAAU,GAAG,OAAO,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC;IACpD,CAAC;IAED,sBAAsB;IACtB,IAAI,OAAO,CAAC,aAAa,KAAK,SAAS,EAAE,CAAC;QACxC,cAAc,CAAC,eAAe,GAAG,OAAO,CAAC,aAAa,CAAC;IACzD,CAAC;IACD,IAAI,OAAO,CAAC,aAAa,KAAK,SAAS,EAAE,CAAC;QACxC,cAAc,CAAC,eAAe,GAAG,OAAO,CAAC,aAAa,CAAC;IACzD,CAAC;IAED,OAAO,cAAc,CAAC;AACxB,CAAC,CAAC;AAEF;;GAEG;AACH,MAAM,sBAAsB,GAAG,CAC7B,MAA2B,EAC3B,OAAwB,EACH,EAAE;IACvB,OAAO,MAAM,CAAC,MAAM,CAAC,CAAC,SAAS,EAAE,EAAE;QACjC,MAAM,KAAK,GAAG,SAAS,CAAC,KAAK,CAAC;QAE9B,0BAA0B;QAC1B,IAAI,OAAO,CAAC,OAAO,IAAI,OAAO,CAAC,OAAO,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YAClD,MAAM,iBAAiB,GAAG,OAAO,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,MAAM,EAAE,EAAE,CACxD,KAAK,CAAC,MAAM,EAAE,WAAW,EAAE,CAAC,QAAQ,CAAC,MAAM,CAAC,WAAW,EAAE,CAAC,CAC3D,CAAC;YACF,IAAI,CAAC,iBAAiB;gBAAE,OAAO,KAAK,CAAC;QACvC,CAAC;QAED,6BAA6B;QAC7B,IAAI,OAAO,CAAC,UAAU,IAAI,OAAO,CAAC,UAAU,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YACxD,MAAM,cAAc,GAAG,KAAK,CAAC,SAAS,EAAE,WAAW,EAAE,CAAC;YACtD,MAAM,oBAAoB,GAAG,OAAO,CAAC,UAAU,CAAC,IAAI,CAClD,CAAC,SAAS,EAAE,EAAE,CAAC,SAAS,KAAK,cAAc,CAC5C,CAAC;YACF,IAAI,CAAC,oBAAoB;gBAAE,OAAO,KAAK,CAAC;QAC1C,CAAC;QAED,2BAA2B;QAC3B,IAAI,OAAO,CAAC,QAAQ,IAAI,OAAO,CAAC,QAAQ,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YACpD,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,QAAQ,CAAC,KAAK,CAAC,OAAO,IAAI,EAAE,CAAC;gBAAE,OAAO,KAAK,CAAC;QACpE,CAAC;QAED,6BAA6B;QAC7B,IAAI,OAAO,CAAC,UAAU,IAAI,OAAO,CAAC,UAAU,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YACxD,IAAI,CAAC,OAAO,CAAC,UAAU,CAAC,QAAQ,CAAC,KAAK,CAAC,UAAU,IAAI,EAAE,CAAC;gBAAE,OAAO,KAAK,CAAC;QACzE,CAAC;QAED,qCAAqC;QACrC,IAAI,OAAO,CAAC,QAAQ,IAAI,OAAO,CAAC,QAAQ,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YACpD,MAAM,WAAW,GAAG,cAAc,CAAC,KAAK,CAAC,WAAW,IAAI,CAAC,CAAC,CAAC;YAC3D,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,QAAQ,CAAC,WAAW,CAAC;gBAAE,OAAO,KAAK,CAAC;QAC5D,CAAC;QAED,qDAAqD;QACrD,IAAI,OAAO,CAAC,IAAI,IAAI,OAAO,CAAC,IAAI,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YAC5C,+DAA+D;QACjE,CAAC;QAED,OAAO,IAAI,CAAC;IACd,CAAC,CAAC,CAAC;AACL,CAAC,CAAC;AAEF;;GAEG;AACH,MAAM,4BAA4B,GAAG,CAAC,SAA4B,EAAO,EAAE;IACzE,MAAM,KAAK,GAAG,SAAS,CAAC,KAAK,CAAC;IAE9B,OAAO;QACL,EAAE,EAAE,KAAK,CAAC,EAAE,EAAE,QAAQ,EAAE,IAAI,GAAG,KAAK,CAAC,IAAI,IAAI,KAAK,CAAC,MAAM,EAAE;QAC3D,MAAM,EAAE,KAAK,CAAC,MAAM,IAAI,SAAS;QACjC,SAAS,EAAE,KAAK,CAAC,SAAS,EAAE,WAAW,EAAE,IAAI,SAAS;QACtD,UAAU,EAAE,KAAK,CAAC,WAAW,IAAI,CAAC;QAClC,SAAS,EAAE,KAAK,CAAC,UAAU,IAAI,CAAC;QAChC,QAAQ,EAAE,KAAK,CAAC,eAAe,IAAI,CAAC;QACpC,SAAS,EAAE,KAAK,CAAC,UAAU,IAAI,KAAK,CAAC,IAAI;QACzC,QAAQ,EAAE,KAAK,CAAC,SAAS,IAAI,KAAK,CAAC,IAAI;QACvC,MAAM,EAAE,cAAc,CAAC,KAAK,CAAC,WAAW,IAAI,CAAC,CAAC;QAC9C,UAAU,EAAE,KAAK,CAAC,WAAW,IAAI,CAAC;QAClC,iBAAiB,EAAE,0BAA0B,CAAC,KAAK,CAAC;QACpD,SAAS,EAAE,KAAK,EAAE,sCAAsC;QACxD,OAAO,EAAE,KAAK,CAAC,OAAO,IAAI,SAAS;QACnC,QAAQ,EAAE,KAAK,CAAC,UAAU,IAAI,SAAS;QACvC,KAAK,EAAE,KAAK,CAAC,KAAK,IAAI,SAAS;QAC/B,SAAS,EAAE,KAAK,CAAC,UAAU,IAAI,CAAC;QAChC,IAAI,EAAE,EAAE,EAAE,yBAAyB;QACnC,KAAK,EAAE,KAAK,CAAC,KAAK,IAAI,EAAE;QACxB,oCAAoC;QACpC,cAAc,EAAE,KAAK,CAAC,sBAAsB,IAAI,CAAC;QACjD,SAAS,EAAE,KAAK,CAAC,UAAU,IAAI,EAAE;QACjC,MAAM,EAAE,KAAK,CAAC,OAAO,IAAI,EAAE;QAC3B,eAAe,EAAE,KAAK,CAAC,iBAAiB,IAAI,EAAE;KAC/C,CAAC;AACJ,CAAC,CAAC;AAEF;;GAEG;AACH,MAAM,cAAc,GAAG,CAAC,UAAkB,EAAgC,EAAE;IAC1E,IAAI,UAAU,GAAG,CAAC;QAAE,OAAO,KAAK,CAAC;IACjC,IAAI,UAAU,GAAG,CAAC;QAAE,OAAO,MAAM,CAAC;IAClC,OAAO,WAAW,CAAC;AACrB,CAAC,CAAC;AAEF;;GAEG;AACH,MAAM,0BAA0B,GAAG,CAAC,KAAU,EAAU,EAAE;IACxD,IAAI,CAAC,KAAK,CAAC,WAAW,IAAI,KAAK,CAAC,WAAW,KAAK,CAAC;QAAE,OAAO,CAAC,CAAC;IAE5D,MAAM,UAAU,GAAG,KAAK,CAAC,WAAW,IAAI,CAAC,CAAC;IAC1C,MAAM,UAAU,GAAG,KAAK,CAAC,WAAW,GAAG,CAAC,KAAK,CAAC,eAAe,IAAI,CAAC,CAAC,CAAC;IAEpE,OAAO,UAAU,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,UAAU,GAAG,UAAU,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;AAC9D,CAAC,CAAC;AAEF;;GAEG;AACH,MAAM,CAAC,MAAM,gBAAgB,GAAG,KAAK,IAKlC,EAAE;IACH,IAAI,CAAC;QACH,MAAM,MAAM,GAAG,MAAM,mBAAmB,CAAC,YAAY,EAAE,CAAC;QAExD,MAAM,OAAO,GAAG,CAAC,GAAG,IAAI,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;QAChF,MAAM,UAAU,GAAG,CAAC,GAAG,IAAI,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,KAAK,CAAC,UAAU,CAAC,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;QACvF,MAAM,QAAQ,GAAG,CAAC,GAAG,IAAI,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;QAClF,MAAM,MAAM,GAAG,CAAC,GAAG,IAAI,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;QAE9E,OAAO;YACL,OAAO,EAAE,OAAO,CAAC,IAAI,EAAE;YACvB,UAAU,EAAE,UAAU,CAAC,IAAI,EAAE;YAC7B,QAAQ,EAAE,QAAQ,CAAC,IAAI,EAAE;YACzB,MAAM,EAAE,MAAM,CAAC,IAAI,EAAE;SACtB,CAAC;IACJ,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,OAAO,CAAC,KAAK,CAAC,+BAA+B,EAAE,KAAK,CAAC,CAAC;QACtD,OAAO;YACL,OAAO,EAAE,EAAE;YACX,UAAU,EAAE,EAAE;YACd,QAAQ,EAAE,EAAE;YACZ,MAAM,EAAE,EAAE;SACX,CAAC;IACJ,CAAC;AACH,CAAC,CAAC;AAEF;;GAEG;AACH,MAAM,CAAC,MAAM,kBAAkB,GAAG,KAAK,IAMpC,EAAE;IACH,IAAI,CAAC;QACH,MAAM,MAAM,GAAG,MAAM,mBAAmB,CAAC,YAAY,EAAE,CAAC;QAExD,MAAM,GAAG,GAAG,IAAI,IAAI,EAAE,CAAC;QACvB,MAAM,KAAK,GAAG,IAAI,IAAI,CAAC,GAAG,CAAC,WAAW,EAAE,EAAE,GAAG,CAAC,QAAQ,EAAE,EAAE,GAAG,CAAC,OAAO,EAAE,CAAC,CAAC;QACzE,MAAM,OAAO,GAAG,IAAI,IAAI,CAAC,GAAG,CAAC,OAAO,EAAE,GAAG,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC,CAAC;QAClE,MAAM,QAAQ,GAAG,IAAI,IAAI,CAAC,GAAG,CAAC,WAAW,EAAE,EAAE,GAAG,CAAC,QAAQ,EAAE,GAAG,CAAC,EAAE,GAAG,CAAC,OAAO,EAAE,CAAC,CAAC;QAEhF,MAAM,WAAW,GAAG,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,IAAI,IAAI,CAAC,CAAC,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,KAAK,CAAC,CAAC,MAAM,CAAC;QACjF,MAAM,UAAU,GAAG,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,IAAI,IAAI,CAAC,CAAC,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,OAAO,CAAC,CAAC,MAAM,CAAC;QAClF,MAAM,WAAW,GAAG,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,IAAI,IAAI,CAAC,CAAC,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,QAAQ,CAAC,CAAC,MAAM,CAAC;QAEpF,MAAM,aAAa,GACjB,MAAM,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,IAAI,IAAI,CAAC,CAAC,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,OAAO,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC;QAE9F,OAAO;YACL,WAAW,EAAE,MAAM,CAAC,MAAM;YAC1B,WAAW;YACX,UAAU;YACV,WAAW;YACX,aAAa,EAAE,aAAa,CAAC,CAAC,CAAC,IAAI,IAAI,CAAC,aAAa,CAAC,CAAC,WAAW,EAAE,CAAC,CAAC,CAAC,IAAI;SAC5E,CAAC;IACJ,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,OAAO,CAAC,KAAK,CAAC,iCAAiC,EAAE,KAAK,CAAC,CAAC;QACxD,OAAO;YACL,WAAW,EAAE,CAAC;YACd,WAAW,EAAE,CAAC;YACd,UAAU,EAAE,CAAC;YACb,WAAW,EAAE,CAAC;YACd,aAAa,EAAE,IAAI;SACpB,CAAC;IACJ,CAAC;AACH,CAAC,CAAC"}