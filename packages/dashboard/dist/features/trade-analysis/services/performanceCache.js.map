{"version": 3, "file": "performanceCache.js", "sourceRoot": "", "sources": ["../../../../src/features/trade-analysis/services/performanceCache.ts"], "names": [], "mappings": "AAAA;;;;;GAKG;AAgBH;;GAEG;AACH,MAAM,uBAAuB;IAA7B;QACU,UAAK,GAAG,IAAI,GAAG,EAA2B,CAAC;QAC3C,WAAM,GAAgB;YAC5B,MAAM,EAAE,CAAC,GAAG,EAAE,GAAG,IAAI,EAAE,YAAY;YACnC,OAAO,EAAE,GAAG,EAAE,oBAAoB;SACnC,CAAC;IAqNJ,CAAC;IAnNC;;OAEG;IACK,gBAAgB,CAAC,MAA2B,EAAE,SAAiB,EAAE,MAAY;QACnF,qEAAqE;QACrE,MAAM,QAAQ,GAAG,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC,IAAI,EAAE,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;QAC9D,MAAM,SAAS,GAAG,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;QACvD,OAAO,GAAG,SAAS,IAAI,QAAQ,IAAI,SAAS,EAAE,CAAC;IACjD,CAAC;IAED;;OAEG;IACK,SAAS,CAAI,GAAW;QAC9B,MAAM,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;QAElC,IAAI,CAAC,KAAK,EAAE,CAAC;YACX,OAAO,IAAI,CAAC;QACd,CAAC;QAED,mCAAmC;QACnC,IAAI,IAAI,CAAC,GAAG,EAAE,GAAG,KAAK,CAAC,SAAS,GAAG,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE,CAAC;YACtD,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC;YACvB,OAAO,IAAI,CAAC;QACd,CAAC;QAED,OAAO,KAAK,CAAC,IAAS,CAAC;IACzB,CAAC;IAED;;OAEG;IACK,SAAS,CAAI,GAAW,EAAE,IAAO;QACvC,wCAAwC;QACxC,IAAI,IAAI,CAAC,KAAK,CAAC,IAAI,IAAI,IAAI,CAAC,MAAM,CAAC,OAAO,EAAE,CAAC;YAC3C,MAAM,SAAS,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC,KAAK,CAAC;YACjD,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC;QAC/B,CAAC;QAED,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,GAAG,EAAE;YAClB,IAAI;YACJ,SAAS,EAAE,IAAI,CAAC,GAAG,EAAE;YACrB,IAAI,EAAE,GAAG;SACV,CAAC,CAAC;IACL,CAAC;IAED;;OAEG;IACH,UAAU;QACR,IAAI,CAAC,KAAK,CAAC,KAAK,EAAE,CAAC;IACrB,CAAC;IAED;;OAEG;IACH,iBAAiB;QACf,MAAM,GAAG,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QACvB,KAAK,MAAM,CAAC,GAAG,EAAE,KAAK,CAAC,IAAI,IAAI,CAAC,KAAK,CAAC,OAAO,EAAE,EAAE,CAAC;YAChD,IAAI,GAAG,GAAG,KAAK,CAAC,SAAS,GAAG,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE,CAAC;gBAC/C,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC;YACzB,CAAC;QACH,CAAC;IACH,CAAC;IAED;;OAEG;IACH,aAAa;QAMX,IAAI,eAAe,GAAkB,IAAI,CAAC;QAE1C,KAAK,MAAM,KAAK,IAAI,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE,EAAE,CAAC;YACxC,IAAI,eAAe,KAAK,IAAI,IAAI,KAAK,CAAC,SAAS,GAAG,eAAe,EAAE,CAAC;gBAClE,eAAe,GAAG,KAAK,CAAC,SAAS,CAAC;YACpC,CAAC;QACH,CAAC;QAED,OAAO;YACL,IAAI,EAAE,IAAI,CAAC,KAAK,CAAC,IAAI;YACrB,OAAO,EAAE,IAAI,CAAC,MAAM,CAAC,OAAO;YAC5B,OAAO,EAAE,CAAC,EAAE,oCAAoC;YAChD,WAAW,EAAE,eAAe;SAC7B,CAAC;IACJ,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,2BAA2B,CAC/B,MAA2B,EAC3B,UAA+D;QAE/D,MAAM,QAAQ,GAAG,IAAI,CAAC,gBAAgB,CAAC,MAAM,EAAE,qBAAqB,CAAC,CAAC;QAEtE,8BAA8B;QAC9B,MAAM,MAAM,GAAG,IAAI,CAAC,SAAS,CAAqB,QAAQ,CAAC,CAAC;QAC5D,IAAI,MAAM,EAAE,CAAC;YACX,OAAO,CAAC,GAAG,CAAC,6CAA6C,CAAC,CAAC;YAC3D,OAAO,MAAM,CAAC;QAChB,CAAC;QAED,iCAAiC;QACjC,OAAO,CAAC,GAAG,CAAC,uCAAuC,CAAC,CAAC;QACrD,MAAM,MAAM,GAAG,UAAU,CAAC,MAAM,CAAC,CAAC;QAClC,IAAI,CAAC,SAAS,CAAC,QAAQ,EAAE,MAAM,CAAC,CAAC;QAEjC,OAAO,MAAM,CAAC;IAChB,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,4BAA4B,CAChC,MAA2B,EAC3B,QAAgB,EAChB,UAAiF;QAEjF,MAAM,QAAQ,GAAG,IAAI,CAAC,gBAAgB,CAAC,MAAM,EAAE,sBAAsB,EAAE,EAAE,QAAQ,EAAE,CAAC,CAAC;QAErF,8BAA8B;QAC9B,MAAM,MAAM,GAAG,IAAI,CAAC,SAAS,CAAwB,QAAQ,CAAC,CAAC;QAC/D,IAAI,MAAM,EAAE,CAAC;YACX,OAAO,CAAC,GAAG,CAAC,4BAA4B,QAAQ,wBAAwB,CAAC,CAAC;YAC1E,OAAO,MAAM,CAAC;QAChB,CAAC;QAED,iCAAiC;QACjC,OAAO,CAAC,GAAG,CAAC,2CAA2C,QAAQ,KAAK,CAAC,CAAC;QACtE,MAAM,MAAM,GAAG,UAAU,CAAC,MAAM,EAAE,QAAe,CAAC,CAAC;QACnD,IAAI,CAAC,SAAS,CAAC,QAAQ,EAAE,MAAM,CAAC,CAAC;QAEjC,OAAO,MAAM,CAAC;IAChB,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,wBAAwB,CAC5B,MAA2B,EAC3B,QAAgB,EAChB,UAA6E;QAE7E,MAAM,QAAQ,GAAG,IAAI,CAAC,gBAAgB,CAAC,MAAM,EAAE,kBAAkB,EAAE,EAAE,QAAQ,EAAE,CAAC,CAAC;QAEjF,8BAA8B;QAC9B,MAAM,MAAM,GAAG,IAAI,CAAC,SAAS,CAAoB,QAAQ,CAAC,CAAC;QAC3D,IAAI,MAAM,EAAE,CAAC;YACX,OAAO,CAAC,GAAG,CAAC,uBAAuB,QAAQ,wBAAwB,CAAC,CAAC;YACrE,OAAO,MAAM,CAAC;QAChB,CAAC;QAED,iCAAiC;QACjC,OAAO,CAAC,GAAG,CAAC,sCAAsC,QAAQ,KAAK,CAAC,CAAC;QACjE,MAAM,MAAM,GAAG,UAAU,CAAC,MAAM,EAAE,QAAe,CAAC,CAAC;QACnD,IAAI,CAAC,SAAS,CAAC,QAAQ,EAAE,MAAM,CAAC,CAAC;QAEjC,OAAO,MAAM,CAAC;IAChB,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,oBAAoB,CACxB,MAA2B,EAC3B,UAA0D;QAE1D,MAAM,QAAQ,GAAG,IAAI,CAAC,gBAAgB,CAAC,MAAM,EAAE,cAAc,CAAC,CAAC;QAE/D,8BAA8B;QAC9B,MAAM,MAAM,GAAG,IAAI,CAAC,SAAS,CAAgB,QAAQ,CAAC,CAAC;QACvD,IAAI,MAAM,EAAE,CAAC;YACX,OAAO,CAAC,GAAG,CAAC,sCAAsC,CAAC,CAAC;YACpD,OAAO,MAAM,CAAC;QAChB,CAAC;QAED,iCAAiC;QACjC,OAAO,CAAC,GAAG,CAAC,gCAAgC,CAAC,CAAC;QAC9C,MAAM,MAAM,GAAG,UAAU,CAAC,MAAM,CAAC,CAAC;QAClC,IAAI,CAAC,SAAS,CAAC,QAAQ,EAAE,MAAM,CAAC,CAAC;QAEjC,OAAO,MAAM,CAAC;IAChB,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,yBAAyB,CAC7B,MAA2B,EAC3B,UAA8D;QAE9D,MAAM,QAAQ,GAAG,IAAI,CAAC,gBAAgB,CAAC,MAAM,EAAE,mBAAmB,CAAC,CAAC;QAEpE,8BAA8B;QAC9B,MAAM,MAAM,GAAG,IAAI,CAAC,SAAS,CAAoB,QAAQ,CAAC,CAAC;QAC3D,IAAI,MAAM,EAAE,CAAC;YACX,OAAO,CAAC,GAAG,CAAC,2CAA2C,CAAC,CAAC;YACzD,OAAO,MAAM,CAAC;QAChB,CAAC;QAED,iCAAiC;QACjC,OAAO,CAAC,GAAG,CAAC,qCAAqC,CAAC,CAAC;QACnD,MAAM,MAAM,GAAG,UAAU,CAAC,MAAM,CAAC,CAAC;QAClC,IAAI,CAAC,SAAS,CAAC,QAAQ,EAAE,MAAM,CAAC,CAAC;QAEjC,OAAO,MAAM,CAAC;IAChB,CAAC;CACF;AAED,4BAA4B;AAC5B,MAAM,CAAC,MAAM,gBAAgB,GAAG,IAAI,uBAAuB,EAAE,CAAC;AAE9D;;;;;GAKG;AACH,MAAM,CAAC,MAAM,uBAAuB,GAAG,KAAK,EAC1C,MAA2B,EAC3B,WAMC,EAaA,EAAE;IACH,OAAO,CAAC,GAAG,CAAC,4CAA4C,CAAC,CAAC;IAE1D,gEAAgE;IAChE,MAAM,CACJ,OAAO,EACP,iBAAiB,EACjB,mBAAmB,EACnB,kBAAkB,EAClB,gBAAgB,EAChB,oBAAoB,EACpB,oBAAoB,EACpB,oBAAoB,EACpB,kBAAkB,EAClB,WAAW,EACX,gBAAgB,EACjB,GAAG,MAAM,OAAO,CAAC,GAAG,CAAC;QACpB,gBAAgB,CAAC,2BAA2B,CAAC,MAAM,EAAE,WAAW,CAAC,kBAAkB,CAAC;QACpF,gBAAgB,CAAC,4BAA4B,CAAC,MAAM,EAAE,QAAQ,EAAE,WAAW,CAAC,mBAAmB,CAAC;QAChG,gBAAgB,CAAC,4BAA4B,CAAC,MAAM,EAAE,YAAY,EAAE,WAAW,CAAC,mBAAmB,CAAC;QACpG,gBAAgB,CAAC,4BAA4B,CAAC,MAAM,EAAE,SAAS,EAAE,WAAW,CAAC,mBAAmB,CAAC;QACjG,gBAAgB,CAAC,4BAA4B,CAAC,MAAM,EAAE,OAAO,EAAE,WAAW,CAAC,mBAAmB,CAAC;QAC/F,gBAAgB,CAAC,4BAA4B,CAAC,MAAM,EAAE,WAAW,EAAE,WAAW,CAAC,mBAAmB,CAAC;QACnG,gBAAgB,CAAC,wBAAwB,CAAC,MAAM,EAAE,WAAW,EAAE,WAAW,CAAC,eAAe,CAAC;QAC3F,gBAAgB,CAAC,wBAAwB,CAAC,MAAM,EAAE,WAAW,EAAE,WAAW,CAAC,eAAe,CAAC;QAC3F,gBAAgB,CAAC,wBAAwB,CAAC,MAAM,EAAE,SAAS,EAAE,WAAW,CAAC,eAAe,CAAC;QACzF,gBAAgB,CAAC,oBAAoB,CAAC,MAAM,EAAE,WAAW,CAAC,WAAW,CAAC;QACtE,gBAAgB,CAAC,yBAAyB,CAAC,MAAM,EAAE,WAAW,CAAC,gBAAgB,CAAC;KACjF,CAAC,CAAC;IAEH,OAAO,CAAC,GAAG,CAAC,yCAAyC,CAAC,CAAC;IAEvD,OAAO;QACL,OAAO;QACP,iBAAiB;QACjB,mBAAmB;QACnB,kBAAkB;QAClB,gBAAgB;QAChB,oBAAoB;QACpB,oBAAoB;QACpB,oBAAoB;QACpB,kBAAkB;QAClB,WAAW;QACX,gBAAgB;KACjB,CAAC;AACJ,CAAC,CAAC;AAEF;;GAEG;AACH,MAAM,CAAC,MAAM,kBAAkB,GAAG;IAChC;;OAEG;IACH,KAAK,CAAC,WAAW,CAAI,IAAY,EAAE,EAAwB;QACzD,MAAM,KAAK,GAAG,WAAW,CAAC,GAAG,EAAE,CAAC;QAChC,MAAM,MAAM,GAAG,MAAM,EAAE,EAAE,CAAC;QAC1B,MAAM,GAAG,GAAG,WAAW,CAAC,GAAG,EAAE,CAAC;QAC9B,OAAO,CAAC,GAAG,CAAC,MAAM,IAAI,SAAS,CAAC,GAAG,GAAG,KAAK,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC;QAC7D,OAAO,MAAM,CAAC;IAChB,CAAC;IAED;;OAEG;IACH,cAAc,CAAC,OAAe;QAC5B,IAAI,QAAQ,IAAI,WAAW,EAAE,CAAC;YAC5B,MAAM,MAAM,GAAI,WAAmB,CAAC,MAAM,CAAC;YAC3C,OAAO,CAAC,GAAG,CAAC,oBAAoB,OAAO,IAAI,EAAE;gBAC3C,IAAI,EAAE,GAAG,CAAC,MAAM,CAAC,cAAc,GAAG,IAAI,GAAG,IAAI,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,KAAK;gBAC9D,KAAK,EAAE,GAAG,CAAC,MAAM,CAAC,eAAe,GAAG,IAAI,GAAG,IAAI,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,KAAK;gBAChE,KAAK,EAAE,GAAG,CAAC,MAAM,CAAC,eAAe,GAAG,IAAI,GAAG,IAAI,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,KAAK;aACjE,CAAC,CAAC;QACL,CAAC;IACH,CAAC;IAED;;OAEG;IACH,aAAa;QACX,OAAO,gBAAgB,CAAC,aAAa,EAAE,CAAC;IAC1C,CAAC;CACF,CAAC"}