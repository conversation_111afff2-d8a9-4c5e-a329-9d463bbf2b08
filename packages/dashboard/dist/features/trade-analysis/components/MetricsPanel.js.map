{"version": 3, "file": "MetricsPanel.js", "sourceRoot": "", "sources": ["../../../../src/features/trade-analysis/components/MetricsPanel.tsx"], "names": [], "mappings": ";AAOA,OAAO,MAAM,MAAM,mBAAmB,CAAC;AAQvC,MAAM,WAAW,GAAG,MAAM,CAAC,GAAG,CAAA;;;SAGrB,CAAC,EAAE,KAAK,EAAE,EAAE,EAAE,CAAC,KAAK,CAAC,OAAO,CAAC,EAAE;CACvC,CAAC;AAEF,MAAM,UAAU,GAAG,MAAM,CAAC,GAAG,CAAA;sBACP,CAAC,EAAE,KAAK,EAAE,EAAE,EAAE,CAAC,KAAK,CAAC,MAAM,CAAC,cAAc;mBAC7C,CAAC,EAAE,KAAK,EAAE,EAAE,EAAE,CAAC,KAAK,CAAC,YAAY,CAAC,EAAE;aAC1C,CAAC,EAAE,KAAK,EAAE,EAAE,EAAE,CAAC,KAAK,CAAC,OAAO,CAAC,EAAE;CAC3C,CAAC;AAEF,MAAM,WAAW,GAAG,MAAM,CAAC,GAAG,CAAA;eACf,CAAC,EAAE,KAAK,EAAE,EAAE,EAAE,CAAC,KAAK,CAAC,SAAS,CAAC,EAAE;WACrC,CAAC,EAAE,KAAK,EAAE,EAAE,EAAE,CAAC,KAAK,CAAC,MAAM,CAAC,aAAa;mBACjC,CAAC,EAAE,KAAK,EAAE,EAAE,EAAE,CAAC,KAAK,CAAC,OAAO,CAAC,EAAE;CACjD,CAAC;AAEF,MAAM,WAAW,GAAG,MAAM,CAAC,GAAG,CAAA;eACf,CAAC,EAAE,KAAK,EAAE,EAAE,EAAE,CAAC,KAAK,CAAC,SAAS,CAAC,EAAE;;WAErC,CAAC,EAAE,KAAK,EAAE,EAAE,EAAE,CAAC,KAAK,CAAC,MAAM,CAAC,WAAW;CACjD,CAAC;AAEF,MAAM,kBAAkB,GAAG,MAAM,CAAC,GAAG,CAAA;;;;;WAK1B,CAAC,EAAE,KAAK,EAAE,EAAE,EAAE,CAAC,KAAK,CAAC,MAAM,CAAC,aAAa;CACnD,CAAC;AAEF;;;;GAIG;AACH,MAAM,YAAY,GAAgC,CAAC,EAAE,OAAO,EAAE,SAAS,EAAE,EAAE,EAAE;IAC3E,IAAI,SAAS,EAAE,CAAC;QACd,OAAO,KAAC,kBAAkB,qCAAwC,CAAC;IACrE,CAAC;IAED,IAAI,CAAC,OAAO,EAAE,CAAC;QACb,OAAO,KAAC,kBAAkB,uCAA0C,CAAC;IACvE,CAAC;IAED,MAAM,cAAc,GAAG,CAAC,KAAa,EAAU,EAAE;QAC/C,OAAO,KAAK,IAAI,CAAC;YACf,CAAC,CAAC,IAAI,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,cAAc,CAAC,OAAO,EAAE;gBAC1C,qBAAqB,EAAE,CAAC;gBACxB,qBAAqB,EAAE,CAAC;aACzB,CAAC,EAAE;YACN,CAAC,CAAC,KAAK,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,cAAc,CAAC,OAAO,EAAE;gBAC3C,qBAAqB,EAAE,CAAC;gBACxB,qBAAqB,EAAE,CAAC;aACzB,CAAC,EAAE,CAAC;IACX,CAAC,CAAC;IAEF,OAAO,CACL,MAAC,WAAW,eACV,MAAC,UAAU,eACT,KAAC,WAAW,2BAAuB,EACnC,MAAC,WAAW,eAAE,OAAO,CAAC,OAAO,SAAgB,IAClC,EAEb,MAAC,UAAU,eACT,KAAC,WAAW,gCAA4B,EACxC,KAAC,WAAW,cAAE,OAAO,CAAC,YAAY,GAAe,IACtC,EAEb,MAAC,UAAU,eACT,KAAC,WAAW,8BAA0B,EACtC,KAAC,WAAW,cAAE,cAAc,CAAC,OAAO,CAAC,UAAU,CAAC,GAAe,IACpD,EAEb,MAAC,UAAU,eACT,KAAC,WAAW,+BAA2B,EACvC,KAAC,WAAW,cAAE,cAAc,CAAC,OAAO,CAAC,WAAW,CAAC,GAAe,IACrD,EAEb,MAAC,UAAU,eACT,KAAC,WAAW,8BAA0B,EACtC,KAAC,WAAW,cAAE,cAAc,CAAC,OAAO,CAAC,UAAU,CAAC,GAAe,IACpD,EAEb,MAAC,UAAU,eACT,KAAC,WAAW,+BAA2B,EACvC,KAAC,WAAW,cAAE,cAAc,CAAC,OAAO,CAAC,WAAW,CAAC,GAAe,IACrD,IACD,CACf,CAAC;AACJ,CAAC,CAAC;AAEF,OAAO,EAAE,YAAY,EAAE,CAAC;AACxB,eAAe,YAAY,CAAC"}