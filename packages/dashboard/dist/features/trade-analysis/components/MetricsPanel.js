import { jsx as _jsx, jsxs as _jsxs } from "react/jsx-runtime";
import styled from 'styled-components';
const MetricsGrid = styled.div `
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: ${({ theme }) => theme.spacing.md};
`;
const MetricCard = styled.div `
  background-color: ${({ theme }) => theme.colors.cardBackground};
  border-radius: ${({ theme }) => theme.borderRadius.sm};
  padding: ${({ theme }) => theme.spacing.md};
`;
const MetricTitle = styled.div `
  font-size: ${({ theme }) => theme.fontSizes.sm};
  color: ${({ theme }) => theme.colors.textSecondary};
  margin-bottom: ${({ theme }) => theme.spacing.xs};
`;
const MetricValue = styled.div `
  font-size: ${({ theme }) => theme.fontSizes.lg};
  font-weight: 600;
  color: ${({ theme }) => theme.colors.textPrimary};
`;
const LoadingPlaceholder = styled.div `
  height: 200px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: ${({ theme }) => theme.colors.textSecondary};
`;
/**
 * MetricsPanel Component
 *
 * Displays a grid of key trading performance metrics.
 */
const MetricsPanel = ({ metrics, isLoading }) => {
    if (isLoading) {
        return _jsx(LoadingPlaceholder, { children: "Loading metrics..." });
    }
    if (!metrics) {
        return _jsx(LoadingPlaceholder, { children: "No metrics available" });
    }
    const formatCurrency = (value) => {
        return value >= 0
            ? `$${Math.abs(value).toLocaleString('en-US', {
                minimumFractionDigits: 2,
                maximumFractionDigits: 2,
            })}`
            : `-$${Math.abs(value).toLocaleString('en-US', {
                minimumFractionDigits: 2,
                maximumFractionDigits: 2,
            })}`;
    };
    return (_jsxs(MetricsGrid, { children: [_jsxs(MetricCard, { children: [_jsx(MetricTitle, { children: "Win Rate" }), _jsxs(MetricValue, { children: [metrics.winRate, "%"] })] }), _jsxs(MetricCard, { children: [_jsx(MetricTitle, { children: "Profit Factor" }), _jsx(MetricValue, { children: metrics.profitFactor })] }), _jsxs(MetricCard, { children: [_jsx(MetricTitle, { children: "Average Win" }), _jsx(MetricValue, { children: formatCurrency(metrics.averageWin) })] }), _jsxs(MetricCard, { children: [_jsx(MetricTitle, { children: "Average Loss" }), _jsx(MetricValue, { children: formatCurrency(metrics.averageLoss) })] }), _jsxs(MetricCard, { children: [_jsx(MetricTitle, { children: "Largest Win" }), _jsx(MetricValue, { children: formatCurrency(metrics.largestWin) })] }), _jsxs(MetricCard, { children: [_jsx(MetricTitle, { children: "Largest Loss" }), _jsx(MetricValue, { children: formatCurrency(metrics.largestLoss) })] })] }));
};
export { MetricsPanel };
export default MetricsPanel;
//# sourceMappingURL=MetricsPanel.js.map