{"version": 3, "file": "useAnalysisNavigation.js", "sourceRoot": "", "sources": ["../../../../src/features/trade-analysis/components/useAnalysisNavigation.ts"], "names": [], "mappings": "AAAA;;;;;;;;;;;;GAYG;AAEH,OAAO,EAAE,QAAQ,EAAE,WAAW,EAAE,SAAS,EAAE,MAAM,OAAO,CAAC;AA2BzD;;GAEG;AACH,MAAM,cAAc,GAAkB,CAAC,SAAS,EAAE,QAAQ,EAAE,QAAQ,EAAE,SAAS,CAAC,CAAC;AAEjF;;GAEG;AACH,MAAM,mBAAmB,GAAG,4CAA4C,CAAC;AAEzE;;GAEG;AACH,MAAM,kBAAkB,GAAG,CAAC,UAAkB,EAAE,UAAuB,EAAe,EAAE;IACtF,IAAI,CAAC;QACH,MAAM,MAAM,GAAG,YAAY,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC;QAChD,IAAI,MAAM,IAAI,cAAc,CAAC,QAAQ,CAAC,MAAqB,CAAC,EAAE,CAAC;YAC7D,OAAO,MAAqB,CAAC;QAC/B,CAAC;IACH,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,OAAO,CAAC,IAAI,CAAC,gDAAgD,EAAE,KAAK,CAAC,CAAC;IACxE,CAAC;IACD,OAAO,UAAU,CAAC;AACpB,CAAC,CAAC;AAEF;;GAEG;AACH,MAAM,gBAAgB,GAAG,CAAC,UAAkB,EAAE,GAAgB,EAAQ,EAAE;IACtE,IAAI,CAAC;QACH,YAAY,CAAC,OAAO,CAAC,UAAU,EAAE,GAAG,CAAC,CAAC;IACxC,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,OAAO,CAAC,IAAI,CAAC,8CAA8C,EAAE,KAAK,CAAC,CAAC;IACtE,CAAC;AACH,CAAC,CAAC;AAEF;;;;GAIG;AACH,MAAM,CAAC,MAAM,qBAAqB,GAAG,CAAC,EACpC,UAAU,GAAG,SAAS,EACtB,UAAU,GAAG,mBAAmB,MACF,EAAE,EAA+B,EAAE;IAEjE,gDAAgD;IAChD,MAAM,CAAC,SAAS,EAAE,iBAAiB,CAAC,GAAG,QAAQ,CAAc,GAAG,EAAE,CAChE,kBAAkB,CAAC,UAAU,EAAE,UAAU,CAAC,CAC3C,CAAC;IAEF;;OAEG;IACH,MAAM,YAAY,GAAG,WAAW,CAAC,CAAC,GAAgB,EAAE,EAAE;QACpD,IAAI,cAAc,CAAC,QAAQ,CAAC,GAAG,CAAC,EAAE,CAAC;YACjC,iBAAiB,CAAC,GAAG,CAAC,CAAC;YACvB,gBAAgB,CAAC,UAAU,EAAE,GAAG,CAAC,CAAC;QACpC,CAAC;IACH,CAAC,EAAE,CAAC,UAAU,CAAC,CAAC,CAAC;IAEjB;;OAEG;IACH,MAAM,OAAO,GAAG,WAAW,CAAC,GAAG,EAAE;QAC/B,MAAM,YAAY,GAAG,cAAc,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC;QACvD,MAAM,SAAS,GAAG,CAAC,YAAY,GAAG,CAAC,CAAC,GAAG,cAAc,CAAC,MAAM,CAAC;QAC7D,YAAY,CAAC,cAAc,CAAC,SAAS,CAAC,CAAC,CAAC;IAC1C,CAAC,EAAE,CAAC,SAAS,EAAE,YAAY,CAAC,CAAC,CAAC;IAE9B;;OAEG;IACH,MAAM,WAAW,GAAG,WAAW,CAAC,GAAG,EAAE;QACnC,MAAM,YAAY,GAAG,cAAc,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC;QACvD,MAAM,aAAa,GAAG,YAAY,KAAK,CAAC,CAAC,CAAC,CAAC,cAAc,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,YAAY,GAAG,CAAC,CAAC;QACxF,YAAY,CAAC,cAAc,CAAC,aAAa,CAAC,CAAC,CAAC;IAC9C,CAAC,EAAE,CAAC,SAAS,EAAE,YAAY,CAAC,CAAC,CAAC;IAE9B;;OAEG;IACH,MAAM,WAAW,GAAG,WAAW,CAAC,CAAC,GAAgB,EAAW,EAAE;QAC5D,OAAO,SAAS,KAAK,GAAG,CAAC;IAC3B,CAAC,EAAE,CAAC,SAAS,CAAC,CAAC,CAAC;IAEhB;;OAEG;IACH,MAAM,WAAW,GAAG,WAAW,CAAC,CAAC,GAAgB,EAAU,EAAE;QAC3D,OAAO,cAAc,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC;IACrC,CAAC,EAAE,EAAE,CAAC,CAAC;IAEP,6BAA6B;IAC7B,SAAS,CAAC,GAAG,EAAE;QACb,MAAM,aAAa,GAAG,CAAC,KAAoB,EAAE,EAAE;YAC7C,qCAAqC;YACrC,IAAI,QAAQ,CAAC,aAAa,EAAE,OAAO,KAAK,OAAO;gBAC3C,QAAQ,CAAC,aAAa,EAAE,OAAO,KAAK,UAAU;gBAC9C,QAAQ,CAAC,aAAa,EAAE,OAAO,KAAK,QAAQ,EAAE,CAAC;gBACjD,OAAO;YACT,CAAC;YAED,kDAAkD;YAClD,IAAI,CAAC,KAAK,CAAC,OAAO,IAAI,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,QAAQ,EAAE,CAAC;gBACxD,QAAQ,KAAK,CAAC,GAAG,EAAE,CAAC;oBAClB,KAAK,WAAW;wBACd,KAAK,CAAC,cAAc,EAAE,CAAC;wBACvB,WAAW,EAAE,CAAC;wBACd,MAAM;oBACR,KAAK,YAAY;wBACf,KAAK,CAAC,cAAc,EAAE,CAAC;wBACvB,OAAO,EAAE,CAAC;wBACV,MAAM;gBACV,CAAC;YACH,CAAC;YAED,+CAA+C;YAC/C,IAAI,KAAK,CAAC,GAAG,IAAI,GAAG,IAAI,KAAK,CAAC,GAAG,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,OAAO,IAAI,CAAC,KAAK,CAAC,OAAO,EAAE,CAAC;gBAC7E,MAAM,QAAQ,GAAG,QAAQ,CAAC,KAAK,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;gBACzC,IAAI,QAAQ,GAAG,cAAc,CAAC,MAAM,EAAE,CAAC;oBACrC,KAAK,CAAC,cAAc,EAAE,CAAC;oBACvB,YAAY,CAAC,cAAc,CAAC,QAAQ,CAAC,CAAC,CAAC;gBACzC,CAAC;YACH,CAAC;QACH,CAAC,CAAC;QAEF,MAAM,CAAC,gBAAgB,CAAC,SAAS,EAAE,aAAa,CAAC,CAAC;QAClD,OAAO,GAAG,EAAE,CAAC,MAAM,CAAC,mBAAmB,CAAC,SAAS,EAAE,aAAa,CAAC,CAAC;IACpE,CAAC,EAAE,CAAC,OAAO,EAAE,WAAW,EAAE,YAAY,CAAC,CAAC,CAAC;IAEzC,OAAO;QACL,SAAS;QACT,YAAY;QACZ,OAAO;QACP,WAAW;QACX,WAAW;QACX,WAAW;QACX,aAAa,EAAE,cAAc;KAC9B,CAAC;AACJ,CAAC,CAAC;AAEF,eAAe,qBAAqB,CAAC"}