import { jsx as _jsx, jsxs as _jsxs } from "react/jsx-runtime";
import { TradeAnalysisFilter } from './TradeAnalysisFilter';
import { TradeAnalysisTable } from './TradeAnalysisTable';
import { TradeAnalysisSummary } from './TradeAnalysisSummary';
import { TradeAnalysisCharts } from './TradeAnalysisCharts';
/**
 * Summary Tab Content
 */
const SummaryTabContent = ({ data, isLoading }) => {
    if (!data.tradeSummary) {
        return (_jsx("div", { style: {
                padding: '48px',
                textAlign: 'center',
                color: '#9ca3af'
            }, children: "\uD83D\uDCCA No summary data available" }));
    }
    return (_jsx(TradeAnalysisSummary, { summary: data.tradeSummary, isLoading: isLoading }));
};
/**
 * Charts Tab Content
 */
const ChartsTabContent = ({ data, isLoading }) => {
    return (_jsx(TradeAnalysisCharts, { equityCurveData: data.equityCurveData, distributionData: data.distributionData, isLoading: isLoading }));
};
/**
 * Trades Tab Content
 */
const TradesTabContent = ({ data, isLoading, handlers }) => {
    return (_jsx(TradeAnalysisTable, { trades: data.trades, sort: data.sort, onSort: handlers.setSort, page: data.page, onPageChange: handlers.setPage, pageSize: data.pageSize, onPageSizeChange: handlers.setPageSize, totalPages: data.totalPages, isLoading: isLoading }));
};
/**
 * Filters Tab Content
 */
const FiltersTabContent = ({ data, isLoading, handlers }) => {
    return (_jsx(TradeAnalysisFilter, { filter: data.filter, onSetFilter: handlers.setFilter, onClearFilters: handlers.clearFilters, isLoading: isLoading }));
};
/**
 * Tab configuration with components and metadata
 */
export const ANALYSIS_TAB_CONFIG = {
    summary: {
        id: 'summary',
        title: 'Performance Summary',
        description: 'Overview of trading performance and key metrics',
        icon: '📊',
        component: SummaryTabContent,
        showInMobile: true,
        requiresData: true,
    },
    charts: {
        id: 'charts',
        title: 'Visual Analysis',
        description: 'Charts and visual representations of trading data',
        icon: '📈',
        component: ChartsTabContent,
        showInMobile: true,
        requiresData: true,
    },
    trades: {
        id: 'trades',
        title: 'Trade Details',
        description: 'Detailed list and analysis of individual trades',
        icon: '📋',
        component: TradesTabContent,
        showInMobile: true,
        requiresData: true,
    },
    filters: {
        id: 'filters',
        title: 'Filter & Search',
        description: 'Advanced filtering and search options',
        icon: '🔍',
        component: FiltersTabContent,
        showInMobile: false,
        requiresData: false,
    },
};
/**
 * Get tab configuration by ID
 */
export const getTabConfig = (tabId) => {
    return ANALYSIS_TAB_CONFIG[tabId];
};
/**
 * Get all tab configurations
 */
export const getAllTabConfigs = () => {
    return Object.values(ANALYSIS_TAB_CONFIG);
};
/**
 * Get mobile-friendly tabs
 */
export const getMobileTabConfigs = () => {
    return getAllTabConfigs().filter(config => config.showInMobile);
};
/**
 * Get tabs that require data
 */
export const getDataRequiredTabConfigs = () => {
    return getAllTabConfigs().filter(config => config.requiresData);
};
/**
 * Tab Content Renderer Component
 */
export const AnalysisTabContentRenderer = (props) => {
    const { activeTab } = props;
    const config = getTabConfig(activeTab);
    if (!config) {
        return (_jsxs("div", { style: {
                padding: '48px',
                textAlign: 'center',
                color: '#ef4444'
            }, children: ["\u274C Unknown tab: ", activeTab] }));
    }
    const TabComponent = config.component;
    return (_jsx("div", { id: `analysis-panel-${activeTab}`, role: "tabpanel", "aria-labelledby": `analysis-tab-${activeTab}`, children: _jsx(TabComponent, { ...props }) }));
};
export default AnalysisTabContentRenderer;
//# sourceMappingURL=analysisTabConfig.js.map