{"version": 3, "file": "analysisTabConfig.js", "sourceRoot": "", "sources": ["../../../../src/features/trade-analysis/components/analysisTabConfig.tsx"], "names": [], "mappings": ";AAgBA,OAAO,EAAE,mBAAmB,EAAE,MAAM,uBAAuB,CAAC;AAC5D,OAAO,EAAE,kBAAkB,EAAE,MAAM,sBAAsB,CAAC;AAC1D,OAAO,EAAE,oBAAoB,EAAE,MAAM,wBAAwB,CAAC;AAC9D,OAAO,EAAE,mBAAmB,EAAE,MAAM,uBAAuB,CAAC;AA2C5D;;GAEG;AACH,MAAM,iBAAiB,GAAsC,CAAC,EAC5D,IAAI,EACJ,SAAS,EACV,EAAE,EAAE;IACH,IAAI,CAAC,IAAI,CAAC,YAAY,EAAE,CAAC;QACvB,OAAO,CACL,cAAK,KAAK,EAAE;gBACV,OAAO,EAAE,MAAM;gBACf,SAAS,EAAE,QAAQ;gBACnB,KAAK,EAAE,SAAS;aACjB,uDAEK,CACP,CAAC;IACJ,CAAC;IAED,OAAO,CACL,KAAC,oBAAoB,IACnB,OAAO,EAAE,IAAI,CAAC,YAAY,EAC1B,SAAS,EAAE,SAAS,GACpB,CACH,CAAC;AACJ,CAAC,CAAC;AAEF;;GAEG;AACH,MAAM,gBAAgB,GAAsC,CAAC,EAC3D,IAAI,EACJ,SAAS,EACV,EAAE,EAAE;IACH,OAAO,CACL,KAAC,mBAAmB,IAClB,eAAe,EAAE,IAAI,CAAC,eAAe,EACrC,gBAAgB,EAAE,IAAI,CAAC,gBAAgB,EACvC,SAAS,EAAE,SAAS,GACpB,CACH,CAAC;AACJ,CAAC,CAAC;AAEF;;GAEG;AACH,MAAM,gBAAgB,GAAsC,CAAC,EAC3D,IAAI,EACJ,SAAS,EACT,QAAQ,EACT,EAAE,EAAE;IACH,OAAO,CACL,KAAC,kBAAkB,IACjB,MAAM,EAAE,IAAI,CAAC,MAAM,EACnB,IAAI,EAAE,IAAI,CAAC,IAAI,EACf,MAAM,EAAE,QAAQ,CAAC,OAAO,EACxB,IAAI,EAAE,IAAI,CAAC,IAAI,EACf,YAAY,EAAE,QAAQ,CAAC,OAAO,EAC9B,QAAQ,EAAE,IAAI,CAAC,QAAQ,EACvB,gBAAgB,EAAE,QAAQ,CAAC,WAAW,EACtC,UAAU,EAAE,IAAI,CAAC,UAAU,EAC3B,SAAS,EAAE,SAAS,GACpB,CACH,CAAC;AACJ,CAAC,CAAC;AAEF;;GAEG;AACH,MAAM,iBAAiB,GAAsC,CAAC,EAC5D,IAAI,EACJ,SAAS,EACT,QAAQ,EACT,EAAE,EAAE;IACH,OAAO,CACL,KAAC,mBAAmB,IAClB,MAAM,EAAE,IAAI,CAAC,MAAM,EACnB,WAAW,EAAE,QAAQ,CAAC,SAAS,EAC/B,cAAc,EAAE,QAAQ,CAAC,YAAY,EACrC,SAAS,EAAE,SAAS,GACpB,CACH,CAAC;AACJ,CAAC,CAAC;AAEF;;GAEG;AACH,MAAM,CAAC,MAAM,mBAAmB,GAA2C;IACzE,OAAO,EAAE;QACP,EAAE,EAAE,SAAS;QACb,KAAK,EAAE,qBAAqB;QAC5B,WAAW,EAAE,iDAAiD;QAC9D,IAAI,EAAE,IAAI;QACV,SAAS,EAAE,iBAAiB;QAC5B,YAAY,EAAE,IAAI;QAClB,YAAY,EAAE,IAAI;KACnB;IACD,MAAM,EAAE;QACN,EAAE,EAAE,QAAQ;QACZ,KAAK,EAAE,iBAAiB;QACxB,WAAW,EAAE,mDAAmD;QAChE,IAAI,EAAE,IAAI;QACV,SAAS,EAAE,gBAAgB;QAC3B,YAAY,EAAE,IAAI;QAClB,YAAY,EAAE,IAAI;KACnB;IACD,MAAM,EAAE;QACN,EAAE,EAAE,QAAQ;QACZ,KAAK,EAAE,eAAe;QACtB,WAAW,EAAE,iDAAiD;QAC9D,IAAI,EAAE,IAAI;QACV,SAAS,EAAE,gBAAgB;QAC3B,YAAY,EAAE,IAAI;QAClB,YAAY,EAAE,IAAI;KACnB;IACD,OAAO,EAAE;QACP,EAAE,EAAE,SAAS;QACb,KAAK,EAAE,iBAAiB;QACxB,WAAW,EAAE,uCAAuC;QACpD,IAAI,EAAE,IAAI;QACV,SAAS,EAAE,iBAAiB;QAC5B,YAAY,EAAE,KAAK;QACnB,YAAY,EAAE,KAAK;KACpB;CACF,CAAC;AAEF;;GAEG;AACH,MAAM,CAAC,MAAM,YAAY,GAAG,CAAC,KAAkB,EAAqB,EAAE;IACpE,OAAO,mBAAmB,CAAC,KAAK,CAAC,CAAC;AACpC,CAAC,CAAC;AAEF;;GAEG;AACH,MAAM,CAAC,MAAM,gBAAgB,GAAG,GAAwB,EAAE;IACxD,OAAO,MAAM,CAAC,MAAM,CAAC,mBAAmB,CAAC,CAAC;AAC5C,CAAC,CAAC;AAEF;;GAEG;AACH,MAAM,CAAC,MAAM,mBAAmB,GAAG,GAAwB,EAAE;IAC3D,OAAO,gBAAgB,EAAE,CAAC,MAAM,CAAC,MAAM,CAAC,EAAE,CAAC,MAAM,CAAC,YAAY,CAAC,CAAC;AAClE,CAAC,CAAC;AAEF;;GAEG;AACH,MAAM,CAAC,MAAM,yBAAyB,GAAG,GAAwB,EAAE;IACjE,OAAO,gBAAgB,EAAE,CAAC,MAAM,CAAC,MAAM,CAAC,EAAE,CAAC,MAAM,CAAC,YAAY,CAAC,CAAC;AAClE,CAAC,CAAC;AAEF;;GAEG;AACH,MAAM,CAAC,MAAM,0BAA0B,GAAsC,CAAC,KAAK,EAAE,EAAE;IACrF,MAAM,EAAE,SAAS,EAAE,GAAG,KAAK,CAAC;IAC5B,MAAM,MAAM,GAAG,YAAY,CAAC,SAAS,CAAC,CAAC;IAEvC,IAAI,CAAC,MAAM,EAAE,CAAC;QACZ,OAAO,CACL,eAAK,KAAK,EAAE;gBACV,OAAO,EAAE,MAAM;gBACf,SAAS,EAAE,QAAQ;gBACnB,KAAK,EAAE,SAAS;aACjB,qCACiB,SAAS,IACrB,CACP,CAAC;IACJ,CAAC;IAED,MAAM,YAAY,GAAG,MAAM,CAAC,SAAS,CAAC;IAEtC,OAAO,CACL,cACE,EAAE,EAAE,kBAAkB,SAAS,EAAE,EACjC,IAAI,EAAC,UAAU,qBACE,gBAAgB,SAAS,EAAE,YAE5C,KAAC,YAAY,OAAK,KAAK,GAAI,GACvB,CACP,CAAC;AACJ,CAAC,CAAC;AAEF,eAAe,0BAA0B,CAAC"}