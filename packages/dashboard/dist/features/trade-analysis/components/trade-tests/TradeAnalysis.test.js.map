{"version": 3, "file": "TradeAnalysis.test.js", "sourceRoot": "", "sources": ["../../../../../src/features/trade-analysis/components/trade-tests/TradeAnalysis.test.js"], "names": [], "mappings": "AAAA,OAAO,EAAE,QAAQ,IAAI,SAAS,EAAE,GAAG,IAAI,IAAI,EAAE,MAAM,mBAAmB,CAAC;AACvE,OAAO,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,wBAAwB,CAAC;AACxD,OAAO,EAAE,aAAa,EAAE,MAAM,kBAAkB,CAAC;AACjD,sBAAsB;AACtB,IAAI,CAAC,IAAI,CAAC,gCAAgC,EAAE,GAAG,EAAE;IAC7C,MAAM,QAAQ,GAAG,IAAI,CAAC,aAAa,CAAC,gCAAgC,CAAC,CAAC;IACtE,OAAO;QACH,GAAG,QAAQ;QACX,WAAW,EAAE,CAAC,EAAE,QAAQ,EAAE,EAAE,EAAE,CAAC,IAAI,CAAC,SAAS,EAAE,EAAE,QAAQ,EAAE,QAAQ,EAAE,CAAC;QACtE,cAAc,EAAE,GAAG,EAAE,CAAC,CAAC;YACnB,IAAI,EAAE;gBACF,IAAI,EAAE,EAAE;aACX;YACD,SAAS,EAAE,KAAK;YAChB,KAAK,EAAE,IAAI;YACX,OAAO,EAAE,IAAI,CAAC,EAAE,EAAE;SACrB,CAAC;KACL,CAAC;AACN,CAAC,CAAC,CAAC;AACH,iBAAiB;AACjB,IAAI,CAAC,IAAI,CAAC,8BAA8B,EAAE,GAAG,EAAE,CAAC,CAAC;IAC7C,gBAAgB,EAAE,GAAG,EAAE,CAAC,CAAC;QACrB,MAAM,EAAE,EAAE;QACV,MAAM,EAAE,EAAE;QACV,IAAI,EAAE,EAAE,KAAK,EAAE,MAAM,EAAE,SAAS,EAAE,MAAM,EAAE;QAC1C,IAAI,EAAE,CAAC;QACP,QAAQ,EAAE,EAAE;QACZ,UAAU,EAAE,CAAC;QACb,YAAY,EAAE;YACV,WAAW,EAAE,CAAC;YACd,aAAa,EAAE,CAAC;YAChB,YAAY,EAAE,CAAC;YACf,eAAe,EAAE,CAAC;YAClB,WAAW,EAAE,CAAC;YACd,SAAS,EAAE,CAAC;YACZ,SAAS,EAAE,CAAC;YACZ,OAAO,EAAE,CAAC;YACV,UAAU,EAAE,CAAC;YACb,WAAW,EAAE,CAAC;YACd,YAAY,EAAE,CAAC;SAClB;QACD,eAAe,EAAE,EAAE;QACnB,gBAAgB,EAAE,EAAE;QACpB,SAAS,EAAE,KAAK;QAChB,KAAK,EAAE,IAAI;QACX,SAAS,EAAE,IAAI,CAAC,EAAE,EAAE;QACpB,YAAY,EAAE,IAAI,CAAC,EAAE,EAAE;QACvB,OAAO,EAAE,IAAI,CAAC,EAAE,EAAE;QAClB,OAAO,EAAE,IAAI,CAAC,EAAE,EAAE;QAClB,WAAW,EAAE,IAAI,CAAC,EAAE,EAAE;QACtB,WAAW,EAAE,IAAI,CAAC,EAAE,EAAE;QACtB,YAAY,EAAE,IAAI,CAAC,EAAE,EAAE;KAC1B,CAAC;CACL,CAAC,CAAC,CAAC;AACJ,QAAQ,CAAC,eAAe,EAAE,GAAG,EAAE;IAC3B,EAAE,CAAC,0BAA0B,EAAE,GAAG,EAAE;QAChC,MAAM,CAAC,IAAI,CAAC,aAAa,EAAE,EAAE,CAAC,CAAC,CAAC;QAChC,MAAM,CAAC,MAAM,CAAC,SAAS,CAAC,gBAAgB,CAAC,CAAC,CAAC,iBAAiB,EAAE,CAAC;IACnE,CAAC,CAAC,CAAC;IACH,EAAE,CAAC,8BAA8B,EAAE,GAAG,EAAE;QACpC,MAAM,CAAC,IAAI,CAAC,aAAa,EAAE,EAAE,CAAC,CAAC,CAAC;QAChC,MAAM,CAAC,MAAM,CAAC,SAAS,CAAC,eAAe,CAAC,CAAC,CAAC,iBAAiB,EAAE,CAAC;IAClE,CAAC,CAAC,CAAC;IACH,EAAE,CAAC,+BAA+B,EAAE,GAAG,EAAE;QACrC,MAAM,CAAC,IAAI,CAAC,aAAa,EAAE,EAAE,CAAC,CAAC,CAAC;QAChC,MAAM,CAAC,MAAM,CAAC,SAAS,CAAC,qBAAqB,CAAC,CAAC,CAAC,iBAAiB,EAAE,CAAC;IACxE,CAAC,CAAC,CAAC;IACH,EAAE,CAAC,6BAA6B,EAAE,GAAG,EAAE;QACnC,MAAM,CAAC,IAAI,CAAC,aAAa,EAAE,EAAE,CAAC,CAAC,CAAC;QAChC,MAAM,CAAC,MAAM,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAC,CAAC,iBAAiB,EAAE,CAAC;IAC3D,CAAC,CAAC,CAAC;IACH,EAAE,CAAC,2BAA2B,EAAE,GAAG,EAAE;QACjC,MAAM,CAAC,IAAI,CAAC,aAAa,EAAE,EAAE,CAAC,CAAC,CAAC;QAChC,MAAM,CAAC,MAAM,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAC,CAAC,iBAAiB,EAAE,CAAC;IAC3D,CAAC,CAAC,CAAC;IACH,EAAE,CAAC,4BAA4B,EAAE,GAAG,EAAE;QAClC,MAAM,CAAC,IAAI,CAAC,aAAa,EAAE,EAAE,CAAC,CAAC,CAAC;QAChC,MAAM,CAAC,MAAM,CAAC,SAAS,CAAC,SAAS,CAAC,CAAC,CAAC,iBAAiB,EAAE,CAAC;IAC5D,CAAC,CAAC,CAAC;AACP,CAAC,CAAC,CAAC;AACH,8CAA8C"}