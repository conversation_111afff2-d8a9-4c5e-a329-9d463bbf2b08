/**
 * MetricsPanel Component
 *
 * Displays key trading performance metrics in a grid layout.
 */
import React from 'react';
import { PerformanceMetrics } from '@adhd-trading-dashboard/shared';
interface MetricsPanelProps {
    metrics: PerformanceMetrics | null;
    isLoading: boolean;
}
/**
 * MetricsPanel Component
 *
 * Displays a grid of key trading performance metrics.
 */
declare const MetricsPanel: React.FC<MetricsPanelProps>;
export { MetricsPanel };
export default MetricsPanel;
//# sourceMappingURL=MetricsPanel.d.ts.map