/**
 * useTradeAnalysis Hook (Deprecated)
 *
 * This hook is deprecated in favor of TradeAnalysisContext.
 * It provides a basic stub for backward compatibility.
 *
 * @deprecated Use TradeAnalysisContext instead
 */
import { PerformanceMetrics, EquityPoint, DistributionBar } from '../types';
/**
 * useTradeAnalysis Hook (Deprecated)
 *
 * Provides basic trade analysis functionality for backward compatibility.
 * New code should use TradeAnalysisContext instead.
 */
export declare const useTradeAnalysis: () => {
    metrics: PerformanceMetrics;
    equityCurveData: EquityPoint[];
    distributionData: DistributionBar[];
    trades: any[];
    filter: any;
    sort: {
        field: string;
        direction: "desc";
    };
    page: number;
    pageSize: number;
    totalPages: number;
    tradeSummary: {
        totalTrades: number;
        winningTrades: number;
        losingTrades: number;
        winRate: number;
        netProfit: number;
        profitFactor: number;
    };
    isLoading: boolean;
    error: string;
    setFilter: import("react").Dispatch<any>;
    clearFilters: () => void;
    setSort: import("react").Dispatch<import("react").SetStateAction<{
        field: string;
        direction: "desc";
    }>>;
    setPage: import("react").Dispatch<import("react").SetStateAction<number>>;
    setPageSize: import("react").Dispatch<import("react").SetStateAction<number>>;
    fetchTrades: () => Promise<void>;
    exportTrades: () => void;
};
//# sourceMappingURL=useTradeAnalysis.d.ts.map