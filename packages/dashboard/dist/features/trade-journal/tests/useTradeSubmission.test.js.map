{"version": 3, "file": "useTradeSubmission.test.js", "sourceRoot": "", "sources": ["../../../../src/features/trade-journal/tests/useTradeSubmission.test.js"], "names": [], "mappings": "AAAA;;;;GAIG;AACH,OAAO,EAAE,QAAQ,EAAE,EAAE,EAAE,MAAM,EAAE,UAAU,EAAE,SAAS,EAAE,EAAE,EAAE,MAAM,QAAQ,CAAC;AACzE,OAAO,EAAE,UAAU,EAAE,GAAG,EAAE,MAAM,wBAAwB,CAAC;AACzD,OAAO,EAAE,kBAAkB,EAAE,MAAM,6BAA6B,CAAC;AACjE,OAAO,EAAE,mBAAmB,EAAE,MAAM,yCAAyC,CAAC;AAC9E,iCAAiC;AACjC,EAAE,CAAC,IAAI,CAAC,yCAAyC,EAAE,GAAG,EAAE,CAAC,CAAC;IACtD,mBAAmB,EAAE;QACjB,oBAAoB,EAAE,EAAE,CAAC,EAAE,EAAE;QAC7B,sBAAsB,EAAE,EAAE,CAAC,EAAE,EAAE;KAClC;CACJ,CAAC,CAAC,CAAC;AACJ,wBAAwB;AACxB,MAAM,YAAY,GAAG,EAAE,CAAC,EAAE,EAAE,CAAC;AAC7B,EAAE,CAAC,IAAI,CAAC,kBAAkB,EAAE,GAAG,EAAE,CAAC,CAAC;IAC/B,WAAW,EAAE,GAAG,EAAE,CAAC,YAAY;CAClC,CAAC,CAAC,CAAC;AACJ,oCAAoC;AACpC,EAAE,CAAC,IAAI,CAAC,6BAA6B,EAAE,GAAG,EAAE,CAAC,CAAC;IAC1C,mBAAmB,EAAE,EAAE,CAAC,EAAE,CAAC,GAAG,EAAE,CAAC,EAAE,CAAC;IACpC,oBAAoB,EAAE,EAAE,CAAC,EAAE,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC;CACvC,CAAC,CAAC,CAAC;AACJ,QAAQ,CAAC,oBAAoB,EAAE,GAAG,EAAE;IAChC,MAAM,gBAAgB,GAAG,EAAE,CAAC,EAAE,EAAE,CAAC;IACjC,MAAM,YAAY,GAAG,EAAE,CAAC,EAAE,EAAE,CAAC;IAC7B,MAAM,cAAc,GAAG,EAAE,CAAC,EAAE,EAAE,CAAC;IAC/B,MAAM,wBAAwB,GAAG,EAAE,CAAC,EAAE,EAAE,CAAC;IACzC,MAAM,sBAAsB,GAAG,EAAE,CAAC,EAAE,EAAE,CAAC;IACvC,MAAM,cAAc,GAAG;QACnB,IAAI,EAAE,YAAY;QAClB,MAAM,EAAE,KAAK;QACb,SAAS,EAAE,MAAM;QACjB,OAAO,EAAE,SAAS;QAClB,SAAS,EAAE,SAAS;QACpB,MAAM,EAAE,KAAK;QACb,UAAU,EAAE,OAAO;QACnB,SAAS,EAAE,OAAO;QAClB,QAAQ,EAAE,GAAG;QACb,MAAM,EAAE,KAAK;QACb,SAAS,EAAE,KAAK;QAChB,MAAM,EAAE,KAAK;QACb,cAAc,EAAE,GAAG;QACnB,SAAS,EAAE,OAAO;QAClB,QAAQ,EAAE,OAAO;QACjB,KAAK,EAAE,YAAY;QACnB,yBAAyB;QACzB,qBAAqB,EAAE,WAAW;QAClC,wBAAwB,EAAE,MAAM;QAChC,qBAAqB,EAAE,UAAU;QACjC,kBAAkB,EAAE,KAAK;QACzB,oBAAoB,EAAE,MAAM;QAC5B,uBAAuB,EAAE,SAAS;QAClC,oBAAoB,EAAE,QAAQ;QAC9B,mBAAmB,EAAE,eAAe;QACpC,sBAAsB;QACtB,OAAO,EAAE,KAAK;QACd,WAAW,EAAE,QAAQ;QACrB,WAAW,EAAE,WAAW;QACxB,UAAU,EAAE,CAAC,OAAO,EAAE,WAAW,CAAC;QAClC,cAAc,EAAE,OAAO;QACvB,gBAAgB,EAAE,MAAM;QACxB,YAAY,EAAE,MAAM;QACpB,kBAAkB,EAAE,UAAU;QAC9B,gBAAgB,EAAE,GAAG;QACrB,QAAQ,EAAE,wBAAwB;KACrC,CAAC;IACF,MAAM,aAAa,GAAG;QAClB,EAAE,EAAE,CAAC;QACL,IAAI,EAAE,YAAY;QAClB,MAAM,EAAE,KAAK;QACb,SAAS,EAAE,MAAM;QACjB,OAAO,EAAE,SAAS;QAClB,SAAS,EAAE,SAAS;QACpB,MAAM,EAAE,KAAK;QACb,UAAU,EAAE,KAAK;QACjB,SAAS,EAAE,KAAK;QAChB,QAAQ,EAAE,CAAC;QACX,MAAM,EAAE,GAAG;QACX,SAAS,EAAE,GAAG;QACd,MAAM,EAAE,KAAK;QACb,cAAc,EAAE,CAAC;QACjB,SAAS,EAAE,OAAO;QAClB,QAAQ,EAAE,OAAO;QACjB,KAAK,EAAE,YAAY;KACtB,CAAC;IACF,UAAU,CAAC,GAAG,EAAE;QACZ,EAAE,CAAC,aAAa,EAAE,CAAC;QACnB,wBAAwB,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC;QAC/C,sBAAsB,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC;IACjD,CAAC,CAAC,CAAC;IACH,SAAS,CAAC,GAAG,EAAE;QACX,EAAE,CAAC,eAAe,EAAE,CAAC;IACzB,CAAC,CAAC,CAAC;IACH,QAAQ,CAAC,aAAa,EAAE,GAAG,EAAE;QACzB,EAAE,CAAC,sCAAsC,EAAE,KAAK,IAAI,EAAE;YAClD,MAAM,eAAe,GAAG,GAAG,CAAC;YAC5B,mBAAmB,CAAC,oBAAoB,CAAC,iBAAiB,CAAC,eAAe,CAAC,CAAC;YAC5E,MAAM,EAAE,MAAM,EAAE,GAAG,UAAU,CAAC,GAAG,EAAE,CAAC,kBAAkB,CAAC,cAAc,EAAE,KAAK,EAAE,aAAa;YAC3F,IAAI,EAAE,aAAa;YACnB,IAAI,EAAE,YAAY;YAClB,wBAAwB,EAAE,sBAAsB,EAAE,OAAO,EAAE,YAAY;YACvE,gBAAgB,EAAE,YAAY,EAAE,cAAc,CAAC,CAAC,CAAC;YACjD,MAAM,SAAS,GAAG,EAAE,cAAc,EAAE,EAAE,CAAC,EAAE,EAAE,EAAE,CAAC;YAC9C,MAAM,GAAG,CAAC,KAAK,IAAI,EAAE;gBACjB,MAAM,MAAM,CAAC,OAAO,CAAC,YAAY,CAAC,SAAS,CAAC,CAAC;YACjD,CAAC,CAAC,CAAC;YACH,MAAM,CAAC,SAAS,CAAC,cAAc,CAAC,CAAC,gBAAgB,EAAE,CAAC;YACpD,MAAM,CAAC,wBAAwB,CAAC,CAAC,gBAAgB,EAAE,CAAC;YACpD,MAAM,CAAC,mBAAmB,CAAC,oBAAoB,CAAC,CAAC,oBAAoB,CAAC,MAAM,CAAC,gBAAgB,CAAC;gBAC1F,KAAK,EAAE,MAAM,CAAC,gBAAgB,CAAC;oBAC3B,IAAI,EAAE,YAAY;oBAClB,MAAM,EAAE,KAAK;oBACb,SAAS,EAAE,MAAM;oBACjB,OAAO,EAAE,SAAS;oBAClB,UAAU,EAAE,SAAS;oBACrB,WAAW,EAAE,KAAK;oBAClB,UAAU,EAAE,KAAK;oBACjB,WAAW,EAAE,GAAG;oBAChB,UAAU,EAAE,GAAG;oBACf,QAAQ,EAAE,KAAK;oBACf,sBAAsB,EAAE,CAAC;iBAC5B,CAAC;gBACF,WAAW,EAAE,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC;gBAC/B,KAAK,EAAE,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC;gBACzB,QAAQ,EAAE,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC;aAC/B,CAAC,CAAC,CAAC;YACJ,MAAM,CAAC,cAAc,CAAC,CAAC,oBAAoB,CAAC,uDAAuD,CAAC,CAAC;YACrG,MAAM,CAAC,YAAY,CAAC,CAAC,oBAAoB,CAAC,UAAU,CAAC,CAAC;QAC1D,CAAC,CAAC,CAAC;QACH,EAAE,CAAC,iCAAiC,EAAE,KAAK,IAAI,EAAE;YAC7C,wBAAwB,CAAC,eAAe,CAAC,KAAK,CAAC,CAAC;YAChD,MAAM,EAAE,MAAM,EAAE,GAAG,UAAU,CAAC,GAAG,EAAE,CAAC,kBAAkB,CAAC,cAAc,EAAE,KAAK,EAAE,aAAa;YAC3F,IAAI,EAAE,aAAa;YACnB,IAAI,EAAE,YAAY;YAClB,wBAAwB,EAAE,sBAAsB,EAAE,OAAO,EAAE,YAAY;YACvE,gBAAgB,EAAE,YAAY,EAAE,cAAc,CAAC,CAAC,CAAC;YACjD,MAAM,SAAS,GAAG,EAAE,cAAc,EAAE,EAAE,CAAC,EAAE,EAAE,EAAE,CAAC;YAC9C,MAAM,GAAG,CAAC,KAAK,IAAI,EAAE;gBACjB,MAAM,MAAM,CAAC,OAAO,CAAC,YAAY,CAAC,SAAS,CAAC,CAAC;YACjD,CAAC,CAAC,CAAC;YACH,MAAM,CAAC,YAAY,CAAC,CAAC,oBAAoB,CAAC,4DAA4D,CAAC,CAAC;YACxG,MAAM,CAAC,gBAAgB,CAAC,CAAC,oBAAoB,CAAC,OAAO,CAAC,CAAC;YACvD,MAAM,CAAC,mBAAmB,CAAC,oBAAoB,CAAC,CAAC,GAAG,CAAC,gBAAgB,EAAE,CAAC;QAC5E,CAAC,CAAC,CAAC;QACH,EAAE,CAAC,sCAAsC,EAAE,KAAK,IAAI,EAAE;YAClD,mBAAmB,CAAC,oBAAoB,CAAC,iBAAiB,CAAC,IAAI,KAAK,CAAC,gBAAgB,CAAC,CAAC,CAAC;YACxF,MAAM,EAAE,MAAM,EAAE,GAAG,UAAU,CAAC,GAAG,EAAE,CAAC,kBAAkB,CAAC,cAAc,EAAE,KAAK,EAAE,aAAa;YAC3F,IAAI,EAAE,aAAa;YACnB,IAAI,EAAE,YAAY;YAClB,wBAAwB,EAAE,sBAAsB,EAAE,OAAO,EAAE,YAAY;YACvE,gBAAgB,EAAE,YAAY,EAAE,cAAc,CAAC,CAAC,CAAC;YACjD,MAAM,SAAS,GAAG,EAAE,cAAc,EAAE,EAAE,CAAC,EAAE,EAAE,EAAE,CAAC;YAC9C,MAAM,GAAG,CAAC,KAAK,IAAI,EAAE;gBACjB,MAAM,MAAM,CAAC,OAAO,CAAC,YAAY,CAAC,SAAS,CAAC,CAAC;YACjD,CAAC,CAAC,CAAC;YACH,MAAM,CAAC,YAAY,CAAC,CAAC,oBAAoB,CAAC,yCAAyC,CAAC,CAAC;YACrF,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,YAAY,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QACpD,CAAC,CAAC,CAAC;IACP,CAAC,CAAC,CAAC;IACH,QAAQ,CAAC,WAAW,EAAE,GAAG,EAAE;QACvB,EAAE,CAAC,8CAA8C,EAAE,KAAK,IAAI,EAAE;YAC1D,mBAAmB,CAAC,sBAAsB,CAAC,iBAAiB,CAAC,SAAS,CAAC,CAAC;YACxE,MAAM,EAAE,MAAM,EAAE,GAAG,UAAU,CAAC,GAAG,EAAE,CAAC,kBAAkB,CAAC,cAAc,EAAE,IAAI,EAAE,aAAa;YAC1F,KAAK,EAAE,aAAa;YACpB,aAAa,EAAE,YAAY;YAC3B,wBAAwB,EAAE,sBAAsB,EAAE,OAAO,EAAE,YAAY;YACvE,gBAAgB,EAAE,YAAY,EAAE,cAAc,CAAC,CAAC,CAAC;YACjD,MAAM,SAAS,GAAG,EAAE,cAAc,EAAE,EAAE,CAAC,EAAE,EAAE,EAAE,CAAC;YAC9C,MAAM,GAAG,CAAC,KAAK,IAAI,EAAE;gBACjB,MAAM,MAAM,CAAC,OAAO,CAAC,YAAY,CAAC,SAAS,CAAC,CAAC;YACjD,CAAC,CAAC,CAAC;YACH,MAAM,CAAC,mBAAmB,CAAC,sBAAsB,CAAC,CAAC,oBAAoB,CAAC,CAAC,EAAE,WAAW;YACtF,MAAM,CAAC,gBAAgB,CAAC;gBACpB,KAAK,EAAE,MAAM,CAAC,gBAAgB,CAAC;oBAC3B,IAAI,EAAE,YAAY;oBAClB,MAAM,EAAE,KAAK;oBACb,SAAS,EAAE,MAAM;oBACjB,WAAW,EAAE,GAAG;iBACnB,CAAC;aACL,CAAC,CAAC,CAAC;YACJ,MAAM,CAAC,cAAc,CAAC,CAAC,oBAAoB,CAAC,mDAAmD,CAAC,CAAC;QACrG,CAAC,CAAC,CAAC;QACH,EAAE,CAAC,0CAA0C,EAAE,KAAK,IAAI,EAAE;YACtD,MAAM,qBAAqB,GAAG,EAAE,GAAG,aAAa,EAAE,EAAE,EAAE,IAAI,EAAE,CAAC;YAC7D,mBAAmB,CAAC,sBAAsB,CAAC,iBAAiB,CAAC,SAAS,CAAC,CAAC;YACxE,MAAM,EAAE,MAAM,EAAE,GAAG,UAAU,CAAC,GAAG,EAAE,CAAC,kBAAkB,CAAC,cAAc,EAAE,IAAI,EAAE,aAAa;YAC1F,KAAK,EAAE,aAAa;YACpB,qBAAqB,EAAE,2BAA2B;YAClD,wBAAwB,EAAE,sBAAsB,EAAE,OAAO,EAAE,YAAY;YACvE,gBAAgB,EAAE,YAAY,EAAE,cAAc,CAAC,CAAC,CAAC;YACjD,MAAM,SAAS,GAAG,EAAE,cAAc,EAAE,EAAE,CAAC,EAAE,EAAE,EAAE,CAAC;YAC9C,MAAM,GAAG,CAAC,KAAK,IAAI,EAAE;gBACjB,MAAM,MAAM,CAAC,OAAO,CAAC,YAAY,CAAC,SAAS,CAAC,CAAC;YACjD,CAAC,CAAC,CAAC;YACH,MAAM,CAAC,mBAAmB,CAAC,sBAAsB,CAAC,CAAC,oBAAoB,CAAC,EAAE,EAAE,sBAAsB;YAClG,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC,CAAC;QACxB,CAAC,CAAC,CAAC;IACP,CAAC,CAAC,CAAC;IACH,QAAQ,CAAC,qBAAqB,EAAE,GAAG,EAAE;QACjC,EAAE,CAAC,+DAA+D,EAAE,KAAK,IAAI,EAAE;YAC3E,mBAAmB,CAAC,oBAAoB,CAAC,iBAAiB,CAAC,CAAC,CAAC,CAAC;YAC9D,MAAM,EAAE,MAAM,EAAE,GAAG,UAAU,CAAC,GAAG,EAAE,CAAC,kBAAkB,CAAC,cAAc,EAAE,KAAK,EAAE,aAAa;YAC3F,IAAI,EAAE,aAAa;YACnB,IAAI,EAAE,YAAY;YAClB,wBAAwB,EAAE,sBAAsB,EAAE,OAAO,EAAE,YAAY;YACvE,gBAAgB,EAAE,YAAY,EAAE,cAAc,CAAC,CAAC,CAAC;YACjD,MAAM,SAAS,GAAG,EAAE,cAAc,EAAE,EAAE,CAAC,EAAE,EAAE,EAAE,CAAC;YAC9C,MAAM,GAAG,CAAC,KAAK,IAAI,EAAE;gBACjB,MAAM,MAAM,CAAC,OAAO,CAAC,YAAY,CAAC,SAAS,CAAC,CAAC;YACjD,CAAC,CAAC,CAAC;YACH,MAAM,QAAQ,GAAG,mBAAmB,CAAC,oBAAoB,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAC3E,MAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,OAAO,CAAC,MAAM,CAAC,gBAAgB,CAAC;gBACnD,SAAS,EAAE,MAAM,EAAE,0BAA0B;gBAC7C,QAAQ,EAAE,KAAK,EAAE,yBAAyB;gBAC1C,WAAW,EAAE,KAAK,EAAE,qBAAqB;gBACzC,UAAU,EAAE,KAAK,EAAE,qBAAqB;gBACxC,WAAW,EAAE,GAAG,EAAE,qBAAqB;gBACvC,UAAU,EAAE,GAAG,EAAE,qBAAqB;gBACtC,eAAe,EAAE,CAAC,EAAE,qBAAqB;aAC5C,CAAC,CAAC,CAAC;QACR,CAAC,CAAC,CAAC;QACH,EAAE,CAAC,kEAAkE,EAAE,KAAK,IAAI,EAAE;YAC9E,mBAAmB,CAAC,oBAAoB,CAAC,iBAAiB,CAAC,CAAC,CAAC,CAAC;YAC9D,MAAM,EAAE,MAAM,EAAE,GAAG,UAAU,CAAC,GAAG,EAAE,CAAC,kBAAkB,CAAC,cAAc,EAAE,KAAK,EAAE,aAAa;YAC3F,IAAI,EAAE,aAAa;YACnB,IAAI,EAAE,YAAY;YAClB,wBAAwB,EAAE,sBAAsB,EAAE,OAAO,EAAE,YAAY;YACvE,gBAAgB,EAAE,YAAY,EAAE,cAAc,CAAC,CAAC,CAAC;YACjD,MAAM,SAAS,GAAG,EAAE,cAAc,EAAE,EAAE,CAAC,EAAE,EAAE,EAAE,CAAC;YAC9C,MAAM,GAAG,CAAC,KAAK,IAAI,EAAE;gBACjB,MAAM,MAAM,CAAC,OAAO,CAAC,YAAY,CAAC,SAAS,CAAC,CAAC;YACjD,CAAC,CAAC,CAAC;YACH,2DAA2D;YAC3D,MAAM,EAAE,mBAAmB,EAAE,oBAAoB,EAAE,GAAG,MAAM,MAAM,CAAC,6BAA6B,CAAC,CAAC;YAClG,MAAM,CAAC,mBAAmB,CAAC,CAAC,gBAAgB,EAAE,CAAC;YAC/C,MAAM,CAAC,oBAAoB,CAAC,CAAC,gBAAgB,EAAE,CAAC;QACpD,CAAC,CAAC,CAAC;QACH,EAAE,CAAC,4DAA4D,EAAE,KAAK,IAAI,EAAE;YACxE,mBAAmB,CAAC,oBAAoB,CAAC,iBAAiB,CAAC,CAAC,CAAC,CAAC;YAC9D,MAAM,EAAE,MAAM,EAAE,GAAG,UAAU,CAAC,GAAG,EAAE,CAAC,kBAAkB,CAAC,cAAc,EAAE,KAAK,EAAE,aAAa;YAC3F,IAAI,EAAE,aAAa;YACnB,IAAI,EAAE,YAAY;YAClB,wBAAwB,EAAE,sBAAsB,EAAE,OAAO,EAAE,YAAY;YACvE,gBAAgB,EAAE,YAAY,EAAE,cAAc,CAAC,CAAC,CAAC;YACjD,MAAM,SAAS,GAAG,EAAE,cAAc,EAAE,EAAE,CAAC,EAAE,EAAE,EAAE,CAAC;YAC9C,MAAM,GAAG,CAAC,KAAK,IAAI,EAAE;gBACjB,MAAM,MAAM,CAAC,OAAO,CAAC,YAAY,CAAC,SAAS,CAAC,CAAC;YACjD,CAAC,CAAC,CAAC;YACH,MAAM,QAAQ,GAAG,mBAAmB,CAAC,oBAAoB,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAC3E,MAAM,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC,OAAO,CAAC,MAAM,CAAC,gBAAgB,CAAC;gBACtD,SAAS,EAAE,wBAAwB;gBACnC,YAAY,EAAE,OAAO;gBACrB,UAAU,EAAE,MAAM;aACrB,CAAC,CAAC,CAAC;QACR,CAAC,CAAC,CAAC;IACP,CAAC,CAAC,CAAC;IACH,QAAQ,CAAC,gBAAgB,EAAE,GAAG,EAAE;QAC5B,EAAE,CAAC,4CAA4C,EAAE,KAAK,IAAI,EAAE;YACxD,IAAI,cAAc,CAAC;YACnB,MAAM,WAAW,GAAG,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,EAAE;gBACxC,cAAc,GAAG,OAAO,CAAC;YAC7B,CAAC,CAAC,CAAC;YACH,mBAAmB,CAAC,oBAAoB,CAAC,eAAe,CAAC,WAAW,CAAC,CAAC;YACtE,MAAM,EAAE,MAAM,EAAE,GAAG,UAAU,CAAC,GAAG,EAAE,CAAC,kBAAkB,CAAC,cAAc,EAAE,KAAK,EAAE,aAAa;YAC3F,IAAI,EAAE,aAAa;YACnB,IAAI,EAAE,YAAY;YAClB,wBAAwB,EAAE,sBAAsB,EAAE,OAAO,EAAE,YAAY;YACvE,gBAAgB,EAAE,YAAY,EAAE,cAAc,CAAC,CAAC,CAAC;YACjD,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,YAAY,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YAChD,MAAM,SAAS,GAAG,EAAE,cAAc,EAAE,EAAE,CAAC,EAAE,EAAE,EAAE,CAAC;YAC9C,GAAG,CAAC,GAAG,EAAE;gBACL,MAAM,CAAC,OAAO,CAAC,YAAY,CAAC,SAAS,CAAC,CAAC;YAC3C,CAAC,CAAC,CAAC;YACH,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,YAAY,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAC/C,MAAM,GAAG,CAAC,KAAK,IAAI,EAAE;gBACjB,cAAc,CAAC,CAAC,CAAC,CAAC;YACtB,CAAC,CAAC,CAAC;YACH,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,YAAY,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QACpD,CAAC,CAAC,CAAC;IACP,CAAC,CAAC,CAAC;AACP,CAAC,CAAC,CAAC;AACH,mDAAmD"}