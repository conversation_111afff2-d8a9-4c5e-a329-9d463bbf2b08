import { jsx as _jsx, jsxs as _jsxs } from "react/jsx-runtime";
import { render, screen, fireEvent } from '@testing-library/react';
import { describe, it, expect, vi } from 'vitest';
import TabPanel from './TabPanel';
import { ThemeProvider } from 'styled-components';
import { f1Theme } from '@adhd-trading-dashboard/shared';
// Mock tabs for testing
const mockTabs = [
    { id: 'tab1', label: 'Tab 1', content: _jsx("div", { children: "Tab 1 Content" }) },
    { id: 'tab2', label: 'Tab 2', content: _jsx("div", { children: "Tab 2 Content" }) },
    { id: 'tab3', label: 'Tab 3', content: _jsx("div", { children: "Tab 3 Content" }) },
];
describe('TabPanel Component', () => {
    it('renders all tab buttons', () => {
        render(_jsx(ThemeProvider, { theme: f1Theme, children: _jsx(TabPanel, { tabs: mockTabs, defaultTab: "tab1" }) }));
        expect(screen.getByText('Tab 1')).toBeInTheDocument();
        expect(screen.getByText('Tab 2')).toBeInTheDocument();
        expect(screen.getByText('Tab 3')).toBeInTheDocument();
    });
    it('shows the default tab content', () => {
        render(_jsx(ThemeProvider, { theme: f1Theme, children: _jsx(TabPanel, { tabs: mockTabs, defaultTab: "tab1" }) }));
        expect(screen.getByText('Tab 1 Content')).toBeInTheDocument();
        expect(screen.queryByText('Tab 2 Content')).not.toBeInTheDocument();
        expect(screen.queryByText('Tab 3 Content')).not.toBeInTheDocument();
    });
    it('switches tabs when clicked', () => {
        render(_jsx(ThemeProvider, { theme: f1Theme, children: _jsx(TabPanel, { tabs: mockTabs, defaultTab: "tab1" }) }));
        // Initially tab 1 content is visible
        expect(screen.getByText('Tab 1 Content')).toBeInTheDocument();
        // Click on tab 2
        fireEvent.click(screen.getByText('Tab 2'));
        // Now tab 2 content should be visible
        expect(screen.queryByText('Tab 1 Content')).not.toBeInTheDocument();
        expect(screen.getByText('Tab 2 Content')).toBeInTheDocument();
    });
    it('calls onTabClick when provided and tab is clicked', () => {
        const onTabClickMock = vi.fn();
        render(_jsx(ThemeProvider, { theme: f1Theme, children: _jsx(TabPanel, { tabs: mockTabs, defaultTab: "tab1", onTabClick: onTabClickMock }) }));
        // Click on tab 2
        fireEvent.click(screen.getByText('Tab 2'));
        // Check if onTabClick was called with the correct tab id
        expect(onTabClickMock).toHaveBeenCalledWith('tab2');
    });
    it('prevents default form submission when tab is clicked', () => {
        const onTabClickMock = vi.fn();
        const preventDefaultMock = vi.fn();
        render(_jsx(ThemeProvider, { theme: f1Theme, children: _jsxs("form", { children: [_jsx(TabPanel, { tabs: mockTabs, defaultTab: "tab1", onTabClick: onTabClickMock }), _jsx("button", { type: "submit", children: "Submit" })] }) }));
        // Get the tab button and simulate a click with preventDefault spy
        const tabButton = screen.getByText('Tab 2');
        const mockEvent = { preventDefault: preventDefaultMock };
        // Directly call the onClick handler
        // This is a bit of a hack since we can't easily spy on preventDefault with fireEvent
        const onClickHandler = tabButton.onclick;
        if (onClickHandler) {
            onClickHandler(mockEvent);
        }
        else {
            // Fallback to fireEvent if onclick is not directly accessible
            fireEvent.click(tabButton);
        }
        // Check if preventDefault was called
        expect(preventDefaultMock).toHaveBeenCalled();
    });
    it('has type="button" on all tab buttons to prevent form submission', () => {
        render(_jsx(ThemeProvider, { theme: f1Theme, children: _jsx("form", { children: _jsx(TabPanel, { tabs: mockTabs, defaultTab: "tab1" }) }) }));
        // Get all tab buttons
        const tabButtons = screen.getAllByRole('button');
        // Check that each button has type="button"
        tabButtons.forEach(button => {
            expect(button).toHaveAttribute('type', 'button');
        });
    });
});
//# sourceMappingURL=TabPanel.test.js.map
//# sourceMappingURL=TabPanel.test.js.map