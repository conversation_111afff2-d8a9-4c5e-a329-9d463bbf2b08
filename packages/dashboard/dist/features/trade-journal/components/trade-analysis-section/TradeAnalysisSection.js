import { jsx as _jsx, jsxs as _jsxs } from "react/jsx-runtime";
import styled from 'styled-components';
import { DashboardSection } from '@adhd-trading-dashboard/shared';
import PatternQualityAssessment from '../trade-pattern-quality/PatternQualityAssessment';
import DOLAnalysis from '../trade-dol-analysis/TradeDOLAnalysis';
// F1 Racing Theme Styled Components
const AnalysisContainer = styled.div `
  display: flex;
  flex-direction: column;
  gap: ${({ theme }) => theme.spacing?.xl || '32px'};
  background: ${({ theme }) => theme.colors?.surface || '#1f2937'};
  border: 1px solid ${({ theme }) => theme.colors?.border || '#4b5563'};
  border-radius: ${({ theme }) => theme.borderRadius?.lg || '8px'};
  overflow: hidden;
  position: relative;

  /* F1 Racing accent */
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 4px;
    height: 100%;
    background: ${({ theme }) => theme.colors?.primary || '#dc2626'};
    z-index: 1;
  }
`;
const SectionHeader = styled.div `
  padding: ${({ theme }) => theme.spacing?.lg || '24px'};
  background: ${({ theme }) => theme.colors?.background || '#111827'};
  border-bottom: 1px solid ${({ theme }) => theme.colors?.border || '#4b5563'};
`;
const HeaderTitleRow = styled.div `
  display: flex;
  align-items: center;
  gap: ${({ theme }) => theme.spacing?.md || '12px'};
`;
const HeaderIcon = styled.div `
  font-size: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  background: ${({ theme }) => theme.colors?.primary || '#dc2626'}20;
  border-radius: ${({ theme }) => theme.borderRadius?.md || '6px'};
  border: 1px solid ${({ theme }) => theme.colors?.primary || '#dc2626'}40;
`;
const HeaderTitle = styled.h3 `
  font-size: ${({ theme }) => theme.fontSizes?.lg || '1.125rem'};
  font-weight: 700;
  color: ${({ theme }) => theme.colors?.textPrimary || '#ffffff'};
  margin: 0;
  text-transform: uppercase;
  letter-spacing: 0.025em;
`;
const HeaderDescription = styled.p `
  font-size: ${({ theme }) => theme.fontSizes?.sm || '0.875rem'};
  color: ${({ theme }) => theme.colors?.textSecondary || '#9ca3af'};
  margin: ${({ theme }) => theme.spacing?.xs || '4px'} 0 0 0;
  line-height: 1.5;
`;
const SectionContent = styled.div `
  padding: ${({ theme }) => theme.spacing?.lg || '24px'};
`;
const SectionDivider = styled.hr `
  border: none;
  border-top: 1px solid ${({ theme }) => theme.colors?.border || '#4b5563'};
  margin: ${({ theme }) => theme.spacing?.xl || '32px'} 0;
  opacity: 0.5;
`;
/**
 * Trade Analysis Section Component
 *
 * Consolidates Pattern Quality Assessment and DOL Analysis into a unified section.
 * Follows the established DashboardSection pattern for consistency.
 */
export const TradeAnalysisSection = ({ formValues, onChange, validationErrors, disabled = false, className, }) => {
    return (_jsxs(AnalysisContainer, { className: className, children: [_jsx(SectionHeader, { children: _jsxs(HeaderTitleRow, { children: [_jsx(HeaderIcon, { children: "\uD83D\uDD0D" }), _jsxs("div", { children: [_jsx(HeaderTitle, { children: "Trade Analysis" }), _jsx(HeaderDescription, { children: "Comprehensive pattern quality assessment and DOL analysis" })] })] }) }), _jsxs(SectionContent, { children: [_jsx(DashboardSection, { name: "pattern-quality", title: "Pattern Quality Assessment", collapsible: true, defaultCollapsed: false, children: _jsx(PatternQualityAssessment, { formValues: formValues, onChange: onChange }) }), _jsx(SectionDivider, {}), _jsx(DashboardSection, { name: "dol-analysis", title: "DOL Analysis", collapsible: true, defaultCollapsed: false, children: _jsx(DOLAnalysis, { formValues: formValues, onChange: onChange, validationErrors: validationErrors }) })] })] }));
};
export default TradeAnalysisSection;
//# sourceMappingURL=TradeAnalysisSection.js.map