import { jsx as _jsx, jsxs as _jsxs } from "react/jsx-runtime";
import { useParams } from 'react-router-dom';
import styled from 'styled-components';
// Removed unused imports - will be added back when needed for real data integration
import { useTradeForm } from './hooks/useTradeForm';
import { TradeAnalysisSection } from './components/trade-analysis-section';
import { TradeFormHeader, TradeFormBasicFields, TradeFormTimingFields, TradeFormRiskFields, TradeFormStrategyFields, TradeFormActions, TradeFormMessages, TradeFormLoading, } from './components/trade-form';
const PageContainer = styled.div `
  display: flex;
  flex-direction: column;
  gap: ${({ theme }) => theme.spacing.lg};
  max-width: 1200px;
  margin: 0 auto;
  padding: ${({ theme }) => theme.spacing.lg};
`;
const ContentSection = styled.div `
  background-color: ${({ theme }) => theme.colors.surface};
  border-radius: ${({ theme }) => theme.borderRadius.md};
  padding: ${({ theme }) => theme.spacing.lg};
  box-shadow: ${({ theme }) => theme.shadows.sm};
  position: relative; /* Required for absolute positioning of loading overlay */
`;
const Form = styled.form `
  display: flex;
  flex-direction: column;
  gap: ${({ theme }) => theme.spacing.xl};
`;
const SectionContainer = styled.div `
  background: ${({ theme }) => theme.colors.background};
  border: 1px solid ${({ theme }) => theme.colors.border};
  border-radius: ${({ theme }) => theme.borderRadius.md};
  padding: ${({ theme }) => theme.spacing.lg};
  margin-bottom: ${({ theme }) => theme.spacing.lg};
`;
const SectionTitle = styled.h2 `
  font-size: ${({ theme }) => theme.fontSizes.lg};
  font-weight: 600;
  color: ${({ theme }) => theme.colors.textPrimary};
  margin: 0 0 ${({ theme }) => theme.spacing.md} 0;
  padding-bottom: ${({ theme }) => theme.spacing.sm};
  border-bottom: 2px solid ${({ theme }) => theme.colors.primary};
`;
const SectionDivider = styled.hr `
  border: none;
  border-top: 1px solid ${({ theme }) => theme.colors.border};
  margin: ${({ theme }) => theme.spacing.xl} 0;
`;
/**
 * TradeForm Component
 *
 * Displays a form for adding or editing trade entries in a single scrollable page
 * with all sections displayed vertically for better workflow.
 */
const TradeForm = () => {
    const { id } = useParams();
    // Enhanced debugging for ID parameter
    console.log(`TradeForm component mounted with ID parameter: "${id}"`);
    console.log(`Current URL: ${window.location.href}`);
    console.log(`Is edit mode: ${id && id !== 'new'}`);
    console.log(`Is new trade: ${id === 'new'}`);
    const { formValues, setFormValues, handleChange, handleSubmit, isSubmitting, isLoading, error, success, validationErrors, isNewTrade, calculateProfitLoss, } = useTradeForm(id);
    return (_jsxs(PageContainer, { children: [_jsx(TradeFormHeader, { isNewTrade: isNewTrade, formValues: formValues }), _jsxs(ContentSection, { children: [_jsx(TradeFormLoading, { isLoading: isLoading }), _jsxs(Form, { onSubmit: handleSubmit, children: [_jsx(TradeFormMessages, { error: error, success: success }), _jsx(TradeFormBasicFields, { formValues: formValues, handleChange: handleChange, validationErrors: validationErrors, calculateProfitLoss: calculateProfitLoss, setFormValues: setFormValues }), _jsx(TradeFormTimingFields, { formValues: formValues, handleChange: handleChange, validationErrors: validationErrors }), _jsxs(SectionContainer, { children: [_jsx(SectionTitle, { children: "\uD83D\uDCCA Strategy & Setup" }), _jsx(TradeFormStrategyFields, { formValues: formValues, handleChange: handleChange, validationErrors: validationErrors, setFormValues: setFormValues })] }), _jsx(SectionDivider, {}), _jsx(TradeAnalysisSection, { formValues: formValues, onChange: (field, value) => {
                                    setFormValues((prev) => ({
                                        ...prev,
                                        [field]: value,
                                    }));
                                }, validationErrors: validationErrors }), _jsxs(SectionContainer, { children: [_jsx(SectionTitle, { children: "\uD83D\uDCB0 Pricing & P&L" }), _jsx(TradeFormRiskFields, { formValues: formValues, handleChange: handleChange, validationErrors: validationErrors })] }), _jsx(SectionDivider, {}), _jsx(TradeFormActions, { isSubmitting: isSubmitting, isLoading: isLoading, isNewTrade: isNewTrade })] })] })] }));
};
export default TradeForm;
//# sourceMappingURL=TradeForm.js.map