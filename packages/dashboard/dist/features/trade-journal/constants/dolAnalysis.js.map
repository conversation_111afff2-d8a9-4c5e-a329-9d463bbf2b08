{"version": 3, "file": "dolAnalysis.js", "sourceRoot": "", "sources": ["../../../../src/features/trade-journal/constants/dolAnalysis.ts"], "names": [], "mappings": "AAAA;;;;GAIG;AAEH,oFAAoF;AAEpF;;GAEG;AACH,MAAM,CAAC,MAAM,gBAAgB,GAAG;IAC9B,EAAE,KAAK,EAAE,OAAO,EAAE,KAAK,EAAE,iDAAiD,EAAE;IAC5E,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,iDAAiD,EAAE;IAC1E,EAAE,KAAK,EAAE,UAAU,EAAE,KAAK,EAAE,+DAA+D,EAAE;IAC7F,EAAE,KAAK,EAAE,WAAW,EAAE,KAAK,EAAE,oDAAoD,EAAE;CACpF,CAAC;AAEF;;GAEG;AACH,MAAM,CAAC,MAAM,oBAAoB,GAAG;IAClC,EAAE,KAAK,EAAE,QAAQ,EAAE,KAAK,EAAE,sDAAsD,EAAE;IAClF,EAAE,KAAK,EAAE,UAAU,EAAE,KAAK,EAAE,0DAA0D,EAAE;IACxF,EAAE,KAAK,EAAE,MAAM,EAAE,KAAK,EAAE,+CAA+C,EAAE;CAC1E,CAAC;AAEF;;GAEG;AACH,MAAM,CAAC,MAAM,oBAAoB,GAAG;IAClC;QACE,KAAK,EAAE,oBAAoB;QAC3B,KAAK,EAAE,2DAA2D;KACnE;IACD;QACE,KAAK,EAAE,kBAAkB;QACzB,KAAK,EAAE,4DAA4D;KACpE;IACD,EAAE,KAAK,EAAE,eAAe,EAAE,KAAK,EAAE,iDAAiD,EAAE;IACpF,EAAE,KAAK,EAAE,cAAc,EAAE,KAAK,EAAE,sDAAsD,EAAE;CACzF,CAAC;AAEF;;GAEG;AACH,MAAM,CAAC,MAAM,mBAAmB,GAAG;IACjC,EAAE,KAAK,EAAE,kBAAkB,EAAE,KAAK,EAAE,kBAAkB,EAAE;IACxD,EAAE,KAAK,EAAE,iBAAiB,EAAE,KAAK,EAAE,iBAAiB,EAAE;IACtD,EAAE,KAAK,EAAE,MAAM,EAAE,KAAK,EAAE,gCAAgC,EAAE;IAC1D,EAAE,KAAK,EAAE,SAAS,EAAE,KAAK,EAAE,qBAAqB,EAAE;IAClD,EAAE,KAAK,EAAE,uBAAuB,EAAE,KAAK,EAAE,uBAAuB,EAAE;IAClE,EAAE,KAAK,EAAE,wBAAwB,EAAE,KAAK,EAAE,wBAAwB,EAAE;IACpE,EAAE,KAAK,EAAE,yBAAyB,EAAE,KAAK,EAAE,yBAAyB,EAAE;IACtE,EAAE,KAAK,EAAE,cAAc,EAAE,KAAK,EAAE,6BAA6B,EAAE;IAC/D,EAAE,KAAK,EAAE,iBAAiB,EAAE,KAAK,EAAE,sCAAsC,EAAE;IAC3E,EAAE,KAAK,EAAE,YAAY,EAAE,KAAK,EAAE,YAAY,EAAE;IAC5C,EAAE,KAAK,EAAE,OAAO,EAAE,KAAK,EAAE,OAAO,EAAE;CACnC,CAAC;AAEF;;GAEG;AACH,MAAM,CAAC,MAAM,yBAAyB,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE,MAAM,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC;IAC7E,KAAK,EAAE,MAAM,CAAC,CAAC,GAAG,CAAC,CAAC;IACpB,KAAK,EAAE,MAAM,CAAC,CAAC,GAAG,CAAC,CAAC;CACrB,CAAC,CAAC,CAAC;AAEJ;;GAEG;AACH,MAAM,CAAC,MAAM,6BAA6B,GAAG;IAC3C,KAAK,EACH,gGAAgG;IAClG,GAAG,EAAE,gGAAgG;IACrG,QAAQ,EACN,8FAA8F;IAChG,SAAS,EACP,4FAA4F;CAC/F,CAAC;AAEF;;GAEG;AACH,MAAM,CAAC,MAAM,+BAA+B,GAAG;IAC7C,MAAM,EAAE,yFAAyF;IACjG,QAAQ,EAAE,sFAAsF;IAChG,IAAI,EAAE,mFAAmF;CAC1F,CAAC;AAEF;;GAEG;AACH,MAAM,CAAC,MAAM,2BAA2B,GACtC,+EAA+E;IAC/E,kEAAkE,CAAC;AAErE;;GAEG;AACH,MAAM,CAAC,MAAM,gCAAgC,GAC3C,wEAAwE;IACxE,iFAAiF,CAAC;AAEpF;;GAEG;AACH,MAAM,CAAC,MAAM,qBAAqB,GAAG,CAAC,OAAe,EAAU,EAAE;IAC/D,MAAM,MAAM,GAAG,gBAAgB,CAAC,IAAI,CAAC,CAAC,MAAM,EAAE,EAAE,CAAC,MAAM,CAAC,KAAK,KAAK,OAAO,CAAC,CAAC;IAC3E,OAAO,MAAM,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;AACpD,CAAC,CAAC;AAEF;;GAEG;AACH,MAAM,CAAC,MAAM,yBAAyB,GAAG,CAAC,WAAmB,EAAU,EAAE;IACvE,MAAM,MAAM,GAAG,oBAAoB,CAAC,IAAI,CAAC,CAAC,MAAM,EAAE,EAAE,CAAC,MAAM,CAAC,KAAK,KAAK,WAAW,CAAC,CAAC;IACnF,OAAO,MAAM,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;AACpD,CAAC,CAAC;AAEF;;GAEG;AACH,MAAM,CAAC,MAAM,yBAAyB,GAAG,CAAC,WAAmB,EAAU,EAAE;IACvE,MAAM,MAAM,GAAG,oBAAoB,CAAC,IAAI,CAAC,CAAC,MAAM,EAAE,EAAE,CAAC,MAAM,CAAC,KAAK,KAAK,WAAW,CAAC,CAAC;IACnF,OAAO,MAAM,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;AACpD,CAAC,CAAC;AAEF;;GAEG;AACH,MAAM,CAAC,MAAM,wBAAwB,GAAG,CAAC,MAAc,EAAU,EAAE;IACjE,IAAI,MAAM,IAAI,CAAC;QAAE,OAAO,SAAS,CAAC,CAAC,QAAQ;IAC3C,IAAI,MAAM,IAAI,CAAC;QAAE,OAAO,SAAS,CAAC,CAAC,cAAc;IACjD,IAAI,MAAM,IAAI,CAAC;QAAE,OAAO,SAAS,CAAC,CAAC,QAAQ;IAC3C,IAAI,MAAM,IAAI,CAAC;QAAE,OAAO,SAAS,CAAC,CAAC,SAAS;IAC5C,OAAO,SAAS,CAAC,CAAC,MAAM;AAC1B,CAAC,CAAC;AAEF;;GAEG;AACH,MAAM,CAAC,MAAM,8BAA8B,GAAG,CAAC,MAAc,EAAU,EAAE;IACvE,IAAI,MAAM,IAAI,CAAC;QAAE,OAAO,aAAa,CAAC;IACtC,IAAI,MAAM,IAAI,CAAC;QAAE,OAAO,WAAW,CAAC;IACpC,IAAI,MAAM,IAAI,CAAC;QAAE,OAAO,WAAW,CAAC;IACpC,IAAI,MAAM,IAAI,CAAC;QAAE,OAAO,MAAM,CAAC;IAC/B,IAAI,MAAM,IAAI,CAAC;QAAE,OAAO,SAAS,CAAC;IAClC,IAAI,MAAM,IAAI,CAAC;QAAE,OAAO,eAAe,CAAC;IACxC,IAAI,MAAM,IAAI,CAAC;QAAE,OAAO,MAAM,CAAC;IAC/B,IAAI,MAAM,IAAI,CAAC;QAAE,OAAO,WAAW,CAAC;IACpC,OAAO,aAAa,CAAC;AACvB,CAAC,CAAC"}