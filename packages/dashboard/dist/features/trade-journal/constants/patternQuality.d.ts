/**
 * Pattern Quality Assessment Constants
 *
 * Constants for the pattern quality assessment feature
 */
import { ScoreRange } from '../types';
/**
 * Score values for each score range
 */
export declare const SCORE_VALUES: Record<ScoreRange, number>;
/**
 * Score range options for dropdowns
 */
export declare const SCORE_RANGE_OPTIONS: {
    value: string;
    label: string;
}[];
/**
 * Pattern Quality Criteria Definitions
 */
export declare const PATTERN_QUALITY_CRITERIA: {
    clarity: {
        title: string;
        description: string;
        excellent: string;
        good: string;
        average: string;
        poor: string;
        unacceptable: string;
    };
    confluence: {
        title: string;
        description: string;
        excellent: string;
        good: string;
        average: string;
        poor: string;
        unacceptable: string;
    };
    context: {
        title: string;
        description: string;
        excellent: string;
        good: string;
        average: string;
        poor: string;
        unacceptable: string;
    };
    risk: {
        title: string;
        description: string;
        excellent: string;
        good: string;
        average: string;
        poor: string;
        unacceptable: string;
    };
    reward: {
        title: string;
        description: string;
        excellent: string;
        good: string;
        average: string;
        poor: string;
        unacceptable: string;
    };
    timeframe: {
        title: string;
        description: string;
        excellent: string;
        good: string;
        average: string;
        poor: string;
        unacceptable: string;
    };
    volume: {
        title: string;
        description: string;
        excellent: string;
        good: string;
        average: string;
        poor: string;
        unacceptable: string;
    };
};
/**
 * Calculate total score from criteria
 */
export declare const calculateTotalScore: (criteria: Record<string, ScoreRange>) => number;
/**
 * Convert total score to 1-10 rating
 */
export declare const convertScoreToRating: (totalScore: number) => number;
/**
 * Get rating description based on rating value
 */
export declare const getRatingDescription: (rating: number) => string;
/**
 * Get color for rating
 */
export declare const getRatingColor: (rating: number) => string;
//# sourceMappingURL=patternQuality.d.ts.map