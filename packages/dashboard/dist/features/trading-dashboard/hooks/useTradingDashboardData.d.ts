/**
 * useTradingDashboardData Hook (REFACTORED)
 *
 * EXTRACTED FROM: useTradingDashboard.ts (375 lines → 100 lines)
 * High-performance data management hook with error handling and caching.
 *
 * IMPROVEMENTS:
 * - Memoized expensive calculations (O(n²) → O(n))
 * - Proper error boundaries and retry logic
 * - Data validation with fallbacks
 * - Performance monitoring
 * - Centralized data transformation
 */
import { CompleteTradeData } from '@adhd-trading-dashboard/shared';
import { PerformanceMetric, ChartDataPoint, SetupPerformance, SessionPerformance } from '../types';
export interface TradingDashboardData {
    trades: CompleteTradeData[];
    performanceMetrics: PerformanceMetric[];
    chartData: ChartDataPoint[];
    setupPerformance: SetupPerformance[];
    sessionPerformance: SessionPerformance[];
    lastUpdated: Date | null;
}
export interface UseTradingDashboardDataReturn extends TradingDashboardData {
    isLoading: boolean;
    error: string | null;
    refreshData: () => Promise<void>;
    clearError: () => void;
}
/**
 * useTradingDashboardData Hook (REFACTORED)
 *
 * High-performance data management hook with comprehensive error handling,
 * memoized calculations, and proper loading states.
 */
export declare const useTradingDashboardData: () => UseTradingDashboardDataReturn;
export default useTradingDashboardData;
//# sourceMappingURL=useTradingDashboardData.d.ts.map