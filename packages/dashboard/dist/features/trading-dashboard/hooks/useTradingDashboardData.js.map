{"version": 3, "file": "useTradingDashboardData.js", "sourceRoot": "", "sources": ["../../../../src/features/trading-dashboard/hooks/useTradingDashboardData.ts"], "names": [], "mappings": "AAAA;;;;;;;;;;;;GAYG;AAEH,OAAO,EAAE,QAAQ,EAAE,SAAS,EAAE,WAAW,EAAE,OAAO,EAAE,MAAM,OAAO,CAAC;AAClE,OAAO,EACL,eAAe,EAEf,mBAAmB,GACpB,MAAM,gCAAgC,CAAC;AACxC,OAAO,EAAE,gBAAgB,EAAE,MAAM,iDAAiD,CAAC;AAyBnF;;GAEG;AACH,MAAM,gBAAgB,GAAG;IACvB;;;;OAIG;IACH,oBAAoB,EAAE,CAAC,iBAAsC,EAAoB,EAAE;QACjF,OAAO,CAAC,GAAG,CACT,sDAAsD,EACtD,iBAAiB,CAAC,MAAM,EACxB,QAAQ,CACT,CAAC;QAEF,IAAI,iBAAiB,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YACjC,OAAO,CAAC,GAAG,CACT,sCAAsC,EACtC,iBAAiB,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC,KAAK,CAAC,EAAE,EAAE,IAAI,EAAE,CAAC,CAAC,KAAK,CAAC,IAAI,EAAE,CAAC,CAAC,CACvE,CAAC;QACJ,CAAC;QAED,sEAAsE;QACtE,MAAM,UAAU,GAAG,CAAC,GAAG,iBAAiB,CAAC,CAAC,IAAI,CAC5C,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,IAAI,IAAI,CAAC,CAAC,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,OAAO,EAAE,GAAG,IAAI,IAAI,CAAC,CAAC,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,OAAO,EAAE,CAC9E,CAAC;QAEF,IAAI,UAAU,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YAC1B,OAAO,CAAC,GAAG,CACT,uCAAuC,EACvC,UAAU,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC,KAAK,CAAC,EAAE,EAAE,IAAI,EAAE,CAAC,CAAC,KAAK,CAAC,IAAI,EAAE,CAAC,CAAC,CAChE,CAAC;QACJ,CAAC;QAED,OAAO,UAAU,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,KAAK,EAAE,EAAE;YACpC,IAAI,CAAC;gBACH,MAAM,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC;gBAEzB,+CAA+C;gBAC/C,IAAI,YAAY,GAAG,UAAU,CAAC;gBAC9B,IAAI,CAAC;oBACH,IAAI,KAAK,CAAC,eAAe,EAAE,CAAC;wBAC1B,YAAY,GAAG,gBAAgB,CAAC,qBAAqB,CAAC,KAAK,CAAC,eAAe,CAAC,CAAC;oBAC/E,CAAC;yBAAM,IAAI,KAAK,CAAC,KAAK,EAAE,CAAC;wBACvB,YAAY,GAAG,KAAK,CAAC,KAAK,CAAC;oBAC7B,CAAC;gBACH,CAAC;gBAAC,OAAO,UAAU,EAAE,CAAC;oBACpB,OAAO,CAAC,IAAI,CAAC,yCAAyC,KAAK,CAAC,EAAE,GAAG,EAAE,UAAU,CAAC,CAAC;oBAC/E,YAAY,GAAG,KAAK,CAAC,KAAK,IAAI,aAAa,CAAC;gBAC9C,CAAC;gBAED,OAAO;oBACL,EAAE,EAAE,KAAK,CAAC,EAAE,EAAE,QAAQ,EAAE,IAAI,KAAK,CAAC,QAAQ,EAAE;oBAC5C,IAAI,EAAE,KAAK,CAAC,IAAI,IAAI,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;oBAC1D,KAAK,EAAE,KAAK,CAAC,UAAU,IAAI,SAAS;oBACpC,OAAO,EAAE,KAAK,CAAC,OAAO,IAAI,SAAS;oBACnC,KAAK,EAAE,YAAY;oBACnB,KAAK,EAAE,KAAK,CAAC,UAAU,IAAI,UAAU;oBACrC,IAAI,EAAE,KAAK,CAAC,SAAS,IAAI,UAAU;oBACnC,SAAS,EAAE,KAAK,CAAC,SAAS,IAAI,MAAM;oBACpC,MAAM,EAAE,KAAK,CAAC,MAAM,IAAI,KAAK;oBAC7B,SAAS,EAAE,MAAM,CAAC,KAAK,CAAC,UAAU,CAAC,IAAI,CAAC;oBACxC,cAAc,EAAE,MAAM,CAAC,KAAK,CAAC,sBAAsB,CAAC,IAAI,CAAC;oBACzD,GAAG,EAAE,KAAK,CAAC,QAAQ,KAAK,KAAK;oBAC7B,UAAU,EAAE,MAAM,CAAC,KAAK,CAAC,WAAW,CAAC,IAAI,CAAC;oBAC1C,SAAS,EAAE,MAAM,CAAC,KAAK,CAAC,UAAU,CAAC,IAAI,CAAC;oBACxC,IAAI,EAAE,MAAM,CAAC,KAAK,CAAC,WAAW,CAAC,IAAI,CAAC;oBACpC,GAAG,EAAE,MAAM,CAAC,KAAK,CAAC,WAAW,CAAC,IAAI,CAAC;oBACnC,SAAS,EAAE,KAAK,CAAC,UAAU,IAAI,EAAE;oBACjC,MAAM,EAAE,KAAK,CAAC,OAAO,IAAI,EAAE;oBAC3B,YAAY,EAAE,IAAI,CAAC,WAAW,EAAE,aAAa,IAAI,EAAE;oBACnD,eAAe,EAAE,IAAI,CAAC,WAAW,EAAE,iBAAiB,IAAI,EAAE;iBAC3D,CAAC;YACJ,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,OAAO,CAAC,KAAK,CAAC,yCAAyC,KAAK,GAAG,EAAE,KAAK,CAAC,CAAC;gBACxE,sCAAsC;gBACtC,OAAO;oBACL,EAAE,EAAE,KAAK,CAAC,QAAQ,EAAE;oBACpB,IAAI,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;oBAC5C,KAAK,EAAE,OAAO;oBACd,OAAO,EAAE,SAAS;oBAClB,KAAK,EAAE,kBAAkB;oBACzB,KAAK,EAAE,UAAU;oBACjB,IAAI,EAAE,UAAU;oBAChB,SAAS,EAAE,MAAM;oBACjB,MAAM,EAAE,KAAK;oBACb,SAAS,EAAE,CAAC;oBACZ,cAAc,EAAE,CAAC;oBACjB,GAAG,EAAE,KAAK;oBACV,UAAU,EAAE,CAAC;oBACb,SAAS,EAAE,CAAC;oBACZ,IAAI,EAAE,CAAC;oBACP,GAAG,EAAE,CAAC;oBACN,SAAS,EAAE,EAAE;oBACb,MAAM,EAAE,EAAE;oBACV,YAAY,EAAE,EAAE;oBAChB,eAAe,EAAE,EAAE;iBACpB,CAAC;YACJ,CAAC;QACH,CAAC,CAAC,CAAC;IACL,CAAC;IAED;;;OAGG;IACH,gBAAgB,EAAE,CAAC,MAA2B,EAAuB,EAAE;QACrE,IAAI,CAAC,MAAM,IAAI,MAAM,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YACnC,OAAO;gBACL,EAAE,KAAK,EAAE,UAAU,EAAE,KAAK,EAAE,MAAM,EAAE;gBACpC,EAAE,KAAK,EAAE,WAAW,EAAE,KAAK,EAAE,OAAO,EAAE;gBACtC,EAAE,KAAK,EAAE,gBAAgB,EAAE,KAAK,EAAE,MAAM,EAAE;gBAC1C,EAAE,KAAK,EAAE,cAAc,EAAE,KAAK,EAAE,CAAC,EAAE;aACpC,CAAC;QACJ,CAAC;QAED,IAAI,CAAC;YACH,MAAM,WAAW,GAAG,MAAM,CAAC,MAAM,CAAC;YAClC,MAAM,aAAa,GAAG,MAAM,CAAC,MAAM,CAAC,CAAC,KAAK,EAAE,EAAE,CAAC,KAAK,CAAC,KAAK,CAAC,QAAQ,KAAK,KAAK,CAAC,CAAC,MAAM,CAAC;YACtF,MAAM,OAAO,GAAG,CAAC,aAAa,GAAG,WAAW,CAAC,GAAG,GAAG,CAAC;YACpD,MAAM,QAAQ,GAAG,MAAM,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,KAAK,EAAE,EAAE,CAAC,GAAG,GAAG,CAAC,KAAK,CAAC,KAAK,CAAC,WAAW,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;YACxF,MAAM,YAAY,GAChB,MAAM,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,KAAK,EAAE,EAAE,CAAC,GAAG,GAAG,CAAC,KAAK,CAAC,KAAK,CAAC,UAAU,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC,GAAG,WAAW,CAAC;YAEtF,OAAO;gBACL,EAAE,KAAK,EAAE,UAAU,EAAE,KAAK,EAAE,GAAG,OAAO,CAAC,OAAO,CAAC,CAAC,CAAC,GAAG,EAAE;gBACtD,EAAE,KAAK,EAAE,WAAW,EAAE,KAAK,EAAE,IAAI,QAAQ,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,EAAE;gBACxD,EAAE,KAAK,EAAE,gBAAgB,EAAE,KAAK,EAAE,YAAY,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE;gBAC3D,EAAE,KAAK,EAAE,cAAc,EAAE,KAAK,EAAE,WAAW,EAAE;aAC9C,CAAC;QACJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,4BAA4B,EAAE,KAAK,CAAC,CAAC;YACnD,OAAO;gBACL,EAAE,KAAK,EAAE,UAAU,EAAE,KAAK,EAAE,OAAO,EAAE;gBACrC,EAAE,KAAK,EAAE,WAAW,EAAE,KAAK,EAAE,OAAO,EAAE;gBACtC,EAAE,KAAK,EAAE,gBAAgB,EAAE,KAAK,EAAE,OAAO,EAAE;gBAC3C,EAAE,KAAK,EAAE,cAAc,EAAE,KAAK,EAAE,OAAO,EAAE;aAC1C,CAAC;QACJ,CAAC;IACH,CAAC;IAED;;;OAGG;IACH,iBAAiB,EAAE,CAAC,MAA2B,EAAoB,EAAE;QACnE,IAAI,CAAC,MAAM,IAAI,MAAM,CAAC,MAAM,KAAK,CAAC;YAAE,OAAO,EAAE,CAAC;QAE9C,IAAI,CAAC;YACH,MAAM,YAAY,GAAG,IAAI,GAAG,EAAkB,CAAC;YAE/C,mCAAmC;YACnC,MAAM,CAAC,OAAO,CAAC,CAAC,KAAK,EAAE,EAAE;gBACvB,IAAI,CAAC;oBACH,MAAM,IAAI,GAAG,IAAI,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,kBAAkB,CAAC,OAAO,EAAE;wBAClE,KAAK,EAAE,SAAS;wBAChB,GAAG,EAAE,SAAS;qBACf,CAAC,CAAC;oBACH,MAAM,UAAU,GAAG,YAAY,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;oBAC/C,YAAY,CAAC,GAAG,CAAC,IAAI,EAAE,UAAU,GAAG,CAAC,KAAK,CAAC,KAAK,CAAC,WAAW,IAAI,CAAC,CAAC,CAAC,CAAC;gBACtE,CAAC;gBAAC,OAAO,SAAS,EAAE,CAAC;oBACnB,OAAO,CAAC,IAAI,CAAC,0BAA0B,KAAK,CAAC,KAAK,CAAC,EAAE,GAAG,EAAE,KAAK,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;gBAC9E,CAAC;YACH,CAAC,CAAC,CAAC;YAEH,4CAA4C;YAC5C,IAAI,UAAU,GAAG,CAAC,CAAC;YACnB,OAAO,KAAK,CAAC,IAAI,CAAC,YAAY,CAAC,OAAO,EAAE,CAAC;iBACtC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,IAAI,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,EAAE,GAAG,IAAI,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,EAAE,CAAC;iBACnE,GAAG,CAAC,CAAC,CAAC,IAAI,EAAE,GAAG,CAAC,EAAE,EAAE;gBACnB,UAAU,IAAI,GAAG,CAAC;gBAClB,OAAO,EAAE,IAAI,EAAE,GAAG,EAAE,UAAU,EAAE,CAAC;YACnC,CAAC,CAAC,CAAC;QACP,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,8BAA8B,EAAE,KAAK,CAAC,CAAC;YACrD,OAAO,EAAE,CAAC;QACZ,CAAC;IACH,CAAC;CACF,CAAC;AAEF;;GAEG;AACH,MAAM,sBAAsB,GAAG;IAC7B;;;OAGG;IACH,yBAAyB,EAAE,CAAC,MAA2B,EAAsB,EAAE;QAC7E,IAAI,CAAC,MAAM,IAAI,MAAM,CAAC,MAAM,KAAK,CAAC;YAAE,OAAO,EAAE,CAAC;QAE9C,IAAI,CAAC;YACH,MAAM,QAAQ,GAAG,IAAI,GAAG,EAA+B,CAAC;YAExD,+BAA+B;YAC/B,MAAM,CAAC,OAAO,CAAC,CAAC,KAAK,EAAE,EAAE;gBACvB,MAAM,KAAK,GAAG,KAAK,CAAC,KAAK,CAAC,KAAK,IAAI,SAAS,CAAC;gBAC7C,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,KAAK,CAAC,EAAE,CAAC;oBACzB,QAAQ,CAAC,GAAG,CAAC,KAAK,EAAE,EAAE,CAAC,CAAC;gBAC1B,CAAC;gBACD,QAAQ,CAAC,GAAG,CAAC,KAAK,CAAC,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC;YACnC,CAAC,CAAC,CAAC;YAEH,0CAA0C;YAC1C,OAAO,KAAK,CAAC,IAAI,CAAC,QAAQ,CAAC,OAAO,EAAE,CAAC;iBAClC,GAAG,CAAC,CAAC,CAAC,IAAI,EAAE,WAAW,CAAC,EAAE,EAAE;gBAC3B,MAAM,WAAW,GAAG,WAAW,CAAC,MAAM,CAAC;gBACvC,MAAM,aAAa,GAAG,WAAW,CAAC,MAAM,CACtC,CAAC,KAAK,EAAE,EAAE,CAAC,KAAK,CAAC,KAAK,CAAC,QAAQ,KAAK,KAAK,CAC1C,CAAC,MAAM,CAAC;gBACT,MAAM,OAAO,GAAG,WAAW,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,aAAa,GAAG,WAAW,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;gBAC1E,MAAM,GAAG,GAAG,WAAW,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,KAAK,EAAE,EAAE,CAAC,GAAG,GAAG,CAAC,KAAK,CAAC,KAAK,CAAC,WAAW,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;gBACxF,MAAM,YAAY,GAChB,WAAW,GAAG,CAAC;oBACb,CAAC,CAAC,WAAW,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,KAAK,EAAE,EAAE,CAAC,GAAG,GAAG,CAAC,KAAK,CAAC,KAAK,CAAC,UAAU,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC;wBAC1E,WAAW;oBACb,CAAC,CAAC,CAAC,CAAC;gBAER,OAAO,EAAE,IAAI,EAAE,OAAO,EAAE,YAAY,EAAE,WAAW,EAAE,GAAG,EAAE,CAAC;YAC3D,CAAC,CAAC;iBACD,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,sCAAsC;QAC1E,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,sCAAsC,EAAE,KAAK,CAAC,CAAC;YAC7D,OAAO,EAAE,CAAC;QACZ,CAAC;IACH,CAAC;IAED;;;OAGG;IACH,2BAA2B,EAAE,CAAC,MAA2B,EAAwB,EAAE;QACjF,IAAI,CAAC,MAAM,IAAI,MAAM,CAAC,MAAM,KAAK,CAAC;YAAE,OAAO,EAAE,CAAC;QAE9C,IAAI,CAAC;YACH,MAAM,UAAU,GAAG,IAAI,GAAG,EAA+B,CAAC;YAE1D,0BAA0B;YAC1B,MAAM,CAAC,OAAO,CAAC,CAAC,KAAK,EAAE,EAAE;gBACvB,MAAM,OAAO,GAAG,KAAK,CAAC,KAAK,CAAC,OAAO,IAAI,SAAS,CAAC;gBACjD,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,OAAO,CAAC,EAAE,CAAC;oBAC7B,UAAU,CAAC,GAAG,CAAC,OAAO,EAAE,EAAE,CAAC,CAAC;gBAC9B,CAAC;gBACD,UAAU,CAAC,GAAG,CAAC,OAAO,CAAC,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC;YACvC,CAAC,CAAC,CAAC;YAEH,qCAAqC;YACrC,OAAO,KAAK,CAAC,IAAI,CAAC,UAAU,CAAC,OAAO,EAAE,CAAC;iBACpC,GAAG,CAAC,CAAC,CAAC,IAAI,EAAE,aAAa,CAAC,EAAE,EAAE;gBAC7B,MAAM,WAAW,GAAG,aAAa,CAAC,MAAM,CAAC;gBACzC,MAAM,aAAa,GAAG,aAAa,CAAC,MAAM,CACxC,CAAC,KAAK,EAAE,EAAE,CAAC,KAAK,CAAC,KAAK,CAAC,QAAQ,KAAK,KAAK,CAC1C,CAAC,MAAM,CAAC;gBACT,MAAM,OAAO,GAAG,WAAW,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,aAAa,GAAG,WAAW,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;gBAC1E,MAAM,GAAG,GAAG,aAAa,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,KAAK,EAAE,EAAE,CAAC,GAAG,GAAG,CAAC,KAAK,CAAC,KAAK,CAAC,WAAW,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;gBAC1F,MAAM,YAAY,GAChB,WAAW,GAAG,CAAC;oBACb,CAAC,CAAC,aAAa,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,KAAK,EAAE,EAAE,CAAC,GAAG,GAAG,CAAC,KAAK,CAAC,KAAK,CAAC,UAAU,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC;wBAC5E,WAAW;oBACb,CAAC,CAAC,CAAC,CAAC;gBAER,OAAO,EAAE,IAAI,EAAE,OAAO,EAAE,YAAY,EAAE,WAAW,EAAE,GAAG,EAAE,CAAC;YAC3D,CAAC,CAAC;iBACD,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,yBAAyB;QAC7D,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,wCAAwC,EAAE,KAAK,CAAC,CAAC;YAC/D,OAAO,EAAE,CAAC;QACZ,CAAC;IACH,CAAC;CACF,CAAC;AAEF;;;;;GAKG;AACH,MAAM,CAAC,MAAM,uBAAuB,GAAG,GAAkC,EAAE;IACzE,MAAM,CAAC,SAAS,EAAE,YAAY,CAAC,GAAG,QAAQ,CAAsB,EAAE,CAAC,CAAC;IACpE,MAAM,CAAC,WAAW,EAAE,cAAc,CAAC,GAAG,QAAQ,CAAc,IAAI,CAAC,CAAC;IAClE,MAAM,EAAE,SAAS,EAAE,KAAK,EAAE,WAAW,EAAE,UAAU,EAAE,GAAG,eAAe,EAAE,CAAC;IAExE,yEAAyE;IACzE,MAAM,MAAM,GAAG,OAAO,CAAC,GAAG,EAAE;QAC1B,OAAO,CAAC,GAAG,CACT,uCAAuC,EACvC,SAAS,CAAC,MAAM,EAChB,0BAA0B,CAC3B,CAAC;QACF,OAAO,SAAS,CAAC;IACnB,CAAC,EAAE,CAAC,SAAS,CAAC,CAAC,CAAC;IAEhB,MAAM,kBAAkB,GAAG,OAAO,CAAC,GAAG,EAAE;QACtC,OAAO,CAAC,IAAI,CAAC,qBAAqB,CAAC,CAAC;QACpC,MAAM,MAAM,GAAG,gBAAgB,CAAC,gBAAgB,CAAC,MAAM,CAAC,CAAC;QACzD,OAAO,CAAC,OAAO,CAAC,qBAAqB,CAAC,CAAC;QACvC,OAAO,MAAM,CAAC;IAChB,CAAC,EAAE,CAAC,MAAM,CAAC,CAAC,CAAC;IAEb,MAAM,SAAS,GAAG,OAAO,CAAC,GAAG,EAAE;QAC7B,OAAO,CAAC,IAAI,CAAC,uBAAuB,CAAC,CAAC;QACtC,MAAM,MAAM,GAAG,gBAAgB,CAAC,iBAAiB,CAAC,MAAM,CAAC,CAAC;QAC1D,OAAO,CAAC,OAAO,CAAC,uBAAuB,CAAC,CAAC;QACzC,OAAO,MAAM,CAAC;IAChB,CAAC,EAAE,CAAC,MAAM,CAAC,CAAC,CAAC;IAEb,MAAM,gBAAgB,GAAG,OAAO,CAAC,GAAG,EAAE;QACpC,OAAO,CAAC,IAAI,CAAC,+BAA+B,CAAC,CAAC;QAC9C,MAAM,MAAM,GAAG,sBAAsB,CAAC,yBAAyB,CAAC,MAAM,CAAC,CAAC;QACxE,OAAO,CAAC,OAAO,CAAC,+BAA+B,CAAC,CAAC;QACjD,OAAO,MAAM,CAAC;IAChB,CAAC,EAAE,CAAC,MAAM,CAAC,CAAC,CAAC;IAEb,MAAM,kBAAkB,GAAG,OAAO,CAAC,GAAG,EAAE;QACtC,OAAO,CAAC,IAAI,CAAC,iCAAiC,CAAC,CAAC;QAChD,MAAM,MAAM,GAAG,sBAAsB,CAAC,2BAA2B,CAAC,MAAM,CAAC,CAAC;QAC1E,OAAO,CAAC,OAAO,CAAC,iCAAiC,CAAC,CAAC;QACnD,OAAO,MAAM,CAAC;IAChB,CAAC,EAAE,CAAC,MAAM,CAAC,CAAC,CAAC;IAEb;;OAEG;IACH,MAAM,WAAW,GAAG,WAAW,CAAC,KAAK,IAAI,EAAE;QACzC,OAAO,CAAC,GAAG,CAAC,gDAAgD,CAAC,CAAC;QAC9D,MAAM,WAAW,CAAC,KAAK,IAAI,EAAE;YAC3B,IAAI,CAAC;gBACH,OAAO,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;gBAC3B,OAAO,CAAC,GAAG,CACT,mFAAmF,CACpF,CAAC;gBACF,MAAM,iBAAiB,GAAG,MAAM,mBAAmB,CAAC,YAAY,EAAE,CAAC;gBACnE,OAAO,CAAC,GAAG,CACT,sCAAsC,EACtC,iBAAiB,CAAC,MAAM,EACxB,wBAAwB,CACzB,CAAC;gBACF,OAAO,CAAC,OAAO,CAAC,YAAY,CAAC,CAAC;gBAE9B,YAAY,CAAC,iBAAiB,CAAC,CAAC;gBAChC,cAAc,CAAC,IAAI,IAAI,EAAE,CAAC,CAAC;YAC7B,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,OAAO,CAAC,KAAK,CAAC,iCAAiC,EAAE,KAAK,CAAC,CAAC;gBACxD,MAAM,IAAI,KAAK,CACb,KAAK,YAAY,KAAK;oBACpB,CAAC,CAAC,sBAAsB,KAAK,CAAC,OAAO,EAAE;oBACvC,CAAC,CAAC,+BAA+B,CACpC,CAAC;YACJ,CAAC;QACH,CAAC,CAAC,CAAC;IACL,CAAC,EAAE,CAAC,WAAW,CAAC,CAAC,CAAC;IAElB,6BAA6B;IAC7B,SAAS,CAAC,GAAG,EAAE;QACb,WAAW,EAAE,CAAC;IAChB,CAAC,EAAE,CAAC,WAAW,CAAC,CAAC,CAAC;IAElB,OAAO;QACL,MAAM;QACN,kBAAkB;QAClB,SAAS;QACT,gBAAgB;QAChB,kBAAkB;QAClB,WAAW;QACX,SAAS;QACT,KAAK;QACL,WAAW;QACX,UAAU;KACX,CAAC;AACJ,CAAC,CAAC;AAEF,eAAe,uBAAuB,CAAC"}