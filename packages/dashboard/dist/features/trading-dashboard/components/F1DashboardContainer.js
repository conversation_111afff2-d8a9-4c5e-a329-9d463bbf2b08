import { jsx as _jsx, jsxs as _jsxs } from "react/jsx-runtime";
/**
 * F1DashboardContainer Component
 *
 * REFACTORED FROM: TradingDashboard.tsx (388 lines → focused components)
 * Main orchestrator for trading dashboard with F1 container pattern.
 *
 * BENEFITS:
 * - Uses F1Container for consistent styling
 * - Separates orchestration from presentation
 * - Better error handling and loading states
 * - Follows proven container pattern
 * - F1 racing theme integration
 */
import { Suspense, useState } from 'react';
import styled from 'styled-components';
import { useTradingDashboard } from '../hooks/useTradingDashboard';
import { F1DashboardHeader } from './F1DashboardHeader';
import { F1DashboardTabs } from './F1DashboardTabs';
import { useDashboardNavigation } from './useDashboardNavigation';
import { DashboardTabContentRenderer } from './dashboardTabConfig';
const Container = styled.div `
  display: flex;
  flex-direction: column;
  gap: ${({ theme }) => theme.spacing?.lg || '24px'};
  background: ${({ theme }) => theme.colors?.background || '#0f0f0f'};
  color: ${({ theme }) => theme.colors?.textPrimary || '#ffffff'};
  min-height: 100vh;
  padding: ${({ theme }) => theme.spacing?.lg || '24px'};
  max-width: 1400px;
  margin: 0 auto;
`;
const ContentArea = styled.div `
  display: flex;
  flex-direction: column;
  gap: ${({ theme }) => theme.spacing?.lg || '24px'};
  flex: 1;
`;
const TabContentContainer = styled.div `
  animation: fadeIn 0.3s ease-in-out;

  @keyframes fadeIn {
    from {
      opacity: 0;
      transform: translateY(10px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }
`;
const LoadingState = styled.div `
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: ${({ theme }) => theme.spacing?.xl || '48px'};
  text-align: center;
  min-height: 400px;
`;
const LoadingIcon = styled.div `
  font-size: 48px;
  margin-bottom: ${({ theme }) => theme.spacing?.md || '12px'};
  opacity: 0.7;
  animation: pulse 2s infinite;

  @keyframes pulse {
    0%,
    100% {
      opacity: 0.7;
    }
    50% {
      opacity: 0.3;
    }
  }
`;
const LoadingText = styled.p `
  font-size: ${({ theme }) => theme.fontSizes?.lg || '1.125rem'};
  color: ${({ theme }) => theme.colors?.textSecondary || '#9ca3af'};
  margin: 0;
`;
const ErrorState = styled.div `
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: ${({ theme }) => theme.spacing?.xl || '48px'};
  text-align: center;
  min-height: 400px;
  background: ${({ theme }) => theme.colors?.error || '#ef4444'}10;
  border: 1px solid ${({ theme }) => theme.colors?.error || '#ef4444'}40;
  border-radius: ${({ theme }) => theme.borderRadius?.lg || '8px'};
  margin: ${({ theme }) => theme.spacing?.lg || '24px'} 0;
`;
const ErrorIcon = styled.div `
  font-size: 48px;
  margin-bottom: ${({ theme }) => theme.spacing?.md || '12px'};
  color: ${({ theme }) => theme.colors?.error || '#ef4444'};
`;
const ErrorTitle = styled.h3 `
  font-size: ${({ theme }) => theme.fontSizes?.lg || '1.125rem'};
  font-weight: 600;
  color: ${({ theme }) => theme.colors?.error || '#ef4444'};
  margin: 0 0 ${({ theme }) => theme.spacing?.sm || '8px'} 0;
`;
const ErrorMessage = styled.p `
  font-size: ${({ theme }) => theme.fontSizes?.sm || '0.875rem'};
  color: ${({ theme }) => theme.colors?.textSecondary || '#9ca3af'};
  margin: 0;
  max-width: 400px;
`;
const RetryButton = styled.button `
  margin-top: ${({ theme }) => theme.spacing?.md || '12px'};
  padding: ${({ theme }) => theme.spacing?.sm || '8px'}
    ${({ theme }) => theme.spacing?.md || '12px'};
  background: ${({ theme }) => theme.colors?.primary || '#dc2626'};
  color: white;
  border: none;
  border-radius: ${({ theme }) => theme.borderRadius?.md || '6px'};
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;

  &:hover {
    background: ${({ theme }) => theme.colors?.primaryDark || '#b91c1c'};
    transform: translateY(-1px);
  }
`;
const LoadingOverlay = styled.div `
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: ${({ theme }) => theme.zIndex?.modal || 1000};
`;
const LoadingSpinner = styled.div `
  border: 4px solid rgba(255, 255, 255, 0.3);
  border-top: 4px solid ${({ theme }) => theme.colors?.primary || '#dc2626'};
  border-radius: 50%;
  width: 40px;
  height: 40px;
  animation: spin 1s linear infinite;

  @keyframes spin {
    0% {
      transform: rotate(0deg);
    }
    100% {
      transform: rotate(360deg);
    }
  }
`;
/**
 * LoadingFallback Component
 */
const LoadingFallback = () => (_jsxs(LoadingState, { children: [_jsx(LoadingIcon, { children: "\uD83C\uDFCE\uFE0F" }), _jsx(LoadingText, { children: "Loading Trading Dashboard..." })] }));
/**
 * ErrorFallback Component
 */
const ErrorFallback = ({ error, onRetry }) => (_jsxs(ErrorState, { children: [_jsx(ErrorIcon, { children: "\u26A0\uFE0F" }), _jsx(ErrorTitle, { children: "Dashboard Error" }), _jsx(ErrorMessage, { children: error }), _jsx(RetryButton, { onClick: onRetry, children: "Try Again" })] }));
/**
 * DashboardContent Component
 */
const DashboardContent = ({ initialTab }) => {
    const { trades, performanceMetrics, chartData, setupPerformance, sessionPerformance, isLoading, error, fetchDashboardData, } = useTradingDashboard();
    const { activeTab, setActiveTab } = useDashboardNavigation({
        defaultTab: initialTab || 'summary',
    });
    // Trade form state for Analytics tab
    const [tradeFormValues, setTradeFormValues] = useState({
        date: new Date().toISOString().split('T')[0],
        symbol: 'MNQ',
        direction: 'long',
        quantity: '1',
        entryPrice: '0',
        exitPrice: '0',
        profit: '0',
        model: '',
        session: '',
        setup: '',
        patternQuality: '',
        dolTarget: '',
        rdType: '',
        drawOnLiquidity: '',
        entryVersion: '',
        notes: '',
        tags: [],
        result: 'win',
    });
    const handleTradeFormChange = (e) => {
        const { name, value, type } = e.target;
        setTradeFormValues((prev) => ({
            ...prev,
            [name]: type === 'number' ? value : value,
        }));
    };
    // Prepare data and handlers for tab content
    const tabContentProps = {
        activeTab,
        data: {
            trades,
            performanceMetrics,
            chartData,
            setupPerformance,
            sessionPerformance,
        },
        isLoading,
        error,
        tradeFormValues,
        handleTradeFormChange,
    };
    if (error) {
        return _jsx(ErrorFallback, { error: error, onRetry: fetchDashboardData });
    }
    return (_jsxs(Container, { children: [_jsx(F1DashboardHeader, { isLoading: isLoading, sessionNumber: 1, isLiveSession: true, onRefresh: fetchDashboardData }), _jsx(F1DashboardTabs, { activeTab: activeTab, onTabChange: setActiveTab, disabled: isLoading }), _jsx(ContentArea, { children: _jsx(TabContentContainer, { children: _jsx(Suspense, { fallback: _jsx(LoadingFallback, {}), children: _jsx(DashboardTabContentRenderer, { ...tabContentProps }) }) }) }), isLoading && (_jsx(LoadingOverlay, { children: _jsx(LoadingSpinner, {}) }))] }));
};
/**
 * F1DashboardContainer Component
 *
 * PATTERN: F1 Container Pattern
 * - Error boundaries and loading states
 * - Consistent F1 styling and theme
 * - Proper separation of concerns
 * - Suspense for code splitting
 */
export const F1DashboardContainer = (props) => {
    return (_jsx(Suspense, { fallback: _jsx(LoadingFallback, {}), children: _jsx(DashboardContent, { ...props }) }));
};
export default F1DashboardContainer;
//# sourceMappingURL=F1DashboardContainer.js.map