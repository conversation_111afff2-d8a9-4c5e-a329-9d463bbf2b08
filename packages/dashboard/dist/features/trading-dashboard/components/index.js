/**
 * Trading Dashboard Components
 *
 * Centralized exports for all trading dashboard components.
 * Part of the refactoring to reduce coupling and improve maintainability.
 */
// Phase 1: Foundation Components (LOW RISK)
export { default as F1Header } from './F1Header';
export { default as DashboardTabs } from './DashboardTabs';
// Phase 3: Form & Integration Components (HIGH RISK)
export { default as QuickTradeForm } from './QuickTradeForm';
export { default as TradingDashboardContainer } from './TradingDashboardContainer';
// Context exports
export { TradingDashboardProvider, useTradingDashboardContext, useTradingDashboardTabs, useTradingDashboardSession, } from '../context/TradingDashboardContext';
// Hook exports (remove duplicate useTradingDashboardData)
export { useTradingDashboardData } from '../hooks/useTradingDashboardData';
//# sourceMappingURL=index.js.map