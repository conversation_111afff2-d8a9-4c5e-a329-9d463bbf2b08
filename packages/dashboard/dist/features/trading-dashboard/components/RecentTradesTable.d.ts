/**
 * Recent Trades Table Component
 *
 * Displays a table of recent trades
 */
import React from 'react';
import { CompleteTradeData } from '@adhd-trading-dashboard/shared';
interface RecentTradesTableProps {
    trades: CompleteTradeData[];
    isLoading?: boolean;
}
/**
 * RecentTradesTable Component
 *
 * Displays a table of recent trades with sorting functionality
 */
export declare const RecentTradesTable: React.FC<RecentTradesTableProps>;
export default RecentTradesTable;
//# sourceMappingURL=RecentTradesTable.d.ts.map