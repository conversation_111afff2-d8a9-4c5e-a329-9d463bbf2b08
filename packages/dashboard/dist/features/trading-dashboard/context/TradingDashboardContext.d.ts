/**
 * TradingDashboardContext
 *
 * EXTRACTED FROM: TradingDashboard.tsx state management
 * Centralized state management for the trading dashboard with performance optimization.
 *
 * BENEFITS:
 * - Eliminates prop drilling
 * - Centralized state management
 * - Performance optimized with useMemo
 * - Type-safe context values
 * - Proper cleanup and error handling
 */
import React, { ReactNode } from 'react';
import { TabType } from '../components/DashboardTabs';
import { CompleteTradeData } from '@adhd-trading-dashboard/shared';
import { PerformanceMetric, ChartDataPoint, SetupPerformance, SessionPerformance } from '../types';
export interface TradingDashboardState {
    trades: CompleteTradeData[];
    performanceMetrics: PerformanceMetric[];
    chartData: ChartDataPoint[];
    setupPerformance: SetupPerformance[];
    sessionPerformance: SessionPerformance[];
    activeTab: TabType;
    isLoading: boolean;
    error: string | null;
    lastUpdated: Date | null;
}
export interface TradingDashboardActions {
    setActiveTab: (tab: TabType) => void;
    refreshData: () => Promise<void>;
    clearError: () => void;
    isLiveSession: () => boolean;
    getCurrentSessionName: () => string;
}
export type TradingDashboardContextValue = TradingDashboardState & TradingDashboardActions;
declare const TradingDashboardContext: React.Context<TradingDashboardContextValue>;
export interface TradingDashboardProviderProps {
    children: ReactNode;
    /** Initial state values */
    initialState?: Partial<TradingDashboardState>;
    /** Custom data fetching function */
    dataFetcher?: () => Promise<{
        trades: CompleteTradeData[];
        performanceMetrics: PerformanceMetric[];
        chartData: ChartDataPoint[];
        setupPerformance: SetupPerformance[];
        sessionPerformance: SessionPerformance[];
    }>;
}
/**
 * TradingDashboardProvider Component
 *
 * Provides centralized state management for the trading dashboard.
 * Uses performance optimization techniques to prevent unnecessary re-renders.
 */
export declare const TradingDashboardProvider: React.FC<TradingDashboardProviderProps>;
/**
 * Hook to use TradingDashboardContext
 *
 * @throws Error if used outside of TradingDashboardProvider
 * @returns TradingDashboardContextValue
 *
 * @example
 * ```typescript
 * const { trades, activeTab, setActiveTab, refreshData } = useTradingDashboardContext();
 * ```
 */
export declare const useTradingDashboardContext: () => TradingDashboardContextValue;
/**
 * Convenience hooks for specific parts of the context
 */
/**
 * Hook for tab management
 */
export declare const useTradingDashboardTabs: () => {
    activeTab: TabType;
    setActiveTab: (tab: TabType) => void;
};
/**
 * Hook for data management (context-based)
 */
export declare const useTradingDashboardDataFromContext: () => {
    trades: CompleteTradeData[];
    performanceMetrics: PerformanceMetric[];
    chartData: ChartDataPoint[];
    setupPerformance: SetupPerformance[];
    sessionPerformance: SessionPerformance[];
    isLoading: boolean;
    error: string;
    lastUpdated: Date;
    refreshData: () => Promise<void>;
    clearError: () => void;
};
/**
 * Hook for session management
 */
export declare const useTradingDashboardSession: () => {
    isLive: boolean;
    sessionName: string;
};
export default TradingDashboardContext;
//# sourceMappingURL=TradingDashboardContext.d.ts.map