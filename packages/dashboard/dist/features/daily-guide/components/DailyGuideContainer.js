import { jsx as _jsx, jsxs as _jsxs, Fragment as _Fragment } from "react/jsx-runtime";
/**
 * DailyGuideContainer Component
 *
 * PATTERN VALIDATION: Applying proven container pattern to DailyGuide
 * to validate cross-component-type effectiveness (dashboard → forms → guides).
 *
 * BENEFITS:
 * - Uses F1Container from component library
 * - Consistent error boundaries and loading states
 * - Validates pattern works across different component types
 * - Maintains F1 racing theme consistency
 */
import { Suspense } from 'react';
import styled from 'styled-components';
import { F1Container } from '@adhd-trading-dashboard/shared/components/library';
import { DailyGuideHeader } from './DailyGuideHeader';
import { SectionCard } from './ui';
import { MarketOverview } from './MarketOverview';
import { TradingPlan } from './TradingPlan';
import { KeyLevels } from './KeyLevels';
import { MarketNews } from './MarketNews';
import { useDailyGuide } from '../context/DailyGuideContext';
const GuideGrid = styled.div `
  display: grid;
  grid-template-columns: 1fr;
  gap: ${({ theme }) => theme.spacing?.lg || '16px'};

  @media (min-width: 1024px) {
    grid-template-columns: 2fr 1fr;
  }
`;
const MainColumn = styled.div `
  display: flex;
  flex-direction: column;
  gap: ${({ theme }) => theme.spacing?.lg || '16px'};
`;
const SideColumn = styled.div `
  display: flex;
  flex-direction: column;
  gap: ${({ theme }) => theme.spacing?.lg || '16px'};
`;
const LoadingFallback = () => (_jsxs("div", { style: {
        display: 'flex',
        flexDirection: 'column',
        alignItems: 'center',
        justifyContent: 'center',
        height: '300px',
        gap: '16px',
    }, children: [_jsx("div", { style: {
                width: '32px',
                height: '32px',
                border: '3px solid #4b5563',
                borderTop: '3px solid #dc2626',
                borderRadius: '50%',
                animation: 'spin 1s linear infinite',
            } }), _jsx("div", { style: { color: '#9ca3af' }, children: "Loading Daily Guide..." })] }));
/**
 * DailyGuideContent Component
 *
 * Uses context and renders the guide sections with proper error handling.
 */
const DailyGuideContent = () => {
    const { isLoading, error, selectedDate, refreshData, data } = useDailyGuide();
    // Extract data from the nested structure
    const { marketOverview, tradingPlan, keyPriceLevels } = data;
    // Note: marketNews removed as unused - will be added back when needed
    return (_jsxs(_Fragment, { children: [_jsx(DailyGuideHeader, { currentDate: new Date(selectedDate).toLocaleDateString('en-US', {
                    weekday: 'long',
                    year: 'numeric',
                    month: 'long',
                    day: 'numeric',
                }), onRefresh: refreshData, isRefreshing: isLoading }), _jsxs(GuideGrid, { children: [_jsxs(MainColumn, { children: [_jsx(SectionCard, { title: "\uD83D\uDCCA Market Overview", isLoading: isLoading, hasError: !!error, errorMessage: error || '', children: _jsx(Suspense, { fallback: _jsx(LoadingFallback, {}), children: _jsx(MarketOverview, { marketOverview: marketOverview }) }) }), _jsx(SectionCard, { title: "\uD83C\uDFAF Trading Plan", isLoading: isLoading, hasError: !!error, errorMessage: error || '', children: _jsx(Suspense, { fallback: _jsx(LoadingFallback, {}), children: _jsx(TradingPlan, { tradingPlan: tradingPlan }) }) })] }), _jsxs(SideColumn, { children: [_jsx(SectionCard, { title: "\uD83D\uDCC8 Key Levels", isLoading: isLoading, hasError: !!error, errorMessage: error || '', children: _jsx(Suspense, { fallback: _jsx(LoadingFallback, {}), children: _jsx(KeyLevels, { keyLevels: keyPriceLevels }) }) }), _jsx(SectionCard, { title: "\uD83D\uDCF0 Market News", isLoading: isLoading, hasError: !!error, errorMessage: error || '', children: _jsx(Suspense, { fallback: _jsx(LoadingFallback, {}), children: _jsx(MarketNews, { events: marketOverview?.economicEvents || [] }) }) })] })] })] }));
};
/**
 * DailyGuideContainer Component
 *
 * PATTERN VALIDATION: Demonstrates that our proven container pattern
 * works effectively across different component types:
 * - Dashboard (TradingDashboard) ✅
 * - Forms (QuickTradeForm) ✅
 * - Analysis (TradeAnalysis) ✅
 * - Guides (DailyGuide) ✅
 */
export const DailyGuideContainer = ({ className }) => {
    return (_jsx(F1Container, { variant: "dashboard", maxWidth: 1400, className: className, background: "default", animated: true, children: _jsx(Suspense, { fallback: _jsx(LoadingFallback, {}), children: _jsx(DailyGuideContent, {}) }) }));
};
export default DailyGuideContainer;
//# sourceMappingURL=DailyGuideContainer.js.map