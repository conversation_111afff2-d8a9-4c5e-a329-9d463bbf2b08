import { jsx as _jsx, jsxs as _jsxs } from "react/jsx-runtime";
import { Card, Badge } from '@adhd-trading-dashboard/shared';
import styled from 'styled-components';
// Styled components
const Container = styled.div `
  display: flex;
  flex-direction: column;
  gap: ${({ theme }) => theme.spacing.md};
`;
const LevelsGrid = styled.div `
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: ${({ theme }) => theme.spacing.md};
`;
const LevelCard = styled.div `
  background-color: ${({ theme }) => theme.colors.background};
  border-radius: ${({ theme }) => theme.borderRadius.sm};
  padding: ${({ theme }) => theme.spacing.md};
  box-shadow: ${({ theme }) => theme.shadows.sm};
`;
const Symbol = styled.div `
  font-size: ${({ theme }) => theme.fontSizes.lg};
  font-weight: 600;
  color: ${({ theme }) => theme.colors.textPrimary};
  margin-bottom: ${({ theme }) => theme.spacing.md};
  display: flex;
  align-items: center;
`;
const LevelsRow = styled.div `
  display: flex;
  margin-bottom: ${({ theme }) => theme.spacing.xs};
`;
const LevelLabel = styled.div `
  width: 100px;
  font-size: ${({ theme }) => theme.fontSizes.sm};
  font-weight: 500;
  color: #9ca3af;
`;
const LevelValue = styled.div `
  font-size: ${({ theme }) => theme.fontSizes.sm};
  color: ${({ theme, type }) => {
    switch (type) {
        case 'support':
            return theme.colors.profit;
        case 'resistance':
            return theme.colors.loss;
        case 'pivot':
            return theme.colors.accent;
        default:
            return theme.colors.textPrimary;
    }
}};
`;
const EmptyState = styled.div `
  padding: ${({ theme }) => theme.spacing.lg};
  text-align: center;
  color: #9ca3af;
  font-style: italic;
`;
/**
 * Key Levels Component
 *
 * A component for displaying key price levels.
 */
export const KeyLevels = ({ keyLevels, isLoading = false, error = null, onRefresh, className, }) => {
    // Loading state
    if (isLoading) {
        return (_jsx(Card, { title: "Key Price Levels", children: _jsx("div", { style: { padding: '24px', textAlign: 'center' }, children: "Loading key levels..." }) }));
    }
    // Error state
    if (error) {
        return (_jsx(Card, { title: "Key Price Levels", children: _jsxs("div", { style: { padding: '24px', textAlign: 'center', color: '#f44336' }, children: ["Error: ", error, onRefresh && (_jsx("button", { onClick: onRefresh, style: {
                            marginLeft: '16px',
                            padding: '8px 16px',
                            background: '#f0f0f0',
                            border: 'none',
                            borderRadius: '4px',
                            cursor: 'pointer',
                        }, children: "Retry" }))] }) }));
    }
    // Empty state
    if (!keyLevels || keyLevels.length === 0) {
        return (_jsx(Card, { title: "Key Price Levels", children: _jsxs(EmptyState, { children: ["No key price levels available.", onRefresh && (_jsx("div", { style: { marginTop: '16px' }, children: _jsx("button", { onClick: onRefresh, style: {
                                padding: '8px 16px',
                                background: '#f0f0f0',
                                border: 'none',
                                borderRadius: '4px',
                                cursor: 'pointer',
                            }, children: "Refresh" }) }))] }) }));
    }
    return (_jsx(Card, { title: "Key Price Levels", actions: onRefresh ? (_jsx("button", { onClick: onRefresh, style: { background: 'none', border: 'none', cursor: 'pointer', color: 'inherit' }, children: "\uD83D\uDD04 Refresh" })) : undefined, children: _jsx(Container, { className: className, children: _jsx(LevelsGrid, { children: keyLevels.map((level, index) => (_jsxs(LevelCard, { children: [_jsxs(Symbol, { children: [level.symbol, _jsxs(Badge, { variant: "primary", style: { marginLeft: '8px' }, children: [level.support.length + level.resistance.length, " levels"] })] }), _jsxs(LevelsRow, { children: [_jsx(LevelLabel, { children: "Resistance" }), _jsx(LevelValue, { type: "resistance", children: level.resistance.join(' | ') })] }), level.pivotPoint && (_jsxs(LevelsRow, { children: [_jsx(LevelLabel, { children: "Pivot" }), _jsx(LevelValue, { type: "pivot", children: level.pivotPoint })] })), _jsxs(LevelsRow, { children: [_jsx(LevelLabel, { children: "Support" }), _jsx(LevelValue, { type: "support", children: level.support.join(' | ') })] })] }, index))) }) }) }));
};
//# sourceMappingURL=KeyLevels.js.map