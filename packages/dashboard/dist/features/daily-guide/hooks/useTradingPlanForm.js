/**
 * useTradingPlanForm Hook
 *
 * Custom hook for managing trading plan form state and logic.
 * Extracted from the original TradingPlan component for better separation of concerns.
 */
import { useState, useCallback } from 'react';
/**
 * Initial state for a new trading plan item
 */
const getInitialItem = () => ({
    description: '',
    priority: 'medium',
    completed: false,
});
/**
 * useTradingPlanForm Hook
 *
 * Manages form state and validation for adding new trading plan items.
 * Provides a clean API for form interactions and state management.
 */
export const useTradingPlanForm = (onItemAdd) => {
    const [showAddForm, setShowAddForm] = useState(false);
    const [newItem, setNewItem] = useState(getInitialItem());
    /**
     * Reset form to initial state
     */
    const resetForm = useCallback(() => {
        setNewItem(getInitialItem());
    }, []);
    /**
     * Handle form submission
     */
    const handleAddItem = useCallback((e) => {
        e.preventDefault();
        // Validate form
        if (!newItem.description.trim() || !onItemAdd) {
            return;
        }
        // Create the new item with a unique ID
        const itemToAdd = {
            ...newItem,
            id: `item-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
            description: newItem.description.trim(),
        };
        // Call the callback
        onItemAdd(itemToAdd);
        // Reset form state
        resetForm();
        setShowAddForm(false);
    }, [newItem, onItemAdd, resetForm]);
    /**
     * Check if form is valid
     */
    const isValid = newItem.description.trim().length > 0;
    return {
        showAddForm,
        setShowAddForm,
        newItem,
        setNewItem,
        handleAddItem,
        resetForm,
        isValid,
    };
};
export default useTradingPlanForm;
//# sourceMappingURL=useTradingPlanForm.js.map