# System Architecture

> **🏗️ Comprehensive guide to the ADHD Trading Dashboard architecture**

## 🎯 Architecture Overview

The ADHD Trading Dashboard is built as a **modern monorepo** with a clean, scalable architecture designed for maintainability, performance, and extensibility. The system follows established patterns and best practices for React applications while being optimized for trading-specific workflows.

## 📦 Monorepo Structure

### Package Organization

```
packages/
├── shared/              # Foundation layer - reusable components & utilities
└── dashboard/           # Application layer - features & business logic
```

### Dependency Flow

```
shared → dashboard
```

This unidirectional dependency ensures:

- **Clean separation** - Each package has clear responsibilities
- **Reusability** - Shared components can be used across features
- **Maintainability** - Changes flow in predictable directions
- **Testability** - Packages can be tested in isolation

## 🔧 Package Details

### Shared Package (`packages/shared/`)

**Purpose**: Foundation layer providing reusable components, utilities, and infrastructure

**Structure**:

```
packages/shared/src/
├── components/         # UI component library
│   ├── atoms/          # Basic elements (Button, Input, Select)
│   ├── molecules/      # Composite components (Card, FormField)
│   └── organisms/      # Complex components (DataTable, Chart)
├── hooks/              # Custom React hooks
├── services/           # Core services (storage, data management)
├── state/              # State management utilities
├── theme/              # Design system and theming
├── types/              # Shared TypeScript types
└── utils/              # Pure utility functions
```

**Key Responsibilities**:

- Atomic design component library
- IndexedDB storage abstraction
- Theme system (Formula 1 inspired design)
- State management utilities
- Common hooks and utilities

### Dashboard Package (`packages/dashboard/`)

**Purpose**: Application layer containing trading-specific features and business logic

**Structure**:

```
packages/dashboard/src/
├── features/           # Feature-based organization
│   ├── trade-analysis/ # Trading performance analysis
│   ├── trade-journal/  # Trade logging and journaling
│   └── daily-guide/    # Daily planning and market overview
├── layouts/            # Application layouts
├── pages/              # Route-level components
└── routes/             # Route definitions and configuration
```

**Key Responsibilities**:

- Trading-specific features and workflows
- Application routing and navigation
- Feature-specific state management
- Business logic and data processing

## 🎨 Design Patterns

### Atomic Design

Components are organized using atomic design principles:

1. **Atoms** (`shared/components/atoms/`)

   - Basic HTML elements (Button, Input, Typography)
   - No business logic, pure presentation
   - Highly reusable across features

2. **Molecules** (`shared/components/molecules/`)

   - Combinations of atoms (FormField, Card, Modal)
   - Simple interactive behavior
   - Domain-agnostic functionality

3. **Organisms** (`shared/components/organisms/`)

   - Complex combinations of molecules and atoms
   - Feature-specific behavior (DataTable, Charts)
   - Reusable across similar use cases

4. **Templates & Pages** (`dashboard/layouts/` & `dashboard/pages/`)
   - Layout structures and full page implementations
   - Business logic integration
   - Feature-specific implementations

### Feature-Based Architecture

Each feature in `dashboard/packages/dashboard/src/features/ follows a consistent structure:

```
feature-name/
├── components/         # Feature-specific UI components
├── hooks/              # Feature-specific custom hooks
├── services/           # API integration and business logic
├── types/              # Feature-specific type definitions
├── utils/              # Feature-specific utilities
├── __tests__/          # Feature tests
├── FeatureName.tsx     # Main feature component
└── index.ts            # Feature exports
```

**Benefits**:

- **Isolation** - Features can be developed independently
- **Reusability** - Common patterns across features
- **Maintainability** - Easy to locate and modify feature code
- **Testing** - Clear boundaries for unit and integration tests

## 🔄 State Management

### Context-Based Architecture

The application uses React Context for state management with these principles:

1. **Feature Isolation** - Each feature manages its own state
2. **Shared State** - Common state lives in shared contexts
3. **Type Safety** - All contexts are fully typed with TypeScript
4. **Performance** - Memoized selectors prevent unnecessary re-renders

### State Layers

```
Application State
├── Storage State (shared/services/tradeStorage)
├── Theme State (shared/theme/)
├── Feature State (dashboard/features/*/hooks/)
└── Component State (local state)
```

### Data Flow

The simplified data flow follows this pattern:

```
Component → TradeStorageService → IndexedDB
```

### State Management Utilities (`shared/state/`)

Provides utilities for:

- Creating typed context stores
- Memoized selectors for derived state
- State persistence with versioning
- Action creators and reducers

## 🗄️ Data Storage Architecture

### IndexedDB-Based Storage (`shared/services/`)

The application uses a client-side storage architecture with IndexedDB:

- **TradeStorageService** - Primary service for trade data management
- **Local Storage** - User preferences and application settings
- **Mock Data** - Development and testing with realistic trading scenarios

### Database Schema

The IndexedDB implementation uses a 4-table relational structure:

1. **trades** - Core trade information (symbol, prices, quantities)
2. **trade_analysis** - Trade analysis and quality ratings
3. **trade_setups** - Setup details and confluence factors
4. **trade_fvg_details** - Fair Value Gap and Draw on Liquidity data

### Key Components

1. **TradeStorageService** (`services/tradeStorage.ts`)

   - CRUD operations for all trade-related data
   - Complex queries and filtering
   - Data validation and transformation

2. **Storage Hooks** (`hooks/`)

   - React hooks for data management
   - Loading states and error handling
   - Optimistic updates and caching

3. **Mock Data Provider** (`services/mockData.ts`)
   - Development data generation
   - Realistic trading scenarios
   - Testing data consistency

## 🎨 Theme System

### Formula 1 Inspired Design

The theme system is built around Formula 1 racing aesthetics:

- **Colors** - Red, black, white primary palette with accent colors
- **Typography** - Clean, technical fonts optimized for data readability
- **Spacing** - Consistent 8px grid system
- **Motion** - Fast, precise animations inspired by racing performance

### Theme Structure (`shared/theme/`)

```
theme/
├── tokens/             # Design tokens (colors, spacing, typography)
├── components/         # Component-specific theme overrides
├── variants/           # Theme variants (F1, light, dark)
└── providers/          # Theme context and switching logic
```

### Design Tokens

- **Colors** - Semantic color system with accessibility compliance
- **Typography** - Scale of font sizes, weights, and line heights
- **Spacing** - Consistent spacing scale for layouts
- **Shadows** - Elevation system for visual hierarchy
- **Borders** - Border styles and radius values

## 🔧 Build Architecture

### TypeScript Configuration

Project uses TypeScript project references for optimal build performance:

```
tsconfig.json          # Root configuration
├── packages/shared/tsconfig.json     # Shared package config
└── packages/dashboard/tsconfig.json  # Dashboard package config
```

**Benefits**:

- **Incremental compilation** - Only rebuild changed packages
- **Type checking** - Cross-package type validation
- **Development speed** - Faster builds and better IDE support

### Build Tools

- **Vite** - Fast development server with HMR
- **TypeScript** - Type checking and compilation
- **Babel** - Code transformation and optimization
- **ESLint** - Code quality and consistency
- **Prettier** - Code formatting

### Bundle Strategy

- **Code Splitting** - Features loaded on demand
- **Tree Shaking** - Remove unused code
- **Asset Optimization** - Compress images and fonts
- **Modern Builds** - Optimized for modern browsers with ES modules

## 🧪 Testing Architecture

### Testing Strategy

1. **Unit Tests** (Vitest)

   - Component testing with React Testing Library
   - Hook testing with custom test utilities
   - Utility function testing

2. **Integration Tests** (Vitest)

   - Feature workflow testing
   - API integration testing
   - State management testing

3. **End-to-End Tests** (Playwright)

   - Complete user workflows
   - Cross-browser compatibility
   - Performance testing

4. **Visual Testing** (Storybook)
   - Component development and documentation
   - Visual regression testing
   - Design system validation

### Test Organization

```
__tests__/              # Test files co-located with source
├── components/         # Component tests
├── hooks/              # Hook tests
├── services/           # Service tests
└── utils/              # Utility tests
```

## 🚀 Performance Considerations

### Optimization Strategies

1. **Bundle Optimization**

   - Code splitting by routes and features
   - Dynamic imports for heavy components
   - Tree shaking to remove unused code

2. **Runtime Performance**

   - Memoized components and selectors
   - Virtualized lists for large datasets
   - Debounced search and filtering

3. **Loading Strategy**
   - Progressive loading of features
   - Skeleton screens for better perceived performance
   - Preloading of critical resources

### Browser Performance Considerations

- **IndexedDB Limits** - Browser storage quotas and performance
- **Memory Management** - Efficient handling of large datasets
- **Offline Functionality** - Service worker caching strategies

**Solutions**:

- Efficient IndexedDB queries and indexing
- Virtual scrolling for large data lists
- Progressive data loading and pagination

## 🔄 Development Workflow

### Development Server

```bash
yarn dev              # Start Vite dev server
yarn test:watch   # Watch mode type checking
yarn test:watch         # Watch mode testing
```

### Build Process

```bash
yarn build              # Build all packages
yarn build:shared       # Build shared package only
yarn build:dashboard    # Build dashboard package only
```

### Quality Assurance

```bash
yarn lint               # ESLint checking
yarn test               # Full test suite
yarn test         # TypeScript validation
yarn analyze     # Code health analysis
```

## 🔐 Security Considerations

### Data Handling

- **Input Validation** - All user inputs validated and sanitized
- **XSS Prevention** - Proper output encoding and CSP headers
- **Data Encryption** - Sensitive data encrypted at rest and in transit

### Data Security

- **Local Storage** - Client-side data encryption for sensitive information
- **Data Validation** - Input sanitization and validation
- **Privacy** - No external data transmission, fully offline-capable

### Code Security

- **Dependency Scanning** - Regular security audits of dependencies
- **Static Analysis** - Security-focused linting rules
- **Build Security** - Secure build pipeline and artifact storage

## 🔄 Deployment Architecture

### Environments

1. **Development** - Local development with mock data
2. **Staging** - Integration testing with production builds
3. **Production** - Static web application deployment

### Deployment Strategies

- **Static Site Hosting** - Deploy to Netlify, Vercel, or GitHub Pages
- **Web Server** - Serve as static files from any web server
- **Docker** - Containerized deployment for enterprise environments

### CI/CD Pipeline

```bash
Code Push → Tests → Build → Type Check → Deploy
```

## 📈 Scalability Considerations

### Horizontal Scaling

- **Feature Independence** - Features can be developed in parallel
- **Component Reusability** - Shared components reduce duplication
- **Package Modularity** - Easy to extract packages into separate repos

### Performance Scaling

- **Lazy Loading** - Load features on demand
- **IndexedDB Optimization** - Efficient queries and indexing strategies
- **Data Management** - Efficient data structures for large trading datasets

### Team Scaling

- **Clear Boundaries** - Package and feature boundaries enable team independence
- **Documentation** - Comprehensive docs reduce onboarding time
- **Tooling** - Automated testing and quality checks

---

**Next Steps**:

- [Getting Started Guide](./GETTING_STARTED.md) - Set up your development environment
- [Shared Package Source](../packages/shared/) - Explore the shared package code
- [Dashboard Package Source](../packages/dashboard/) - Explore the dashboard features code
