{"timestamp": "2025-05-28T18:48:22.408Z", "packages": {"root": {"version": "1.0.0", "dependencyCount": 62, "dependencies": {"core-js": "3.35.0", "react": "18.2.0", "react-dom": "18.2.0", "react-router-dom": "6.6.2", "recharts": "2.10.3", "styled-components": "5.3.6", "@babel/cli": "7.23.9", "@babel/core": "7.23.6", "@babel/plugin-proposal-class-properties": "7.18.6", "@babel/plugin-syntax-dynamic-import": "7.8.3", "@babel/plugin-transform-runtime": "7.23.6", "@babel/preset-env": "7.23.6", "@babel/preset-react": "7.23.3", "@babel/preset-typescript": "7.23.3", "@babel/runtime": "7.23.6", "@playwright/test": "1.40.1", "@storybook/addon-a11y": "7.6.7", "@storybook/addon-docs": "7.6.7", "@storybook/addon-essentials": "7.6.7", "@storybook/addon-interactions": "7.6.7", "@storybook/addon-links": "7.6.7", "@storybook/react": "7.6.7", "@storybook/react-webpack5": "7.6.7", "@testing-library/jest-dom": "5.16.5", "@testing-library/react": "13.4.0", "@testing-library/user-event": "13.5.0", "@types/node": "16.18.11", "@types/react": "18.0.28", "@types/react-dom": "18.0.11", "@types/react-router-dom": "5.3.3", "@types/styled-components": "5.1.26", "@typescript-eslint/eslint-plugin": "8.32.1", "@typescript-eslint/parser": "8.32.1", "@typescript-eslint/typescript-estree": "8.32.1", "@vitejs/plugin-react": "4.0.0", "acorn": "8.14.1", "babel-loader": "9.1.3", "babel-plugin-styled-components": "2.1.4", "chalk": "5.4.1", "commander": "14.0.0", "css-loader": "6.8.1", "css-minimizer-webpack-plugin": "5.0.1", "eslint": "8.56.0", "eslint-plugin-react": "7.33.2", "eslint-plugin-react-hooks": "4.6.0", "figlet": "1.8.1", "glob": "11.0.2", "html-webpack-plugin": "5.6.3", "mini-css-extract-plugin": "2.9.2", "rimraf": "6.0.1", "schema-utils": "3.3.0", "storybook": "7.6.7", "style-loader": "3.3.3", "terser-webpack-plugin": "5.3.14", "typescript": "^5.8.0", "vite": "4.3.1", "vite-plugin-dts": "4.5.4", "vitest": "0.30.1", "webpack": "5.89.0", "webpack-bundle-analyzer": "4.10.2", "webpack-cli": "5.1.4", "webpack-dev-server": "4.15.1"}}, "shared": {"version": "1.0.0", "dependencyCount": 7, "dependencies": {"@anthropic-ai/sdk": "0.52.0", "@babel/parser": "7.27.2", "@babel/traverse": "7.27.1", "canvas": "3.1.0", "react": "18.2.0", "react-dom": "18.2.0", "typescript": "^5.8.0"}}, "dashboard": {"version": "0.1.0", "dependencyCount": 34, "dependencies": {"@adhd-trading-dashboard/shared": "1.0.0", "@anthropic-ai/sdk": "0.52.0", "@babel/parser": "7.27.2", "@babel/traverse": "7.27.1", "canvas": "3.1.0", "express": "4.21.2", "idb": "8.0.3", "react-router-dom": "6.6.2", "@storybook/addon-essentials": "7.6.7", "@storybook/addon-interactions": "7.6.7", "@storybook/addon-links": "7.6.7", "@storybook/addon-onboarding": "^1.0.10", "@storybook/addon-a11y": "7.6.7", "@storybook/blocks": "^7.6.7", "@storybook/react": "7.6.7", "@storybook/react-vite": "^7.6.7", "@storybook/test": "^7.6.7", "@types/babel__traverse": "7.20.0", "@types/figlet": "1.5.0", "@types/inquirer": "9.0.0", "chalk": "5.4.1", "commander": "14.0.0", "figlet": "1.8.1", "glob": "11.0.2", "inquirer": "9.2.0", "react": "18.2.0", "react-dom": "18.2.0", "react-scripts": "5.0.1", "recharts": "2.10.3", "storybook": "7.6.7", "styled-components": "5.3.6", "typescript": "^5.8.0", "vite-plugin-pwa": "1.0.0", "web-vitals": "2.1.4"}}}, "summary": {"totalDependencies": 103, "uniqueDependencies": 80, "inconsistencies": 0}}