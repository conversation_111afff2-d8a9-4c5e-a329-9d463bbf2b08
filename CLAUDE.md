# ADHD Trading Dashboard - AI Assistant Guide

> **🤖 Instructions for AI assistants working with this codebase**

## 📋 Project Overview

This is a **React TypeScript monorepo** for a trading dashboard with Formula 1-inspired design. The project follows modern development practices with strict typing, atomic design principles, and comprehensive tooling.

## 🏗️ Architecture

### Monorepo Structure
- **`packages/shared/`** - Reusable components, hooks, theme system, utilities
- **`packages/dashboard/`** - Main trading application with features and pages

### Dependency Flow
```
shared → dashboard
```

### Key Patterns
- **Atomic Design**: Components organized as atoms → molecules → organisms
- **Feature-Based**: Features isolated in `packages/dashboard/src/features/
- **TypeScript First**: Strict typing throughout the codebase
- **Context State**: React Context for state management

## 🛠️ Development Commands

- `yarn dev`
- `yarn dev`
- `yarn dev:storybook`
- `yarn build`
- `yarn build:clean`
- `yarn build:dev`
- `yarn build:watch`
- `yarn build:validate`
- `yarn build:shared`
- `yarn build:dashboard`
- `yarn test`
- `yarn test:watch`
- `yarn typescript:optimize`
- `yarn typescript:validate`
- `yarn deps:check`
- `yarn deps:sync`
- `yarn deps:outdated`
- `yarn deps:audit`
- `yarn deps:optimize`
- `yarn deps:report`
- `yarn test`
- `yarn test:watch`
- `yarn test:component`
- `yarn test:e2e`
- `yarn test:coverage`
- `yarn test:performance`
- `yarn test:all`
- `yarn lint`
- `yarn storybook`
- `yarn build-storybook`
- `yarn analyze`
- `yarn analyze:data-flow`
- `yarn analyze:components`
- `yarn analyze:state`
- `yarn analyze:performance`
- `yarn analyze:generate-docs`
- `yarn docs`
- `yarn docs:check`
- `yarn cleanup`
- `yarn cleanup:legacy`
- `yarn health`
- `yarn health:fix`
- `yarn setup`
- `yarn clean`
- `yarn clean:deep`
- `yarn // Enhanced Development Tools`
- `yarn // Legacy Scripts`
- `yarn deps:check`
- `yarn deps:sync`
- `yarn manage-assets`
- `yarn health`

## 📁 Important Directories

```
packages/
├── shared/src/
│   ├── components/     # Atomic design components
│   ├── hooks/         # Custom React hooks  
│   ├── theme/         # F1-inspired theme system
│   ├── types/         # TypeScript type definitions
│   └── utils/         # Utility functions
└── dashboard/packages/dashboard/src/
    ├── features/      # Trading features (trade-journal, trade-analysis, etc.)
    ├── pages/         # Route-level components
    ├── layouts/       # Application layouts
    └── routes/        # Route definitions
```

## 🎯 Development Guidelines

### Code Quality
- Use TypeScript for all new code
- Follow atomic design principles for components
- Implement proper error boundaries
- Write tests for critical functionality

### Architecture Principles
- Keep features isolated and self-contained
- Use shared components from the shared package
- Follow the established folder structure
- Maintain clear separation of concerns

### Performance
- Use React.memo for expensive components
- Implement proper loading states
- Optimize bundle size with code splitting
- Use IndexedDB for client-side data persistence

## 🔧 Common Tasks

### Adding New Components
1. Determine atomic level (atom/molecule/organism)
2. Place in appropriate package (`shared` for reusable, `dashboard` for specific)
3. Follow TypeScript interface patterns
4. Include proper JSDoc documentation

### Adding New Features
1. Create feature directory in `packages/dashboard/src/features/
2. Include: components, hooks, types, state (if needed)
3. Export through feature index file
4. Add route configuration if needed

### Debugging Issues
- Use `yarn health` for system diagnostics
- Check `yarn analyze` for architecture insights
- Validate documentation with `yarn docs:check`

## 📚 Documentation

- All documentation is in `docs/` directory
- Follow established documentation standards
- Update docs when making architectural changes
- Use `yarn docs:check` to validate accuracy

## ⚠️ Important Notes

- **Node.js v18 LTS** recommended for compatibility
- **Yarn workspaces** - always use yarn, not npm
- **ES Modules** - project uses ES module syntax
- **Exact versions** - no ^ in package.json dependencies

The codebase follows disciplined patterns and comprehensive tooling for maintainable, high-performance trading software.
